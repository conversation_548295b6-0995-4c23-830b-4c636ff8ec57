{"extends": ["@rocketseat/eslint-config/node", "prettier"], "plugins": ["prettier", "import-helpers"], "rules": {"semi": ["error", "always"], "import-helpers/order-imports": ["warn", {"newlinesBetween": "always", "groups": ["module", ["parent", "sibling", "index"]], "alphabetize": {"order": "asc", "ignoreCase": true}}], "no-useless-constructor": "off", "prettier/prettier": ["error", {"semi": true, "singleQuote": true, "trailingComma": "es5", "printWidth": 100, "tabWidth": 2}]}}