name: Deploy Serverless API

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch para deploy'
        required: false
        default: 'develop'
  push:
    branches:
      - main
      - develop
      - staging
      - 'release/*'

permissions:
  contents: read
  pull-requests: write

jobs:
  deploy_dev:
    if: startsWith(github.ref, 'refs/heads/develop')
    name: Deploy to Development
    runs-on: ubuntu-latest
    environment: DEV
    strategy:
      matrix:
        node-version: [20.x]
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
      - run: npm i
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Set up environment variables for dev
        run: |
          cat << EOF > .env
          ENVIRONMENT=${{ secrets.ENVIRONMENT }}
          CLOUD_FRONT_ARN=${{ secrets.CLOUD_FRONT_ARN }}
          ACM_ARN=${{ secrets.ACM_ARN }}
          CLOUD_ACCESS_KEY_ID=${{ secrets.CLOUD_ACCESS_KEY_ID }}
          COGNITO_CLIENT_ID=${{ secrets.COGNITO_CLIENT_ID }}
          CLOUD_REGION=${{ secrets.CLOUD_REGION }}
          JWT_SECRET=${{ secrets.JWT_SECRET }}
          INTEGRATION_TOKEN=${{ secrets.INTEGRATION_TOKEN }}
          CLOUD_SECRET_ACCESS_KEY_ID=${{ secrets.CLOUD_SECRET_ACCESS_KEY_ID }}
          COGNITO_USERPOOL_ID=${{ secrets.COGNITO_USERPOOL_ID }}
          GOOGLE_CLIENT_ID=${{ secrets.GOOGLE_CLIENT_ID }}
          DB_PASSWORD=${{ secrets.DB_PASSWORD }}
          SQS_QUEUE_SUBDOMAIN_ARN=${{ secrets.SQS_QUEUE_SUBDOMAIN_ARN }}
          SQS_QUEUE_DOMAIN_ARN=${{ secrets.SQS_QUEUE_DOMAIN_ARN }}
          SQS_URL_SUBDOMAIN=${{ secrets.SQS_URL_SUBDOMAIN }}
          SQS_URL_DOMAIN=${{ secrets.SQS_URL_DOMAIN }}
          ROUTE_53_ARN=${{ secrets.ROUTE_53_ARN }}
          HOSTED_ZONE_ID=${{ secrets.HOSTED_ZONE_ID }}
          INTERNAL_USER_PASSWORD=${{ secrets.INTERNAL_USER_PASSWORD }}
          CLOUD_FRONT_DISTRIBUTION_ID=${{ secrets.CLOUD_FRONT_DISTRIBUTION_ID }}
          API_GTW_REST_API_ID=${{ secrets.API_GTW_REST_API_ID }}
          API_GTW_REST_API_RESOURCE_ID=${{ secrets.API_GTW_REST_API_RESOURCE_ID }}
          DB_HOST=${{ vars.DB_HOST }}
          DB_NAME=${{ vars.DB_NAME }}
          DB_PORT=${{ vars.DB_PORT }}
          DB_USERNAME=${{ vars.DB_USERNAME }}
          API_KEY=${{ vars.API_KEY }}
          MAIN_DOMAIN=${{ vars.MAIN_DOMAIN }}
          FIRST_CUSTOMER_TAX_NUMBER=${{ vars.FIRST_CUSTOMER_TAX_NUMBER }}
          FIRST_CUSTOMER_DOMAIN=${{ vars.FIRST_CUSTOMER_DOMAIN }}
          CUSTOM_AWS_REGION=${{ vars.CUSTOM_AWS_REGION }}
          CORS_ORIGIN=${{ vars.CORS_ORIGIN }}
          FRONT_URL=${{ vars.FRONT_URL }}
          AWS_S3_PUBLIC_BUCKET=${{ vars.AWS_S3_PUBLIC_BUCKET }}
          S3_BUCKET=${{ vars.S3_BUCKET }}
          SECURITY_GROUP_ID=${{ vars.SECURITY_GROUP_ID }}
          SUBNET_PRIVATE_ID_ONE=${{ vars.SUBNET_PRIVATE_ID_ONE }}
          SUBNET_PRIVATE_ID_TWO=${{ vars.SUBNET_PRIVATE_ID_TWO }}
          BUCKET_FILES=${{ vars.BUCKET_FILES }}
          TOKEN_DATABASE=${{ vars.TOKEN_DATABASE }}
          INTERNAL_USER_EMAIL=${{ vars.INTERNAL_USER_EMAIL }}
          EMAIL_FROM=${{ vars.EMAIL_FROM }}
          EMAIL_DEBUG_MODE=${{ vars.EMAIL_DEBUG_MODE }}
          EMAIL_REPORT_DEV=${{ vars.EMAIL_REPORT_DEV }}
          SES_EMAIL_ARN=${{ vars.SES_EMAIL_ARN }}
          MAX_EXCEL_ROWS=${{ vars.MAX_EXCEL_ROWS }}
          MAX_EXCEL_FILE_SIZE_MB=${{ vars.MAX_EXCEL_FILE_SIZE_MB }}
          BQ_API_URL=${{ vars.BQ_API_URL }}
          EOF

      - name: Install Plugins and Deploy to dev
        run: |
          serverless plugin install -n serverless-plugin-typescript
          serverless plugin install -n serverless-offline@^13.0.0
          serverless plugin install -n serverless-dotenv-plugin
          serverless deploy --stage=dev --verbose
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Create Pull Request to main
        uses: repo-sync/pull-request@v2
        with:
          source_branch: 'develop'
          destination_branch: 'main'
          github_token: ${{ secrets.GITHUB_TOKEN }}
          pr_title: '🚀 Auto PR: Deploy develop to main'
          pr_body: |
            ## 🚀 Automated Deployment PR

            This PR was automatically created after successful deployment to development environment.

            **Commit:** ${{ github.sha }}
            **Mensagem:** ${{ github.event.head_commit.message }}
            **Autor:** ${{ github.event.head_commit.author.name }}

            ### ✅ Deployment Status
            - ✅ Development deployment completed successfully

            ### 📋 Checklist
            - [x] Development deployment successful

            Ready for production deployment! 🎉

  deploy_pd:
    if: startsWith(github.ref, 'refs/heads/main')
    name: Deploy to Production
    runs-on: ubuntu-latest
    environment: PRD
    strategy:
      matrix:
        node-version: [20.x]
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
      - run: npm i
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Set up environment variables for prd
        run: |
          cat << EOF > .env
          ENVIRONMENT=${{ secrets.ENVIRONMENT }}
          CLOUD_FRONT_ARN=${{ secrets.CLOUD_FRONT_ARN }}
          ACM_ARN=${{ secrets.ACM_ARN }}
          CLOUD_ACCESS_KEY_ID=${{ secrets.CLOUD_ACCESS_KEY_ID }}
          COGNITO_CLIENT_ID=${{ secrets.COGNITO_CLIENT_ID }}
          CLOUD_REGION=${{ secrets.CLOUD_REGION }}
          JWT_SECRET=${{ secrets.JWT_SECRET }}
          INTEGRATION_TOKEN=${{ secrets.INTEGRATION_TOKEN }}
          CLOUD_SECRET_ACCESS_KEY_ID=${{ secrets.CLOUD_SECRET_ACCESS_KEY_ID }}
          COGNITO_USERPOOL_ID=${{ secrets.COGNITO_USERPOOL_ID }}
          GOOGLE_CLIENT_ID=${{ secrets.GOOGLE_CLIENT_ID }}
          DB_PASSWORD=${{ secrets.DB_PASSWORD }}
          SQS_QUEUE_SUBDOMAIN_ARN=${{ secrets.SQS_QUEUE_SUBDOMAIN_ARN }}
          SQS_QUEUE_DOMAIN_ARN=${{ secrets.SQS_QUEUE_DOMAIN_ARN }}
          SQS_URL_SUBDOMAIN=${{ secrets.SQS_URL_SUBDOMAIN }}
          SQS_URL_DOMAIN=${{ secrets.SQS_URL_DOMAIN }}
          ROUTE_53_ARN=${{ secrets.ROUTE_53_ARN }}
          HOSTED_ZONE_ID=${{ secrets.HOSTED_ZONE_ID }}
          CLOUD_FRONT_DISTRIBUTION_ID=${{ secrets.CLOUD_FRONT_DISTRIBUTION_ID }}
          INTERNAL_USER_PASSWORD=${{ secrets.INTERNAL_USER_PASSWORD }}
          API_GTW_REST_API_ID=${{ secrets.API_GTW_REST_API_ID }}
          API_GTW_REST_API_RESOURCE_ID=${{ secrets.API_GTW_REST_API_RESOURCE_ID }}
          DB_HOST=${{ vars.DB_HOST }}
          DB_NAME=${{ vars.DB_NAME }}
          DB_PORT=${{ vars.DB_PORT }}
          DB_USERNAME=${{ vars.DB_USERNAME }}
          API_KEY=${{ vars.API_KEY }}
          MAIN_DOMAIN=${{ vars.MAIN_DOMAIN }}
          FIRST_CUSTOMER_TAX_NUMBER=${{ vars.FIRST_CUSTOMER_TAX_NUMBER }}
          FIRST_CUSTOMER_DOMAIN=${{ vars.FIRST_CUSTOMER_DOMAIN }}
          CUSTOM_AWS_REGION=${{ vars.CUSTOM_AWS_REGION }}
          CORS_ORIGIN=${{ vars.CORS_ORIGIN }}
          FRONT_URL=${{ vars.FRONT_URL }}
          AWS_S3_PUBLIC_BUCKET=${{ vars.AWS_S3_PUBLIC_BUCKET }}
          S3_BUCKET=${{ vars.S3_BUCKET }}
          SECURITY_GROUP_ID=${{ vars.SECURITY_GROUP_ID }}
          SUBNET_PRIVATE_ID_ONE=${{ vars.SUBNET_PRIVATE_ID_ONE }}
          SUBNET_PRIVATE_ID_TWO=${{ vars.SUBNET_PRIVATE_ID_TWO }}
          BUCKET_FILES=${{ vars.BUCKET_FILES }}
          TOKEN_DATABASE=${{ vars.TOKEN_DATABASE }}
          INTERNAL_USER_EMAIL=${{ vars.INTERNAL_USER_EMAIL }}
          EMAIL_FROM=${{ vars.EMAIL_FROM }}
          EMAIL_REPORT_DEV=${{ vars.EMAIL_REPORT_DEV }}
          SES_EMAIL_ARN=${{ vars.SES_EMAIL_ARN }}
          MAX_EXCEL_ROWS=${{ vars.MAX_EXCEL_ROWS }}
          MAX_EXCEL_FILE_SIZE_MB=${{ vars.MAX_EXCEL_FILE_SIZE_MB }}
          BQ_API_URL=${{ vars.BQ_API_URL }}
          EOF

      - name: Install Plugins and Deploy to prd
        run: |
          serverless plugin install -n serverless-plugin-typescript
          serverless plugin install -n serverless-offline@^13.0.0
          serverless plugin install -n serverless-dotenv-plugin
          serverless deploy --stage=prd --verbose
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
