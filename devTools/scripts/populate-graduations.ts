import { randomUUID } from 'crypto';

import { knexInstance } from '../../src/config/connectionDatabase.config';

async function populateGraduations() {
  try {
    console.log('🌱 Iniciando população do banco de dados com graduações...');

    // Limpa a tabela de graduações
    await knexInstance.raw('TRUNCATE TABLE graduations RESTART IDENTITY CASCADE');
    console.log('🧹 Tabela de graduações limpa.');

    const now = new Date();

    const graduations = [
      // Bacharelados
      { name: '<PERSON>na', type: 'Bacharelado' },
      { name: '<PERSON><PERSON><PERSON>', type: 'Bacharelado' },
      { name: 'Engenharia Civil', type: 'Bacharelado' },
      { name: 'Administração', type: 'Bacharelado' },
      { name: 'Ciência da Computação', type: 'Bacharelado' },
      { name: 'Pedagogia', type: '<PERSON>arel<PERSON>' },
      { name: 'Jornalism<PERSON>', type: '<PERSON>arel<PERSON>' },
      { name: 'Economia', type: 'Bacharelado' },
      { name: 'Arquitetura e Urbanismo', type: 'Bacharelado' },
      { name: '<PERSON>s Visuais', type: 'Bacharelado' },
      { name: 'Turismo', type: 'Bacharelado' },
      { name: 'Agronomia', type: 'Bacharelado' },
      { name: 'Segurança Pública', type: 'Bacharelado' },
      { name: 'Logística', type: 'Bacharelado' },
      { name: 'Gestão Ambiental', type: 'Bacharelado' },
      { name: 'Saúde Pública', type: 'Bacharelado' },
      { name: 'Educação Física', type: 'Bacharelado' },
      { name: 'Moda', type: 'Bacharelado' },
      { name: 'Gastronomia', type: 'Bacharelado' },
      { name: 'Música', type: 'Bacharelado' },
      { name: 'Design', type: 'Bacharelado' },
      { name: 'Marketing', type: 'Bacharelado' },
      { name: 'Gestão de Recursos Humanos', type: 'Bacharelado' },
      { name: 'Finanças', type: 'Bacharelado' },
      { name: 'Gestão Comercial', type: 'Bacharelado' },
      { name: 'Gestão da Qualidade', type: 'Bacharelado' },
      { name: 'Gestão da Inovação', type: 'Bacharelado' },
      { name: 'Medicina Veterinária', type: 'Bacharelado' },
      { name: 'Odontologia', type: 'Bacharelado' },
      { name: 'Farmácia', type: 'Bacharelado' },
      { name: 'Enfermagem', type: 'Bacharelado' },
      { name: 'Fisioterapia', type: 'Bacharelado' },
      { name: 'Fonoaudiologia', type: 'Bacharelado' },
      { name: 'Terapia Ocupacional', type: 'Bacharelado' },
      { name: 'Nutrição', type: 'Bacharelado' },
      { name: 'Biomedicina', type: 'Bacharelado' },
      { name: 'Psicologia', type: 'Bacharelado' },
      { name: 'Engenharia Mecânica', type: 'Bacharelado' },
      { name: 'Engenharia Elétrica', type: 'Bacharelado' },
      { name: 'Engenharia Química', type: 'Bacharelado' },
      { name: 'Engenharia de Produção', type: 'Bacharelado' },
      { name: 'Engenharia de Software', type: 'Bacharelado' },
      { name: 'Engenharia da Computação', type: 'Bacharelado' },
      { name: 'Engenharia Biomédica', type: 'Bacharelado' },
      { name: 'Engenharia Ambiental', type: 'Bacharelado' },
      { name: 'Engenharia Aeronáutica', type: 'Bacharelado' },
      { name: 'Engenharia Naval', type: 'Bacharelado' },
      { name: 'Letras', type: 'Bacharelado' },
      { name: 'História', type: 'Bacharelado' },
      { name: 'Geografia', type: 'Bacharelado' },
      { name: 'Matemática', type: 'Bacharelado' },
      { name: 'Física', type: 'Bacharelado' },
      { name: 'Química', type: 'Bacharelado' },
      { name: 'Biologia', type: 'Bacharelado' },
      { name: 'Filosofia', type: 'Bacharelado' },
      { name: 'Sociologia', type: 'Bacharelado' },
      { name: 'Publicidade e Propaganda', type: 'Bacharelado' },
      { name: 'Relações Públicas', type: 'Bacharelado' },
      { name: 'Rádio e TV', type: 'Bacharelado' },
      { name: 'Produção Audiovisual', type: 'Bacharelado' },
      { name: 'Sistemas de Informação', type: 'Bacharelado' },
      { name: 'Contabilidade', type: 'Bacharelado' },
      { name: 'Ciências Contábeis', type: 'Bacharelado' },
      { name: 'Design de Interiores', type: 'Bacharelado' },
      { name: 'Design de Produtos', type: 'Bacharelado' },
      { name: 'Design Gráfico', type: 'Bacharelado' },
      { name: 'Paisagismo', type: 'Bacharelado' },
      { name: 'Teatro', type: 'Bacharelado' },
      { name: 'Dança', type: 'Bacharelado' },
      { name: 'Cinema', type: 'Bacharelado' },
      { name: 'Fotografia', type: 'Bacharelado' },
      { name: 'Hotelaria', type: 'Bacharelado' },
      { name: 'Zootecnia', type: 'Bacharelado' },
      { name: 'Criminologia', type: 'Bacharelado' },
      { name: 'Perícia Criminal', type: 'Bacharelado' },
      { name: 'Ciências Ambientais', type: 'Bacharelado' },
      { name: 'Ecologia', type: 'Bacharelado' },
      { name: 'Sustentabilidade', type: 'Bacharelado' },
      { name: 'Epidemiologia', type: 'Bacharelado' },
      { name: 'Vigilância Sanitária', type: 'Bacharelado' },
      { name: 'Gestão em Saúde', type: 'Bacharelado' },
      { name: 'Esporte', type: 'Bacharelado' },
      { name: 'Gestão Esportiva', type: 'Bacharelado' },
      { name: 'Fisioterapia Esportiva', type: 'Bacharelado' },
      { name: 'Design de Moda', type: 'Bacharelado' },
      { name: 'Produção de Moda', type: 'Bacharelado' },
      { name: 'Gestão de Moda', type: 'Bacharelado' },
      { name: 'Tecnologia em Gastronomia', type: 'Bacharelado' },
      { name: 'Gestão de Restaurantes', type: 'Bacharelado' },
      { name: 'Nutrição e Gastronomia', type: 'Bacharelado' },
      { name: 'Composição', type: 'Bacharelado' },
      { name: 'Regência', type: 'Bacharelado' },
      { name: 'Instrumento', type: 'Bacharelado' },
      { name: 'Canto', type: 'Bacharelado' },
      { name: 'Produção Musical', type: 'Bacharelado' },
      { name: 'Design Digital', type: 'Bacharelado' },
      { name: 'Design de Produto', type: 'Bacharelado' },
      { name: 'Design de Moda', type: 'Bacharelado' },
      { name: 'Marketing Digital', type: 'Bacharelado' },
      { name: 'Marketing Estratégico', type: 'Bacharelado' },
      { name: 'Gestão de Marketing', type: 'Bacharelado' },
      { name: 'Psicologia Organizacional', type: 'Bacharelado' },
      { name: 'Administração de Recursos Humanos', type: 'Bacharelado' },
      { name: 'Gestão de Pessoas', type: 'Bacharelado' },
      { name: 'Gestão de Investimentos', type: 'Bacharelado' },
      { name: 'Gestão de Vendas', type: 'Bacharelado' },
      { name: 'Técnicas de Vendas', type: 'Bacharelado' },
      { name: 'Controle de Qualidade', type: 'Bacharelado' },
      { name: 'Auditoria', type: 'Bacharelado' },
      { name: 'Gestão de Processos', type: 'Bacharelado' },
      { name: 'Empreendedorismo', type: 'Bacharelado' },
      { name: 'Startup', type: 'Bacharelado' },
      { name: 'Gestão de Projetos', type: 'Bacharelado' },

      // Licenciaturas
      { name: 'Pedagogia', type: 'Licenciatura' },
      { name: 'Letras - Português', type: 'Licenciatura' },
      { name: 'Letras - Inglês', type: 'Licenciatura' },
      { name: 'Letras - Espanhol', type: 'Licenciatura' },
      { name: 'História', type: 'Licenciatura' },
      { name: 'Geografia', type: 'Licenciatura' },
      { name: 'Matemática', type: 'Licenciatura' },
      { name: 'Física', type: 'Licenciatura' },
      { name: 'Química', type: 'Licenciatura' },
      { name: 'Biologia', type: 'Licenciatura' },
      { name: 'Filosofia', type: 'Licenciatura' },
      { name: 'Sociologia', type: 'Licenciatura' },
      { name: 'Psicologia Educacional', type: 'Licenciatura' },
      { name: 'Educação Física', type: 'Licenciatura' },
      { name: 'Artes Visuais', type: 'Licenciatura' },
      { name: 'Música', type: 'Licenciatura' },
      { name: 'Teatro', type: 'Licenciatura' },
      { name: 'Dança', type: 'Licenciatura' },
      { name: 'Física - Licenciatura', type: 'Licenciatura' },
      { name: 'Química - Licenciatura', type: 'Licenciatura' },
      { name: 'Biologia - Licenciatura', type: 'Licenciatura' },
      { name: 'Matemática - Licenciatura', type: 'Licenciatura' },
      { name: 'História - Licenciatura', type: 'Licenciatura' },
      { name: 'Geografia - Licenciatura', type: 'Licenciatura' },
      { name: 'Letras - Licenciatura', type: 'Licenciatura' },
      { name: 'Filosofia - Licenciatura', type: 'Licenciatura' },
      { name: 'Sociologia - Licenciatura', type: 'Licenciatura' },
      { name: 'Pedagogia - Licenciatura', type: 'Licenciatura' },

      // Tecnólogos
      { name: 'Análise e Desenvolvimento de Sistemas', type: 'Tecnólogo' },
      { name: 'Tecnologia em Redes de Computadores', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão da Tecnologia da Informação', type: 'Tecnólogo' },
      { name: 'Tecnologia em Jogos Digitais', type: 'Tecnólogo' },
      { name: 'Tecnologia em Segurança da Informação', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gastronomia', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão de Turismo', type: 'Tecnólogo' },
      { name: 'Tecnologia em Eventos', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão da Cadeia de Suprimentos', type: 'Tecnólogo' },
      { name: 'Tecnologia em Comércio Exterior', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão Portuária', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão Ambiental', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão de Segurança Privada', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão de Recursos Humanos', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão Financeira', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão Comercial', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão de Marketing', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão da Qualidade', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão de Processos', type: 'Tecnólogo' },
      { name: 'Tecnologia em Design Gráfico', type: 'Tecnólogo' },
      { name: 'Tecnologia em Design de Interiores', type: 'Tecnólogo' },
      { name: 'Tecnologia em Design de Produto', type: 'Tecnólogo' },
      { name: 'Tecnologia em Produção Audiovisual', type: 'Tecnólogo' },
      { name: 'Tecnologia em Fotografia', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão de Restaurantes', type: 'Tecnólogo' },
      { name: 'Tecnologia em Produção de Moda', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão de Vendas', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão de Projetos', type: 'Tecnólogo' },
      { name: 'Tecnologia em Empreendedorismo', type: 'Tecnólogo' },
      { name: 'Tecnologia em Inovação', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão da Inovação', type: 'Tecnólogo' },
      { name: 'Tecnologia em Startup', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão de Qualidade', type: 'Tecnólogo' },
      { name: 'Tecnologia em Controle de Qualidade', type: 'Tecnólogo' },
      { name: 'Tecnologia em Auditoria', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão de Processos', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão de Investimentos', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão de Pessoas', type: 'Tecnólogo' },
      { name: 'Tecnologia em Psicologia Organizacional', type: 'Tecnólogo' },
      { name: 'Tecnologia em Marketing Digital', type: 'Tecnólogo' },
      { name: 'Tecnologia em Marketing Estratégico', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão de Marketing', type: 'Tecnólogo' },
      { name: 'Tecnologia em Design Digital', type: 'Tecnólogo' },
      { name: 'Tecnologia em Design de Produto', type: 'Tecnólogo' },
      { name: 'Tecnologia em Design de Moda', type: 'Tecnólogo' },
      { name: 'Tecnologia em Produção Musical', type: 'Tecnólogo' },
      { name: 'Tecnologia em Canto', type: 'Tecnólogo' },
      { name: 'Tecnologia em Instrumento', type: 'Tecnólogo' },
      { name: 'Tecnologia em Regência', type: 'Tecnólogo' },
      { name: 'Tecnologia em Composição', type: 'Tecnólogo' },
      { name: 'Tecnologia em Fisioterapia Esportiva', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão Esportiva', type: 'Tecnólogo' },
      { name: 'Tecnologia em Esporte', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão em Saúde', type: 'Tecnólogo' },
      { name: 'Tecnologia em Vigilância Sanitária', type: 'Tecnólogo' },
      { name: 'Tecnologia em Epidemiologia', type: 'Tecnólogo' },
      { name: 'Tecnologia em Saúde Pública', type: 'Tecnólogo' },
      { name: 'Tecnologia em Sustentabilidade', type: 'Tecnólogo' },
      { name: 'Tecnologia em Ecologia', type: 'Tecnólogo' },
      { name: 'Tecnologia em Ciências Ambientais', type: 'Tecnólogo' },
      { name: 'Tecnologia em Perícia Criminal', type: 'Tecnólogo' },
      { name: 'Tecnologia em Criminologia', type: 'Tecnólogo' },
      { name: 'Tecnologia em Segurança Pública', type: 'Tecnólogo' },
      { name: 'Tecnologia em Zootecnia', type: 'Tecnólogo' },
      { name: 'Tecnologia em Hotelaria', type: 'Tecnólogo' },
      { name: 'Tecnologia em Turismo', type: 'Tecnólogo' },
      { name: 'Tecnologia em Paisagismo', type: 'Tecnólogo' },
      { name: 'Tecnologia em Design Gráfico', type: 'Tecnólogo' },
      { name: 'Tecnologia em Fotografia', type: 'Tecnólogo' },
      { name: 'Tecnologia em Cinema', type: 'Tecnólogo' },
      { name: 'Tecnologia em Dança', type: 'Tecnólogo' },
      { name: 'Tecnologia em Teatro', type: 'Tecnólogo' },
      { name: 'Tecnologia em Música', type: 'Tecnólogo' },
      { name: 'Tecnologia em Artes Visuais', type: 'Tecnólogo' },
      { name: 'Tecnologia em Ciências Contábeis', type: 'Tecnólogo' },
      { name: 'Tecnologia em Contabilidade', type: 'Tecnólogo' },
      { name: 'Tecnologia em Gestão Financeira', type: 'Tecnólogo' },
      { name: 'Tecnologia em Finanças', type: 'Tecnólogo' },
      { name: 'Tecnologia em Sistemas de Informação', type: 'Tecnólogo' },
      { name: 'Tecnologia em Ciência da Computação', type: 'Tecnólogo' },
      { name: 'Tecnologia em Engenharia Civil', type: 'Tecnólogo' },
      { name: 'Tecnologia em Engenharia Mecânica', type: 'Tecnólogo' },
      { name: 'Tecnologia em Engenharia Elétrica', type: 'Tecnólogo' },
      { name: 'Tecnologia em Engenharia Química', type: 'Tecnólogo' },
      { name: 'Tecnologia em Engenharia de Produção', type: 'Tecnólogo' },
      { name: 'Tecnologia em Engenharia de Software', type: 'Tecnólogo' },
      { name: 'Tecnologia em Engenharia da Computação', type: 'Tecnólogo' },
      { name: 'Tecnologia em Engenharia Biomédica', type: 'Tecnólogo' },
      { name: 'Tecnologia em Engenharia Ambiental', type: 'Tecnólogo' },
      { name: 'Tecnologia em Engenharia Aeronáutica', type: 'Tecnólogo' },
      { name: 'Tecnologia em Engenharia Naval', type: 'Tecnólogo' },
      { name: 'Tecnologia em Administração', type: 'Tecnólogo' },
      { name: 'Tecnologia em Administração de Empresas', type: 'Tecnólogo' },
      { name: 'Tecnologia em Administração Pública', type: 'Tecnólogo' },
      { name: 'Tecnologia em Administração Hospitalar', type: 'Tecnólogo' },
      { name: 'Tecnologia em Administração Financeira', type: 'Tecnólogo' },
      { name: 'Tecnologia em Administração de Marketing', type: 'Tecnólogo' },
      { name: 'Tecnologia em Administração de Recursos Humanos', type: 'Tecnólogo' },
      { name: 'Tecnologia em Direito', type: 'Tecnólogo' },
      { name: 'Tecnologia em Direito Administrativo', type: 'Tecnólogo' },
      { name: 'Tecnologia em Direito Civil', type: 'Tecnólogo' },
      { name: 'Tecnologia em Direito Constitucional', type: 'Tecnólogo' },
      { name: 'Tecnologia em Direito Penal', type: 'Tecnólogo' },
      { name: 'Tecnologia em Direito Trabalhista', type: 'Tecnólogo' },
      { name: 'Tecnologia em Direito Tributário', type: 'Tecnólogo' },
      { name: 'Tecnologia em Direito Internacional', type: 'Tecnólogo' },
      { name: 'Tecnologia em Direito Ambiental', type: 'Tecnólogo' },
      { name: 'Tecnologia em Medicina', type: 'Tecnólogo' },
      { name: 'Tecnologia em Medicina Veterinária', type: 'Tecnólogo' },
      { name: 'Tecnologia em Odontologia', type: 'Tecnólogo' },
      { name: 'Tecnologia em Farmácia', type: 'Tecnólogo' },
      { name: 'Tecnologia em Enfermagem', type: 'Tecnólogo' },
      { name: 'Tecnologia em Fisioterapia', type: 'Tecnólogo' },
      { name: 'Tecnologia em Fonoaudiologia', type: 'Tecnólogo' },
      { name: 'Tecnologia em Terapia Ocupacional', type: 'Tecnólogo' },
      { name: 'Tecnologia em Nutrição', type: 'Tecnólogo' },
      { name: 'Tecnologia em Biomedicina', type: 'Tecnólogo' },
      { name: 'Tecnologia em Psicologia', type: 'Tecnólogo' },
      { name: 'Tecnologia em Agronomia', type: 'Tecnólogo' },
      { name: 'Tecnologia em Engenharia Agronômica', type: 'Tecnólogo' },
      { name: 'Tecnologia em Arquitetura e Urbanismo', type: 'Tecnólogo' },
      { name: 'Tecnologia em Economia', type: 'Tecnólogo' },
      { name: 'Tecnologia em Ciências Econômicas', type: 'Tecnólogo' },
      { name: 'Tecnologia em Jornalismo', type: 'Tecnólogo' },
      { name: 'Tecnologia em Publicidade e Propaganda', type: 'Tecnólogo' },
      { name: 'Tecnologia em Relações Públicas', type: 'Tecnólogo' },
      { name: 'Tecnologia em Rádio e TV', type: 'Tecnólogo' },
      { name: 'Tecnologia em Letras', type: 'Tecnólogo' },
      { name: 'Tecnologia em História', type: 'Tecnólogo' },
      { name: 'Tecnologia em Geografia', type: 'Tecnólogo' },
      { name: 'Tecnologia em Matemática', type: 'Tecnólogo' },
      { name: 'Tecnologia em Física', type: 'Tecnólogo' },
      { name: 'Tecnologia em Química', type: 'Tecnólogo' },
      { name: 'Tecnologia em Biologia', type: 'Tecnólogo' },
      { name: 'Tecnologia em Filosofia', type: 'Tecnólogo' },
      { name: 'Tecnologia em Sociologia', type: 'Tecnólogo' },
      { name: 'Tecnologia em Psicologia Educacional', type: 'Tecnólogo' },
    ];

    const graduationsWithId = graduations.map((graduation) => ({
      id: randomUUID(),
      ...graduation,
      created_at: now,
      updated_at: now,
      deleted_at: null,
    }));

    await knexInstance('graduations').insert(graduationsWithId);

    console.log(`✅ População concluída com sucesso!`);
    console.log(`📊 Total de graduações criadas: ${graduations.length}`);
    console.log('');
    console.log('🎯 Tipos de formação incluídos:');
    console.log('   • Bacharelado');
    console.log('   • Licenciatura');
    console.log('   • Tecnólogo');
    console.log('');
    console.log('📋 Distribuição por tipo:');
    const bacharelados = graduations.filter((g) => g.type === 'Bacharelado').length;
    const licenciaturas = graduations.filter((g) => g.type === 'Licenciatura').length;
    const tecnologos = graduations.filter((g) => g.type === 'Tecnólogo').length;
    console.log(`   • Bacharelados: ${bacharelados}`);
    console.log(`   • Licenciaturas: ${licenciaturas}`);
    console.log(`   • Tecnólogos: ${tecnologos}`);
    console.log('');
    console.log('🚀 Para testar, acesse: GET /v2/graduations');
    console.log('🔍 Exemplo de busca: GET /v2/graduations?search=medicina&page=1&limit=10');
    console.log('📋 Exemplo por tipo: GET /v2/graduations?type=Bacharelado&page=1&limit=20');
  } catch (error) {
    console.error('❌ Erro ao popular graduações:', error);
    process.exit(1);
  } finally {
    await knexInstance.destroy();
  }
}

// Executa o script
populateGraduations();
