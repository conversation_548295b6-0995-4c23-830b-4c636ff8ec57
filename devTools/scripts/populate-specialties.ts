import { randomUUID } from 'crypto';

import { knexInstance } from '../../src/config/connectionDatabase.config';

async function populateSpecialties() {
  try {
    console.log('🌱 Iniciando população do banco de dados com especialidades médicas...');

    // Limpa a tabela de especialidades
    await knexInstance.raw('TRUNCATE TABLE specialties RESTART IDENTITY CASCADE');
    console.log('🧹 Tabela de especialidades limpa.');

    const now = new Date();

    const specialties = [
      // Especialidades Médicas Principais
      { name: 'Cardiologia' },
      { name: 'Dermatologia' },
      { name: 'Endocrinologia' },
      { name: 'Gastroenterologia' },
      { name: 'Ginecologia' },
      { name: 'Neurologia' },
      { name: 'Oftalmologia' },
      { name: 'Ortopedia' },
      { name: 'Otorrinolaringologia' },
      { name: '<PERSON><PERSON><PERSON><PERSON>' },
      { name: '<PERSON>neumolo<PERSON>' },
      { name: '<PERSON>si<PERSON>at<PERSON>' },
      { name: 'Radiologia' },
      { name: 'Urolog<PERSON>' },

      // Subespecialidades Cardiovasculares
      { name: 'Cardiologia Intervencionista' },
      { name: 'Eletrofisiologia' },
      { name: 'Hemodinâmica' },

      // Subespecialidades Neurológicas
      { name: 'Neurocirurgia' },
      { name: 'Neurologia Vascular' },
      { name: 'Neuropediatria' },

      // Subespecialidades Pediátricas
      { name: 'Cardiologia Pediátrica' },
      { name: 'Endocrinologia Pediátrica' },
      { name: 'Gastroenterologia Pediátrica' },
      { name: 'Neurologia Pediátrica' },
      { name: 'Oncologia Pediátrica' },
      { name: 'Pneumologia Pediátrica' },

      // Subespecialidades Oncológicas
      { name: 'Oncologia Clínica' },
      { name: 'Oncologia Cirúrgica' },
      { name: 'Radioterapia' },

      // Subespecialidades Cirúrgicas
      { name: 'Cirurgia Cardiovascular' },
      { name: 'Cirurgia Geral' },
      { name: 'Cirurgia Plástica' },
      { name: 'Cirurgia Torácica' },
      { name: 'Cirurgia Vascular' },

      // Subespecialidades de Imagem
      { name: 'Radiologia Intervencionista' },
      { name: 'Ressonância Magnética' },
      { name: 'Tomografia Computadorizada' },
      { name: 'Ultrassonografia' },

      // Subespecialidades de Emergência
      { name: 'Medicina de Emergência' },
      { name: 'Terapia Intensiva' },
      { name: 'Terapia Intensiva Pediátrica' },

      // Subespecialidades de Laboratório
      { name: 'Anatomia Patológica' },
      { name: 'Bioquímica Clínica' },
      { name: 'Hematologia' },
      { name: 'Imunologia' },
      { name: 'Microbiologia' },

      // Subespecialidades de Reabilitação
      { name: 'Fisiatria' },
      { name: 'Fisioterapia' },
      { name: 'Fonoaudiologia' },
      { name: 'Terapia Ocupacional' },

      // Subespecialidades de Saúde Mental
      { name: 'Psiquiatria da Infância e Adolescência' },
      { name: 'Psiquiatria Forense' },
      { name: 'Psicologia' },

      // Subespecialidades de Saúde Pública
      { name: 'Epidemiologia' },
      { name: 'Medicina Preventiva' },
      { name: 'Saúde Coletiva' },

      // Subespecialidades de Genética
      { name: 'Genética Clínica' },
      { name: 'Genética Médica' },

      // Subespecialidades de Transplante
      { name: 'Transplante de Medula Óssea' },
      { name: 'Transplante de Órgãos' },

      // Subespecialidades de Dor
      { name: 'Medicina da Dor' },
      { name: 'Algologia' },

      // Subespecialidades de Medicina do Trabalho
      { name: 'Medicina do Trabalho' },
      { name: 'Medicina Legal' },

      // Subespecialidades de Medicina Esportiva
      { name: 'Medicina Esportiva' },
      { name: 'Ortopedia Esportiva' },

      // Subespecialidades de Medicina Nuclear
      { name: 'Medicina Nuclear' },

      // Subespecialidades de Medicina de Família
      { name: 'Medicina de Família e Comunidade' },
      { name: 'Clínica Médica' },

      // Subespecialidades de Medicina do Idoso
      { name: 'Geriatria' },
      { name: 'Gerontologia' },

      // Subespecialidades de Medicina do Sono
      { name: 'Medicina do Sono' },

      // Subespecialidades de Medicina Paliativa
      { name: 'Medicina Paliativa' },

      // Subespecialidades de Medicina de Viagem
      { name: 'Medicina de Viagem' },
      { name: 'Medicina Tropical' },

      // Subespecialidades de Medicina Aeroespacial
      { name: 'Medicina Aeroespacial' },

      // Subespecialidades de Medicina Subaquática
      { name: 'Medicina Subaquática' },

      // Subespecialidades de Medicina de Tráfego
      { name: 'Medicina de Tráfego' },

      // Subespecialidades de Medicina de Reabilitação
      { name: 'Reabilitação Cardiovascular' },
      { name: 'Reabilitação Pulmonar' },
      { name: 'Reabilitação Neurológica' },

      // Subespecialidades de Medicina Integrativa
      { name: 'Acupuntura' },
      { name: 'Homeopatia' },
      { name: 'Medicina Chinesa' },

      // Subespecialidades de Medicina de Precisão
      { name: 'Medicina Personalizada' },
      { name: 'Farmacogenética' },

      // Subespecialidades de Medicina Digital
      { name: 'Telemedicina' },
      { name: 'Medicina Digital' },
    ];

    const specialtiesWithId = specialties.map((specialty) => ({
      id: randomUUID(),
      ...specialty,
      created_at: now,
      updated_at: now,
      deleted_at: null,
    }));

    await knexInstance('specialties').insert(specialtiesWithId);

    console.log(`✅ População concluída com sucesso!`);
    console.log(`📊 Total de especialidades criadas: ${specialties.length}`);
    console.log('');
    console.log('🎯 Categorias de especialidades incluídas:');
    console.log('   • Especialidades Médicas Principais');
    console.log('   • Subespecialidades Cardiovasculares');
    console.log('   • Subespecialidades Neurológicas');
    console.log('   • Subespecialidades Pediátricas');
    console.log('   • Subespecialidades Oncológicas');
    console.log('   • Subespecialidades Cirúrgicas');
    console.log('   • Subespecialidades de Imagem');
    console.log('   • Subespecialidades de Emergência');
    console.log('   • Subespecialidades de Laboratório');
    console.log('   • Subespecialidades de Reabilitação');
    console.log('   • Subespecialidades de Saúde Mental');
    console.log('   • Subespecialidades de Saúde Pública');
    console.log('   • E muito mais...');
    console.log('');
    console.log('🚀 Para testar, acesse: GET /v2/specialties');
    console.log('🔍 Exemplo de busca: GET /v2/specialties?search=cardio&page=1&limit=10');
  } catch (error) {
    console.error('❌ Erro ao popular especialidades:', error);
    process.exit(1);
  } finally {
    await knexInstance.destroy();
  }
}

// Executa o script
populateSpecialties();
