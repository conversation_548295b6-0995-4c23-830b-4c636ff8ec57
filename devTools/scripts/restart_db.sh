#!/bin/bash

# Parar os containers do Docker
echo "Desligando os containers Docker..."
docker compose down

# Remover a pasta de dados com permissão elevada
echo "Removendo pasta de dados..."
sudo rm -rf data/

# Subir os containers novamente
echo "Subindo os containers Docker..."
docker compose up -d

# Aguardar o banco e a aplicação subirem
echo "Aguardando o banco e a aplicação iniciarem..."
sleep 5  # Ajuste o tempo conforme necessário

# Executar init (migrations, seeds, etc.)
echo "Executando npm run init..."
npx knex migrate:latest && npx knex seed:run && npm run dev
sleep 20 

