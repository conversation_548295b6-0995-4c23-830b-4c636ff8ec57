import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';

// Rotas
import indexRouter from './routes/index.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export const app = express();

app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

app.use(express.static(path.join(__dirname, 'public')));

// Rotas da API devem vir antes das rotas gerais
app.use('/api', indexRouter);

app.use('/', indexRouter);

// Redireciona qualquer rota não encontrada para a home
app.use((req, res) => {
  res.redirect('/');
});

const PORT = 4000;
app.listen(PORT, () => {
  console.log(`DevTools Web rodando em http://localhost:${PORT}`);
});
