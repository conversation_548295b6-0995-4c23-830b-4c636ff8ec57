@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap');

body {
  font-family: 'Inter', Arial, sans-serif;
  background: #18181b;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}
.header {
  background: #23232b;
  color: #fff;
  padding: 38px 0 22px 0;
  text-align: center;
  box-shadow: none;
  position: sticky;
  top: 0;
  z-index: 10;
}
.header h1 {
  margin: 0;
  font-size: 2.3rem;
  font-weight: 700;
  letter-spacing: 0.5px;
}
.container {
  max-width: 1200px;
  margin: 48px auto 0 auto;
  background: #202027;
  border-radius: 22px;
  box-shadow: none;
  padding: 56px 36px 40px 36px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.actions {
  margin-top: 32px;
  display: flex;
  flex-direction: column;
  gap: 18px;
  width: 100%;
}
.actions form {
  width: 100%;
}
button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 14px;
  padding: 14px 0 14px 18px;
  font-size: 1.08rem;
  border: none;
  border-radius: 8px;
  background: linear-gradient(90deg, #23272e 0%, #2d3748 100%);
  color: #e0e7ef;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s, transform 0.08s, box-shadow 0.15s;
  box-shadow: 0 2px 8px #23272e33;
  outline: none;
  position: relative;
}
button svg {
  flex-shrink: 0;
  filter: drop-shadow(0 1px 2px #0008);
}
button:active {
  transform: scale(0.97) translateY(1px);
  box-shadow: 0 1px 2px #23272e55;
}
button:focus-visible {
  box-shadow: 0 0 0 3px #6366f1cc, 0 2px 8px #23272e33;
  border: 1.5px solid #6366f1;
  background: linear-gradient(90deg, #3730a3 0%, #0ea5e9 100%);
}
button:hover {
  background: linear-gradient(90deg, #3730a3 0%, #0ea5e9 100%);
  color: #fff;
}
.terminal {
  margin-top: 48px;
  background: linear-gradient(120deg, #18181d 80%, #23232b 100%);
  color: #e5e7eb;
  border: 2.5px solid #22d3a8;
  box-shadow: 0 0 16px 0 #22d3a855;
  font-family: 'Fira Mono', 'Consolas', 'Menlo', 'Monaco', monospace;
  font-size: 1.28rem;
  padding: 32px 48px;
  border-left: 1.5px solid #22d3a8;
  border-right: 1.5px solid #22d3a8;
  border-top: 1.5px solid #22d3a8;
  border-bottom: 1.5px solid #22d3a8;
  position: relative;
}
.terminal-success {
  background: rgba(0, 40, 20, 0.85);
  color: #22d3a8;
  border: 1.5px solid #22d3a8;
  font-family: 'Fira Mono', 'Consolas', 'Menlo', 'Monaco', monospace;
  font-size: 1.28rem;
  padding: 32px 48px;
  margin-top: 24px;
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: none;
  white-space: pre-wrap;
  word-break: break-all;
 
}
.terminal-error {
  background: rgba(40, 0, 0, 0.85);
  color: #f87171;
  border: 1.5px solid #f87171;
  font-family: 'Fira Mono', 'Consolas', 'Menlo', 'Monaco', monospace;
  font-size: 1.28rem;
  padding: 32px 48px;
  margin-top: 24px;
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: none;
  white-space: pre-wrap;
  word-break: break-all;

}
.icon-alert {
  display: inline-flex;
  align-items: center;
  margin-right: 8px;
  color: #f87171;
  font-size: 1.2em;
}
.icon-success {
  display: inline-flex;
  align-items: center;
  margin-right: 8px;
  color: #22d3a8;
  font-size: 1.2em;
}
.terminal-success strong {
  color: #22d3a8;
}
.terminal-error strong {
  color: #f87171;
}
.terminal strong {
  font-weight: 700;
  font-size: 1.13em;
}
.terminal .cmd {
  color: #60a5fa;
  font-weight: 600;
}
.terminal .saida {
  color: #cbd5e1;
}
.terminal .keyword {
  color: #fbbf24;
  font-weight: 700;
}
.terminal .error-keyword {
  color: #f87171;
  font-weight: 700;
}
.terminal pre {
  margin: 0;
  font-size: 1em;
}
.terminal-copy-btn {
  position: absolute;
  top: 18px;
  right: 22px;
  background: #23232b;
  color: #60a5fa;
  border: 1.5px solid #23232b;
  border-radius: 7px;
  padding: 6px 16px;
  font-size: 1.01rem;
  font-family: inherit;
  font-weight: 600;
  cursor: pointer;
  opacity: 0.85;
  transition: background 0.13s, color 0.13s, border 0.13s;
  z-index: 10;
}
.terminal-copy-btn:hover {
  background: #23233a;
  color: #fff;
  border-color: #60a5fa;
  opacity: 1;
}
.terminal::-webkit-scrollbar {
  width: 8px;
  background: #23232b;
}
.terminal::-webkit-scrollbar-thumb {
  background: #23233a;
  border-radius: 8px;
}
.terminal::-webkit-scrollbar-thumb:hover {
  background: #6366f1;
}
#process-alert {
  display: none;
  position: fixed;
  top: 38px;
  left: 50%;
  transform: translateX(-50%);
  background: #23232b;
  color: #fff;
  padding: 14px 36px;
  border-radius: 10px;
  box-shadow: none;
  font-size: 1.13rem;
  z-index: 1000;
  transition: opacity 0.2s;
  opacity: 0.97;
  min-width: 200px;
  display: flex;
  align-items: center;
  gap: 12px;
  border: 1.5px solid #6366f1;
}
#process-alert:before {
  content: '';
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2.5px solid #6366f1;
  border-top: 2.5px solid #fff;
  border-radius: 50%;
  animation: spinner-rotate 0.8s linear infinite;
  margin-right: 10px;
}
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
@keyframes spinner-rotate {
  100% { transform: rotate(360deg); }
}
@media (max-width: 1200px) {
  .container {
    max-width: 99vw;
    padding: 18px 2vw 18px 2vw;
  }
  .dashboard {
    grid-template-columns: 1fr 1fr;
    gap: 18px;
  }
}
@media (max-width: 900px) {
  .dashboard {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  .container {
    max-width: 99vw;
    padding: 12px 2vw 12px 2vw;
  }
}
@media (max-width: 600px) {
  .container {
    max-width: 99vw;
    padding: 8px 1vw 8px 1vw;
  }
  .header h1 {
    font-size: 1.3rem;
  }
  .terminal {
    font-size: 0.99rem;
    padding: 12px 4px;
  }
  .card-title {
    font-size: 1.08rem;
  }
  .card-icon .lucide {
    width: 32px;
    height: 32px;
  }
  .card-icon {
    width: 38px;
    height: 38px;
    min-width: 38px;
    min-height: 38px;
  }
}
.dashboard {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 28px;
  width: 100%;
  margin-top: 0;
}
.card {
  background: #23232b;
  border-radius: 18px;
  border: 1.5px solid #23232b;
  box-shadow: none;
  transition: border 0.16s, background 0.16s, transform 0.10s;
  padding: 0;
  min-width: 0;
  min-height: 0;
  outline: none;
}
.card button {
  background: none;
  border: none;
  width: 100%;
  padding: 38px 28px 32px 28px;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 28px;
  cursor: pointer;
  border-radius: 18px;
  outline: none;
  transition: background 0.13s, border 0.13s, transform 0.09s;
  position: relative;
}
.card:hover, .card button:focus-visible {
  border: 1.5px solid #6366f1;
  background: #23233a;
  transform: translateY(-2px) scale(1.018);
}
.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border-radius: 0;
  width: 60px;
  height: 60px;
  min-width: 60px;
  min-height: 60px;
  margin-right: 0;
}
.card-icon .lucide {
  width: 48px;
  height: 48px;
  stroke-width: 2.5;
  filter: none;
}
.card-icon-green .lucide {
  color: #22d3a8;
  stroke: #22d3a8;
}
.card-icon-red .lucide {
  color: #f87171;
  stroke: #f87171;
}
.card-icon-orange .lucide {
  color: #fbbf24;
  stroke: #fbbf24;
}
.card-icon-blue .lucide {
  color: #60a5fa;
  stroke: #60a5fa;
}
.card-icon-green-light .lucide {
  color: #4ade80;
  stroke: #4ade80;
}
.card-icon-indigo .lucide {
  color: #6366f1;
}

.card-icon-purple .lucide {
  color: #a855f7;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}
.card-title {
  font-size: 1.35rem;
  font-weight: 700;
  color: #fff;
  letter-spacing: 0.01em;
  margin-bottom: 2px;
}
.card-desc {
  font-size: 1.08rem;
  color: #b3b3c6;
  font-weight: 400;
  margin-top: 0;
}
.card:active, .card button:active {
  background: #23233a;
  border: 1.5px solid #6366f1;
  transform: scale(0.99);
}
.card button:focus-visible {
  box-shadow: 0 0 0 2.5px #6366f1cc;
} 