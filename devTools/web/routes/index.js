import axios from 'axios';
import { exec } from 'child_process';
import dotenv from 'dotenv';
import 'dotenv/config';
import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config({ path: path.resolve(__dirname, '../../../.env') });

const router = express.Router();

router.get('/', (req, res) => {
  res.render('index', {
    title: 'Dev Tools BQ',
    message: 'Painel de DevTools',
    result: req.query.result || undefined,
  });
});

function runCommand(command) {
  return new Promise((resolve, reject) => {
    exec(
      command,
      {
        cwd: path.resolve(__dirname, '../../../'),
        env: process.env,
      },
      (error, stdout, stderr) => {
        if (error) return reject(stderr || error.message);
        resolve(stdout || stderr);
      }
    );
  });
}

function getDeleteDataCommand() {
  const isWSL = process.env.WSL_DISTRO_NAME || process.platform === 'win32';
  if (isWSL) {
    return 'wsl rm -rf data/postgres';
  }
  return 'rm -rf data/postgres';
}

function encodeResult(result) {
  return encodeURIComponent(result);
}

router.post('/docker-down', async (req, res) => {
  try {
    await runCommand('docker compose down');
    res.redirect('/?result=' + encodeResult('Docker derrubado com sucesso!'));
  } catch (err) {
    res.redirect('/?result=' + encodeResult('Erro ao derrubar Docker: ' + err));
  }
});

router.post('/delete-data', async (req, res) => {
  try {
    const downLog = await runCommand('docker compose down');
    const deleteLog = await runCommand(getDeleteDataCommand());
    const upLog = await runCommand('docker compose up -d');
    const result = `Data apagado e Docker reiniciado com sucesso!\n\nSaída:\n${downLog}\n${deleteLog}\n${upLog}`;
    res.redirect('/?result=' + encodeResult(result));
  } catch (err) {
    res.redirect('/?result=' + encodeResult('Erro ao apagar data e reiniciar Docker: ' + err));
  }
});

router.post('/docker-up', async (req, res) => {
  try {
    const upLog = await runCommand('docker compose up -d');
    const result = `Docker iniciado com sucesso!\n\nSaída:\n${upLog}`;
    res.redirect('/?result=' + encodeResult(result));
  } catch (err) {
    res.redirect('/?result=' + encodeResult('Erro ao iniciar Docker: ' + err));
  }
});

function formatTerminalResult({ title, command, output, isError }) {
  return `
<strong style='color:${isError ? '#f87171' : '#22d3ee'};'>${title}</strong>

<strong>Comando:</strong>\n${command}


<strong>Saída:</strong>\n${output}
`;
}

async function waitForPostgresReady(maxTries = 20, delayMs = 1500) {
  const { Client } = await import('pg');
  for (let i = 0; i < maxTries; i++) {
    const client = new Client({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      user: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
    });
    try {
      await client.connect();
      await client.end();
      return true;
    } catch (e) {
      await new Promise((resolve) => setTimeout(resolve, delayMs));
    }
  }
  throw new Error('Banco de dados não ficou pronto a tempo.');
}

router.post('/migrate', async (req, res) => {
  try {
    const knexCliPath = path.resolve(__dirname, '../../../node_modules/knex/bin/cli.js');
    const cmd = `npx dotenv -e .env -- node --loader ts-node/esm "${knexCliPath}" migrate:latest --knexfile knexfile.ts`;
    const log = await runCommand(cmd);
    const result = formatTerminalResult({
      title: 'Migrations executadas com sucesso!',
      command: cmd,
      output: log,
      isError: false,
    });
    res.redirect('/?result=' + encodeResult(result));
  } catch (err) {
    const result = formatTerminalResult({
      title: 'Erro ao rodar migrations',
      command: 'npx dotenv ...',
      output: err,
      isError: true,
    });
    res.redirect('/?result=' + encodeResult(result));
  }
});

router.post('/seed', async (req, res) => {
  try {
    const knexCliPath = path.resolve(__dirname, '../../../node_modules/knex/bin/cli.js');
    const cmd = `npx dotenv -e .env -- node --loader ts-node/esm "${knexCliPath}" seed:run --knexfile knexfile.ts`;
    const log = await runCommand(cmd);
    const result = formatTerminalResult({
      title: 'Seeds executadas com sucesso!',
      command: cmd,
      output: log,
      isError: false,
    });
    res.redirect('/?result=' + encodeResult(result));
  } catch (err) {
    const result = formatTerminalResult({
      title: 'Erro ao rodar seeds',
      command: 'npx dotenv ...',
      output: err,
      isError: true,
    });
    res.redirect('/?result=' + encodeResult(result));
  }
});

router.post('/all', async (req, res) => {
  const logs = [];
  try {
    logs.push('--- [1/5] Derrubando Docker... ---');
    logs.push(await runCommand('docker compose down'));
    logs.push('--- [2/5] Apagando dados... ---');
    logs.push(await runCommand(getDeleteDataCommand()));
    logs.push('--- [3/5] Subindo Docker... ---');
    logs.push(await runCommand('docker compose up -d'));
    await waitForPostgresReady();
    const knexCliPath = path.resolve(__dirname, '../../../node_modules/knex/bin/cli.js');
    const migrateCmd = `npx dotenv -e .env -- node --loader ts-node/esm "${knexCliPath}" migrate:latest --knexfile knexfile.ts`;
    logs.push('--- [4/5] Rodando migrations... ---');
    logs.push(await runCommand(migrateCmd));
    const seedCmd = `npx dotenv -e .env -- node --loader ts-node/esm "${knexCliPath}" seed:run --knexfile knexfile.ts`;
    logs.push('--- [5/5] Rodando seeds... ---');
    logs.push(await runCommand(seedCmd));
    const result = formatTerminalResult({
      title: 'Ambiente resetado com sucesso!',
      command: 'docker compose down + delete data + up + migrate + seed',
      output: logs.join('\n'),
      isError: false,
    });
    res.redirect('/?result=' + encodeResult(result));
  } catch (err) {
    logs.push('--- ERRO ---');
    logs.push(err);
    const result = formatTerminalResult({
      title: 'Erro ao resetar ambiente',
      command: 'docker compose down + delete data + up + migrate + seed',
      output: logs.join('\n'),
      isError: true,
    });
    res.redirect('/?result=' + encodeResult(result));
  }
});

router.post('/databaseSeeder', async (req, res) => {
  try {
    const response = await axios.post('http://localhost:3001/v2/populate-database');
    const result = formatTerminalResult({
      title: 'Banco populado com sucesso!',
      command: 'POST /v2/populate-database',
      output:
        typeof response.data === 'string' ? response.data : JSON.stringify(response.data, null, 2),
      isError: false,
    });
    res.redirect('/?result=' + encodeResult(result));
  } catch (err) {
    const result = formatTerminalResult({
      title: 'Erro ao popular banco',
      command: 'POST /v2/populate-database',
      output: err.response?.data || err.message,
      isError: true,
    });
    res.redirect('/?result=' + encodeResult(result));
  }
});

router.post('/userSeeder', async (req, res) => {
  let bancoOutput = '';
  let usuariosOutput = '';
  try {
    // 1. Popula o banco de dados
    const bancoResponse = await axios.post('http://localhost:3001/v2/populate-database');
    bancoOutput =
      typeof bancoResponse.data === 'string'
        ? bancoResponse.data
        : JSON.stringify(bancoResponse.data, null, 2);
    // 2. Popula os usuários de teste
    const usuariosResponse = await axios.post('http://localhost:3001/v2/populate-users');
    usuariosOutput =
      typeof usuariosResponse.data === 'string'
        ? usuariosResponse.data
        : JSON.stringify(usuariosResponse.data, null, 2);
    // 3. Mostra resultado combinado
    const result = formatTerminalResult({
      title: 'Banco e usuários de teste populados com sucesso!',
      command: 'POST /v2/populate-database + /v2/populate-users',
      output: bancoOutput + '\n\n' + usuariosOutput,
      isError: false,
    });
    res.redirect('/?result=' + encodeResult(result));
  } catch (err) {
    const result = formatTerminalResult({
      title: 'Erro ao popular banco ou usuários de teste',
      command: 'POST /v2/populate-database + /v2/populate-users',
      output: (bancoOutput ? bancoOutput + '\n\n' : '') + (err.response?.data || err.message),
      isError: true,
    });
    res.redirect('/?result=' + encodeResult(result));
  }
});

router.post('/populate-specialties', async (req, res) => {
  try {
    const scriptPath = path.resolve(__dirname, '../../scripts/populate-specialties.ts');
    const cmd = `cd "${path.resolve(__dirname, '../../')}" && npx dotenv -e .env -- ts-node "${scriptPath}"`;
    const log = await runCommand(cmd);
    const result = formatTerminalResult({
      title: 'Especialidades médicas populadas com sucesso!',
      command: cmd,
      output: log,
      isError: false,
    });
    res.redirect('/?result=' + encodeResult(result));
  } catch (err) {
    const result = formatTerminalResult({
      title: 'Erro ao popular especialidades médicas',
      command: 'ts-node populate-specialties.ts',
      output: err,
      isError: true,
    });
    res.redirect('/?result=' + encodeResult(result));
  }
});

router.post('/populate-graduations', async (req, res) => {
  try {
    const scriptPath = path.resolve(__dirname, '../../scripts/populate-graduations.ts');
    const cmd = `cd "${path.resolve(__dirname, '../../')}" && npx dotenv -e .env -- ts-node "${scriptPath}"`;
    const log = await runCommand(cmd);
    const result = formatTerminalResult({
      title: 'Graduações populadas com sucesso!',
      command: cmd,
      output: log,
      isError: false,
    });
    res.redirect('/?result=' + encodeResult(result));
  } catch (err) {
    const result = formatTerminalResult({
      title: 'Erro ao popular graduações',
      command: 'ts-node populate-graduations.ts',
      output: err,
      isError: true,
    });
    res.redirect('/?result=' + encodeResult(result));
  }
});

router.get('/api/status', async (req, res) => {
  try {
    const status = {
      timestamp: new Date().toISOString(),
      services: {},
      ports: {
        devTools: process.env.PORT || 4000,
        api: process.env.API_PORT || 3001,
        database: process.env.DB_PORT || 5432,
      },
      environment: process.env.NODE_ENV || 'development',
      system: {
        platform: process.platform,
        nodeVersion: process.version,
        memoryUsage: process.memoryUsage(),
        uptime: process.uptime(),
      },
      database: {
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        name: process.env.DB_NAME,
        user: process.env.DB_USERNAME,
      },
    };

    // Verificar status do Docker
    try {
      const dockerStatus = await runCommand(
        'docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"'
      );
      const containerCount = dockerStatus.split('\n').filter((line) => line.trim()).length - 1;
      status.services.docker = {
        running: true,
        containers: dockerStatus,
        containerCount,
      };
    } catch (err) {
      status.services.docker = {
        running: false,
        error: err.message,
      };
    }

    // Verificar status da API
    try {
      // Primeiro tenta o endpoint de health check
      const apiResponse = await axios.get(`http://localhost:${status.ports.api}/v2/healthCheck`, {
        timeout: 5000,
      });
      status.services.api = {
        running: true,
        status: apiResponse.status,
        data: apiResponse.data,
      };
    } catch (err) {
      // Se falhar, tenta verificar se o servidor está rodando
      try {
        const fallbackResponse = await axios.get(`http://localhost:${status.ports.api}/v2`, {
          timeout: 3000,
        });
        status.services.api = {
          running: true,
          status: fallbackResponse.status,
          note: 'Servidor rodando (health check não disponível)',
        };
      } catch (fallbackErr) {
        status.services.api = {
          running: false,
          error: err.message,
        };
      }
    }

    // Verificar status do banco de dados
    try {
      const { Client } = await import('pg');
      const client = new Client({
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        user: process.env.DB_USERNAME,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
      });
      await client.connect();
      const dbResult = await client.query('SELECT version()');
      await client.end();

      status.services.database = {
        running: true,
        version: dbResult.rows[0].version,
      };
    } catch (err) {
      status.services.database = {
        running: false,
        error: err.message,
      };
    }

    res.json(status);
  } catch (err) {
    console.error('❌ Erro ao gerar status:', err);
    res.status(500).json({
      error: 'Erro ao obter status',
      message: err.message,
    });
  }
});

export default router;
