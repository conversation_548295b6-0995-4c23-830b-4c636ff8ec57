import axios from 'axios';

async function testStatus() {
  try {
    console.log('🧪 Testando endpoint /api/status...');

    const response = await axios.get('http://localhost:4000/api/status');

    console.log('✅ Status recebido com sucesso!');
    console.log('📊 Status:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('❌ Erro ao testar endpoint:', error.message);
    if (error.response) {
      console.error('📥 Response status:', error.response.status);
      console.error('📥 Response data:', error.response.data);
    }
  }
}

testStatus();
