<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %></title>
  <link rel="stylesheet" href="/style/main.css">
  <link rel="icon" href="https://lucide.dev/favicon.ico">
  <script src="https://unpkg.com/lucide@latest"></script>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <div class="app-container">
    <!-- Sidebar Moderna -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <div class="logo-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M12 2L2 7l10 5 10-5-10-5z"/>
              <path d="M2 17l10 5 10-5"/>
              <path d="M2 12l10 5 10-5"/>
            </svg>
          </div>
          <div class="logo-text">
            <h1>DevTools</h1>
            <span>Painel de Controle</span>
          </div>
        </div>
      </div>
      
      <nav class="sidebar-nav">
        <a href="#dashboard" class="nav-item active" data-section="dashboard">
          <div class="nav-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <rect x="3" y="3" width="7" height="7"/>
              <rect x="14" y="3" width="7" height="7"/>
              <rect x="14" y="14" width="7" height="7"/>
              <rect x="3" y="14" width="7" height="7"/>
            </svg>
          </div>
          <span>Dashboard</span>
        </a>
        
        <a href="#database" class="nav-item" data-section="database">
          <div class="nav-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <ellipse cx="12" cy="5" rx="9" ry="3"/>
              <path d="M3 5v14c0 1.66 4.03 3 9 3s9-1.34 9-3V5"/>
              <path d="M3 12c0 1.66 4.03 3 9 3s9-1.34 9-3"/>
            </svg>
          </div>
          <span>Banco de Dados</span>
        </a>
        
        <a href="#populate" class="nav-item" data-section="populate">
          <div class="nav-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M12 22s8-4 8-10V5.5A2.5 2.5 0 0 0 17.5 3c-2.5 0-3.5 2-5.5 2S6.5 3 4.5 3A2.5 2.5 0 0 0 2 5.5V12c0 6 8 10 8 10z"/>
            </svg>
          </div>
          <span>Popular Dados</span>
        </a>
      </nav>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
      <header class="page-header">
        <div class="header-content">
          <h1 id="section-title"><%= title %></h1>
          <p id="section-desc">Painel de controle para desenvolvedores</p>
        </div>
        <div class="header-actions">
          <div class="status-badge" id="overall-status">
            <div class="status-dot"></div>
            <span>Verificando...</span>
          </div>
        </div>
      </header>
      
      <div class="content-wrapper">
        <!-- Dashboard Section -->
        <section id="dashboard-section" class="dashboard-section active">
          <!-- Status Cards -->
          <div class="status-cards">
            <div class="status-card" id="api-card">
              <div class="card-header">
                <div class="card-icon api-icon">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                    <path d="M2 17l10 5 10-5"/>
                    <path d="M2 12l10 5 10-5"/>
                  </svg>
                </div>
                <div class="card-status">
                  <div class="status-indicator" id="api-indicator"></div>
                </div>
              </div>
                              <div class="card-content">
                  <h3>Servidor API</h3>
                  <div class="card-metrics">
                    <div class="metric">
                      <span class="metric-label">Status</span>
                      <span class="metric-value" id="api-status">Verificando...</span>
                    </div>
                    <div class="metric">
                      <span class="metric-label">Porta</span>
                      <span class="metric-value" id="api-port">3001</span>
                    </div>
                    <div class="metric">
                      <span class="metric-label">Resposta</span>
                      <span class="metric-value" id="api-response">-</span>
                    </div>
                  </div>
                </div>
            </div>

            <div class="status-card" id="db-card">
              <div class="card-header">
                <div class="card-icon db-icon">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <ellipse cx="12" cy="5" rx="9" ry="3"/>
                    <path d="M3 5v14c0 1.66 4.03 3 9 3s9-1.34 9-3V5"/>
                    <path d="M3 12c0 1.66 4.03 3 9 3s9-1.34 9-3"/>
                  </svg>
                </div>
                <div class="card-status">
                  <div class="status-indicator" id="db-indicator"></div>
                </div>
              </div>
                              <div class="card-content">
                  <h3>Banco de Dados</h3>
                  <div class="card-metrics">
                    <div class="metric">
                      <span class="metric-label">Status</span>
                      <span class="metric-value" id="db-status">Verificando...</span>
                    </div>
                    <div class="metric">
                      <span class="metric-label">Porta</span>
                      <span class="metric-value" id="db-port">5432</span>
                    </div>
                    <div class="metric">
                      <span class="metric-label">Versão</span>
                      <span class="metric-value" id="db-version">-</span>
                    </div>
                  </div>
                </div>
            </div>

            <div class="status-card" id="docker-card">
              <div class="card-header">
                <div class="card-icon docker-icon">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                    <circle cx="9" cy="9" r="2"/>
                    <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                  </svg>
                </div>
                <div class="card-status">
                  <div class="status-indicator" id="docker-indicator"></div>
                </div>
              </div>
                              <div class="card-content">
                  <h3>Serviços Docker</h3>
                  <div class="card-metrics">
                    <div class="metric">
                      <span class="metric-label">Containers</span>
                      <span class="metric-value" id="docker-containers">-</span>
                    </div>
                    <div class="metric">
                      <span class="metric-label">Status</span>
                      <span class="metric-value" id="docker-status">Verificando...</span>
                    </div>
                    <div class="metric">
                      <span class="metric-label">Memória</span>
                      <span class="metric-value" id="docker-memory">-</span>
                    </div>
                  </div>
                </div>
            </div>

            <div class="status-card" id="devtools-card">
              <div class="card-header">
                <div class="card-icon devtools-icon">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <rect x="3" y="3" width="7" height="7"/>
                    <rect x="14" y="3" width="7" height="7"/>
                    <rect x="14" y="14" width="7" height="7"/>
                    <rect x="3" y="14" width="7" height="7"/>
                  </svg>
                </div>
                <div class="card-status">
                  <div class="status-indicator status-online"></div>
                </div>
              </div>
                              <div class="card-content">
                  <h3>Painel DevTools</h3>
                  <div class="card-metrics">
                    <div class="metric">
                      <span class="metric-label">Status</span>
                      <span class="metric-value">Online</span>
                    </div>
                    <div class="metric">
                      <span class="metric-label">Porta</span>
                      <span class="metric-value">4000</span>
                    </div>
                    <div class="metric">
                      <span class="metric-label">Última Atualização</span>
                      <span class="metric-value" id="last-update">-</span>
                    </div>
                  </div>
                </div>
            </div>
          </div>

          <!-- System Info Grid -->
          <div class="info-grid">
            <div class="info-panel">
              <h3>Informações do Sistema</h3>
              <div class="info-list">
                <div class="info-item">
                  <span class="info-label">Ambiente</span>
                  <span class="info-value" id="env-type">-</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Node.js</span>
                  <span class="info-value" id="node-version">-</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Plataforma</span>
                  <span class="info-value" id="platform">-</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Memória</span>
                  <span class="info-value" id="memory-usage">-</span>
                </div>
              </div>
            </div>

            <div class="info-panel">
              <h3>Status da Rede</h3>
              <div class="info-list">
                <div class="info-item">
                  <span class="info-label">API Endpoint</span>
                  <span class="info-value">http://localhost:3001</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Banco de Dados</span>
                  <span class="info-value">postgresql://localhost:5432</span>
                </div>
                <div class="info-item">
                  <span class="info-label">DevTools</span>
                  <span class="info-value">http://localhost:4000</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Verificação de Saúde</span>
                  <span class="info-value" id="health-status">-</span>
                </div>
              </div>
            </div>

            <div class="info-panel">
              <h3>Ações Rápidas</h3>
              <div class="info-list">
                <div class="info-item">
                  <span class="info-label">Status Docker</span>
                  <span class="info-value" id="docker-quick-status">-</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Tempo Ativo</span>
                  <span class="info-value" id="system-uptime">-</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Serviços Ativos</span>
                  <span class="info-value" id="active-services">-</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Última Atualização</span>
                  <span class="info-value" id="last-update-time">-</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Database Management Section -->
        <section id="database-section" class="dashboard-section">
          <div class="action-grid">
            <div class="action-card">
              <form action="/docker-up" method="POST">
                <button type="submit" class="action-button action-success">
                  <div class="action-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <polygon points="5 3 19 12 5 21 5 3"/>
                    </svg>
                  </div>
                  <div class="action-content">
                    <h3>Iniciar Docker</h3>
                    <p>Inicia containers do banco e serviços</p>
                  </div>
                </button>
              </form>
            </div>

            <div class="action-card">
              <form action="/docker-down" method="POST">
                <button type="submit" class="action-button action-danger">
                  <div class="action-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <rect x="5" y="5" width="14" height="14" rx="2"/>
                    </svg>
                  </div>
                  <div class="action-content">
                    <h3>Parar Docker</h3>
                    <p>Para todos os containers Docker</p>
                  </div>
                </button>
              </form>
            </div>

            <div class="action-card">
              <form action="/delete-data" method="POST">
                <button type="submit" class="action-button action-warning">
                  <div class="action-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <polyline points="3 6 5 6 21 6"/>
                      <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v2"/>
                    </svg>
                  </div>
                  <div class="action-content">
                    <h3>Limpar Dados</h3>
                    <p>Remove dados do banco e reinicia Docker</p>
                  </div>
                </button>
              </form>
            </div>

            <div class="action-card">
              <form action="/migrate" method="POST">
                <button type="submit" class="action-button action-primary">
                  <div class="action-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <ellipse cx="12" cy="5" rx="9" ry="3"/>
                      <path d="M3 5v14c0 1.66 4.03 3 9 3s9-1.34 9-3V5"/>
                      <path d="M3 12c0 1.66 4.03 3 9 3s9-1.34 9-3"/>
                    </svg>
                  </div>
                  <div class="action-content">
                    <h3>Executar Migrations</h3>
                    <p>Executa as migrations do banco de dados</p>
                  </div>
                </button>
              </form>
            </div>

            <div class="action-card">
              <form action="/seed" method="POST">
                <button type="submit" class="action-button action-success">
                  <div class="action-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path d="M12 22s8-4 8-10V5.5A2.5 2.5 0 0 0 17.5 3c-2.5 0-3.5 2-5.5 2S6.5 3 4.5 3A2.5 2.5 0 0 0 2 5.5V12c0 6 8 10 8 10z"/>
                    </svg>
                  </div>
                  <div class="action-content">
                    <h3>Executar Seeds</h3>
                    <p>Popula o banco com dados iniciais</p>
                  </div>
                </button>
              </form>
            </div>

            <div class="action-card">
              <form action="/all" method="POST">
                <button type="submit" class="action-button action-info">
                  <div class="action-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <polyline points="23 4 23 10 17 10"/>
                      <polyline points="1 20 1 14 7 14"/>
                      <path d="M3.51 9a9 9 0 0 1 14.13-3.36L23 10M1 14l5.36 5.36A9 9 0 0 0 20.49 15"/>
                    </svg>
                  </div>
                  <div class="action-content">
                    <h3>Resetar Ambiente</h3>
                    <p>Sequência completa de reset do ambiente</p>
                  </div>
                </button>
              </form>
            </div>
          </div>
        </section>

        <!-- Populate Data Section -->
        <section id="populate-section" class="dashboard-section">
          <div class="action-grid">
            <div class="action-card">
              <form action="/databaseSeeder" method="POST">
                <button type="submit" class="action-button action-success">
                  <div class="action-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <ellipse cx="12" cy="5" rx="9" ry="3"/>
                      <path d="M3 5v14c0 1.66 4.03 3 9 3s9-1.34 9-3V5"/>
                      <path d="M12 12v6"/>
                      <path d="M9 15h6"/>
                    </svg>
                  </div>
                  <div class="action-content">
                    <h3>Popular Banco de Dados</h3>
                    <p>Chama o endpoint databaseSeeder</p>
                  </div>
                </button>
              </form>
            </div>

            <div class="action-card">
              <form action="/userSeeder" method="POST">
                <button type="submit" class="action-button action-primary">
                  <div class="action-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                      <circle cx="9" cy="7" r="4"/>
                      <line x1="19" y1="8" x2="19" y2="14"/>
                      <line x1="22" y1="11" x2="16" y2="11"/>
                    </svg>
                  </div>
                  <div class="action-content">
                    <h3>Usuários de Teste</h3>
                    <p>Cria usuários de teste e vincula à turma exclusiva</p>
                  </div>
                </button>
              </form>
            </div>

            <div class="action-card">
              <form action="/populate-specialties" method="POST">
                <button type="submit" class="action-button action-warning">
                  <div class="action-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                      <path d="M2 17l10 5 10-5"/>
                      <path d="M2 12l10 5 10-5"/>
                    </svg>
                  </div>
                  <div class="action-content">
                    <h3>Especialidades Médicas</h3>
                    <p>Popula 88+ especialidades médicas por categoria</p>
                  </div>
                </button>
              </form>
            </div>

            <div class="action-card">
              <form action="/populate-graduations" method="POST">
                <button type="submit" class="action-button action-info">
                  <div class="action-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                      <path d="M2 17l10 5 10-5"/>
                      <path d="M2 12l10 5 10-5"/>
                    </svg>
                  </div>
                  <div class="action-content">
                    <h3>Graduações</h3>
                    <p>Popula 150+ graduações organizadas por área</p>
                  </div>
                </button>
              </form>
            </div>
          </div>
        </section>
      </div>
      
      <!-- Feedback Card -->
      <div id="feedback-card" class="feedback-card" style="display:none;"></div>
      
      <!-- Terminal Output -->
      <% if (result) { %>
        <section id="terminal-section" class="terminal <%= result.includes('Erro') ? 'terminal-error' : 'terminal-success' %>" style="display:none">
          <%- decodeURIComponent(result) %>
        </section>
      <% } %>
    </main>
  </div>

  <script>lucide.createIcons();</script>
  <script>
    // Navigation
    const navItems = document.querySelectorAll('.nav-item');
    const sections = document.querySelectorAll('.dashboard-section');
    const sectionTitle = document.getElementById('section-title');
    const sectionDesc = document.getElementById('section-desc');

          const sectionData = {
        dashboard: {
          title: 'Dashboard',
          desc: 'Visão geral do sistema e monitoramento de status'
        },
        database: {
          title: 'Gerenciamento de Banco',
          desc: 'Controle do banco de dados e serviços Docker'
        },
        populate: {
          title: 'População de Dados',
          desc: 'Scripts para popular o banco com dados'
        }
      };

    navItems.forEach(item => {
      item.addEventListener('click', (e) => {
        e.preventDefault();
        
        navItems.forEach(nav => nav.classList.remove('active'));
        sections.forEach(section => section.classList.remove('active'));
        
        item.classList.add('active');
        
        const sectionName = item.getAttribute('data-section');
        const section = document.getElementById(sectionName + '-section');
        if (section) {
          section.classList.add('active');
        }
        
        const data = sectionData[sectionName];
        if (data) {
          sectionTitle.textContent = data.title;
          sectionDesc.textContent = data.desc;
        }
      });
    });

    // Status loading function
    async function loadProjectStatus() {
      try {
        console.log('🔄 Carregando status do projeto...');
        console.log('📡 Fazendo requisição para /api/status...');
        
        const response = await fetch('/api/status');
        console.log('📥 Response status:', response.status);
        console.log('📥 Response ok:', response.ok);
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const status = await response.json();
        console.log('📊 Status recebido:', status);
        
        // Update API status
        const apiStatus = document.getElementById('api-status');
        const apiIndicator = document.getElementById('api-indicator');
        const apiResponse = document.getElementById('api-response');
        
        if (status.services?.api?.running) {
          apiStatus.textContent = 'Online';
          apiStatus.className = 'metric-value status-online';
          apiIndicator.className = 'status-indicator status-online';
          apiResponse.textContent = '< 100ms';
        } else {
          apiStatus.textContent = 'Offline';
          apiStatus.className = 'metric-value status-offline';
          apiIndicator.className = 'status-indicator status-offline';
          apiResponse.textContent = 'N/A';
        }
        
        // Update Database status
        const dbStatus = document.getElementById('db-status');
        const dbIndicator = document.getElementById('db-indicator');
        const dbVersion = document.getElementById('db-version');
        
        if (status.services?.database?.running) {
          dbStatus.textContent = 'Conectado';
          dbStatus.className = 'metric-value status-online';
          dbIndicator.className = 'status-indicator status-online';
          dbVersion.textContent = status.services.database.version?.split(' ')[0] || 'PostgreSQL';
        } else {
          dbStatus.textContent = 'Desconectado';
          dbStatus.className = 'metric-value status-offline';
          dbIndicator.className = 'status-indicator status-offline';
          dbVersion.textContent = 'N/A';
        }
        
        // Update Docker status
        const dockerStatus = document.getElementById('docker-status');
        const dockerIndicator = document.getElementById('docker-indicator');
        const dockerContainers = document.getElementById('docker-containers');
        const dockerMemory = document.getElementById('docker-memory');
        
        if (status.services?.docker?.running) {
          const containerCount = status.services.docker.containerCount || 0;
          dockerStatus.textContent = 'Executando';
          dockerStatus.className = 'metric-value status-online';
          dockerIndicator.className = 'status-indicator status-online';
          dockerContainers.textContent = containerCount;
          dockerMemory.textContent = 'Ativo';
        } else {
          dockerStatus.textContent = 'Parado';
          dockerStatus.className = 'metric-value status-offline';
          dockerIndicator.className = 'status-indicator status-offline';
          dockerContainers.textContent = '0';
          dockerMemory.textContent = 'N/A';
        }
        
        // Update system info
        document.getElementById('env-type').textContent = status.environment || 'development';
        document.getElementById('node-version').textContent = status.system?.nodeVersion || 'Node.js';
        document.getElementById('platform').textContent = status.system?.platform || 'Unknown';
        document.getElementById('memory-usage').textContent = status.system?.memoryUsage ? 
          `${Math.round(status.system.memoryUsage.heapUsed / 1024 / 1024)}MB` : 'N/A';
        document.getElementById('last-update').textContent = new Date(status.timestamp || Date.now()).toLocaleString('pt-BR');
        document.getElementById('last-update-time').textContent = new Date(status.timestamp || Date.now()).toLocaleString('pt-BR');
        
        // Update health status
        const healthStatus = document.getElementById('health-status');
        if (status.services?.api?.running && status.services?.database?.running) {
          healthStatus.textContent = 'Todos os Sistemas Operacionais';
          healthStatus.className = 'info-value status-online';
        } else {
          healthStatus.textContent = 'Alguns Serviços Inativos';
          healthStatus.className = 'info-value status-offline';
        }

        // Update quick actions
        const dockerQuickStatus = document.getElementById('docker-quick-status');
        const systemUptime = document.getElementById('system-uptime');
        const activeServices = document.getElementById('active-services');

        if (status.services?.docker?.running) {
          dockerQuickStatus.textContent = 'Executando';
          dockerQuickStatus.className = 'info-value status-online';
        } else {
          dockerQuickStatus.textContent = 'Parado';
          dockerQuickStatus.className = 'info-value status-offline';
        }

        // System uptime
        const uptimeSeconds = status.system?.uptime || 0;
        const hours = Math.floor(uptimeSeconds / 3600);
        const minutes = Math.floor((uptimeSeconds % 3600) / 60);
        systemUptime.textContent = `${hours}h ${minutes}m`;

        // Active services count
        let activeCount = 0;
        if (status.services?.api?.running) activeCount++;
        if (status.services?.database?.running) activeCount++;
        if (status.services?.docker?.running) activeCount++;
        activeServices.textContent = `${activeCount}/3 Serviços`;
        
        // Update overall status
        const overallStatus = document.getElementById('overall-status');
        if (activeCount === 3) {
          overallStatus.innerHTML = '<div class="status-dot status-online"></div><span>Todos os Sistemas Online</span>';
        } else if (activeCount > 0) {
          overallStatus.innerHTML = '<div class="status-dot status-warning"></div><span>Sistemas Parcialmente Online</span>';
        } else {
          overallStatus.innerHTML = '<div class="status-dot status-offline"></div><span>Todos os Sistemas Offline</span>';
        }
        
        console.log('✅ Status atualizado com sucesso!');
        
              } catch (error) {
          console.error('❌ Erro ao carregar status:', error);
          
          // Fallback - mostrar status como desconhecido
          document.getElementById('api-status').textContent = 'Desconhecido';
          document.getElementById('api-status').className = 'metric-value status-offline';
          document.getElementById('api-indicator').className = 'status-indicator status-offline';
          
          document.getElementById('db-status').textContent = 'Desconhecido';
          document.getElementById('db-status').className = 'metric-value status-offline';
          document.getElementById('db-indicator').className = 'status-indicator status-offline';
          
          document.getElementById('docker-status').textContent = 'Desconhecido';
          document.getElementById('docker-status').className = 'metric-value status-offline';
          document.getElementById('docker-indicator').className = 'status-indicator status-offline';
          
          // Atualizar status geral
          const overallStatus = document.getElementById('overall-status');
          overallStatus.innerHTML = '<div class="status-dot status-offline"></div><span>Status Desconhecido</span>';
        }
    }

    // Load initial status
    loadProjectStatus();

    // Update status every 30 seconds
    setInterval(loadProjectStatus, 30000);

    // Feedback card functions
    function showFeedbackCard(type, title, message, onLog, onRetry) {
      const card = document.getElementById('feedback-card');
      let icon = '';
      let color = '';
      let btns = '';
      
              if (type === 'loading') {
          icon = `<div class='feedback-icon loading'><svg width='32' height='32' viewBox='0 0 50 50'><circle cx='25' cy='25' r='20' fill='none' stroke='#6366f1' stroke-width='5' stroke-linecap='round' stroke-dasharray='31.4 31.4' transform='rotate(-90 25 25)'><animateTransform attributeName='transform' type='rotate' from='0 25 25' to='360 25 25' dur='0.8s' repeatCount='indefinite'/></circle></svg></div>`;
          color = 'feedback-loading';
          btns = `<button class='feedback-btn' disabled>Aguarde...</button>`;
        } else if (type === 'success') {
          icon = `<div class='feedback-icon success'><svg width='32' height='32' viewBox='0 0 24 24' fill='none' stroke='#22d3a8' stroke-width='3'><circle cx='12' cy='12' r='10' stroke='#22d3a8' fill='none'/><path d='M8 12l2 2l4-4' stroke='#22d3a8' stroke-width='3' fill='none'/></svg></div>`;
          color = 'feedback-success';
          btns = `<button class='feedback-btn' id='btn-log'>Ver log</button>`;
        } else if (type === 'error') {
          icon = `<div class='feedback-icon error'><svg width='32' height='32' viewBox='0 0 24 24' fill='none' stroke='#f87171' stroke-width='3'><circle cx='12' cy='12' r='10' stroke='#f87171' fill='none'/><line x1='9' y1='9' x2='15' y2='15' stroke='#f87171' stroke-width='3'/><line x1='15' y1='9' x2='9' y2='15' stroke='#f87171' stroke-width='3'/></svg></div>`;
          color = 'feedback-error';
          btns = `<button class='feedback-btn' id='btn-log'>Ver log</button> <button class='feedback-btn' id='btn-retry'>Tentar novamente</button>`;
        }
      
      card.className = `feedback-card ${color}`;
      card.innerHTML = `
        <div class='feedback-content'>
          ${icon}
          <div class='feedback-text'>
            <h3>${title}</h3>
            <p>${message}</p>
          </div>
        </div>
        <div class='feedback-actions'>${btns}</div>
      `;
      card.style.display = 'flex';
      
      if (onLog) {
        setTimeout(() => {
          const btn = document.getElementById('btn-log');
          if (btn) btn.onclick = onLog;
        }, 0);
      }
      if (onRetry) {
        setTimeout(() => {
          const btn = document.getElementById('btn-retry');
          if (btn) btn.onclick = onRetry;
        }, 0);
      }
    }
    
    function hideFeedbackCard() {
      document.getElementById('feedback-card').style.display = 'none';
    }
    
    // Form submission handling
    document.querySelectorAll('.action-button').forEach(button => {
      button.addEventListener('click', function(e) {
        showFeedbackCard('loading', 'Processando...', 'Executando sua ação, aguarde...', null, null);
      });
    });
    
          // Handle terminal display
      window.addEventListener('DOMContentLoaded', () => {
        const terminal = document.getElementById('terminal-section');
        const result = terminal ? terminal.innerText : '';
        if (terminal && result) {
          if (result.includes('Erro')) {
            showFeedbackCard('error', 'Ocorreu um erro!', 'Não foi possível concluir a ação. Veja o log para detalhes.', () => {
              hideFeedbackCard();
              terminal.style.display = 'block';
            }, () => { window.location.reload(); });
          } else {
            showFeedbackCard('success', 'Ação concluída!', 'Sua ação foi executada com sucesso. Veja o log para detalhes.', () => {
              hideFeedbackCard();
              terminal.style.display = 'block';
            });
          }
        }
      });
  </script>
  <style>
    /* Reset and Base Styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #0a0a0f;
      color: #ffffff;
      line-height: 1.6;
      overflow-x: hidden;
    }

    /* App Container */
    .app-container {
      display: flex;
      min-height: 100vh;
    }

    /* Sidebar */
    .sidebar {
      width: 280px;
      background: linear-gradient(180deg, #111118 0%, #1a1a24 100%);
      border-right: 1px solid #2a2a35;
      padding: 0;
      position: fixed;
      height: 100vh;
      overflow-y: auto;
      z-index: 100;
    }

    .sidebar-header {
      padding: 32px 24px;
      border-bottom: 1px solid #2a2a35;
    }

    .logo {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .logo-icon {
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, #6366f1, #8b5cf6);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }

    .logo-icon svg {
      width: 24px;
      height: 24px;
    }

    .logo-text h1 {
      font-size: 1.5rem;
      font-weight: 700;
      color: #ffffff;
      margin: 0;
    }

    .logo-text span {
      font-size: 0.875rem;
      color: #8b8b9a;
      font-weight: 500;
    }

    .sidebar-nav {
      padding: 24px 16px;
    }

    .nav-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px 20px;
      color: #8b8b9a;
      text-decoration: none;
      border-radius: 12px;
      margin-bottom: 8px;
      transition: all 0.3s ease;
      font-weight: 500;
      font-size: 0.95rem;
    }

    .nav-item:hover {
      background: #2a2a35;
      color: #ffffff;
      transform: translateX(4px);
    }

    .nav-item.active {
      background: linear-gradient(135deg, #6366f1, #8b5cf6);
      color: #ffffff;
      box-shadow: 0 4px 16px rgba(99, 102, 241, 0.3);
    }

    .nav-icon {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .nav-icon svg {
      width: 20px;
      height: 20px;
    }

    /* Main Content */
    .main-content {
      flex: 1;
      margin-left: 280px;
      padding: 0;
      background: #0a0a0f;
      min-height: 100vh;
    }

    /* Page Header */
    .page-header {
      background: linear-gradient(135deg, #111118 0%, #1a1a24 100%);
      border-bottom: 1px solid #2a2a35;
      padding: 32px 40px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .header-content h1 {
      font-size: 2rem;
      font-weight: 700;
      color: #ffffff;
      margin-bottom: 8px;
    }

    .header-content p {
      color: #8b8b9a;
      font-size: 1rem;
      font-weight: 400;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .status-badge {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      background: #2a2a35;
      border-radius: 20px;
      font-size: 0.875rem;
      font-weight: 500;
    }

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #8b8b9a;
    }

    .status-dot.status-online {
      background: #22d3a8;
      box-shadow: 0 0 8px rgba(34, 211, 168, 0.4);
    }

    .status-dot.status-warning {
      background: #f59e0b;
      box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
    }

    .status-dot.status-offline {
      background: #f87171;
      box-shadow: 0 0 8px rgba(248, 113, 113, 0.4);
    }

    /* Content Wrapper */
    .content-wrapper {
      padding: 40px;
    }

    /* Dashboard Section */
    .dashboard-section {
      display: none;
    }

    .dashboard-section.active {
      display: block;
    }

    /* Status Cards */
    .status-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 24px;
      margin-bottom: 40px;
    }

    .status-card {
      background: linear-gradient(135deg, #1a1a24 0%, #23232e 100%);
      border-radius: 16px;
      padding: 24px;
      border: 1px solid #2a2a35;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .status-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #6366f1, #8b5cf6);
    }

    .status-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 32px rgba(99, 102, 241, 0.15);
      border-color: #6366f1;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .card-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }

    .api-icon {
      background: linear-gradient(135deg, #6366f1, #8b5cf6);
    }

    .db-icon {
      background: linear-gradient(135deg, #22d3a8, #10b981);
    }

    .docker-icon {
      background: linear-gradient(135deg, #0ea5e9, #06b6d4);
    }

    .devtools-icon {
      background: linear-gradient(135deg, #f59e0b, #f97316);
    }

    .card-icon svg {
      width: 24px;
      height: 24px;
    }

    .card-status {
      display: flex;
      align-items: center;
    }

    .status-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      border: 2px solid #2a2a35;
      position: relative;
    }

    .status-indicator.status-online {
      background: #22d3a8;
      border-color: #22d3a8;
      box-shadow: 0 0 8px rgba(34, 211, 168, 0.4);
    }

    .status-indicator.status-offline {
      background: #f87171;
      border-color: #f87171;
      box-shadow: 0 0 8px rgba(248, 113, 113, 0.4);
    }

    .status-indicator.status-warning {
      background: #f59e0b;
      border-color: #f59e0b;
      box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
    }

    .card-content h3 {
      font-size: 1.125rem;
      font-weight: 600;
      color: #ffffff;
      margin-bottom: 16px;
    }

    .card-metrics {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .metric {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #2a2a35;
    }

    .metric:last-child {
      border-bottom: none;
    }

    .metric-label {
      color: #8b8b9a;
      font-size: 0.875rem;
      font-weight: 500;
    }

    .metric-value {
      color: #ffffff;
      font-weight: 600;
      font-size: 0.875rem;
      font-family: 'Fira Mono', 'Consolas', monospace;
    }

    .metric-value.status-online {
      color: #22d3a8;
    }

    .metric-value.status-offline {
      color: #f87171;
    }

    /* Info Grid */
    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 24px;
    }

    .info-panel {
      background: linear-gradient(135deg, #1a1a24 0%, #23232e 100%);
      border-radius: 16px;
      padding: 24px;
      border: 1px solid #2a2a35;
    }

    .info-panel h3 {
      font-size: 1.125rem;
      font-weight: 600;
      color: #ffffff;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .info-panel h3::before {
      content: '';
      width: 4px;
      height: 16px;
      background: linear-gradient(90deg, #6366f1, #8b5cf6);
      border-radius: 2px;
    }

    .info-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #2a2a35;
    }

    .info-item:last-child {
      border-bottom: none;
    }

    .info-label {
      color: #8b8b9a;
      font-size: 0.875rem;
      font-weight: 500;
    }

    .info-value {
      color: #ffffff;
      font-weight: 600;
      font-size: 0.875rem;
      font-family: 'Fira Mono', 'Consolas', monospace;
    }

    .info-value.status-online {
      color: #22d3a8;
    }

    .info-value.status-offline {
      color: #f87171;
    }

    /* Action Grid */
    .action-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 24px;
    }

    .action-card {
      background: linear-gradient(135deg, #1a1a24 0%, #23232e 100%);
      border-radius: 16px;
      border: 1px solid #2a2a35;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .action-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 32px rgba(99, 102, 241, 0.15);
      border-color: #6366f1;
    }

    .action-button {
      width: 100%;
      padding: 24px;
      background: none;
      border: none;
      color: #ffffff;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 20px;
      transition: all 0.3s ease;
      text-align: left;
    }

    .action-button:hover {
      background: rgba(99, 102, 241, 0.1);
    }

    .action-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      flex-shrink: 0;
    }

    .action-success .action-icon {
      background: linear-gradient(135deg, #22d3a8, #10b981);
    }

    .action-danger .action-icon {
      background: linear-gradient(135deg, #f87171, #ef4444);
    }

    .action-warning .action-icon {
      background: linear-gradient(135deg, #f59e0b, #f97316);
    }

    .action-primary .action-icon {
      background: linear-gradient(135deg, #6366f1, #8b5cf6);
    }

    .action-info .action-icon {
      background: linear-gradient(135deg, #0ea5e9, #06b6d4);
    }

    .action-icon svg {
      width: 24px;
      height: 24px;
    }

    .action-content h3 {
      font-size: 1.125rem;
      font-weight: 600;
      color: #ffffff;
      margin-bottom: 8px;
    }

    .action-content p {
      color: #8b8b9a;
      font-size: 0.875rem;
      font-weight: 400;
      line-height: 1.5;
    }

    /* Feedback Card */
    .feedback-card {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: linear-gradient(135deg, #1a1a24 0%, #23232e 100%);
      border-radius: 20px;
      padding: 32px;
      border: 1px solid #2a2a35;
      box-shadow: 0 16px 64px rgba(0, 0, 0, 0.4);
      z-index: 1000;
      min-width: 400px;
      max-width: 90vw;
    }

    .feedback-content {
      display: flex;
      align-items: center;
      gap: 20px;
      margin-bottom: 24px;
    }

    .feedback-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .feedback-icon.loading {
      background: linear-gradient(135deg, #6366f1, #8b5cf6);
    }

    .feedback-icon.success {
      background: linear-gradient(135deg, #22d3a8, #10b981);
    }

    .feedback-icon.error {
      background: linear-gradient(135deg, #f87171, #ef4444);
    }

    .feedback-text h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: #ffffff;
      margin-bottom: 8px;
    }

    .feedback-text p {
      color: #8b8b9a;
      font-size: 0.875rem;
      line-height: 1.5;
    }

    .feedback-actions {
      display: flex;
      gap: 12px;
      justify-content: flex-end;
    }

    .feedback-btn {
      padding: 10px 20px;
      border-radius: 8px;
      border: 1px solid #6366f1;
      background: transparent;
      color: #6366f1;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 0.875rem;
    }

    .feedback-btn:hover {
      background: #6366f1;
      color: #ffffff;
    }

    .feedback-btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    /* Terminal */
    .terminal {
      background: #0a0a0f;
      border: 1px solid #2a2a35;
      border-radius: 12px;
      padding: 24px;
      margin-top: 24px;
      font-family: 'Fira Mono', 'Consolas', monospace;
      font-size: 0.875rem;
      line-height: 1.6;
      color: #ffffff;
      overflow-x: auto;
    }

    .terminal-success {
      border-color: #22d3a8;
    }

    .terminal-error {
      border-color: #f87171;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
      .sidebar {
        width: 240px;
      }
      
      .main-content {
        margin-left: 240px;
      }
      
      .status-cards {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      }
    }

    @media (max-width: 768px) {
      .sidebar {
        width: 200px;
      }
      
      .main-content {
        margin-left: 200px;
      }
      
      .page-header {
        padding: 24px 20px;
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
      }
      
      .content-wrapper {
        padding: 20px;
      }
      
      .status-cards {
        grid-template-columns: 1fr;
      }
      
      .info-grid {
        grid-template-columns: 1fr;
      }
      
      .action-grid {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 480px) {
      .sidebar {
        width: 180px;
      }
      
      .main-content {
        margin-left: 180px;
      }
      
      .page-header {
        padding: 20px 16px;
      }
      
      .content-wrapper {
        padding: 16px;
      }
      
      .status-card {
        padding: 20px;
      }
      
      .action-card {
        padding: 20px;
      }
      
      .feedback-card {
        min-width: 320px;
        padding: 24px;
      }
    }

    /* Animations */
    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .dashboard-section.active {
      animation: fadeIn 0.3s ease;
    }

    /* Loading animation */
    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }

    .loading svg {
      animation: spin 1s linear infinite;
    }
  </style>
</body>
</html> 