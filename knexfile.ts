import { K<PERSON> } from 'knex';

import 'dotenv/config';
import { env } from './src/env';
// import 'ts-node/register';

const config: Knex.Config = {
  client: 'pg',
  connection: {
    host: env.DB_HOST,
    database: env.DB_NAME,
    user: env.DB_USERNAME,
    password: env.DB_PASSWORD,
    port: Number(env.DB_PORT),
  },
  pool: {
    min: 0, // very important!!!
    max: 10,
    createTimeoutMillis: 30000,
    acquireTimeoutMillis: 30000,
    idleTimeoutMillis: 1000, // since no share, set this to a small number
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 100,
    propagateCreateError: true,
  },
  migrations: {
    directory: './src/database/migrations',
    extension: 'ts',
  },
  seeds: {
    directory: './src/database/seeds',
    extension: 'ts',
    timestampFilenamePrefix: true,
  },
};

export default config;
