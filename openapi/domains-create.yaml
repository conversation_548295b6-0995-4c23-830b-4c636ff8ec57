openapi: 3.0.0
info:
  title: API de Domínios
  description: API para gerenciamento de domínios e certificados
  version: 1.0.0

paths:
  /domains:
    post:
      summary: Criar novo domínio
      description: Cria um novo domínio, hosted zone e certificado ACM
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - domain
              properties:
                domain:
                  type: string
                  description: Nome do domínio
                  example: 'exemplo.com'
                customer_id:
                  type: string
                  format: uuid
                  description: ID do customer (opcional)
                  example: '123e4567-e89b-12d3-a456-************'
      responses:
        '200':
          description: Domínio criado com sucesso
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: 'Domínio criado com sucesso e aguardando validação'
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                        format: uuid
                        example: '123e4567-e89b-12d3-a456-************'
                      domain:
                        type: string
                        example: 'exemplo.com'
                      hostedZoneId:
                        type: string
                        example: 'Z1234567890ABC'
                      certificateArn:
                        type: string
                        example: 'arn:aws:acm:us-east-1:123456789012:certificate/12345678-1234-1234-1234-123456789012'
                      status:
                        type: string
                        enum: [pending, validating, validated, cloudfront_created, error]
                        example: 'pending'
        '400':
          description: Dados inválidos
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: 'Domínio inválido'
        '401':
          description: Não autorizado
        '500':
          description: Erro interno do servidor

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
