openapi: 3.0.0
info:
  title: API de Processamento de Domínios
  description: API para processamento de domínios pendentes
  version: 1.0.0

paths:
  /domains/process-pending:
    post:
      summary: Processar domínios pendentes
      description: Processa domínios pendentes de validação e criação de CloudFront
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Processamento concluído com sucesso
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: 'Processamento de domínios pendentes concluído'
                  data:
                    type: object
                    properties:
                      processed:
                        type: integer
                        description: Número total de domínios processados
                        example: 5
                      validated:
                        type: integer
                        description: Número de certificados validados
                        example: 2
                      cloudfrontCreated:
                        type: integer
                        description: Número de distribuições CloudFront criadas
                        example: 1
                      errors:
                        type: integer
                        description: Número de erros encontrados
                        example: 0
                      details:
                        type: object
                        properties:
                          validated:
                            type: array
                            items:
                              type: string
                            description: Lista de domínios validados
                            example: ['exemplo1.com', 'exemplo2.com']
                          cloudfrontCreated:
                            type: array
                            items:
                              type: string
                            description: Lista de domínios com CloudFront criado
                            example: ['exemplo1.com']
                          errors:
                            type: array
                            items:
                              type: object
                              properties:
                                domain:
                                  type: string
                                  example: 'exemplo3.com'
                                error:
                                  type: string
                                  example: 'Erro ao validar certificado'
                            description: Lista de erros encontrados
        '401':
          description: Não autorizado
        '500':
          description: Erro interno do servidor

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
