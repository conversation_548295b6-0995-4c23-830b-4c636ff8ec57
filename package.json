{"name": "bq-serverless-api", "version": "1.0.0", "description": "Repository to Banco de Questões Serverless API Project", "main": "index.js", "scripts": {"dev": "NODE_NO_WARNINGS=1 nodemon --watch 'src/**/*.ts' --exec 'ts-node' src/index.ts", "dev:web": "concurrently \"NODE_NO_WARNINGS=1 nodemon --watch 'src/**/*.ts' --exec 'ts-node' src/index.ts\" \"cd devTools/web && npm run dev\"", "tsx": "tsx src/index.ts", "tsnode": "ts-node src/index.ts", "copy:static": "copyfiles -u 1 \"src/templates/**/*.html\" dist/src", "build": "tsc && npm run copy:static", "deploy": "npm run build && serverless deploy", "package": "npm run build && serverless package", "knex": "ts-node -r tsconfig-paths/register ./node_modules/knex/bin/cli.js --knexfile knexfile.ts", "prettier-exec": "npx prettier . --write", "server-dev": "ts-node src/index.ts", "seed:run": "npx knex seed:run", "seed:create": "npx knex seed:make", "migrate:run": "npx knex migrate:latest", "migrate:create": "npx knex migrate:make", "migrate:remove": "npx knex migrate:rollback --all", "check": "eslint .", "check:fix": "eslint --fix .", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky", "test": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "start": "ts-node src/index.ts", "restart:run": "chmod +x ./devTools/scripts/restart_db.sh && ./devTools/scripts/restart_db.sh", "populate:run": "chmod +x ./devTools/scripts/populate_db.sh && ./devTools/scripts/populate_db.sh"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"]}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-ses": "^3.810.0", "@aws-sdk/s3-request-presigner": "3.842.0", "aws-sdk": "^2.1692.0", "bcryptjs": "^2.4.3", "bq-knex-base-repository": "^1.1.10", "copyfiles": "^2.4.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "ejs": "3.1.10", "express": "4.21.2", "express-async-errors": "^3.1.1", "file-type": "^21.0.0", "google-auth-library": "10.1.0", "handlebars": "^4.7.8", "jsonwebtoken": "^8.5.1", "knex": "^3.1.0", "mime-types": "3.0.1", "multer": "^1.4.5-lts.1", "pg": "^8.13.1", "serverless-http": "^3.2.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "ua-parser-js": "^2.0.3", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0", "zod": "^3.25.4"}, "devDependencies": {"@rocketseat/eslint-config": "^2.2.2", "@types/aws-lambda": "^8.10.145", "@types/aws-sdk": "0.0.42", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.7", "@types/knex": "^0.15.2", "@types/mime-types": "^3.0.1", "@types/multer": "^1.4.12", "@types/node": "^22.10.2", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.7", "@types/xlsx": "^0.0.35", "@vitest/coverage-v8": "^1.4.0", "@vitest/ui": "^1.4.0", "concurrently": "8.2.2", "dotenv-cli": "8.0.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import-helpers": "^1.3.1", "eslint-plugin-prettier": "^5.2.1", "husky": "9.1.7", "lint-staged": "^15.2.10", "nodemon": "^3.1.7", "prettier": "^3.4.1", "serverless-dotenv-plugin": "^6.0.0", "serverless-offline": "^13.8.3", "serverless-plugin-typescript": "^2.1.5", "ts-node": "^10.9.2", "tsx": "^4.19.2", "typescript": "^5.6.3", "vitest": "^1.4.0"}}