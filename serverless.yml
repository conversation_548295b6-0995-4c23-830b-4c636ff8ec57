service: propofando-bq-api
frameworkVersion: '3'

useDotenv: true

custom:
  corsConfig:
    origin: '${env:CORS_ORIGIN, "*"}'
    methods:
      - GET
      - HEAD
      - PUT
      - PATCH
      - POST
      - DELETE
      - OPTIONS
    headers:
      - Content-Type
      - X-Amz-Date
      - Authorization
      - X-Amz-Security-Token
      - X-Amz-User-Agent
      - X-Api-Key
      - X-Amz-Content-Sha256
      - Accept
      - Origin
      - Referer
    allowCredentials: true
  webpack:
    includeModules:
      forceInclude:
        - knex
        - pg

plugins:
  - serverless-plugin-typescript
  - serverless-offline
  - serverless-dotenv-plugin

package:
  individually: true
  include:
    - src/templates/**/*.html
  exclude:
    - devTools/**
    - src/tests/**
    - .git/**
    - .env
    - README.md
    - scripts/**
    - '**/*.spec.ts'
    - '**/*.test.ts'
    - '**/*.md'
    - '**/*.log'

provider:
  name: aws
  runtime: nodejs20.x
  region: us-east-1
  apiGateway:
    binaryMediaTypes:
      - '*/*'
  iam:
    role:
      statements:
        - Effect: 'Allow'
          Action:
            - cognito-idp:*
          Resource: 'arn:aws:cognito-idp:${self:provider.region}:*:userpool/*'
        - Effect: 'Allow'
          Action:
            - 'sqs:GetQueueUrl'
            - 'sqs:SendMessage'
            - 'sqs:ListQueues'
            - 'sqs:ReceiveMessage'
            - 'sqs:DeleteMessage'
            - 'sqs:GetQueueAttributes'
          Resource:
            - ${env:SQS_QUEUE_SUBDOMAIN_ARN}
            - ${env:SQS_QUEUE_DOMAIN_ARN}
        - Effect: 'Allow'
          Action:
            - 'route53:*'
          Resource: ${env:ROUTE_53_ARN}
        - Effect: 'Allow'
          Action:
            - 'route53:*'
          Resource: '*'
        - Effect: 'Allow'
          Action:
            - "cloudfront:*"
          Resource: ${env:CLOUD_FRONT_ARN}
        - Effect: 'Allow'
          Action:
            - "cloudfront:*"
          Resource: "*"
        - Effect: 'Allow'
          Action:
            - "acm:*"
          Resource: ${env:ACM_ARN}
        - Effect: 'Allow'
          Action:
            - "acm:*"
          Resource: '*'
        - Effect: 'Allow'
          Action:
            - "s3:*"
          Resource: '*'
        - Effect: 'Allow'
          Action:
            - "ses:*"
          Resource: '*'
  vpc:
    securityGroupIds:
      - ${env:SECURITY_GROUP_ID}
    subnetIds:
      - ${env:SUBNET_PRIVATE_ID_ONE}
      - ${env:SUBNET_PRIVATE_ID_TWO}

functions:
  app:
    handler: ./src/index.handler
    events:
      - http:
          method: ANY
          path: /
          cors: ${self:custom.corsConfig}
      - http:
          method: ANY
          path: '{proxy+}'
          cors: ${self:custom.corsConfig}
  sqssubdomain:
    handler: ./src/events/sqs/subdomain.handler
    events:
      - sqs: ${env:SQS_QUEUE_SUBDOMAIN_ARN}
    timeout: 900
  sqsdomain:
    handler: ./src/events/sqs/domain.handler
    events:
      - sqs: ${env:SQS_QUEUE_DOMAIN_ARN}
    timeout: 900
  finishScheduledAndExpiredSimulateds:
    name: finishSimulExpired
    handler: ./src/events/lambda/finishScheduledAndExpiredSimulateds.handler
    events:
      - schedule: rate(1 minute)
    timeout: 900
