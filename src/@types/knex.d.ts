import { IAlternative } from '../model/IAlternative';
import { ICategorie } from '../model/ICategorie';
import { IClasses } from '../model/IClasses';
import { ICustomer } from '../model/ICustomer';
import { IDiscipline } from '../model/IDiscipline';
import { IDisciplineSubcategoriesAccess } from '../model/IDisciplineSubcategoryAccess';
import { IExam } from '../model/IExam';
import { IExamAccess } from '../model/IExamAccess';
import { IExamsType } from '../model/IExamsType';
import { IExamsTypeAccess } from '../model/IExamsTypeAccess';
import { IInstitution } from '../model/IInstitution';
import { IQuestion } from '../model/IQuestion';
import { IQuestionExam } from '../model/IQuestionExam';
import { IQuestionExamAnswered } from '../model/IQuestionExamAnswered';
import { IQuestionSimulated } from '../model/IQuestionSimulated';
import { IQuestionSimulatedAnswered } from '../model/IQuestionSimulatedAnswered';
import { IQuestionType } from '../model/IQuestionType';
import { IRecovery } from '../model/IRecovery';
import { IReport } from '../model/IReport';
import { ISimulated } from '../model/ISimulated';
import { ISimulatedAccess } from '../model/ISimulatedAcces';
import { ISimulatedTemplate } from '../model/ISimulatedTemplate';
import { ISimulatedTemplateQuestion } from '../model/ISimulatedTemplateQuestion';
import { ISimulatedType } from '../model/ISimulateType';
import { ISubCategory } from '../model/ISubcategory';
import { ITerm } from '../model/ITerm';
import { ITermsAccepted } from '../model/ITermsAccepted';
import { IUser } from '../model/IUser';
import { IClassStudent } from '../model/IUserClasses';
import { IUserType } from '../model/IUserType';
import { IUserTypeAccess } from '../model/IUserTypeAccess';

declare module 'knex/types/tables' {
  export interface Tables {
    alternatives: IAlternative;
    disciplines: IDiscipline;
    categories_access: IUserTypeAccess;
    disciplione_subcategories_access: IDisciplineSubcategoriesAccess;
    classes: IClasses;
    classes_students: IClassStudent;
    customers: ICustomer;
    exams: IExam;
    exams_access: IExamAccess;
    exams_type: IExamsType;
    exams_type_access: IExamsTypeAccess;
    questions: IQuestion;
    questions_exams: IQuestionExam;
    questions_exams_answered: IQuestionExamAnswered;
    questions_simulated: IQuestionSimulated;
    questions_simulated_answered: IQuestionSimulatedAnswered;
    questions_types: IQuestionType;
    recoveries: IRecovery;
    reports: IReport;
    simulateds: ISimulated;
    simulateds_access: ISimulatedAccess;
    simulateds_types: ISimulatedType;
    subcategories: ISubCategory;
    terms: ITerm;
    terms_accepted: ITermsAccepted;
    categories: ICategorie;
    users: IUser;
    users_types: IUserType;
    users_types_access: IUserTypeAccess;
    simulated_templates: ISimulatedTemplate;
    simulated_template_questions: ISimulatedTemplateQuestion;
    institutions: IInstitution;
  }
}
