import swaggerJsdoc from 'swagger-jsdoc';

import { externalPaths } from '../docs/swagger/external/indexPath';

const options: swaggerJsdoc.Options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Banco de Questões - API de Integração Externa',
      version: '2.0.0',
      description: `API de integração da plataforma Banco de Questões, destinada a sistemas externos.

Esta API permite que parceiros e sistemas integrados realizem operações como:
- Gerenciamento de clientes e suas configurações
- Criação e gestão de turmas
- Administração de usuários e suas permissões

Para utilizar esta API, é necessário um token de autenticação válido (x-api-key).
Entre em contato com nossa equipe de suporte para obter suas credenciais de acesso.`,
    },
    components: {
      securitySchemes: {
        ApiToken: {
          type: 'apiKey',
          in: 'header',
          name: 'x-api-key',
          description: 'key de autenticação para sistemas externos',
        },
      },
    },
    security: [{ ApiToken: [] }],
    paths: externalPaths,
  },
  apis: ['./src/model/*.ts'],
};

const swaggerExternalSpec = swaggerJsdoc(options);

export default swaggerExternalSpec;
