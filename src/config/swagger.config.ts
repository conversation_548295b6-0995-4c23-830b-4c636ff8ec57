import swaggerJsdoc from 'swagger-jsdoc';

import { combinedPaths } from '../docs/swagger/indexPath';

const options: swaggerJsdoc.Options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'API de Simulado',
      version: '1.0.0',
      description:
        'Esta é a documentação da API de Simulado, que permite criar e gerenciar simulados, usuários e outras entidades relacionadas.',
    },

    components: {},
    paths: combinedPaths,
  },
  apis: ['./src/model/*.ts'],
};

const swaggerSpec = swaggerJsdoc(options);

export default swaggerSpec;
