import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'customers', async (table) => {
    table.string('name').notNullable();
    table.string('email');
    table.string('tax_number').unique();
    table.string('logo_url');
    table.string('primary_color');
    table.string('secondary_color');
    table.boolean('status');
    table.string('subdomain').notNullable();
    table.string('website');
    table.string('external_customer_id');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'customers');
};
