import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'users', async (table) => {
    table.string('first_name').notNullable();
    table.string('last_name');
    table.string('gender');
    table.date('birth_date');
    table.string('phone_number');
    table.string('cpf');
    table.string('email').notNullable();
    table.string('password');
    table.boolean('active').defaultTo(true);
    table.boolean('terms_accept').defaultTo(false);
    table.string('role').notNullable();
    table.uuid('customer_id').references('id').inTable('customers').onDelete('CASCADE');
    table.uuid('user_type_id').references('id').inTable('users_types');
    table.string('external_user_id');
    table.string('created_by');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'users');
};
