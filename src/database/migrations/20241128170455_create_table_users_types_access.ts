import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'users_types_access', async (table) => {
    table.uuid('user_type_id').references('id').inTable('users_types');
    table.uuid('user_id').references('id').inTable('users');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'users_types_access');
};
