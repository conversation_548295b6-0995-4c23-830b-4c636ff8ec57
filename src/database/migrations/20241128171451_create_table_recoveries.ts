import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'recoveries', async (table) => {
    table.uuid('user_id').references('id').inTable('users');
    table.text('token').notNullable();
    table.timestamp('expires_at').notNullable();
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'recoveries');
};
