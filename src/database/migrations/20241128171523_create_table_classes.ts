import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'classes', async (table) => {
    table.string('name').notNullable();
    table.string('description', 10000);
    table.enum('status', ['active', 'inactive']).notNullable();
    table.date('start_date').notNullable();
    table.date('end_date').notNullable();
    table.uuid('instructor_id').references('id').inTable('users');
    table.string('external_class_id');
    table.uuid('customer_id').references('id').inTable('customers');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'classes');
};
