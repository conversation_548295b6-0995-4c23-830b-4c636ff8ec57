import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'users_classes', async (table) => {
    table.enum('status', ['active', 'inactive']).notNullable();
    table.date('apply_date').notNullable();
    table.uuid('class_id').references('id').inTable('classes');
    table.uuid('user_id').references('id').inTable('users');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'users_classes');
};
