import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'disciplines_access', async (table) => {
    table.uuid('discipline_id').references('id').inTable('disciplines');
    table.uuid('user_type_id').references('id').inTable('users_types');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'disciplines_access');
};
