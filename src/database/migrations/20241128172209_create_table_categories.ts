import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'categories', async (table) => {
    table.string('name').notNullable();
    table.uuid('customer_id').references('id').inTable('customers');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'categories');
};
