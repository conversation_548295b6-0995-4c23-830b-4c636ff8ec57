import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'subcategories', async (table) => {
    table.string('name').notNullable();
    table.uuid('customer_id').references('id').inTable('customers');
    table.uuid('category_id').references('id').inTable('categories');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'subcategories');
};
