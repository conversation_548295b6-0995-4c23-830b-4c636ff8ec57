import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'disciplines_categories_access', async (table) => {
    table.uuid('customer_id').references('id').inTable('customers');
    table.uuid('discipline_id').references('id').inTable('disciplines');
    table.uuid('category_id').references('id').inTable('categories');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'disciplines_categories_access');
};
