import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'questions', async (table) => {
    table.timestamp('published_at', { useTz: true }).defaultTo(null);
    table.string('title').notNullable();
    table.string('description', 10000);
    table.string('difficulty');
    table.string('image_url', 500);
    table.string('explanation_video', 2000);
    table.string('explanation_text', 10000);
    table.string('explanation_image', 1500);
    table.boolean('published').defaultTo(false);
    table.string('status').notNullable().defaultTo('draft');
    table.string('correct_text', 10000);
    table.string('reference', 500);
    table.string('institution', 500);
    table.integer('year');
    table.uuid('question_type_id').references('id').inTable('questions_types');
    table.uuid('discipline_id').references('id').inTable('disciplines');
    table.uuid('category_id').references('id').inTable('categories');
    table.uuid('subcategory_id').references('id').inTable('subcategories');
    table.uuid('customer_id').references('id').inTable('customers');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'questions');
};
