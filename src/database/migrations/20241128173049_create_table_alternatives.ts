import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'alternatives', async (table) => {
    table.uuid('question_id').references('id').inTable('questions');
    table.boolean('correct').defaultTo(false);
    table.string('option').notNullable();
    table.string('description', 4000).notNullable();
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'alternatives');
};
