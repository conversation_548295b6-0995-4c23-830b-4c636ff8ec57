import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'exams', async (table) => {
    table.string('name').notNullable();
    table.date('exam_date');
    table.boolean('published').defaultTo(false);
    table.specificType('exam_time', 'interval');
    table.string('exam_period');
    table.integer('total_questions');
    table.integer('total_questions_annulled');
    table.decimal('pass_percentage');
    table.uuid('exam_type_id').references('id').inTable('exams_type');
    table.uuid('customer_id').references('id').inTable('customers');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'exams');
};
