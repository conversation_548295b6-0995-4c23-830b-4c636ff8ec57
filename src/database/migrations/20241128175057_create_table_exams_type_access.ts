import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'exams_type_access', async (table) => {
    table.uuid('customer_id').references('id').inTable('customers').notNullable();
    table.uuid('exam_type_id').references('id').inTable('exams_type').notNullable();
    table.uuid('user_type_id').references('id').inTable('users_types').notNullable();
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'exams_type_access');
};
