import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'exams_access', async (table) => {
    table.boolean('active').defaultTo(true);
    table.timestamp('start_exams').defaultTo(null);
    table.timestamp('end_exams').defaultTo(null);
    table.uuid('user_id').references('id').inTable('users').notNullable();
    table.uuid('exam_id').references('id').inTable('exams').notNullable();
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'exams_access');
};
