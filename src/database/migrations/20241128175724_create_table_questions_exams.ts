import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'questions_exams', async (table) => {
    table.integer('order_by');
    table.uuid('exam_id').references('id').inTable('exams').notNullable();
    table.uuid('question_id').references('id').inTable('questions').notNullable();
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'questions_exams');
};
