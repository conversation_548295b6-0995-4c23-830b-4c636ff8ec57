import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'questions_exams_answered', async (table) => {
    table.boolean('answered').defaultTo(false);
    table.string('answered_text', 10000);
    table.boolean('restored').defaultTo(false);
    table.timestamp('date_answered');
    table.uuid('exam_access_id').references('id').inTable('exams_access').notNullable();
    table.uuid('question_exam_id').references('id').inTable('questions_exams').notNullable();
    table.uuid('alternative_id').references('id').inTable('alternatives');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'questions_exams_answered');
};
