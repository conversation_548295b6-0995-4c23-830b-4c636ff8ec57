import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'simulateds', async (table) => {
    table.string('name').notNullable();
    table.boolean('active').defaultTo(true);
    table.uuid('simulated_type_id').references('id').inTable('simulateds_types');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'simulateds');
};
