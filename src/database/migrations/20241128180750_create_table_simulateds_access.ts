import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'simulateds_access', async (table) => {
    table.boolean('active').defaultTo(true);
    table.timestamp('start_simulated');
    table.timestamp('end_simulated');
    table.uuid('user_id').references('id').inTable('users');
    table.uuid('simulated_id').references('id').inTable('simulateds');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'simulateds_access');
};
