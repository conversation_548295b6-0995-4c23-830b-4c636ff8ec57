import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'questions_simulated', async (table) => {
    table.integer('order_by');
    table.uuid('simulated_id').references('id').inTable('simulateds');
    table.uuid('question_id').references('id').inTable('questions');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'questions_simulated');
};
