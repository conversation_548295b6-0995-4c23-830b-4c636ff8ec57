import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'questions_simulated_answered', async (table) => {
    table.boolean('answered').defaultTo(false);
    table.boolean('restored').defaultTo(false);
    table.string('answered_text', 10000);
    table.timestamp('date_answered');
    table.uuid('simulated_access').references('id').inTable('simulateds_access');
    table.uuid('questions_simulated_id').references('id').inTable('questions_simulated');
    table.uuid('alternative_id').references('id').inTable('alternatives');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'questions_simulated_answered');
};
