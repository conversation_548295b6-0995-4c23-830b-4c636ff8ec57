import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'reports', async (table) => {
    table.string('description', 10000);
    table.string('category');
    table.string('platform');
    table.string('web_browser');
    table.boolean('isMobile').defaultTo(false);
    table.string('device');
    table.string('browser');
    table.string('operational_system');
    table.string('user_agent');
    table.string('status');
    table.boolean('viewed').defaultTo(false);
    table.boolean('finished').defaultTo(false);
    table.boolean('send_email').defaultTo(false);
    table.uuid('user_id').references('id').inTable('users');
    table.uuid('question_id').references('id').inTable('questions');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'reports');
};
