import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'terms_accepted', async (table) => {
    table.boolean('accepted_terms').defaultTo(false);
    table.text('ip_accepted_terms');
    table.uuid('user_id').references('id').inTable('users');
    table.uuid('terms_id').references('id').inTable('terms');
    table.timestamp('date_accepted_terms').defaultTo(knex.fn.now());
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'terms_accepted');
};
