import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'questions_groups_access', async (table) => {
    table.uuid('customer_id').references('id').inTable('customers');
    table.uuid('question_group_id').references('id').inTable('questions_groups');
    table.uuid('question_id').references('id').inTable('questions');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'questions_groups_access');
};
