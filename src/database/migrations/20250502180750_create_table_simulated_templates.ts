import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'simulated_templates', async (table) => {
    table.string('name').notNullable();
    table.uuid('user_id').references('id').inTable('users');
    table.uuid('simulated_type_id').references('id').inTable('simulateds_types');
    table.uuid('question_group_id').references('id').inTable('questions_groups');
    table.integer('time_limit');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'simulated_templates');
};
