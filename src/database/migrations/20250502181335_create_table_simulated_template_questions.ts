import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'simulated_template_questions', async (table) => {
    table
      .uuid('simulated_template_id')
      .references('id')
      .inTable('simulated_templates')
      .notNullable();
    table.uuid('question_id').references('id').inTable('questions').notNullable();
    table.integer('order_by').notNullable();
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'simulated_template_questions');
};
