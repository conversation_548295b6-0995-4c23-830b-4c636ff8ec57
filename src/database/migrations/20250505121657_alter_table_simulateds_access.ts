import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'simulateds_access', async (table) => {
    table
      .integer('total_seconds')
      .comment('Tempo total em segundos que o usuário levou para completar o simulado');
    table.integer('time_limit').comment('Limite de tempo em segundos para completar o simulado');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'simulateds_access', async (table) => {
    table.dropColumn('total_seconds');
    table.dropColumn('time_limit');
  });
};
