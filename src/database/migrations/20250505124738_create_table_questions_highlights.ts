import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'questions_highlights', async (table) => {
    table.boolean('saved').defaultTo(false).comment('Indica se a questão foi salva pelo usuário');
    table
      .jsonb('highlight_description')
      .defaultTo('[]')
      .comment('Array de destaques no enunciado da questão');
    table
      .jsonb('highlight_explanation_text')
      .defaultTo('[]')
      .comment('Array de destaques na explicação da questão');
    table.specificType('alternatives_line_through', 'text[]').defaultTo('{}');
    table.uuid('simulated_id').references('id').inTable('simulateds');
    table.uuid('simulated_access_id').references('id').inTable('simulateds_access');
    table.uuid('user_id').references('id').inTable('users').notNullable();
    table.uuid('question_id').references('id').inTable('questions').notNullable();
    table.uuid('exam_id').references('id').inTable('exams');
    table.uuid('exam_access_id').references('id').inTable('exams_access');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'questions_highlights');
};
