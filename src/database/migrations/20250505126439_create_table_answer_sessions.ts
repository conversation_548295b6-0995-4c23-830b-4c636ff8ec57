import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'answer_sessions', async (table) => {
    table.timestamp('started_at').notNullable();
    table.timestamp('paused_at');
    table.uuid('simulated_access_id').references('id').inTable('simulateds_access').notNullable();
    table.integer('duration_seconds').comment('Duração em segundos, calculado');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'answer_sessions');
};
