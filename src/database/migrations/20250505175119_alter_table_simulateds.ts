import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'simulateds', async (table) => {
    table.uuid('question_group_id').references('id').inTable('questions_groups');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'simulateds', async (table) => {
    table.dropColumn('question_group_id');
  });
};
