import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'exams_access', async (table) => {
    table
      .integer('total_seconds')
      .comment('Tempo total em segundos que o usuário levou para completar o exame');
    table.integer('time_limit').comment('Limite de tempo em segundos para completar o exame');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'exams_access', async (table) => {
    table.dropColumn('total_seconds');
    table.dropColumn('time_limit');
  });
};
