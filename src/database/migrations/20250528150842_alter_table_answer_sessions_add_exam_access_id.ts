import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'answer_sessions', async (table) => {
    table.uuid('exam_access_id').references('id').inTable('exams_access').nullable();
    table.uuid('simulated_access_id').nullable().alter();
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'answer_sessions', async (table) => {
    table.dropColumn('exam_access_id');
    table.dropColumn('simulated_access_id');
  });
};
