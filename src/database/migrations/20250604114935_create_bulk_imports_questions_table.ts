import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'bulk_imports', async (table) => {
    table.string('file_name').notNullable();
    table.string('file_key').nullable();
    table.integer('total_rows').notNullable();
    table.integer('valid_count').notNullable();
    table.integer('invalid_count').notNullable();
    table.decimal('success_percentage', 5, 2).notNullable();
    table
      .enum('status', ['pending', 'processing', 'imported', 'cancelled', 'validation_failed'])
      .defaultTo('pending');
    table.uuid('customer_id').references('id').inTable('customers').notNullable();
    table.uuid('created_by').references('id').inTable('users').notNullable();
    table.uuid('updated_by').references('id').inTable('users');
    table.timestamp('imported_at').nullable();
  });

  await BaseRepository.createTable(knex, 'bulk_import_details', async (table) => {
    table.uuid('bulk_import_id').references('id').inTable('bulk_imports').notNullable();
    table.jsonb('row_data');
    table.boolean('is_valid').notNullable();
    table.specificType('errors', 'text[]').nullable();
    table.uuid('question_id').references('id').inTable('questions').nullable();
    table.integer('row_index').notNullable();
  });
};

exports.down = async function (knex: Knex) {
  await knex.schema.dropTableIfExists('bulk_import_details');
  await knex.schema.dropTableIfExists('bulk_imports');
};
