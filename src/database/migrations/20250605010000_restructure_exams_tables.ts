import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'exams_type_access');

  await knex.schema.alterTable('exams', (table) => {
    table.dropColumn('exam_time');
  });

  await knex.schema.alterTable('exams', (table) => {
    table.dropColumn('pass_percentage');
    table.dropColumn('total_questions');
    table.dropColumn('total_questions_annulled');
    table.dropColumn('exam_period');
    table.dropColumn('exam_date');
    table.dropColumn('exam_type_id');

    table.uuid('institution_id').references('id').inTable('institutions');
    table.integer('year');
    table.string('status').defaultTo('draft');
    table.integer('exam_time');
  });

  await BaseRepository.dropTable(knex, 'exams_type');
};

exports.down = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'exams_type', async (table) => {
    table.string('name').notNullable();
    table.uuid('customer_id').references('id').inTable('customers');
  });

  await knex.schema.alterTable('exams', (table) => {
    table.decimal('pass_percentage');
    table.integer('total_questions');
    table.integer('total_questions_annulled');
    table.string('exam_period');
    table.date('exam_date');
    table.uuid('exam_type_id').references('id').inTable('exams_type');

    table.dropColumn('institution_id');
    table.dropColumn('year');
    table.dropColumn('status');
    table.dropColumn('exam_time');
  });

  await BaseRepository.createTable(knex, 'exams_type_access', async (table) => {
    table.uuid('customer_id').references('id').inTable('customers').notNullable();
    table.uuid('exam_type_id').references('id').inTable('exams_type').notNullable();
    table.uuid('user_type_id').references('id').inTable('users_types').notNullable();
  });
};
