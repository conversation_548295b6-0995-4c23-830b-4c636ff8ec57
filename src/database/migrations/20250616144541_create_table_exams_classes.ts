import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'exams_classes', async (table) => {
    table.uuid('exam_id').references('id').inTable('exams');
    table.uuid('class_id').references('id').inTable('classes');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'exams_classes');
};
