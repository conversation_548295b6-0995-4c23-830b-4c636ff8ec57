import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('simulateds', (table) => {
    table.string('status').nullable();
    table.integer('time_limit').nullable();
    table.timestamp('start_date').nullable();
    table.timestamp('end_date').nullable();
    table.uuid('customer_id').references('id').inTable('customers');
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('simulateds', (table) => {
    table.dropColumn('status');
    table.dropColumn('time_limit');
    table.dropColumn('start_date');
    table.dropColumn('end_date');
    table.dropColumn('customer_id');
  });
}
