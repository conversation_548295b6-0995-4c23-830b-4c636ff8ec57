import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'simulateds_classes', async (table) => {
    table.uuid('simulated_id').references('id').inTable('simulateds');
    table.uuid('class_id').references('id').inTable('classes');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'simulateds_classes');
};
