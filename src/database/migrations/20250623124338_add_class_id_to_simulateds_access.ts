import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await BaseRepository.alterTable(knex, 'simulateds_access', async (table) => {
    table.uuid('class_id').references('id').inTable('classes');
  });
  await BaseRepository.alterTable(knex, 'simulateds_access', async (table) => {
    table.unique(['user_id', 'simulated_id', 'class_id']);
  });
}

export async function down(knex: Knex): Promise<void> {
  await BaseRepository.alterTable(knex, 'simulateds_access', async (table) => {
    table.dropUnique(['user_id', 'simulated_id', 'class_id']);
    table.dropColumn('class_id');
  });
}
