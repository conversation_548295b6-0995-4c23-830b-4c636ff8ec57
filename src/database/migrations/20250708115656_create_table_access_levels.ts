import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'access_levels', async (table) => {
    table.string('name').notNullable();
    table.string('role').notNullable();
    table.string('description').notNullable();
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'access_levels');
};
