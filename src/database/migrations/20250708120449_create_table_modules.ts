import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'courses_modules', async (table) => {
    table.string('name');
    table.string('description_module');
    table.integer('order_by');
    table.uuid('course_id').references('id').inTable('courses');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'courses_modules');
};
