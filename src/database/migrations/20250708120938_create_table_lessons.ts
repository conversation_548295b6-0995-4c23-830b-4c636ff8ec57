import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'courses_lessons', async (table) => {
    table.string('title');
    table.string('lesson_url');
    table.string('duration');
    table.text('description_lessons');
    table.uuid('module_id').references('id').inTable('courses_modules');
    table.uuid('question_group_id').references('id').inTable('questions_groups');
    table.integer('order_by').defaultTo(0);
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'courses_lessons');
};
