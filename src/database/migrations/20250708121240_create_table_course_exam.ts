import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'courses_exams', async (table) => {
    table.uuid('course_id').references('id').inTable('courses');
    table.uuid('exam_id').references('id').inTable('exams');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'courses_exams');
};
