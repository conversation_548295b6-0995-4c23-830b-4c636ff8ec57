import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'courses_questions_groups', async (table) => {
    table.uuid('course_id').references('id').inTable('courses');
    table.uuid('question_group_id').references('id').inTable('questions_groups');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'courses_questions_groups');
};
