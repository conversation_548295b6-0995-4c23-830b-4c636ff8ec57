import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'customers_legals_docs', async (table) => {
    table.string('type');
    table.string('file');
    table.uuid('customer_id').references('id').inTable('customers');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'customers_legals_docs');
};
