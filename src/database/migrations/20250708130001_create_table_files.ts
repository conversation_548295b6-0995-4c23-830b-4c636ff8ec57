import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'files', async (table) => {
    table.string('name');
    table.string('url');
    table.uuid('course_id').references('id').inTable('courses');
    table.uuid('module_id').references('id').inTable('courses_modules');
    table.uuid('lesson_id').references('id').inTable('courses_lessons');
    table.uuid('user_id').references('id').inTable('users');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'files');
};
