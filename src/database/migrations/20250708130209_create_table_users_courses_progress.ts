import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'users_courses_progress', async (table) => {
    table.string('status');
    table.timestamp('started_at');
    table.timestamp('completed_at');
    table.timestamp('last_accessed_at');
    table.uuid('user_id').references('id').inTable('users');
    table.uuid('course_id').references('id').inTable('courses');
    table.string('user_course_progress_status');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'users_courses_progress');
};
