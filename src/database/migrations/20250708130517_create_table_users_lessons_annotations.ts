import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'users_lessons_annotations', async (table) => {
    table.string('annotation_content');
    table.uuid('lesson_id').references('id').inTable('courses_lessons');
    table.uuid('user_id').references('id').inTable('users');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'users_lessons_annotations');
};
