import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'users_lessons_progress', async (table) => {
    table.string('status');
    table.timestamp('started_at');
    table.timestamp('completed_at');
    table.timestamp('last_accessed_at');
    table.string('current_position');
    table.uuid('lesson_id').references('id').inTable('courses_lessons');
    table.uuid('user_id').references('id').inTable('users');
    table.string('user_lesson_progress_status');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'users_lessons_progress');
};
