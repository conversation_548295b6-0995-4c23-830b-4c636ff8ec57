import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'users_modules_progress', async (table) => {
    table.string('status');
    table.timestamp('started_at');
    table.timestamp('completed_at');
    table.timestamp('last_accessed_at');
    table.uuid('module_id').references('id').inTable('courses_modules');
    table.uuid('user_id').references('id').inTable('users');
    table.string('user_module_progress_status');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'users_modules_progress');
};
