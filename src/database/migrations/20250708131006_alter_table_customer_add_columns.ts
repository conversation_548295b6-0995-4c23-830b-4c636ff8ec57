import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'customers', async (table) => {
    table.string('description');
    table.string('phone');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'customers', async (table) => {
    table.dropColumn('description');
    table.dropColumn('phone');
  });
};
