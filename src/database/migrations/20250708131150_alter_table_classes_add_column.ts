import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'classes', async (table) => {
    table.uuid('course_id').references('id').inTable('courses');
    table.date('end_date').nullable().alter();
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'classes', async (table) => {
    table.dropColumn('course_id');
    table.dropColumn('end_date');
  });
};
