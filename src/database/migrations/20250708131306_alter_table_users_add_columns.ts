import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'users', async (table) => {
    table.string('image');
    table.jsonb('additional_info');
    table.uuid('access_level_id').references('id').inTable('access_levels');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'users', async (table) => {
    table.dropColumn('image');
    table.dropColumn('additional_info');
    table.dropColumn('access_level_id');
  });
};
