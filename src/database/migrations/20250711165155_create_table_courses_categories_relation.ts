import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.createTable(knex, 'courses_categories_relation', async (table) => {
    table.uuid('course_id').references('id').inTable('courses');
    table.uuid('courses_categories_id').references('id').inTable('courses_categories');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.dropTable(knex, 'courses_categories_relation');
};
