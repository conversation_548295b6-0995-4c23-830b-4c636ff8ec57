import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'courses_modules', async (table) => {
    table.boolean('published').defaultTo(false);
    table.string('status').defaultTo('draft');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'courses_modules', async (table) => {
    table.dropColumn('published');
    table.dropColumn('status');
  });
};
