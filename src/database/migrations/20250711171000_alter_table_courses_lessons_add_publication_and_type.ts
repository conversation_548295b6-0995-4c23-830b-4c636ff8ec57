import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'courses_lessons', async (table) => {
    table.boolean('published').defaultTo(false);
    table.string('status').defaultTo('draft');
    table.string('lesson_type');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'courses_lessons', async (table) => {
    table.dropColumn('published');
    table.dropColumn('status');
    table.dropColumn('lesson_type');
  });
};
