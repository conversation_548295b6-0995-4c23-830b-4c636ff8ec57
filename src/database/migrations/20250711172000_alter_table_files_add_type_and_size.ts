import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'files', async (table) => {
    table.string('file_name');
    table.string('file_type');
    table.integer('file_size');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'files', async (table) => {
    table.dropColumn('file_name');
    table.dropColumn('file_type');
    table.dropColumn('file_size');
  });
};
