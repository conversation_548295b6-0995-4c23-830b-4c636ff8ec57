import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'classes', async (table) => {
    table.boolean('is_default').defaultTo(false);
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'classes', async (table) => {
    table.dropColumn('is_default');
  });
};
