import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'users', async (table) => {
    table.string('first_name').nullable().alter();
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'users', async (table) => {
    table.string('first_name').notNullable().alter();
  });
};
