import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

exports.up = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'users', async (table) => {
    table.dateTime('last_accessed_at');
  });
};

exports.down = async function (knex: Knex) {
  await BaseRepository.alterTable(knex, 'users', async (table) => {
    table.dropColumn('last_accessed_at');
  });
};
