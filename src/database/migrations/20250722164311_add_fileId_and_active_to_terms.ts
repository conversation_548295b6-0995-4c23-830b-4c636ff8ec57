import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await BaseRepository.alterTable(knex, 'terms', (table) => {
    table.uuid('file_id').references('id').inTable('files');
    table.boolean('active').notNullable().defaultTo(true);
  });
}

export async function down(knex: Knex): Promise<void> {
  await BaseRepository.alterTable(knex, 'terms', (table) => {
    table.dropColumn('file_id');
    table.dropColumn('active');
  });
}
