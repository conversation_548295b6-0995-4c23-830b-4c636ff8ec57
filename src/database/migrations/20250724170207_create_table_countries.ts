import { BaseRepository } from 'bq-knex-base-repository';
import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return BaseRepository.createTable(knex, 'countries', (table) => {
    table.string('name').comment('Nome do país');
    table.string('ddi').comment('Código DDI do país');
    table.string('alpha_2').comment('Código ALPHA-2 do país');
    table.string('alpha_3').comment('Código ALPHA-3 do país');
    table.string('image_url').comment('URL da imagem da bandeira do país');
  });
}

export async function down(knex: Knex): Promise<void> {
  return BaseRepository.dropTable(knex, 'countries');
}
