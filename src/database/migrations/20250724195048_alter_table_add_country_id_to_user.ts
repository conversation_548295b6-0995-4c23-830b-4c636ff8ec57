import { BaseRepository } from 'bq-knex-base-repository';
import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await BaseRepository.alterTable(knex, 'users', (table) => {
    table.uuid('country_id').references('id').inTable('countries');
  });
}

export async function down(knex: Knex): Promise<void> {
  await BaseRepository.alterTable(knex, 'users', (table) => {
    table.dropColumn('country_id');
  });
}
