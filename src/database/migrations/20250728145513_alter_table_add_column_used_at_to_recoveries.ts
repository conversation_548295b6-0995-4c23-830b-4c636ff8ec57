import { BaseRepository } from 'bq-knex-base-repository';
import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await BaseRepository.alterTable(knex, 'recoveries', (table) => {
    table.timestamp('used_at');
  });
}

export async function down(knex: Knex): Promise<void> {
  await BaseRepository.alterTable(knex, 'recoveries', (table) => {
    table.dropColumn('used_at');
  });
}
