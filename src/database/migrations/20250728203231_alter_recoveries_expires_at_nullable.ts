import { BaseRepository } from 'bq-knex-base-repository';
import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await BaseRepository.alterTable(knex, 'recoveries', (table) => {
    table.timestamp('expires_at').nullable().alter();
  });
}

export async function down(knex: Knex): Promise<void> {
  await BaseRepository.alterTable(knex, 'recoveries', (table) => {
    table.timestamp('expires_at').notNullable().alter();
  });
}
