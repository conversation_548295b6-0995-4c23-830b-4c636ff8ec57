import { BaseRepository } from 'bq-knex-base-repository';
import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await BaseRepository.alterTable(knex, 'users', (table) => {
    table.string('ddi').nullable();
    table.boolean('accept_marketing').nullable();
    table.boolean('accept_newsletter').nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await BaseRepository.alterTable(knex, 'users', (table) => {
    table.dropColumn('ddi');
    table.dropColumn('accept_marketing');
    table.dropColumn('accept_newsletter');
  });
}
