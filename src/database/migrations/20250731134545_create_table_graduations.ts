import { BaseRepository } from 'bq-knex-base-repository';
import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return BaseRepository.createTable(knex, 'graduations', (table) => {
    table.string('name').comment('Nome da graduação');
    table.string('type').comment('Tipo da graduação');
  });
}

export async function down(knex: Knex): Promise<void> {
  return BaseRepository.dropTable(knex, 'graduations');
}
