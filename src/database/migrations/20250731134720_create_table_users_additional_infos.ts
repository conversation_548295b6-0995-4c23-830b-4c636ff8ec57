import { BaseRepository } from 'bq-knex-base-repository';
import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return BaseRepository.createTable(knex, 'users_additional_infos', (table) => {
    table.string('referral_source').nullable();
    table.string('formation').nullable();
    table.integer('completion_year').nullable();
    table.boolean('has_specialty').nullable();
    table.boolean('currently_attending').nullable();
    table.string('attendance_location').nullable();
    table.uuid('user_id').references('id').inTable('users').nullable();
    table.uuid('institution_id').references('id').inTable('institutions').nullable();
    table.uuid('graduation_id').references('id').inTable('graduations').nullable();
    table.uuid('primary_specialty_id').references('id').inTable('specialties').nullable();
    table.uuid('secondary_specialty_id').references('id').inTable('specialties').nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  return BaseRepository.dropTable(knex, 'users_additional_infos');
}
