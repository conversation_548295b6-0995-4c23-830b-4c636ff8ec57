import { BaseRepository } from 'bq-knex-base-repository';
import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await BaseRepository.createTable(knex, 'domains', async (table) => {
    table.string('domain');
    table.string('hosted_zone_id');
    table.string('certificate_arn');
    table.uuid('customer_id').references('id').inTable('customers');
    table.string('status');
    table.text('error_message');
    table.string('cloudfront_distribution_id');
    table.string('cloudfront_distribution_url');
    table.timestamp('validated_at');
    table.timestamp('cloudfront_created_at');
    table.timestamp('last_processed_at');
  });
}

export async function down(knex: Knex): Promise<void> {
  await BaseRepository.dropTable(knex, 'domains');
}
