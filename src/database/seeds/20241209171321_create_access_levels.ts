import { randomUUID } from 'crypto';

import { KnexAccessLevelsRepository } from '../../repositories/knex/accessLevels.repositories';

export async function seed(): Promise<void> {
  const repository = new KnexAccessLevelsRepository();

  const existingLevels = await repository.findAll();
  if (existingLevels.length > 0) {
    console.log('⏭️ AccessLevels já existem, pulando seed...');
    return;
  }

  const now = new Date();

  const levels = [
    { role: 'OWNER', name: 'owner', description: 'Propriet<PERSON><PERSON>' },
    { role: 'SYSTEM_ADMIN', name: 'admin sistema', description: 'Administrador da plataforma' },
    { role: 'SUPER_ADMIN', name: 'super admin', description: 'Super Administrador' },
    { role: 'ADMIN', name: 'admin', description: 'Administrador' },
    { role: 'COORDINATOR', name: 'coordenador', description: '<PERSON>ordena<PERSON>' },
    { role: 'CREATOR', name: 'criador', description: '<PERSON><PERSON><PERSON>' },
    { role: 'STUDENT', name: 'al<PERSON>', description: '<PERSON><PERSON>' },
  ];

  const levelsWithId = levels.map((level) => ({
    id: randomUUID(),
    ...level,
    created_at: now,
    updated_at: now,
    deleted_at: null,
  }));

  await repository.insertAll(levelsWithId);
  console.log('✅ Seed de AccessLevels inserida com sucesso!');
}
