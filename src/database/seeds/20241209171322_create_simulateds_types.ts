import { KnexSimulatedTypesRepository } from '../../repositories/knex/simulatedsTypes.repositories';

export async function seed(): Promise<void> {
  const simulatedTypeRepository = new KnexSimulatedTypesRepository();

  try {
    const existingSimulatedType = await simulatedTypeRepository.findAll();

    if (existingSimulatedType.length > 0) {
      console.log('⏭️ Tipos de simulados já existem, pulando seed...');
      return;
    }

    const simulatedsTypes = [{ name: 'default' }, { name: 'exclusive' }, { name: 'free' }];

    await simulatedTypeRepository.insertAll(simulatedsTypes);

    console.log('✅ Tipos de simulados criados com sucesso!');
  } catch (error) {
    console.error('Erro ao criar tipos de simulados:', error);
  }
}
