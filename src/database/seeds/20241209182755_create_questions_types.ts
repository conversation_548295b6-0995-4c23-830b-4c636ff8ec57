import { KnexQuestionsTypesRepository } from '../../repositories/knex/questionsTypes.repositories';
export async function seed(): Promise<void> {
  const questionTypeRepository = new KnexQuestionsTypesRepository();

  const existingQuestionType = await questionTypeRepository.findAll();

  if (existingQuestionType.length > 0) {
    return console.log('⏭️ Tipos de dados já existem, pulando seed...');
  }

  const questionsTypes = [
    { type: 'alternatives', description: 'Múltipla escolha' },
    { type: 'true or false', description: 'Verdadeiro ou falso' },
  ];

  await questionTypeRepository.insertAll(questionsTypes);

  console.log('✅ Tipos de questões criados com sucesso!');
}
