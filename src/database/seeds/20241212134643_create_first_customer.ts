import { env } from '../../env';
import { KnexCustomerRepository } from '../../repositories/knex/customers.repositories';

export async function seed(): Promise<void> {
  const customerRepository = new KnexCustomerRepository();

  const existingCustomer = await customerRepository.getTaxNumber(env.FIRST_CUSTOMER_TAX_NUMBER);

  if (existingCustomer) {
    console.log('⏭️ Já existe um cliente com este CNPJ, pulando seed...');
    return;
  }

  const firstCustomer = {
    name: 'Propofando',
    email: '<EMAIL>',
    tax_number: process.env.FIRST_CUSTOMER_TAX_NUMBER,
    logo_url: 'https://images.prismic.io/bq-propofando/ZsCRfUaF0TcGJBpi_Group28.png',
    primary_color: '#E01F87',
    secondary_color: '#C1086C',
    status: true,
    subdomain: process.env.FIRST_CUSTOMER_DOMAIN,
    website: 'https://propofando.com.br',
  };

  await customerRepository.insert(firstCustomer);

  console.log('✅ Novo cliente inserido com sucesso!');
}
