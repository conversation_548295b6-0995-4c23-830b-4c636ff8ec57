import bcrypt from 'bcryptjs';

import { env } from '../../env';
import { InternalRolesEnum } from '../../model/enums/roles.enum';
import { KnexAccessLevelsRepository } from '../../repositories/knex/accessLevels.repositories';
import { KnexCustomerRepository } from '../../repositories/knex/customers.repositories';
import { KnexUserRepository } from '../../repositories/knex/users.repositories';

export async function seed(): Promise<void> {
  const userRepository = new KnexUserRepository();
  const customerRepository = new KnexCustomerRepository();
  const accessLevelRepository = new KnexAccessLevelsRepository();

  const customer = await customerRepository.getTaxNumber(env.FIRST_CUSTOMER_TAX_NUMBER);

  if (!customer) {
    console.log('Cliente inicial não encontrado. Execute a seed de cliente primeiro.');
    return;
  }

  const existingUser = await userRepository.findOneBy({
    email: env.INTERNAL_USER_EMAIL,
    role: InternalRolesEnum.OWNER,
  });

  if (existingUser) {
    console.log('⏭️ Usuário interno já existe, pulando seed...');
    return;
  }

  const accessLevel = await accessLevelRepository.findByRole(InternalRolesEnum.OWNER);

  const hashedPassword = await bcrypt.hash(env.INTERNAL_USER_PASSWORD, 8);

  const internalUser = {
    first_name: 'System',
    last_name: 'Internal',
    email: env.INTERNAL_USER_EMAIL,
    password: hashedPassword,
    active: true,
    terms_accept: true,
    customer_id: customer.id,
    role: InternalRolesEnum.OWNER,
    access_level_id: accessLevel?.id,
    created_by: 'BQ',
  };

  await userRepository.insert(internalUser);

  console.log('✅ Usuário interno criado com sucesso!');
}
