import { randomUUID } from 'crypto';
import { Knex } from 'knex';

export async function seed(knex: Knex): Promise<void> {
  const existingCategories = await knex('courses_categories').count('id as count').first();
  if (existingCategories && Number(existingCategories.count) > 0) {
    console.log('⏭️ Categorias de cursos já existem, pulando seed...');
    return;
  }

  const categorias = [
    { name: 'Anatomia', description: 'Estudo da estrutura do corpo humano.' },
    { name: 'Fisiologia', description: 'Estudo do funcionamento dos órgãos e sistemas.' },
    { name: 'Bioquímica', description: 'Estudo das reações químicas no organismo.' },
    { name: 'Farmacologia', description: 'Estudo dos medicamentos e seus efeitos.' },
    { name: 'Patologia', description: 'Estudo das doenças.' },
    { name: 'Microbiologia', description: 'Estudo dos microrganismos.' },
    { name: 'Imunologia', description: 'Estudo do sistema imunológico.' },
    { name: 'Genética Médica', description: 'Estudo dos genes e hereditariedade.' },
    { name: 'Clínica Médica', description: 'Diagnóstico e tratamento clínico.' },
    { name: 'Cirurgia', description: 'Procedimentos cirúrgicos.' },
    { name: 'Ginecologia e Obstetrícia', description: 'Saúde da mulher e parto.' },
    { name: 'Pediatria', description: 'Saúde da criança e do adolescente.' },
    { name: 'Psiquiatria', description: 'Saúde mental.' },
    { name: 'Radiologia', description: 'Exames de imagem.' },
    { name: 'Dermatologia', description: 'Saúde da pele.' },
    { name: 'Oftalmologia', description: 'Saúde dos olhos.' },
    { name: 'Ortopedia', description: 'Saúde dos ossos e músculos.' },
    { name: 'Cardiologia', description: 'Saúde do coração.' },
    { name: 'Neurologia', description: 'Saúde do sistema nervoso.' },
    { name: 'Oncologia', description: 'Estudo do câncer.' },
    { name: 'Urologia', description: 'Saúde do trato urinário.' },
    { name: 'Otorrinolaringologia', description: 'Ouvido, nariz e garganta.' },
    { name: 'Endocrinologia', description: 'Hormônios e metabolismo.' },
    { name: 'Gastroenterologia', description: 'Saúde do sistema digestivo.' },
    { name: 'Nefrologia', description: 'Saúde dos rins.' },
    { name: 'Reumatologia', description: 'Doenças reumáticas.' },
    { name: 'Infectologia', description: 'Doenças infecciosas.' },
    { name: 'Medicina Preventiva', description: 'Promoção da saúde e prevenção de doenças.' },
    { name: 'Medicina Legal', description: 'Aplicação da medicina na justiça.' },
    { name: 'Saúde Coletiva', description: 'Saúde pública e coletiva.' },
    { name: 'Emergências Médicas', description: 'Atendimento a situações de emergência.' },
  ];

  for (const categoria of categorias) {
    const exists = await knex('courses_categories').where({ name: categoria.name }).first();

    if (!exists) {
      await knex('courses_categories').insert({
        id: randomUUID(),
        ...categoria,
      });
    }
  }

  console.log('✅ Categorias de cursos criadas com sucesso!');
}
