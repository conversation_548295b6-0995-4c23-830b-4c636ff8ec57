import { randomUUID } from 'crypto';
import { Knex } from 'knex';

import { KnexSpecialtiesRepository } from '../../repositories/knex/specialties.repositories';

export async function seed(knex: Knex): Promise<void> {
  const specialtiesRepository = new KnexSpecialtiesRepository();

  const existingSpecialties = await specialtiesRepository.findAll();
  if (existingSpecialties.length > 0) {
    console.log('⏭️ Especialidades já existem, pulando seed...');
    return;
  }

  await knex('specialties').insert([
    { id: randomUUID(), name: '<PERSON><PERSON>' },
    { id: randomUUID(), name: 'Enfermage<PERSON>' },
    { id: randomUUID(), name: 'Fisioterapia' },
    { id: randomUUID(), name: 'Farmácia' },
    { id: randomUUID(), name: 'Biomedicina' },
    { id: randomUUID(), name: '<PERSON><PERSON><PERSON><PERSON>' },
    { id: randomUUID(), name: '<PERSON><PERSON><PERSON>log<PERSON>' },
    { id: randomUUID(), name: '<PERSON>onoaudiolog<PERSON>' },
    { id: randomUUID(), name: 'Psicolog<PERSON>' },
    { id: randomUUID(), name: 'Terapia Ocupacional' },
  ]);

  console.log('✅ Especialidades criadas com sucesso!');
}
