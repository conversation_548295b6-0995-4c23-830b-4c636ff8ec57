import { randomUUID } from 'crypto';
import { Knex } from 'knex';

import { KnexInstitutionsRepository } from '../../repositories/knex/institutions.repositories';

export async function seed(knex: Knex): Promise<void> {
  const institutionsRepository = new KnexInstitutionsRepository();

  const existingInstitutions = await institutionsRepository.findAllBy({ customer_id: null });
  if (existingInstitutions.length > 0) {
    console.log('⏭️ Instituições já existem, pulando seed...');
    return;
  }

  await knex('institutions').insert([
    {
      id: randomUUID(),
      name: 'Faculdade de Ciências Médicas de Campina Grande - PB - Unifacisa',
      acronym: 'UNIFACISA - FCM',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade Estácio do Pantanal - Estácio FAPAN',
      acronym: 'FAPAN',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Gurupi - Campus Paraíso - UNIRG',
      acronym: 'UNIRG',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade do Sul de Santa Catarina - Palhoça/SC - UNISUL',
      acronym: 'UNISUL',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade do Estado do Pará - Belem/PA - UEPA',
      acronym: 'UEPA',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Ciências Médicas de Minas Gerais- FELUMA - FCMMG -',
      acronym: 'FCM-MG',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal de Sergipe - UFS/Campus Lagarto',
      acronym: 'UFS/Campus de Lagarto',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Rio Verde - UniRV - Rio Verde/GO',
      acronym: 'UniRV',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal do Estado do Rio de Janeiro/RJ - UNIRIO',
      acronym: 'UNIRIO',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal do Paraná - Curitiba - UFPR',
      acronym: 'UFPR',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de São Paulo - Campus São Paulo - USP-SP',
      acronym: 'FMSP-USP',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Ciências Médicas e da Saúde de Juiz de Fora-MG - SUPREMA',
      acronym: 'FCMS-JF',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal do Pará - UFPA',
      acronym: 'UFPA',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal de Pernambuco - UFPE',
      acronym: 'UFPE',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal de São Paulo - UNIFESP',
      acronym: 'UNIFESP',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade do Estado do Rio de Janeiro - UERJ',
      acronym: 'UERJ',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal do Ceará - UFC',
      acronym: 'UFC',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Pernambuco-/Recife - UPE',
      acronym: 'UPE',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal de Alagoas - UFAL',
      acronym: 'UFAL',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Maringá - CESUMAR',
      acronym: 'UNICESUMAR',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade Santa Marcelina - FASM',
      acronym: 'FASM',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Ciências Médicas e da Saúde de Itajubá- MG',
      acronym: 'FACISB',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Pontifícia Universidade Católica do Rio Grande do Sul - PUC RS',
      acronym: 'PUC-RS',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal de Santa Catarina - UFSC',
      acronym: 'UFSC',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Ciências Médicas da Santa Casa de São Paulo - FCMSCSP',
      acronym: 'FCMSCSP',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Luterana do Brasil - ULBRA',
      acronym: 'ULBRA',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Fortaleza - UNIFOR',
      acronym: 'UNIFOR',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário Christus - Unichristus',
      acronym: 'UNiCHRISTUS',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal do Rio Grande do Sul - UFRGS',
      acronym: 'UFRGS',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade Evangélica Mackenzie do Paraná',
      acronym: 'FEMPAR',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina São Judas Tadeu',
      acronym: 'FMSJT',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Mogi das Cruzes - UMC',
      acronym: 'UMC',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário Ingá - UNINGÁ',
      acronym: 'UNINGÁ',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário de Rio Preto - UNIRP',
      acronym: 'UNIRP',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Estadual do Oeste do Paraná - UNIOESTE',
      acronym: 'UNIOESTE',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal do Rio Grande do Norte - UFRN',
      acronym: 'UFRN',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal de Ciências da Saúde de Porto Alegre - UFCSPA',
      acronym: 'UFCSPA',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade da Região de Joinville - Univille',
      acronym: 'Univille',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Olinda - FMO',
      acronym: 'FMO',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Caxias do Sul - UCS',
      acronym: 'UCS',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Santa Cruz do Sul - UNISC',
      acronym: 'UNISC',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário de Patos de Minas - UNIPAM',
      acronym: 'UNIPAM',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Taubaté - UNITAU',
      acronym: 'UNITAU',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade Meridional - IMED',
      acronym: 'IMED',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Passo Fundo - UPF',
      acronym: 'UPF',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade São Francisco - USF',
      acronym: 'USF',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade do Extremo Sul Catarinense - UNESC',
      acronym: 'UNESC',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade do Vale do Rio dos Sinos - UNISINOS',
      acronym: 'UNISINOS',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade do Vale do Rio Verde - Unincor',
      acronym: 'Unincor',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade da Região da Campanha - URCAMP',
      acronym: 'URCAMP',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade do Oeste de Santa Catarina - UNOESC',
      acronym: 'UNOESC',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal do Vale do São Francisco - UNIVASF',
      acronym: 'UNIVASF',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Regional Integrada do Alto Uruguai e das Missões - URI',
      acronym: 'URI',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Tecnologia e Ciências de Itabuna - FTC',
      acronym: 'FTC',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Regional do Noroeste do Estado do Rio Grande do Sul - UNIJUÍ',
      acronym: 'UNIJUÍ',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário Luterano de Palmas - CEULP',
      acronym: 'CEULP',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário de Votuporanga - Unifev',
      acronym: 'Unifev',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal Fluminense - UFF',
      acronym: 'UFF',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade da Amazônia - UNAMA',
      acronym: 'UNAMA',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade do Planalto Catarinense - UNIPLAC',
      acronym: 'UNIPLAC',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Estadual do Sudoeste da Bahia - UESB',
      acronym: 'UESB',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Pontifícia Universidade Católica de Campinas - PUC Campinas',
      acronym: 'PUCCAMP',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Jundiaí - FMJ',
      acronym: 'FMJ',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Tiradentes - UNIT - Aracaju/SE',
      acronym: 'UNIT - Aracaju',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Barbacena - FAME',
      acronym: 'FAME',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina Nova Esperança - FAMENE',
      acronym: 'FAMENE',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Campos - FMC',
      acronym: 'FMC',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário São Camilo - SP',
      acronym: 'SÃO CAMILO',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade Ciências Médicas de Minas Gerais - FCMMG',
      acronym: 'FCMMG',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Pontifícia Universidade Católica de Sorocaba - PUC Sorocaba',
      acronym: 'PUC SOROCABA',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Nove de Julho - UNINOVE',
      acronym: 'UNINOVE',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal de Ouro Preto - UFOP',
      acronym: 'UFOP',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal de Viçosa - UFV',
      acronym: 'UFV',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal de São João del-Rei - UFSJ',
      acronym: 'UFSJ',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Vassouras - UNIFA',
      acronym: 'UNIFA',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário de Lavras - UNILAVRAS',
      acronym: 'UNILAVRAS',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Pontifícia Universidade Católica do Rio de Janeiro - PUC RJ',
      acronym: 'PUC-RJ',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Marília - UNIMAR',
      acronym: 'UNIMAR',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal do Triângulo Mineiro - UFTM',
      acronym: 'UFTM',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário Estácio de Ribeirão Preto',
      acronym: 'UNIESTÁCIO',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Estácio de Sá - UNESA',
      acronym: 'UNESA',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal da Bahia - UFBA',
      acronym: 'UFBA',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Ciências Médicas da Paraíba - FCM PB',
      acronym: 'FCM PB',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Ribeirão Preto - UNAERP - Campus Ribeirão Preto',
      acronym: 'UNAERP',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário de Caratinga - UNEC',
      acronym: 'UNEC',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal da Paraíba - UFPB',
      acronym: 'UFPB',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Santo Amaro - UNISA',
      acronym: 'UNISA',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade do Grande Rio - UNIGRANRIO',
      acronym: 'UNIGRANRIO',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário do Rio de Janeiro - UNIRJ',
      acronym: 'UNIRJ',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Anhembi Morumbi',
      acronym: 'ANHEMBI MORUMBI',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Estácio de São Paulo',
      acronym: 'ESTÁCIO',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Itabuna - FMI',
      acronym: 'FMI',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Estadual do Mato Grosso do Sul - UEMS',
      acronym: 'UEMS',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário de Sete Lagoas - UNIFEMM',
      acronym: 'UNIFEMM',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Ciências Médicas de Campina Grande - PB - FACISA',
      acronym: 'FACISA',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário do Norte - UNINORTE',
      acronym: 'UNINORTE',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal da Grande Dourados - UFGD',
      acronym: 'UFGD',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade do Oeste Paulista - UNOESTE',
      acronym: 'UNOESTE',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Metropolitana de Santos - UNIMES',
      acronym: 'UNIMES',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário Barão de Mauá',
      acronym: 'BARÃO DE MAUÁ',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Marília - FAMEMA',
      acronym: 'FAMEMA',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário de Araras - UNAR',
      acronym: 'UNAR',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de São José do Rio Preto - FAMERP',
      acronym: 'FAMERP',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Franca - UNIFRAN',
      acronym: 'UNIFRAN',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Sorocaba - UNISO',
      acronym: 'UNISO',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Alfenas - UNIFENAS',
      acronym: 'UNIFENAS',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Ciências Médicas de São Paulo - FCMS',
      acronym: 'FCMS',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade do Sagrado Coração - USC',
      acronym: 'USC',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário Unieuro - UNIEURO',
      acronym: 'UNIEURO',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade do Vale do Sapucaí - UNIVÁS',
      acronym: 'UNIVÁS',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário do Planalto Central - UNICEPLAC',
      acronym: 'UNICEPLAC',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Brasília - UNB',
      acronym: 'UNB',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Botucatu',
      acronym: 'FMB',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal de Goiás - UFG',
      acronym: 'UFG',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Cuiabá - UNIC',
      acronym: 'UNIC',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Caxias do Sul - UCS',
      acronym: 'UCS',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal do Mato Grosso - UFMT',
      acronym: 'UFMT',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade do Vale do Itajaí - UNIVALI',
      acronym: 'UNIVALI',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade do Sul de Santa Catarina - UNISUL',
      acronym: 'UNISUL',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade para o Desenvolvimento do Alto Vale do Itajaí - UNIDAVI',
      acronym: 'UNIDAVI',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de São José do Rio Preto - FAMEP',
      acronym: 'FAMEP',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade da Serra Gaúcha - FSG',
      acronym: 'FSG',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Ciências Médicas de Cacoal - FACIMED',
      acronym: 'FACIMED',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Ciências Médicas de Rondônia - FACIMED',
      acronym: 'FACIMED',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Araraquara - UNIARA',
      acronym: 'UNIARA',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal de Rondônia - UNIR',
      acronym: 'UNIR',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal de Roraima - UFRR',
      acronym: 'UFRR',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário São Lucas - UniSL',
      acronym: 'UniSL',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal de Mato Grosso do Sul - UFMS',
      acronym: 'UFMS',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade do Mato Grosso - UNEMAT',
      acronym: 'UNEMAT',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal do Acre - UFAC',
      acronym: 'UFAC',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Estadual do Piauí - UESPI',
      acronym: 'UESPI',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal do Piauí - UFPI',
      acronym: 'UFPI',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina do ABC - FMABC',
      acronym: 'FMABC',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal do Espírito Santo - UFES',
      acronym: 'UFES',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Escola Superior de Ciências da Santa Casa de Misericórdia de Vitória - EMESCAM',
      acronym: 'EMESCAM',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário do Estado do Pará - PA - CESUPA',
      acronym: 'CESUPA',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Federal de Minas Gerais - UFMG',
      acronym: 'UFMG',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Pontifícia Universidade Católica do Paraná - Toledo - PUC PR',
      acronym: 'PUC PR',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário do Vale do Araguaia',
      acronym: 'UNIVAR',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade de Ribeirão Preto - UNAERP - Campus Guarujá',
      acronym: 'UNAERP',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Estadual do Tocantins',
      acronym: 'Unitins',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade Tiradentes de Goiana - FITS',
      acronym: 'FITS',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Tiradentes - Unit - Estância',
      acronym: 'UNIT - Estância',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Universidade Regional do Cariri - URCA',
      acronym: 'URCA',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Ciências Aplicadas e Sociais de Petrolina',
      acronym: 'FACAPE',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário AGES',
      acronym: 'UniAGES',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade Paraíso Araripina',
      acronym: 'FAP',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina e Saúde de Diamantina',
      acronym: 'FAMS',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário de Adamantina',
      acronym: 'UNIFAI',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Ciências Médicas de Campina Grande',
      acronym: 'FACIG',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade Unirb Salvador',
      acronym: 'UNIRB SALVADOR',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade Unirb Parnaíba',
      acronym: 'UNIRB PARNAÍBA',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Centro Universitário de Excelência',
      acronym: 'UNEX',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Escola Superior de Ciências da Santa Casa de Misericórdia de Belo Horizonte',
      acronym: 'EMESCAM BH',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Guanambi',
      acronym: 'FAG',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade São Leopoldo Mandic de Campinas',
      acronym: 'SLM CAMPINAS',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Itabira',
      acronym: 'FMITA',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Juína',
      acronym: 'FAMMJ',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Teixeira de Freitas',
      acronym: 'FACIT-TF',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Parnaíba',
      acronym: 'FAMEP',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Rio Verde',
      acronym: 'FAME',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Santo Amaro',
      acronym: 'UNISA',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Santo Amaro',
      acronym: 'FMSA',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: "Faculdade de Medicina de Santa Bárbara d'Oeste",
      acronym: 'FMSBO',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de São Sebastião',
      acronym: 'FMSS',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Sobral',
      acronym: 'FMS',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Teresina',
      acronym: 'FMT',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Pindamonhangaba',
      acronym: 'FMP',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Taubaté',
      acronym: 'FMT',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Volta Redonda',
      acronym: 'FMVR',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Vitória',
      acronym: 'FMV',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Três Rios',
      acronym: 'FMTR',
      customer_id: null,
    },
    {
      id: randomUUID(),
      name: 'Faculdade de Medicina de Viçosa',
      acronym: 'FMV',
      customer_id: null,
    },
  ]);

  console.log('✅ Instituições criadas com sucesso!');
}
