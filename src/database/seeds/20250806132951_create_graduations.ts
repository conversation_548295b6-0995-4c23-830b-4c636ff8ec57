import { randomUUID } from 'crypto';
import { Knex } from 'knex';

export async function seed(knex: Knex): Promise<void> {
  const existing = await knex('graduations').count('id as count').first();
  if (existing && Number(existing.count) > 0) {
    console.log('⏭️ Graduações j<PERSON> existem, pulando seed...');
    return;
  }

  await knex('graduations').insert([{ id: randomUUID(), name: '<PERSON><PERSON>', type: 'Gradua<PERSON>' }]);

  console.log('✅ Graduação criada com sucesso!');
}
