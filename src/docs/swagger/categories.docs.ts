import { CategoryDocSchema } from './schemas/category.schema';
import { tag } from './tag';

export const categoriesPaths = {
  '/v2/categories': {
    post: {
      tags: [tag.categories.name],
      summary: 'Criar uma nova categoria',
      description: 'Endpoint para criar uma nova categoria.',
      security: [{ bearerAuth: [] }],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: CategoryDocSchema.createCategorySchema,
          },
        },
      },
      responses: {
        201: {
          description: 'Categoria criada com sucesso.',
          content: {
            'application/json': {
              schema: CategoryDocSchema.categoryResponseSchema,
            },
          },
        },
        400: {
          description: 'Bad request',
        },
        500: {
          description: 'Internal server error',
        },
      },
    },
  },
  '/v2/categories/{id}': {
    put: {
      tags: [tag.categories.name],
      summary: 'Atualizar uma categoria existente',
      description: 'Endpoint para atualizar uma categoria existente.',
      security: [{ bearerAuth: [] }],
      parameters: [
        {
          in: 'path',
          name: 'id',
          required: true,
          schema: {
            type: 'string',
          },
        },
      ],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: CategoryDocSchema.createCategorySchema,
          },
        },
      },
      responses: {
        200: {
          description: 'Categoria atualizada com sucesso',
          content: {
            'application/json': {
              schema: CategoryDocSchema.categoryUpdateResponseSchema,
            },
          },
        },
        400: {
          description: 'Requisição inválida',
        },
        404: {
          description: 'Categoria não encontrada',
        },
        500: {
          description: 'Erro interno do servidor',
        },
      },
    },
    delete: {
      tags: [tag.categories.name],
      summary: 'Deletar uma categoria existente',
      description: 'Endpoint para deletar uma categoria existente.',
      security: [{ bearerAuth: [] }],
      parameters: [
        {
          in: 'path',
          name: 'id',
          required: true,
          schema: {
            type: 'string',
          },
        },
      ],
      responses: {
        200: {
          description: 'Categoria deletada com sucesso',
          content: {
            'application/json': {
              schema: CategoryDocSchema.categoryDeleteResponseSchema,
            },
          },
        },
        404: {
          description: 'Categoria não encontrada',
        },
        500: {
          description: 'Erro interno do servidor',
        },
      },
    },
  },
};
