import { tag } from './tag';

const customerSchemas = {
  CreateCustomerRequest: {
    type: 'object',
    required: ['customerName', 'email', 'taxNumber', 'subdomain'],
    properties: {
      customerName: {
        type: 'string',
        description: 'Nome do cliente',
        example: 'Empresa ABC',
      },
      email: {
        type: 'string',
        format: 'email',
        description: 'Email do cliente',
        example: '<EMAIL>',
      },
      taxNumber: {
        type: 'string',
        description: 'CNPJ do cliente',
        example: '12345678000190',
      },
      logoUrl: {
        type: 'string',
        description: 'URL do logo do cliente',
        example: 'https://exemplo.com/logo.png',
      },
      primaryColor: {
        type: 'string',
        description: 'Cor primária da marca',
        example: '#FF0000',
      },
      secondaryColor: {
        type: 'string',
        description: 'Cor secundária da marca',
        example: '#00FF00',
      },
      status: {
        type: 'boolean',
        description: 'Status do cliente',
        example: true,
      },
      subdomain: {
        type: 'string',
        description: 'Subdomínio do cliente',
        example: 'empresaabc',
      },
      websiteUrl: {
        type: 'string',
        description: 'Website do cliente',
        example: 'https://empresaabc.com.br',
      },
      externalCustomerId: {
        type: 'string',
        description: 'ID externo do cliente',
        example: 'ext_123',
      },
    },
  },
  CreateCustomerResponse: {
    type: 'object',
    properties: {
      message: {
        type: 'string',
        example: 'Cliente cadastrado com sucesso!',
      },
      data: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            format: 'uuid',
            example: '123e4567-e89b-12d3-a456-************',
          },
          name: {
            type: 'string',
            example: 'Empresa ABC',
          },
          email: {
            type: 'string',
            example: '<EMAIL>',
          },
          tax_number: {
            type: 'string',
            example: '12345678000190',
          },
          logo_url: {
            type: 'string',
            example: 'https://exemplo.com/logo.png',
          },
          primary_color: {
            type: 'string',
            example: '#FF0000',
          },
          secondary_color: {
            type: 'string',
            example: '#00FF00',
          },
          status: {
            type: 'boolean',
            example: true,
          },
          subdomain: {
            type: 'string',
            example: 'empresaabc',
          },
          website: {
            type: 'string',
            example: 'https://empresaabc.com.br',
          },
        },
      },
    },
  },
};

export const customersPaths = {
  '/v2/external/customers': {
    post: {
      tags: [tag.customers.name],
      summary: 'Criar novo cliente',
      description: 'Cria um novo cliente no sistema',
      security: [
        {
          ApiToken: [],
        },
      ],
      parameters: [
        {
          in: 'header',
          name: 'x-api-key',
          required: true,
          schema: {
            type: 'string',
          },
          description: 'Token de autenticação para criação do cliente',
        },
      ],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/CreateCustomerRequest',
            },
          },
        },
      },
      responses: {
        201: {
          description: 'Cliente criado com sucesso',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/CreateCustomerResponse',
              },
            },
          },
        },
        400: {
          description: 'Dados inválidos',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: {
                    type: 'string',
                    example: 'Dados inválidos',
                  },
                },
              },
            },
          },
        },
        409: {
          description: 'Cliente já existe',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: {
                    type: 'string',
                    example: 'Já existe um cliente com esse tax_number',
                  },
                },
              },
            },
          },
        },
      },
    },
  },
};

export const customersSchemas = customerSchemas;
