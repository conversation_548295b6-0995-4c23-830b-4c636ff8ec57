import { updateDisciplineSchema } from '../../schema/disciplines.schema';
import * as disciplineExamples from './examples/discipline.example';
import { DisciplineDocSchema } from './schemas/discipline.schema';
import { DisciplineCategoryAccessDocSchema } from './schemas/disciplineCategoryAccess.schema';
import { tag } from './tag';

export const disciplinesPaths = {
  '/v2/disciplines': {
    post: {
      tags: [tag.disciplines.name],
      summary: 'Criar uma nova disciplina para um customer específico',
      description: 'Endpoint para criar uma nova disciplina no sistema.',
      security: [{ bearerAuth: [] }],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: DisciplineDocSchema.createDisciplineSchema,
            examples: {
              Success: disciplineExamples.createDiscipline,
            },
          },
        },
      },
      responses: {
        201: {
          description:
            'Disciplina criada com sucesso. Retorna a disciplina recém-criada no corpo da resposta.',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: {
                    type: 'string',
                    example: 'Disciplina criada com sucesso',
                  },
                  discipline: {
                    type: 'object',
                    properties: {
                      name: {
                        type: 'string',
                        example: 'Math',
                      },
                      code: {
                        type: 'string',
                        example: 'MTH123',
                      },
                      customer_id: {
                        type: 'string',
                        example: 'customer-id',
                      },
                    },
                  },
                },
              },
            },
          },
        },
        500: {
          description: 'Internal server error',
        },
        400: {
          description: 'Bad request',
          content: {
            'application/json': {
              examples: {
                BadRequest: {
                  summary: 'Requisição inválida',
                  value: {
                    message: 'Requisição inválida',
                  },
                },
              },
            },
          },
        },
        security: [{ bearerAuth: [] }],
      },
    },
  },
  '/v2/disciplines/{id}': {
    put: {
      tags: [tag.disciplines.name],
      summary: 'Atualizar uma disciplina existente',
      security: [{ bearerAuth: [] }],
      parameters: [
        {
          in: 'path',
          name: 'id',
          required: true,
          schema: {
            type: 'string',
          },
        },
      ],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: updateDisciplineSchema,
          },
        },
      },
      responses: {
        200: {
          description: 'Disciplina atualizada com sucesso',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: {
                    type: 'string',
                    example: 'Disciplina atualizada com sucesso!',
                  },
                },
              },
            },
          },
        },
        400: {
          description: 'Requisição inválida',
        },
        404: {
          description: 'Disciplina não encontrada',
        },
        500: {
          description: 'Erro interno do servidor',
        },
      },
    },
    delete: {
      tags: [tag.disciplines.name],
      summary: 'Deletar uma disciplina existente',
      security: [{ bearerAuth: [] }],
      parameters: [
        {
          in: 'path',
          name: 'id',
          required: true,
          schema: {
            type: 'string',
          },
        },
      ],
      responses: {
        204: {
          description: 'Disciplina deletada com sucesso',
        },
        404: {
          description: 'Disciplina não encontrada',
        },
        500: {
          description: 'Erro interno do servidor',
        },
      },
    },
  },
  '/v2/disciplines/{disciplineId}/categories': {
    post: {
      tags: [tag.disciplines.name],
      summary: 'Adicionar categoria a uma disciplina',
      description: 'Endpoint para adicionar uma categoria a uma disciplina existente.',
      security: [{ bearerAuth: [] }],
      parameters: [
        {
          in: 'path',
          name: 'disciplineId',
          required: true,
          schema: {
            type: 'string',
          },
        },
      ],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: DisciplineCategoryAccessDocSchema.addCategoryInDisciplineBodySchema,
          },
        },
      },
      responses: {
        201: {
          description: 'Categoria adicionada à disciplina com sucesso.',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: {
                    type: 'string',
                    example: 'Categoria adicionada a disciplina com sucesso!',
                  },
                },
              },
            },
          },
        },
        400: {
          description: 'Bad request',
        },
        404: {
          description: 'Disciplina não encontrada',
        },
        500: {
          description: 'Erro interno do servidor',
        },
      },
    },
  },
};
