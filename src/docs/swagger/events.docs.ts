import { EventDocSchema } from './schemas/event.schema';
import { tag } from './tag';

export const eventsPaths = {
  '/v2/events/subdomain': {
    post: {
      tags: [tag.events.name],
      summary: 'Criar um novo subdomínio de evento',
      description: 'Endpoint para criar um novo subdomínio de evento.',
      security: [{ bearerAuth: [] }],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: EventDocSchema.createSubdomainRequestBodySchema,
          },
        },
      },
      responses: {
        201: {
          description: 'Subdomínio de evento criado com sucesso.',
          content: {
            'application/json': {
              schema: EventDocSchema.eventResponseSchema,
            },
          },
        },
        400: {
          description: 'Bad request',
        },
        500: {
          description: 'Internal server error',
        },
      },
    },
  },
  '/v2/events/domain': {
    post: {
      tags: [tag.events.name],
      summary: 'Criar um novo domínio de evento',
      description: 'Endpoint para criar um novo domínio de evento.',
      security: [{ bearerAuth: [] }],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: EventDocSchema.createSubdomainRequestBodySchema,
          },
        },
      },
      responses: {
        201: {
          description: 'Domínio de evento criado com sucesso.',
          content: {
            'application/json': {
              schema: EventDocSchema.eventResponseSchema,
            },
          },
        },
        400: {
          description: 'Bad request',
        },
        500: {
          description: 'Internal server error',
        },
      },
    },
  },
};
