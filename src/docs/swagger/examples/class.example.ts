export const createClassRequestExample = {
  value: {
    name: 'Turma A',
    description: 'Turma de medicina 2024',
    customerId: '550e8400-e29b-41d4-a716-446655440000',
    status: true,
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    externalClassId: 'TURMA2024A',
  },
};

export const createClassResponseExample = {
  value: {
    id: '550e8400-e29b-41d4-a716-446655440000',
    name: 'Turma A',
    description: 'Turma de medicina 2024',
    customerId: '550e8400-e29b-41d4-a716-446655440000',
    status: true,
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    externalClassId: 'TURMA2024A',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
    deletedAt: null,
  },
};

export const updateClassRequestExample = {
  value: {
    name: 'Turma A Atualizada',
    description: 'Turma de medicina 2024 - Atualizada',
    status: true,
    startDate: '2024-02-01',
    endDate: '2024-12-31',
  },
};

export const updateClassResponseExample = {
  value: {
    id: '550e8400-e29b-41d4-a716-446655440000',
    name: 'Turma A Atualizada',
    description: 'Turma de medicina 2024 - Atualizada',
    customerId: '550e8400-e29b-41d4-a716-446655440000',
    status: true,
    startDate: '2024-02-01',
    endDate: '2024-12-31',
    externalClassId: 'TURMA2024A',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
    deletedAt: null,
  },
};
