export const createCustomerRequestExample = {
  value: {
    customerName: 'Empresa Exemplo LTDA',
    email: '<EMAIL>',
    taxNumber: '12345678000190',
    logoUrl: 'https://exemplo.com/logo.png',
    primaryColor: '#FF0000',
    secondaryColor: '#00FF00',
    status: true,
    subdomain: 'exemplo',
    websiteUrl: 'https://exemplo.com.br',
    id: 'EXT123',
    supportEmail: '<EMAIL>',
  },
};

export const createCustomerResponseExample = {
  value: {
    data: {
      id: '123e4567-e89b-12d3-a456-426614174000',
      customer_name: 'Empresa Exemplo LTDA',
      email: '<EMAIL>',
      tax_number: '12345678000190',
      logo_url: 'https://exemplo.com/logo.png',
      primary_color: '#FF0000',
      secondary_color: '#00FF00',
      status: true,
      subdomain: 'exemplo',
      website_url: 'https://exemplo.com.br',
      external_customer_id: 'EXT123',
      support_email: '<EMAIL>',
    },
  },
};
