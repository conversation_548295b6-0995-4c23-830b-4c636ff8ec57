export const createUserRequestExample = {
  value: {
    firstName: '<PERSON>',
    lastName: 'Doe',
    cpf: '12345678900',
    email: '<EMAIL>',
    password: 'password123',
    customerId: '550e8400-e29b-41d4-a716-446655440000',
    classId: '550e8400-e29b-41d4-a716-446655440000',
    externalUserId: 'USER123',
    status: true,
  },
};

export const createUserResponseExample = {
  value: {
    id: '550e8400-e29b-41d4-a716-446655440000',
    firstName: 'John',
    lastName: 'Doe',
    cpf: '12345678900',
    email: '<EMAIL>',
    customerId: '550e8400-e29b-41d4-a716-446655440000',
    classId: '550e8400-e29b-41d4-a716-446655440000',
    externalUserId: 'USER123',
    status: true,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
    deletedAt: null,
  },
};

export const updateUserRequestExample = {
  value: {
    firstName: 'John',
    lastName: 'Doe Updated',
    email: '<EMAIL>',
    status: true,
  },
};
