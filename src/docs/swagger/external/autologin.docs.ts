export const externalAutologinPaths = {
  '/autologin': {
    get: {
      tags: ['Authentication'],
      summary: 'Autologin via token',
      description: `Endpoint para realizar autologin através de token.

Fluxo da operação:
1. Valida o token de autologin fornecido via query parameter
2. Verifica se o cliente existe através do external_customer_id
3. Verifica se o usuário existe e está ativo
4. Gera um novo token JWT para acesso ao sistema
5. Retorna o token de acesso e dados do cliente

Estrutura necessária do token JWT:
- id: "string": "ID do usuário no sistema externo (external_user_id)"
- customerId: "string": "ID do cliente no sistema externo (external_customer_id)"

Observações importantes:
- O token de autologin deve ser fornecido como query parameter 'token'
- É necessário fornecer o x-api-key no header para autenticação do sistema externo
- O token retornado deve ser usado nas requisições subsequentes como Bearer token
- O token tem validade limitada por questões de segurança
- O token deve ser um JWT válido contendo os campos id e customerId`,
      parameters: [
        {
          in: 'header',
          name: 'x-api-key',
          required: true,
          schema: {
            type: 'string',
          },
          description: 'Token de autenticação para sistemas externos',
        },
        {
          in: 'query',
          name: 'token',
          required: true,
          schema: {
            type: 'string',
          },
          description: 'Token JWT contendo id e customerId do usuário',
        },
      ],
      security: [{ ApiToken: [] }],
      responses: {
        200: {
          description: 'Login realizado com sucesso',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: {
                    type: 'string',
                    example: 'Login realizado com sucesso!',
                  },
                  token: {
                    type: 'string',
                    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                    description: 'Novo token JWT para autenticação nas próximas requisições',
                  },
                  customer: {
                    type: 'object',
                    description: 'Dados do cliente',
                    properties: {
                      id: {
                        type: 'string',
                        example: '123e4567-e89b-12d3-a456-426614174000',
                      },
                      external_customer_id: {
                        type: 'string',
                        example: 'external-123',
                      },
                      name: {
                        type: 'string',
                        example: 'Nome do Cliente',
                      },
                    },
                  },
                },
              },
            },
          },
        },
        401: {
          description: 'Não autorizado',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: {
                    type: 'string',
                    example: 'Token inválido',
                  },
                },
              },
            },
          },
        },
        404: {
          description: 'Recurso não encontrado',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  error: {
                    type: 'string',
                    example: 'ResourceNotFoundError',
                  },
                  message: {
                    type: 'string',
                  },
                  statusCode: {
                    type: 'number',
                    example: 404,
                  },
                },
              },
              examples: {
                'Cliente não encontrado': {
                  value: {
                    error: 'ResourceNotFoundError',
                    message: 'Cliente não encontrado',
                  },
                },
                'Usuário não encontrado': {
                  value: {
                    error: 'ResourceNotFoundError',
                    message: 'Usuário não encontrado',
                  },
                },
              },
            },
          },
        },
      },
    },
  },
};
