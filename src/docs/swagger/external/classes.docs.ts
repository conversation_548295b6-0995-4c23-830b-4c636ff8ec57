import { ClassDocSchema } from '../schemas/class.schema';

export const externalClassesPaths = {
  '/v2/external/classes': {
    post: {
      tags: ['External Classes'],
      summary: 'Criar uma nova turma',
      description: `Endpoint para criar uma nova turma no sistema.

Fluxo da operação:
1. Valida o token de API fornecido no header
2. Valida os dados de entrada
3. Verifica se o cliente (customerId) existe
4. Verifica se já existe turma com o mesmo external_class_id para o cliente
5. Se fornecido instructorId, valida se o instrutor existe
6. Cria a turma e retorna os dados

Observações importantes:
- O token de API deve ser fornecido no header 'x-api-key'
- Os campos obrigatórios são: name, status, customerId e id (external_class_id)
- O status deve ser 'active' ou 'inactive'
- Se não fornecidas, start_date e end_date serão definidas como a data atual
- Se fornecido, o instructorId deve ser um ID de um usuário existente`,
      security: [{ apiToken: [] }],
      parameters: [
        {
          in: 'header',
          name: 'x-api-key',
          required: true,
          schema: {
            type: 'string',
          },
          description: 'Token de autenticação para sistemas externos',
        },
      ],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: ClassDocSchema.createClassRequestBodySchema,
            example: {
              name: 'Turma de Matemática 2024',
              description: 'Turma preparatória para o vestibular',
              status: 'active',
              startDate: '2024-01-01',
              endDate: '2024-12-31',
              instructorId: '550e8400-e29b-41d4-a716-************',
              customerId: '550e8400-e29b-41d4-a716-************',
              id: 'TURMA2024A',
            },
          },
        },
      },
      responses: {
        201: {
          description: 'Turma criada com sucesso',
          content: {
            'application/json': {
              schema: ClassDocSchema.classResponseSchema,
              example: {
                message: 'Turma criada com sucesso.',
                class: {
                  id: '550e8400-e29b-41d4-a716-************',
                  name: 'Turma de Matemática 2024',
                  description: 'Turma preparatória para o vestibular',
                  status: 'active',
                  start_date: '2024-01-01',
                  end_date: '2024-12-31',
                  instructor_id: '550e8400-e29b-41d4-a716-************',
                  customer_id: '550e8400-e29b-41d4-a716-************',
                  external_class_id: 'TURMA2024A',
                  created_at: '2023-12-20T10:00:00.000Z',
                  updated_at: '2023-12-20T10:00:00.000Z',
                  deleted_at: null,
                },
              },
            },
          },
        },
        400: {
          description: 'Dados inválidos na requisição',
          content: {
            'application/json': {
              examples: {
                'Campos obrigatórios': {
                  value: {
                    error: 'ValidationError',
                    details: ['name é obrigatório', 'customerId é obrigatório', 'id é obrigatório'],
                  },
                },
              },
            },
          },
        },
        401: {
          description: 'Token de API inválido ou não fornecido',
          content: {
            'application/json': {
              example: {
                error: 'UnauthorizedError',
                message: 'Token de API inválido ou não fornecido',
              },
            },
          },
        },
        404: {
          description: 'Recurso não encontrado',
          content: {
            'application/json': {
              example: {
                error: 'ResourceNotFoundError',
                message: 'Cliente não encontrado',
              },
            },
          },
        },
        409: {
          description: 'Turma já existe',
          content: {
            'application/json': {
              example: {
                message: 'Turma já cadastrada.',
                class: {
                  id: '550e8400-e29b-41d4-a716-************',
                  name: 'Turma de Matemática 2024',
                  external_class_id: 'TURMA2024A',
                  // ... outros campos
                },
              },
            },
          },
        },
        500: {
          description: 'Erro interno do servidor',
          content: {
            'application/json': {
              example: {
                error: 'InternalServerError',
                message: 'Ocorreu um erro interno no servidor',
              },
            },
          },
        },
      },
    },
  },
  '/v2/external/classes/{id}': {
    put: {
      tags: ['External Classes'],
      summary: 'Atualizar uma turma existente',
      description: `Endpoint para atualizar os dados de uma turma existente.

Fluxo da operação:
1. Valida o token de API fornecido no header
2. Valida os dados de entrada
3. Verifica se o cliente (customerId) existe
4. Verifica se a turma existe para o cliente informado
5. Se fornecido instructorId, valida se o user existe
6. Atualiza apenas os campos fornecidos na requisição

Observações importantes:
- O token de API deve ser fornecido no header 'x-api-key'
- O customerId é obrigatório
- Apenas os campos enviados serão atualizados
- O id na URL deve ser o external_class_id da turma que vem de quem consulta a API
- O status deve ser 'active' ou 'inactive'`,
      security: [{ apiToken: [] }],
      parameters: [
        {
          in: 'header',
          name: 'x-api-key',
          required: true,
          schema: {
            type: 'string',
          },
          description: 'Token de autenticação para sistemas externos',
        },
        {
          in: 'path',
          name: 'id',
          required: true,
          schema: {
            type: 'string',
          },
          description: 'ID externo da turma (external_class_id)',
          example: '12342',
        },
      ],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: ClassDocSchema.updateClassRequestBodySchema,
            example: {
              name: 'Turma de Matemática 2024 - Atualizada',
              description: 'Nova descrição da turma',
              status: 'inactive',
              startDate: '2024-02-01',
              endDate: '2024-12-31',
              instructorId: '550e8400-e29b-41d4-a716-************',
              customerId: '550e8400-e29b-41d4-a716-************',
            },
          },
        },
      },
      responses: {
        200: {
          description: 'Turma atualizada com sucesso',
          content: {
            'application/json': {
              schema: ClassDocSchema.classResponseSchema,
              example: {
                id: '550e8400-e29b-41d4-a716-************',
                name: 'Turma de Matemática 2024 - Atualizada',
                description: 'Nova descrição da turma',
                status: 'inactive',
                start_date: '2024-02-01',
                end_date: '2024-12-31',
                instructor_id: '550e8400-e29b-41d4-a716-************',
                customer_id: '550e8400-e29b-41d4-a716-************',
                external_class_id: 'TURMA2024A',
                created_at: '2023-12-20T10:00:00.000Z',
                updated_at: '2023-12-20T15:00:00.000Z',
                deleted_at: null,
              },
            },
          },
        },
        400: {
          description: 'Dados inválidos na requisição',
          content: {
            'application/json': {
              examples: {
                'Campo obrigatório': {
                  value: {
                    error: 'ValidationError',
                    details: ['customerId é obrigatório'],
                  },
                },
              },
            },
          },
        },
        401: {
          description: 'Token de API inválido ou não fornecido',
          content: {
            'application/json': {
              example: {
                error: 'UnauthorizedError',
                message: 'Token de API inválido ou não fornecido',
              },
            },
          },
        },
        404: {
          description: 'Recurso não encontrado',
          content: {
            'application/json': {
              examples: {
                'Turma não encontrada': {
                  value: {
                    error: 'ResourceNotFoundError',
                    message: 'Turma não encontrada',
                  },
                },
                'Cliente não encontrado': {
                  value: {
                    error: 'ResourceNotFoundError',
                    message: 'Cliente não encontrado',
                  },
                },
                'Instrutor não encontrado': {
                  value: {
                    error: 'ResourceNotFoundError',
                    message: 'Instrutor não encontrado',
                  },
                },
              },
            },
          },
        },
        500: {
          description: 'Erro interno do servidor',
          content: {
            'application/json': {
              example: {
                error: 'InternalServerError',
                message: 'Ocorreu um erro interno no servidor',
              },
            },
          },
        },
      },
    },
    delete: {
      tags: ['External Classes'],
      summary: 'Excluir uma turma',
      description: `Endpoint para excluir uma turma existente.
      
Fluxo da operação:
1. Valida o token de API fornecido no header
2. Valida os dados de entrada
3. Verifica se o cliente existe e tem permissão
4. Verifica se a turma existe para o cliente informado
5. Remove os acessos dos usuários à turma
6. Realiza a exclusão lógica da turma

Observações importantes:
- O token de API deve ser fornecido no header 'x-api-key'
- O customerId é obrigatório como query parameter
- A exclusão é lógica (soft delete)
- O id na URL deve ser o external_class_id da turma`,
      security: [{ apiToken: [] }],
      parameters: [
        {
          in: 'header',
          name: 'x-api-key',
          required: true,
          schema: {
            type: 'string',
          },
          description: 'Token de autenticação para sistemas externos',
        },
        {
          in: 'path',
          name: 'id',
          required: true,
          schema: {
            type: 'string',
          },
          description: 'ID externo da turma (external_class_id)',
          example: '1346553',
        },
        {
          in: 'query',
          name: 'customerId',
          required: true,
          schema: {
            type: 'string',
          },
          description: 'ID do cliente',
          example: '550e8400-e29b-41d4-a716-************',
        },
      ],
      responses: {
        200: {
          description: 'Turma excluída com sucesso',
          content: {
            'application/json': {
              example: {
                message: 'Turma excluída com sucesso',
                deletedClass: {
                  id: '550e8400-e29b-41d4-a716-************',
                  name: 'Turma de Matemática 2024',
                  description: 'Turma preparatória para o vestibular',
                  status: 'inactive',
                  start_date: '2024-01-01',
                  end_date: '2024-12-31',
                  instructor_id: '550e8400-e29b-41d4-a716-************',
                  customer_id: '550e8400-e29b-41d4-a716-************',
                  external_class_id: 'TURMA2024A',
                  created_at: '2023-12-20T10:00:00.000Z',
                  updated_at: '2023-12-20T15:00:00.000Z',
                  deleted_at: '2023-12-20T15:00:00.000Z',
                },
              },
            },
          },
        },
        400: {
          description: 'Dados inválidos na requisição',
          content: {
            'application/json': {
              example: {
                error: 'ValidationError',
                details: ['customerId é obrigatório'],
              },
            },
          },
        },
        401: {
          description: 'Token de API inválido ou não fornecido',
          content: {
            'application/json': {
              example: {
                error: 'UnauthorizedError',
                message: 'Token de API inválido ou não fornecido',
              },
            },
          },
        },
        404: {
          description: 'Recurso não encontrado',
          content: {
            'application/json': {
              examples: {
                'Turma não encontrada': {
                  value: {
                    error: 'ResourceNotFoundError',
                    message: 'Turma não encontrada',
                  },
                },
                'Cliente não encontrado': {
                  value: {
                    error: 'ResourceNotFoundError',
                    message: 'Cliente não encontrado',
                  },
                },
              },
            },
          },
        },
        500: {
          description: 'Erro interno do servidor',
          content: {
            'application/json': {
              example: {
                error: 'GenericError',
                message: 'Erro ao excluir turma',
              },
            },
          },
        },
      },
    },
  },
  '/v2/external/classes/exams': {
    post: {
      tags: ['External Classes'],
      summary: 'Vincular provas a turmas',
      description: `Endpoint para vincular provas a turmas existentes.

Fluxo da operação:
1. Valida o token de API fornecido no header
2. Valida os dados de entrada
3. Verifica se o cliente existe
4. Verifica se todas as turmas existem para o cliente informado
5. Verifica se todas as provas existem para o cliente informado
6. Vincula as provas às turmas, removendo vínculos anteriores não incluídos

Observações importantes:
- O token de API deve ser fornecido no header 'x-api-key'
- Os IDs das turmas devem ser os external_class_id
- Os IDs das provas devem ser os UUIDs internos das provas
- Todas as provas serão vinculadas a todas as turmas informadas
- Vínculos existentes que não estejam na lista serão removidos`,
      security: [{ apiToken: [] }],
      parameters: [
        {
          in: 'header',
          name: 'x-api-key',
          required: true,
          schema: {
            type: 'string',
          },
          description: 'Token de autenticação para sistemas externos',
        },
      ],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              required: ['classIds', 'examIds', 'customerId'],
              properties: {
                classIds: {
                  type: 'array',
                  items: {
                    type: 'string',
                  },
                  description: 'Lista de IDs externos das turmas',
                },
                examIds: {
                  type: 'array',
                  items: {
                    type: 'string',
                    format: 'uuid',
                  },
                  description: 'Lista de IDs das provas',
                },
                customerId: {
                  type: 'string',
                  format: 'uuid',
                  description: 'ID do cliente',
                },
              },
            },
            example: {
              classIds: ['TURMA2024A', 'TURMA2024B'],
              examIds: [
                '550e8400-e29b-41d4-a716-************',
                '550e8400-e29b-41d4-a716-************',
              ],
              customerId: '550e8400-e29b-41d4-a716-************',
            },
          },
        },
      },
      responses: {
        200: {
          description: 'Provas vinculadas com sucesso',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  linkedExams: {
                    type: 'number',
                    description: 'Número de provas vinculadas',
                  },
                  unlinkedExams: {
                    type: 'number',
                    description: 'Número de provas desvinculadas',
                  },
                  classExams: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        classId: {
                          type: 'string',
                          format: 'uuid',
                        },
                        examId: {
                          type: 'string',
                          format: 'uuid',
                        },
                      },
                    },
                  },
                },
              },
              example: {
                linkedExams: 4,
                unlinkedExams: 2,
                classExams: [
                  {
                    classId: '550e8400-e29b-41d4-a716-************',
                    examId: '550e8400-e29b-41d4-a716-************',
                  },
                  {
                    classId: '550e8400-e29b-41d4-a716-************',
                    examId: '550e8400-e29b-41d4-a716-************',
                  },
                  {
                    classId: '550e8400-e29b-41d4-a716-************',
                    examId: '550e8400-e29b-41d4-a716-************',
                  },
                  {
                    classId: '550e8400-e29b-41d4-a716-************',
                    examId: '550e8400-e29b-41d4-a716-************',
                  },
                ],
              },
            },
          },
        },
        400: {
          description: 'Dados inválidos na requisição',
          content: {
            'application/json': {
              examples: {
                'Campos obrigatórios': {
                  value: {
                    error: 'ValidationError',
                    details: [
                      'classIds é obrigatório',
                      'examIds é obrigatório',
                      'customerId é obrigatório',
                    ],
                  },
                },
                'Provas não encontradas': {
                  value: {
                    error: 'BadRequestError',
                    message:
                      'Provas não encontradas: 550e8400-e29b-41d4-a716-446655440002, 550e8400-e29b-41d4-a716-446655440003',
                  },
                },
              },
            },
          },
        },
        401: {
          description: 'Token de API inválido ou não fornecido',
          content: {
            'application/json': {
              example: {
                error: 'UnauthorizedError',
                message: 'Token de API inválido ou não fornecido',
              },
            },
          },
        },
        404: {
          description: 'Recurso não encontrado',
          content: {
            'application/json': {
              examples: {
                'Turmas não encontradas': {
                  value: {
                    error: 'ResourceNotFoundError',
                    message: 'Turmas não encontradas: TURMA2024C, TURMA2024D',
                  },
                },
                'Cliente não encontrado': {
                  value: {
                    error: 'ResourceNotFoundError',
                    message: 'Cliente não encontrado',
                  },
                },
              },
            },
          },
        },
        500: {
          description: 'Erro interno do servidor',
          content: {
            'application/json': {
              example: {
                error: 'InternalServerError',
                message: 'Ocorreu um erro interno no servidor',
              },
            },
          },
        },
      },
    },
  },
};
