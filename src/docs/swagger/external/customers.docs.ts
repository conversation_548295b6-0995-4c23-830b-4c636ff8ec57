import {
  createCustomerRequestExample,
  createCustomerResponseExample,
} from '../examples/customer.example';
import { CustomerDocSchema } from '../schemas/customer.schema';

export const externalCustomersPaths = {
  '/v2/external/customers': {
    post: {
      tags: ['External Customers'],
      summary: 'Criar um novo cliente (External)',
      description: `Endpoint para sistemas externos criarem um novo cliente.

Fluxo da operação:
1. Valida o token de API fornecido no header
2. Valida os dados de entrada
3. Se fornecido, verifica se já existe cliente com o mesmo taxNumber
4. Cria o cliente com os dados fornecidos
5. Retorna os dados públicos do cliente criado

Observações importantes:
- O token de API deve ser fornecido no header 'x-api-key'
- Apenas o campo customerName é obrigatório
- Se fornecido, o taxNumber deve ser único no sistema
- Se fornecidas, as cores (primaryColor e secondaryColor) devem estar no formato hexadecimal
- Se fornecido, o websiteUrl deve ser uma URL válida
- Se fornecido, o logoUrl deve ser uma URL válida`,
      security: [{ apiToken: [] }],
      parameters: [
        {
          in: 'header',
          name: 'x-api-key',
          required: true,
          schema: {
            type: 'string',
          },
          description: 'Key de autenticação para sistemas externos (x-api-key) Ex: bq123',
        },
      ],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: CustomerDocSchema.createCustomerRequestBodySchema,
            example: createCustomerRequestExample.value,
          },
        },
      },
      responses: {
        201: {
          description: 'Cliente cadastrado com sucesso',
          content: {
            'application/json': {
              schema: CustomerDocSchema.customerResponseSchema,
              example: {
                message: 'Cliente cadastrado com sucesso!',
                data: createCustomerResponseExample.value.data,
              },
            },
          },
        },
        400: {
          description: 'Dados inválidos na requisição',
          content: {
            'application/json': {
              examples: {
                'Campo obrigatório': {
                  value: {
                    message: 'Erro na requisição',
                    error: 'ZodError',
                    issue: [
                      {
                        code: 'invalid_type',
                        expected: 'string',
                        received: 'undefined',
                        path: ['customerName'],
                        message: 'O campo customerName é obrigatório',
                      },
                    ],
                  },
                },
              },
            },
          },
        },
        401: {
          description: 'Token de API inválido ou não fornecido',
          content: {
            'application/json': {
              example: {
                error: 'UnauthorizedError',
                message: 'Token de API inválido ou não fornecido',
              },
            },
          },
        },
        409: {
          description: 'Recurso já existe',
          content: {
            'application/json': {
              example: {
                error: 'ResourceAlreadyExistsError',
                message: 'Já existe um cliente com esse tax_number',
              },
            },
          },
        },
        500: {
          description: 'Erro interno do servidor',
          content: {
            'application/json': {
              example: {
                error: 'InternalServerError',
                message: 'Ocorreu um erro interno no servidor',
              },
            },
          },
        },
      },
    },
  },
  '/v2/customers/{id}': {
    put: {
      tags: ['External Customers'],
      summary: 'Atualizar um cliente existente',
      description: `Endpoint para atualizar os dados de um cliente existente.

Fluxo da operação:
1. Valida o token de API fornecido no header
2. Valida o ID do cliente
3. Valida os dados de atualização fornecidos
4. Atualiza o cliente com os novos dados
5. Retorna os dados atualizados do cliente

Observações importantes:
- O token de API deve ser fornecido no header 'x-api-key'
- Todos os campos são opcionais
- Se fornecidas, as cores (primaryColor e secondaryColor) devem estar no formato hexadecimal
- Se fornecido, o websiteUrl deve ser uma URL válida
- Se fornecido, o logoUrl deve ser uma URL válida

IMPORTANTE: Este endpoint não realiza o upload de arquivos.`,
      security: [{ apiToken: [] }],
      parameters: [
        {
          in: 'header',
          name: 'x-api-key',
          required: true,
          schema: {
            type: 'string',
          },
          description: 'Key de autenticação para sistemas externos (x-api-key) Ex: bq123',
        },
        {
          in: 'path',
          name: 'id',
          required: true,
          schema: {
            type: 'string',
            format: 'string',
          },
          description: 'ID do cliente a ser atualizado',
        },
      ],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                customerName: {
                  type: 'string',
                  description: 'Nome do cliente',
                },
                email: {
                  type: 'string',
                  format: 'email',
                  description: 'Email do cliente',
                },
                taxNumber: {
                  type: 'string',
                  description: 'Número de documento do cliente (CPF/CNPJ)',
                },
                logoUrl: {
                  type: 'string',
                  format: 'uri',
                  description: 'URL do logo do cliente',
                },
                primaryColor: {
                  type: 'string',
                  pattern: '^#(?:[0-9a-fA-F]{3}|[0-9a-fA-F]{6})$',
                  description: 'Cor primária em formato hexadecimal (ex: #000000)',
                },
                secondaryColor: {
                  type: 'string',
                  pattern: '^#(?:[0-9a-fA-F]{3}|[0-9a-fA-F]{6})$',
                  description: 'Cor secundária em formato hexadecimal (ex: #000000)',
                },
                status: {
                  type: 'boolean',
                  description: 'Status do cliente (ativo/inativo)',
                },
                subdomain: {
                  type: 'string',
                  description: 'Subdomínio do cliente',
                },
                websiteUrl: {
                  type: 'string',
                  format: 'uri',
                  description: 'URL do website do cliente',
                },
                externalCustomerId: {
                  type: 'string',
                  description: 'ID externo do cliente',
                },
              },
            },
            example: {
              customerName: 'Empresa Exemplo LTDA',
              email: '<EMAIL>',
              taxNumber: '12345678000190',
              logoUrl: 'https://exemplo.com/logo.png',
              primaryColor: '#FF0000',
              secondaryColor: '#00FF00',
              status: true,
              subdomain: 'squad-bq.propofando-lxp-dev.com.br',
              websiteUrl: 'https://exemplo.com.br',
            },
          },
        },
      },
      responses: {
        200: {
          description: 'Cliente atualizado com sucesso',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: {
                    type: 'string',
                    example: 'Cliente atualizado com sucesso',
                  },
                  customer: {
                    type: 'object',
                    properties: {
                      id: {
                        type: 'string',
                        format: 'uuid',
                      },
                      name: {
                        type: 'string',
                      },
                      email: {
                        type: 'string',
                        format: 'email',
                      },
                      tax_number: {
                        type: 'string',
                      },
                      logo_url: {
                        type: 'string',
                        format: 'uri',
                      },
                      primary_color: {
                        type: 'string',
                      },
                      secondary_color: {
                        type: 'string',
                      },
                      status: {
                        type: 'boolean',
                      },
                      subdomain: {
                        type: 'string',
                      },
                      website: {
                        type: 'string',
                        format: 'uri',
                      },
                      external_customer_id: {
                        type: 'string',
                      },
                    },
                  },
                },
              },
              example: {
                message: 'Cliente atualizado com sucesso',
                customer: {
                  id: '123e4567-e89b-12d3-a456-426614174000',
                  name: 'Empresa Exemplo LTDA',
                  email: '<EMAIL>',
                  tax_number: '12345678000190',
                  logo_url: 'https://exemplo.com/logo.png',
                  primary_color: '#FF0000',
                  secondary_color: '#00FF00',
                  status: true,
                  subdomain: 'exemplo',
                  website: 'https://exemplo.com.br',
                  external_customer_id: 'EXT123',
                },
              },
            },
          },
        },
        400: {
          description: 'Dados inválidos',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: {
                    type: 'string',
                  },
                  errors: {
                    type: 'array',
                    items: {
                      type: 'string',
                    },
                  },
                },
              },
              example: {
                message: 'Erro de validação',
                errors: [
                  'O campo primaryColor deve estar no formato hexadecimal (ex: #000000)',
                  'O campo email deve ser um email válido',
                ],
              },
            },
          },
        },
        404: {
          description: 'Cliente não encontrado',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: {
                    type: 'string',
                  },
                },
              },
              example: {
                message: 'Cliente não encontrado',
              },
            },
          },
        },
      },
    },
  },
};
