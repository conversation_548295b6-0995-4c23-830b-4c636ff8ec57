export const externalExamsPaths = {
  '/v2/external/exams': {
    get: {
      tags: ['External Exams'],
      summary: 'Listar provas',
      description: `Endpoint para listar provas de um cliente específico.

Fluxo da operação:
1. Valida o token de API fornecido no header
2. Valida os parâmetros de entrada
3. Verifica se o cliente existe
4. Retorna a lista de provas

Observações importantes:
- O token de API deve ser fornecido no header 'x-api-key'
- O customerId é obrigatório como query parameter
- É possível filtrar por termo de busca usando o parâmetro 'search'
- É possível ordenar os resultados usando 'orderByColumn' e 'orderDirection'`,
      security: [{ ApiToken: [] }],
      parameters: [
        {
          in: 'header',
          name: 'x-api-key',
          required: true,
          schema: {
            type: 'string',
          },
          description: 'Token de autenticação para sistemas externos',
        },
        {
          in: 'query',
          name: 'customerId',
          required: true,
          schema: {
            type: 'string',
            format: 'uuid',
          },
          description: 'ID do cliente',
          example: '550e8400-e29b-41d4-a716-************',
        },
        {
          in: 'query',
          name: 'search',
          required: false,
          schema: {
            type: 'string',
          },
          description: 'Termo para busca por nome da prova',
          example: 'Matemática',
        },
        {
          in: 'query',
          name: 'orderByColumn',
          required: false,
          schema: {
            type: 'string',
            enum: ['name', 'createdAt'],
          },
          description: 'Coluna para ordenação dos resultados',
          example: 'name',
        },
        {
          in: 'query',
          name: 'orderDirection',
          required: false,
          schema: {
            type: 'string',
            enum: ['ASC', 'DESC'],
          },
          description: 'Direção da ordenação (ascendente ou descendente)',
          example: 'ASC',
        },
      ],
      responses: {
        200: {
          description: 'Provas listadas com sucesso',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: {
                    type: 'string',
                    example: 'Provas listadas com sucesso',
                  },
                  exams: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        id: {
                          type: 'string',
                          format: 'uuid',
                          example: '550e8400-e29b-41d4-a716-************',
                        },
                        name: {
                          type: 'string',
                          example: 'Prova de Matemática 2024',
                        },
                        description: {
                          type: 'string',
                          example: 'Prova de matemática para o ensino médio',
                        },
                        createdAt: {
                          type: 'string',
                          format: 'date-time',
                          example: '2023-01-01T12:00:00Z',
                        },
                        updatedAt: {
                          type: 'string',
                          format: 'date-time',
                          example: '2023-01-01T12:00:00Z',
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        400: {
          description: 'Dados inválidos na requisição',
          content: {
            'application/json': {
              examples: {
                'Campo obrigatório': {
                  value: {
                    message: 'Erro na requisição',
                    error: 'ZodError',
                    issue: [
                      {
                        code: 'invalid_type',
                        expected: 'string',
                        received: 'undefined',
                        path: ['customerId'],
                        message: 'O campo customerId é obrigatório',
                      },
                    ],
                  },
                },
              },
            },
          },
        },
        401: {
          description: 'Token de API inválido ou não fornecido',
          content: {
            'application/json': {
              example: {
                error: 'UnauthorizedError',
                message: 'Token de API inválido ou não fornecido',
              },
            },
          },
        },
        404: {
          description: 'Cliente não encontrado',
          content: {
            'application/json': {
              example: {
                error: 'ResourceNotFoundError',
                message: 'Cliente não encontrado',
              },
            },
          },
        },
        500: {
          description: 'Erro interno do servidor',
          content: {
            'application/json': {
              example: {
                error: 'InternalServerError',
                message: 'Ocorreu um erro interno no servidor',
              },
            },
          },
        },
      },
    },
  },
};
