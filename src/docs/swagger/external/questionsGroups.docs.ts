export const externalQuestionsGroupsPaths = {
  '/v2/external/questions-groups': {
    get: {
      tags: ['Questions Groups'],
      summary: 'Listar grupos de questões',
      description: `Endpoint para listar grupos de questões.

Fluxo da operação:
1. Valida o token de API fornecido no header
2. Valida os parâmetros de entrada
3. Verifica se o cliente existe
4. Retorna a lista de grupos de questões com paginação

Observações importantes:
- O token de API deve ser fornecido no header 'x-api-key'
- O customerId é obrigatório como query parameter
- Suporta busca por nome do grupo
- Suporta ordenação por diferentes campos`,
      security: [{ ApiToken: [] }],
      parameters: [
        {
          in: 'header',
          name: 'x-api-key',
          required: true,
          schema: {
            type: 'string',
          },
          description: 'Token de autenticação para sistemas externos',
        },
        {
          in: 'query',
          name: 'customerId',
          required: true,
          schema: {
            oneOf: [{ type: 'string', format: 'uuid' }, { type: 'integer' }],
          },
          description: 'ID do cliente (UUID ou integer)',
        },
        {
          in: 'query',
          name: 'search',
          required: false,
          schema: {
            type: 'string',
          },
          description: 'Termo para busca por nome do grupo',
        },
        {
          in: 'query',
          name: 'orderByColumn',
          required: false,
          schema: {
            type: 'string',
            enum: ['id', 'name', 'created_at', 'updated_at', 'deleted_at', 'customer_id'],
          },
          description: 'Campo para ordenação',
        },
        {
          in: 'query',
          name: 'orderDirection',
          required: false,
          schema: {
            type: 'string',
            enum: ['asc', 'desc'],
          },
          description: 'Direção da ordenação',
        },
      ],
      responses: {
        200: {
          description: 'Lista de grupos de questões obtida com sucesso',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: {
                    type: 'string',
                    example: 'Grupos de questões listados com sucesso',
                  },
                  totalItems: {
                    type: 'number',
                    example: 1,
                  },
                  questionsGroups: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        id: {
                          type: 'string',
                          format: 'uuid',
                          example: 'eb090c8d-8eb5-4071-a295-bcdd6bcb0866',
                        },
                        created_at: {
                          type: 'string',
                          format: 'date-time',
                          example: '2025-04-15T11:57:59.720Z',
                        },
                        updated_at: {
                          type: 'string',
                          format: 'date-time',
                          example: '2025-04-15T11:57:59.720Z',
                        },
                        deleted_at: {
                          type: 'string',
                          format: 'date-time',
                          nullable: true,
                          example: null,
                        },
                        name: {
                          type: 'string',
                          example: 'group test',
                        },
                        customer_id: {
                          type: 'string',
                          format: 'uuid',
                          example: '32f94d30-6b92-46c7-85ec-30727c657355',
                        },
                      },
                    },
                  },
                },
                example: {
                  message: 'Grupos de questões listados com sucesso',
                  totalItems: 1,
                  questionsGroups: [
                    {
                      id: 'eb090c8d-8eb5-4071-a295-bcdd6bcb0866',
                      created_at: '2025-04-15T11:57:59.720Z',
                      updated_at: '2025-04-15T11:57:59.720Z',
                      deleted_at: null,
                      name: 'group test',
                      customer_id: '32f94d30-6b92-46c7-85ec-30727c657355',
                    },
                  ],
                },
              },
            },
          },
        },
        400: {
          description: 'Erro de validação dos parâmetros',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: {
                    type: 'string',
                    example: 'Erro na requisição',
                  },
                  error: {
                    type: 'string',
                    example: 'ZodError',
                  },
                  issue: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        code: {
                          type: 'string',
                          example: 'invalid_type',
                        },
                        expected: {
                          type: 'string',
                          example: 'string',
                        },
                        received: {
                          type: 'string',
                          example: 'undefined',
                        },
                        path: {
                          type: 'array',
                          items: {
                            type: 'string',
                          },
                          example: ['customerId'],
                        },
                        message: {
                          type: 'string',
                          example: 'O campo customerId é obrigatório',
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        404: {
          description: 'Cliente não encontrado',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: {
                    type: 'string',
                    example: 'Cliente não encontrado',
                  },
                  error: {
                    type: 'string',
                    example: 'ResourceNotFoundError',
                  },
                },
              },
            },
          },
        },
      },
    },
  },
  'v2/external/questions-groups/{id}': {
    get: {
      tags: ['Questions Groups'],
      summary: 'Obter grupo de questões por ID',
      description: `Endpoint para obter um grupo de questões específico.

Fluxo da operação:
1. Valida o token de API fornecido no header
2. Valida os parâmetros de entrada (id e customerId)
3. Verifica se o cliente existe
4. Busca o grupo de questões
5. Retorna o grupo com o total de questões publicadas

Observações importantes:
- O token de API deve ser fornecido no header 'x-api-key'
- O customerId é obrigatório como query parameter
- Retorna erro 404 se o grupo não for encontrado
- O total de questões considera apenas questões publicadas`,
      security: [{ ApiToken: [] }],
      parameters: [
        {
          in: 'header',
          name: 'x-api-key',
          required: true,
          schema: {
            type: 'string',
          },
          description: 'Token de autenticação para sistemas externos',
        },
        {
          in: 'path',
          name: 'id',
          required: true,
          schema: {
            type: 'string',
            format: 'uuid',
          },
          description: 'ID do grupo de questões',
        },
        {
          in: 'query',
          name: 'customerId',
          required: true,
          schema: {
            oneOf: [{ type: 'string', format: 'uuid' }, { type: 'integer' }],
          },
          description: 'ID do cliente (UUID ou integer)',
        },
      ],
      responses: {
        200: {
          description: 'Grupo de questões obtido com sucesso',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: {
                    type: 'string',
                    example: 'Grupo de questões obtido com sucesso',
                  },
                  questionGroup: {
                    type: 'object',
                    properties: {
                      id: {
                        type: 'string',
                        format: 'uuid',
                        example: 'eb090c8d-8eb5-4071-a295-bcdd6bcb0866',
                      },
                      name: {
                        type: 'string',
                        example: 'group test',
                      },
                      customer_id: {
                        type: 'string',
                        format: 'uuid',
                        example: '32f94d30-6b92-46c7-85ec-30727c657355',
                      },
                      totalPublishedQuestions: {
                        type: 'number',
                        example: 5,
                      },
                      created_at: {
                        type: 'string',
                        format: 'date-time',
                        example: '2025-04-15T11:57:59.720Z',
                      },
                      updated_at: {
                        type: 'string',
                        format: 'date-time',
                        example: '2025-04-15T11:57:59.720Z',
                      },
                      deleted_at: {
                        type: 'string',
                        format: 'date-time',
                        nullable: true,
                        example: null,
                      },
                    },
                  },
                },
              },
            },
          },
        },
        400: {
          description: 'Erro de validação dos parâmetros',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: {
                    type: 'string',
                    example: 'Erro na requisição',
                  },
                  error: {
                    type: 'string',
                    example: 'ZodError',
                  },
                  issue: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        code: {
                          type: 'string',
                          example: 'invalid_type',
                        },
                        expected: {
                          type: 'string',
                          example: 'string',
                        },
                        received: {
                          type: 'string',
                          example: 'undefined',
                        },
                        path: {
                          type: 'array',
                          items: {
                            type: 'string',
                          },
                          example: ['customerId'],
                        },
                        message: {
                          type: 'string',
                          example: 'O campo customerId é obrigatório',
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        404: {
          description: 'Recurso não encontrado',
          content: {
            'application/json': {
              examples: {
                'Grupo não encontrado': {
                  value: {
                    message: 'Grupo de questões não encontrado',
                    error: 'ResourceNotFoundError',
                  },
                },
                'Cliente não encontrado': {
                  value: {
                    message: 'Cliente não encontrado',
                    error: 'ResourceNotFoundError',
                  },
                },
              },
            },
          },
        },
      },
    },
  },
};
