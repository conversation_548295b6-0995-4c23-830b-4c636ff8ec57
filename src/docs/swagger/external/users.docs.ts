export const externalUsersPaths = {
  '/v2/external/users': {
    post: {
      tags: ['External Users'],
      summary: 'Criar um novo usuário externo',
      description: `Endpoint para criar um novo usuário no sistema.

Fluxo da operação:
1. Valida o token de API fornecido no header
2. Valida os dados de entrada
3. Verifica se o cliente existe
4. Verifica se já existe um usuário com o mesmo email para o cliente
5. Se informado classId, valida se a turma existe
6. Cria o usuário e, se necessário, associa à turma informada

Observações importantes:
- O token de API deve ser fornecido no header 'x-api-key'
- O id deve ser único para cada usuário do cliente (externalUserId)
- O email deve ser único por cliente
- O role deve ser um dos valores:  'SYSTEM_ADMIN', 'SUPER_ADMIN', 'ADMIN', 'COORDINATOR', 'CREATOR' ou'STUDENT',
- O classId é opcional, mas se informado deve ser válido`,
      security: [{ apiToken: [] }],
      parameters: [
        {
          in: 'header',
          name: 'x-api-key',
          required: true,
          schema: {
            type: 'string',
          },
          description: 'Token de autenticação para sistemas externos',
        },
      ],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              required: ['firstName', 'lastName', 'email', 'id', 'customerId', 'role'],
              properties: {
                firstName: {
                  type: 'string',
                  description: 'Nome do usuário',
                },
                lastName: {
                  type: 'string',
                  description: 'Sobrenome do usuário',
                },
                email: {
                  type: 'string',
                  format: 'email',
                  description: 'Email do usuário (único por cliente)',
                },
                id: {
                  type: 'string',
                  description: 'ID externo único do usuário',
                },
                customerId: {
                  type: 'string',
                  format: 'uuid',
                  description: 'ID do cliente',
                },
                classId: {
                  type: 'string',
                  format: 'uuid',
                  description: 'ID da turma (opcional)',
                },
                role: {
                  type: 'string',
                  enum: [
                    'SYSTEM_ADMIN',
                    'SUPER_ADMIN',
                    'ADMIN',
                    'COORDINATOR',
                    'CREATOR',
                    'STUDENT',
                  ],
                  description: 'Papel do usuário',
                },
                status: {
                  type: 'string',
                  enum: ['ACTIVE', 'INACTIVE'],
                  description: 'Status do usuário (opcional)',
                  default: 'ACTIVE',
                },
              },
            },
            example: {
              firstName: 'João',
              lastName: 'Silva',
              email: '<EMAIL>',
              id: 'USER123',
              customerId: '550e8400-e29b-41d4-a716-************',
              classId: '550e8400-e29b-41d4-a716-************',
              role: 'STUDENT',
              status: 'ACTIVE',
            },
          },
        },
      },
      responses: {
        201: {
          description: 'Usuário criado com sucesso',
          content: {
            'application/json': {
              example: {
                id: '550e8400-e29b-41d4-a716-************',
                firstName: 'João',
                lastName: 'Silva',
                email: '<EMAIL>',
                customerId: '550e8400-e29b-41d4-a716-************',
                role: 'STUDENT',
                active: true,
                createdAt: '2023-12-20T10:00:00.000Z',
                updatedAt: '2023-12-20T10:00:00.000Z',
              },
            },
          },
        },
        400: {
          description: 'Dados inválidos na requisição',
          content: {
            'application/json': {
              examples: {
                'Campos obrigatórios': {
                  value: {
                    error: 'ValidationError',
                    details: ['firstName é obrigatório', 'email deve ser um email válido'],
                  },
                },
                'Email duplicado': {
                  value: {
                    error: 'ResourceAlreadyExistsError',
                    message: 'Já existe um usuário com este email para este cliente',
                  },
                },
              },
            },
          },
        },
        401: {
          description: 'Token de API inválido',
          content: {
            'application/json': {
              example: {
                error: 'UnauthorizedError',
                message: 'Token de API inválido ou não fornecido',
              },
            },
          },
        },
        404: {
          description: 'Recurso não encontrado',
          content: {
            'application/json': {
              examples: {
                'Cliente não encontrado': {
                  value: {
                    error: 'ResourceNotFoundError',
                    message: 'Cliente não encontrado',
                  },
                },
                'Turma não encontrada': {
                  value: {
                    error: 'ResourceNotFoundError',
                    message: 'Turma não encontrada',
                  },
                },
              },
            },
          },
        },
      },
    },
  },
  '/v2/external/users/{id}': {
    put: {
      tags: ['External Users'],
      summary: 'Atualizar um usuário externo',
      description: `Endpoint para atualizar os dados de um usuário existente.

Fluxo da operação:
1. Valida o token de API fornecido no header
2. Valida os dados de entrada
3. Verifica se o cliente existe
4. Localiza o usuário pelo ID externo
5. Atualiza apenas os campos fornecidos

Observações importantes:
- O token de API deve ser fornecido no header 'x-api-key'
- O customerId é obrigatório
- Apenas os campos enviados serão atualizados
- O ID na URL deve corresponder ao ID externo do usuário`,
      security: [{ apiToken: [] }],
      parameters: [
        {
          in: 'header',
          name: 'x-api-key',
          required: true,
          schema: {
            type: 'string',
          },
          description: 'Token de autenticação para sistemas externos',
        },
        {
          in: 'path',
          name: 'id',
          required: true,
          schema: {
            type: 'string',
          },
          description: 'ID externo do usuário',
          example: '24235',
        },
      ],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              required: ['customerId'],
              properties: {
                firstName: {
                  type: 'string',
                  description: 'Nome do usuário',
                },
                lastName: {
                  type: 'string',
                  description: 'Sobrenome do usuário',
                },
                email: {
                  type: 'string',
                  format: 'email',
                  description: 'Email do usuário',
                },
                customerId: {
                  type: 'string',
                  description: 'ID externo do cliente',
                },
                phoneNumber: {
                  type: 'string',
                  description: 'Número de telefone',
                },
                gender: {
                  type: 'string',
                  description: 'Gênero do usuário',
                },
                birthDate: {
                  type: 'string',
                  description: 'Data de nascimento',
                },
                cpf: {
                  type: 'string',
                  description: 'CPF do usuário',
                },
                role: {
                  type: 'string',
                  enum: [
                    'SYSTEM_ADMIN',
                    'SUPER_ADMIN',
                    'ADMIN',
                    'COORDINATOR',
                    'CREATOR',
                    'STUDENT',
                  ],
                  description: 'Papel do usuário no sistema',
                },
                password: {
                  type: 'string',
                  description: 'Nova senha do usuário',
                },
              },
            },
            example: {
              firstName: 'João',
              lastName: 'Silva Santos',
              email: '<EMAIL>',
              customerId: 'CUSTOMER123',
              phoneNumber: '11999999999',
              gender: 'M',
              birthDate: '1990-01-01',
              cpf: '12345678900',
              role: 'STUDENT',
            },
          },
        },
      },
      responses: {
        200: {
          description: 'Usuário atualizado com sucesso',
          content: {
            'application/json': {
              example: {
                message: 'Usuário atualizado com sucesso',
                user: {
                  id: '550e8400-e29b-41d4-a716-************',
                  firstName: 'João',
                  lastName: 'Silva Santos',
                  email: '<EMAIL>',
                  customerId: '550e8400-e29b-41d4-a716-************',
                  phoneNumber: '11999999999',
                  gender: 'M',
                  birthDate: '1990-01-01',
                  cpf: '12345678900',
                  role: 'STUDENT',
                  active: true,
                  createdAt: '2023-12-20T10:00:00.000Z',
                  updatedAt: '2023-12-20T10:00:00.000Z',
                },
              },
            },
          },
        },
        400: {
          description: 'Dados inválidos na requisição',
          content: {
            'application/json': {
              examples: {
                'Campos inválidos': {
                  value: {
                    error: 'ValidationError',
                    details: ['email deve ser um email válido'],
                  },
                },
                'Cliente não informado': {
                  value: {
                    error: 'ValidationError',
                    message: 'customerId é obrigatório',
                  },
                },
              },
            },
          },
        },
        401: {
          description: 'Token de API inválido',
          content: {
            'application/json': {
              example: {
                error: 'UnauthorizedError',
                message: 'Token de API inválido ou não fornecido',
              },
            },
          },
        },
        404: {
          description: 'Recurso não encontrado',
          content: {
            'application/json': {
              examples: {
                'Cliente não encontrado': {
                  value: {
                    error: 'ResourceNotFoundError',
                    message: 'Cliente não encontrado',
                  },
                },
                'Usuário não encontrado': {
                  value: {
                    error: 'ResourceNotFoundError',
                    message: 'Usuário não encontrado',
                  },
                },
              },
            },
          },
        },
      },
    },
    delete: {
      tags: ['External Users'],
      summary: 'Excluir um usuário externo',
      description: `Endpoint para excluir um usuário existente.

Fluxo da operação:
1. Valida o token de API fornecido no header
2. Valida os dados de entrada
3. Verifica se o cliente existe
4. Localiza o usuário pelo ID externo
5. Remove os acessos do usuário
6. Realiza a exclusão lógica do usuário

Observações importantes:
- O token de API deve ser fornecido no header 'x-api-key'
- O customerId é obrigatório como query parameter
- A exclusão é lógica (soft delete)
- O id na URL deve ser o external_user_id do usuário`,
      security: [{ apiToken: [] }],
      parameters: [
        {
          in: 'header',
          name: 'x-api-key',
          required: true,
          schema: {
            type: 'string',
          },
          description: 'Token de autenticação para sistemas externos',
        },
        {
          in: 'path',
          name: 'id',
          required: true,
          schema: {
            type: 'string',
          },
          description: 'ID externo do usuário',
          example: 'USER123',
        },
        {
          in: 'query',
          name: 'customerId',
          required: true,
          schema: {
            type: 'string',
            format: 'uuid',
          },
          description: 'ID do cliente',
          example: '550e8400-e29b-41d4-a716-************',
        },
      ],
      responses: {
        200: {
          description: 'Usuário excluído com sucesso',
          content: {
            'application/json': {
              example: {
                message: 'Usuário excluído com sucesso',
                deletedUser: {
                  id: '550e8400-e29b-41d4-a716-************',
                  firstName: 'João',
                  lastName: 'Silva',
                  email: '<EMAIL>',
                  customerId: '550e8400-e29b-41d4-a716-************',
                  role: 'STUDENT',
                  active: false,
                  createdAt: '2023-12-20T10:00:00.000Z',
                  updatedAt: '2023-12-20T15:00:00.000Z',
                  deletedAt: '2023-12-20T15:00:00.000Z',
                },
              },
            },
          },
        },
        400: {
          description: 'Dados inválidos na requisição',
          content: {
            'application/json': {
              examples: {
                'CustomerId não informado': {
                  value: {
                    error: 'ValidationError',
                    message: 'customerId é obrigatório',
                  },
                },
                'ID inválido': {
                  value: {
                    error: 'ValidationError',
                    message: 'ID do usuário inválido',
                  },
                },
              },
            },
          },
        },
        401: {
          description: 'Token de API inválido',
          content: {
            'application/json': {
              example: {
                error: 'UnauthorizedError',
                message: 'Token de API inválido ou não fornecido',
              },
            },
          },
        },
        404: {
          description: 'Recurso não encontrado',
          content: {
            'application/json': {
              examples: {
                'Cliente não encontrado': {
                  value: {
                    error: 'ResourceNotFoundError',
                    message: 'Cliente não encontrado',
                  },
                },
                'Usuário não encontrado': {
                  value: {
                    error: 'ResourceNotFoundError',
                    message: 'Usuário não encontrado',
                  },
                },
              },
            },
          },
        },
        500: {
          description: 'Erro interno do servidor',
          content: {
            'application/json': {
              example: {
                error: 'GenericError',
                message: 'Erro ao excluir usuário',
              },
            },
          },
        },
      },
    },
  },
};
