export const CategoryDocSchema = {
  createCategorySchema: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        description: 'Category name',
      },
    },
    required: ['name'],
  },
  categoryResponseSchema: {
    type: 'object',
    properties: {
      id: {
        type: 'string',
        example: 'category-id',
      },
      name: {
        type: 'string',
        example: 'Category name',
      },
      customer_id: {
        type: 'string',
        example: 'customer-id',
      },
      created_at: {
        type: 'string',
        example: '2024-01-24T10:00:00.000Z',
      },
      updated_at: {
        type: 'string',
        example: '2024-01-24T10:00:00.000Z',
      },
    },
  },
  categoryUpdateResponseSchema: {
    type: 'object',
    properties: {
      message: {
        type: 'string',
        example: 'Categoria atualizada com sucesso!',
      },
    },
  },
  categoryDeleteResponseSchema: {
    type: 'object',
    properties: {
      message: {
        type: 'string',
        example: 'Categoria excluida com sucesso',
      },
    },
  },
};
