export const ClassDocSchema = {
  createClassRequestBodySchema: {
    type: 'object',
    required: ['name', 'customerId', 'id'],
    properties: {
      name: {
        type: 'string',
        description: 'Nome da turma',
      },
      description: {
        type: 'string',
        description: 'Descrição da turma',
      },
      customerId: {
        type: 'string',
        format: 'uuid',
        description: 'ID do cliente',
      },
      status: {
        type: 'boolean',
        description: 'Status da turma',
      },
      startDate: {
        type: 'string',
        format: 'date',
        description: 'Data de início',
      },
      endDate: {
        type: 'string',
        format: 'date',
        description: 'Data de término',
      },
      id: {
        type: 'string',
        description: 'ID externo da turma',
      },
    },
  },

  updateClassRequestBodySchema: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        description: 'Nome da turma',
      },
      description: {
        type: 'string',
        description: 'Descrição da turma',
      },
      status: {
        type: 'boolean',
        description: 'Status da turma',
      },
      startDate: {
        type: 'string',
        format: 'date',
        description: 'Data de início',
      },
      endDate: {
        type: 'string',
        format: 'date',
        description: 'Data de término',
      },
    },
  },

  classResponseSchema: {
    type: 'object',
    properties: {
      id: {
        type: 'string',
        format: 'uuid',
        description: 'ID da turma',
      },
      name: {
        type: 'string',
        description: 'Nome da turma',
      },
      description: {
        type: 'string',
        description: 'Descrição da turma',
      },
      customerId: {
        type: 'string',
        format: 'uuid',
        description: 'ID do cliente',
      },
      status: {
        type: 'boolean',
        description: 'Status da turma',
      },
      startDate: {
        type: 'string',
        format: 'date',
        description: 'Data de início',
      },
      endDate: {
        type: 'string',
        format: 'date',
        description: 'Data de término',
      },
      externalClassId: {
        type: 'string',
        description: 'ID externo da turma',
      },
      createdAt: {
        type: 'string',
        format: 'date-time',
        description: 'Data de criação',
      },
      updatedAt: {
        type: 'string',
        format: 'date-time',
        description: 'Data de atualização',
      },
      deletedAt: {
        type: 'string',
        format: 'date-time',
        description: 'Data de exclusão',
        nullable: true,
      },
    },
  },
};
