export class UserDocSchema {
  static createUserSchema = {
    type: 'object',
    properties: {
      first_name: {
        type: 'string',
        description: 'Nome do usuário',
      },
      last_name: {
        type: 'string',
        description: 'Sobrenome do usuário',
      },
      email: {
        type: 'string',
        description: 'Email do usuário',
      },
      password: {
        type: 'string',
        description: 'Senha do usuário',
      },
      cpf: {
        type: 'string',
        description: 'Senha do usuário',
      },
    },
  };
}
