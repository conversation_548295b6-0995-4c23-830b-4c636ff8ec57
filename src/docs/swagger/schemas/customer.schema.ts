export const CustomerDocSchema = {
  createCustomerRequestBodySchema: {
    type: 'object',
    required: ['customerName'],
    properties: {
      customerName: {
        type: 'string',
        description: 'Nome do cliente',
      },
      email: {
        type: 'string',
        format: 'email',
        description: 'Email do cliente',
      },
      taxNumber: {
        type: 'string',
        description: 'CNPJ do cliente',
      },
      logoUrl: {
        type: 'string',
        format: 'uri',
        description: 'URL do logo do cliente',
      },
      primaryColor: {
        type: 'string',
        pattern: '^#(?:[0-9a-fA-F]{3}|[0-9a-fA-F]{6})$',
        description: 'Cor primária em formato hexadecimal (ex: #000000)',
      },
      secondaryColor: {
        type: 'string',
        pattern: '^#(?:[0-9a-fA-F]{3}|[0-9a-fA-F]{6})$',
        description: 'Cor secundária em formato hexadecimal (ex: #000000)',
      },
      status: {
        type: 'boolean',
        description: 'Status do cliente',
      },
      subdomain: {
        type: 'string',
        description: 'Subdomínio do cliente',
      },
      websiteUrl: {
        type: 'string',
        format: 'uri',
        description: 'URL do website do cliente',
      },
      id: {
        type: 'string',
        description: 'ID externo do cliente',
      },
      supportEmail: {
        type: 'string',
        format: 'email',
        description: 'Email de suporte do cliente (se não fornecido, será usado o email principal)',
      },
    },
  },
  customerResponseSchema: {
    type: 'object',
    properties: {
      message: {
        type: 'string',
        example: 'Cliente criado com sucesso',
      },
      data: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'Customer ID',
          },
          customer_name: {
            type: 'string',
            description: 'Nome do cliente',
          },
          email: {
            type: 'string',
            description: 'Email do cliente',
          },
          tax_number: {
            type: 'string',
            description: 'CNPJ do cliente',
          },
          logo_url: {
            type: 'string',
            description: 'URL do logo do cliente',
          },
          primary_color: {
            type: 'string',
            description: 'Cor primária do cliente',
          },
          secondary_color: {
            type: 'string',
            description: 'Cor secundária do cliente',
          },
          status: {
            type: 'boolean',
            description: 'Status do cliente',
          },
          subdomain: {
            type: 'string',
            description: 'Subdomínio do cliente',
          },
          website_url: {
            type: 'string',
            description: 'URL do website do cliente',
          },
          external_customer_id: {
            type: 'string',
            description: 'ID externo do cliente',
          },
          support_email: {
            type: 'string',
            description: 'Email de suporte do cliente',
          },
        },
      },
    },
  },
};
