export const DisciplineDocSchema = {
  createDisciplineSchema: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        description: 'Discipline name',
      },
      customerId: {
        type: 'string',
        description: 'Customer ID',
      },
    },
    required: ['name', 'customerId'],
  },
  disciplineSchema: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        example: 'Math',
      },
      code: {
        type: 'string',
        example: 'MTH123',
      },
      customer_id: {
        type: 'string',
        example: 'customer-id',
      },
    },
  },
};
