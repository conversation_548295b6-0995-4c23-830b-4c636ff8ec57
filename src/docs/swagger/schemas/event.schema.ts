export const EventDocSchema = {
  createSubdomainRequestBodySchema: {
    type: 'object',
    properties: {
      subdomain: {
        type: 'string',
        description: 'Subdomain name',
      },
    },
    required: ['subdomain'],
  },
  eventResponseSchema: {
    type: 'object',
    properties: {
      message: {
        type: 'string',
        example: 'Subdomain created successfully',
      },
    },
  },
};
