export const SimulatedDocSchema = {
  getSimulatedByIdResponseSchema: {
    type: 'object',
    properties: {
      message: {
        type: 'string',
        example: 'Simulado retornado com sucesso',
      },
      simulatedQuestions: {
        type: 'array',
        items: {
          type: 'object',
        },
      },
    },
  },
  simulatedSchema: {
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      description: { type: 'string' },
      created_at: { type: 'string', format: 'date-time' },
      updated_at: { type: 'string', format: 'date-time' },
    },
  },
};
