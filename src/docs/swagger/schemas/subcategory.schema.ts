export const SubcategoryDocSchema = {
  addSubcategoryRequestBodySchema: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        description: 'Subcategory name',
      },
    },
    required: ['name'],
  },
  subcategoryResponseSchema: {
    type: 'object',
    properties: {
      id: {
        type: 'string',
        example: 'subcategory-id',
      },
      name: {
        type: 'string',
        example: 'Subcategory name',
      },
      category_id: {
        type: 'string',
        example: 'category-id',
      },
      created_at: {
        type: 'string',
        example: '2024-01-24T10:00:00.000Z',
      },
      updated_at: {
        type: 'string',
        example: '2024-01-24T10:00:00.000Z',
      },
    },
  },
  subcategoryUpdateResponseSchema: {
    type: 'object',
    properties: {
      message: {
        type: 'string',
        example: 'Subcategoria atualizada com sucesso!',
      },
    },
  },
  subcategoryDeleteResponseSchema: {
    type: 'object',
    properties: {
      message: {
        type: 'string',
        example: 'Subcategoria excluida com sucesso',
      },
    },
  },
};
