export const UserDocSchema = {
  createUserRequestBodySchema: {
    type: 'object',
    required: [
      'firstName',
      'lastName',
      'cpf',
      'email',
      'password',
      'customerId',
      'classId',
      'externalUserId',
      'status',
    ],
    properties: {
      firstName: {
        type: 'string',
        description: 'Nome do usuário',
      },
      lastName: {
        type: 'string',
        description: 'Sobrenome do usuário',
      },
      cpf: {
        type: 'string',
        description: 'CPF do usuário',
      },
      email: {
        type: 'string',
        description: 'Email do usuário',
      },
      password: {
        type: 'string',
        description: 'Senha do usuário',
      },
      customerId: {
        type: 'string',
        format: 'uuid',
        description: 'ID do cliente',
      },
      classId: {
        type: 'string',
        format: 'uuid',
        description: 'ID da turma',
      },
      externalUserId: {
        type: 'string',
        description: 'ID externo do usuário',
      },
      status: {
        type: 'boolean',
        description: 'Status do usuário',
      },
    },
  },

  loginRequestBodySchema: {
    type: 'object',
    required: ['email', 'password'],
    properties: {
      email: {
        type: 'string',
        description: 'Email do usuário',
      },
      password: {
        type: 'string',
        description: 'Senha do usuário',
      },
    },
  },

  loginResponseSchema: {
    type: 'object',
    properties: {
      user: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            format: 'uuid',
            description: 'ID do usuário',
          },
          firstName: {
            type: 'string',
            description: 'Nome do usuário',
          },
          lastName: {
            type: 'string',
            description: 'Sobrenome do usuário',
          },
          email: {
            type: 'string',
            description: 'Email do usuário',
          },
        },
      },
      token: {
        type: 'string',
        description: 'Token JWT de autenticação',
      },
    },
  },

  updateUserRequestBodySchema: {
    type: 'object',
    properties: {
      firstName: {
        type: 'string',
        description: 'Nome do usuário',
      },
      lastName: {
        type: 'string',
        description: 'Sobrenome do usuário',
      },
      email: {
        type: 'string',
        description: 'Email do usuário',
      },
      status: {
        type: 'boolean',
        description: 'Status do usuário',
      },
    },
  },

  userResponseSchema: {
    type: 'object',
    properties: {
      id: {
        type: 'string',
        format: 'uuid',
        description: 'ID do usuário',
      },
      firstName: {
        type: 'string',
        description: 'Nome do usuário',
      },
      lastName: {
        type: 'string',
        description: 'Sobrenome do usuário',
      },
      cpf: {
        type: 'string',
        description: 'CPF do usuário',
      },
      email: {
        type: 'string',
        description: 'Email do usuário',
      },
      customerId: {
        type: 'string',
        format: 'uuid',
        description: 'ID do cliente',
      },
      classId: {
        type: 'string',
        format: 'uuid',
        description: 'ID da turma',
      },
      externalUserId: {
        type: 'string',
        description: 'ID externo do usuário',
      },
      status: {
        type: 'boolean',
        description: 'Status do usuário',
      },
      createdAt: {
        type: 'string',
        format: 'date-time',
        description: 'Data de criação',
      },
      updatedAt: {
        type: 'string',
        format: 'date-time',
        description: 'Data de atualização',
      },
      deletedAt: {
        type: 'string',
        format: 'date-time',
        description: 'Data de exclusão',
        nullable: true,
      },
    },
  },
};
