import { SimulatedDocSchema } from './schemas/simulated.schema';
import { tag } from './tag';

export const simulatedsPaths = {
  '/v2/simulateds/{simulatedId}': {
    get: {
      tags: [tag.simulateds.name],
      summary: 'Obter simulado por ID',
      description: 'Endpoint para obter um simulado específico por ID.',
      security: [{ bearerAuth: [] }],
      parameters: [
        {
          in: 'path',
          name: 'simulatedId',
          required: true,
          schema: {
            type: 'string',
          },
        },
      ],
      responses: {
        200: {
          description: 'Simulado retornado com sucesso.',
          content: {
            'application/json': {
              schema: SimulatedDocSchema.getSimulatedByIdResponseSchema,
            },
          },
        },
        404: {
          description: 'Simulado não encontrado',
        },
        500: {
          description: 'Internal server error',
        },
      },
    },
  },
};
