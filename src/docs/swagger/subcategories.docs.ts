import { SubcategoryDocSchema } from './schemas/subcategory.schema';
import { tag } from './tag';

export const subcategoriesPaths = {
  '/v2/subcategories/{id}': {
    put: {
      tags: [tag.subcategories.name],
      summary: 'Atualizar uma subcategoria existente',
      description: 'Endpoint para atualizar uma subcategoria existente.',
      security: [{ bearerAuth: [] }],
      parameters: [
        {
          in: 'path',
          name: 'id',
          required: true,
          schema: {
            type: 'string',
          },
        },
      ],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: SubcategoryDocSchema.addSubcategoryRequestBodySchema,
          },
        },
      },
      responses: {
        200: {
          description: 'Subcategoria atualizada com sucesso',
          content: {
            'application/json': {
              schema: SubcategoryDocSchema.subcategoryUpdateResponseSchema,
            },
          },
        },
        400: {
          description: 'Requisição inválida',
        },
        404: {
          description: 'Subcategoria não encontrada',
        },
        500: {
          description: 'Erro interno do servidor',
        },
      },
    },
  },
  '/v2/subcategories/{categoryId}': {
    post: {
      tags: [tag.subcategories.name],
      summary: 'Criar uma nova subcategoria para uma categoria específica',
      description: 'Endpoint para criar uma nova subcategoria em uma categoria.',
      security: [{ bearerAuth: [] }],
      parameters: [
        {
          in: 'path',
          name: 'categoryId',
          required: true,
          schema: {
            type: 'string',
          },
        },
      ],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: SubcategoryDocSchema.addSubcategoryRequestBodySchema,
          },
        },
      },
      responses: {
        201: {
          description: 'Subcategoria criada com sucesso.',
          content: {
            'application/json': {
              schema: SubcategoryDocSchema.subcategoryResponseSchema,
            },
          },
        },
        400: {
          description: 'Bad request',
        },
        500: {
          description: 'Internal server error',
        },
      },
    },
  },
  '/v2/subcategories/{subcategoryId}': {
    delete: {
      tags: [tag.subcategories.name],
      summary: 'Deletar uma subcategoria existente',
      description: 'Endpoint para deletar uma subcategoria existente.',
      security: [{ bearerAuth: [] }],
      parameters: [
        {
          in: 'path',
          name: 'subcategoryId',
          required: true,
          schema: {
            type: 'string',
          },
        },
      ],
      responses: {
        200: {
          description: 'Subcategoria deletada com sucesso',
          content: {
            'application/json': {
              schema: SubcategoryDocSchema.subcategoryDeleteResponseSchema,
            },
          },
        },
        404: {
          description: 'Subcategoria não encontrada',
        },
        500: {
          description: 'Erro interno do servidor',
        },
      },
    },
  },
};
