import { UserDocSchema } from './schemas/user.schema';
import { tag } from './tag';

export const usersPaths = {
  '/v2/users': {
    post: {
      tags: [tag.user.name],
      summary: 'Criar um novo usuário inicial',
      description: 'Endpoint para criar um novo usuário inicial (temporário).',
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: UserDocSchema.createUserRequestBodySchema,
          },
        },
      },
      responses: {
        200: {
          description: 'Usuário criado com sucesso.',
          content: {
            'application/json': {
              schema: UserDocSchema.userResponseSchema,
            },
          },
        },
        400: {
          description: 'Bad request',
        },
        500: {
          description: 'Internal server error',
        },
      },
    },
  },
  '/v2/login': {
    post: {
      tags: [tag.user.name],
      summary: 'Login de usuário',
      description: 'Endpoint para realizar login de usuário e obter token de acesso.',
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: UserDocSchema.loginRequestBodySchema,
          },
        },
      },
      responses: {
        200: {
          description: 'Login realizado com sucesso.',
          content: {
            'application/json': {
              schema: UserDocSchema.loginResponseSchema,
            },
          },
        },
        400: {
          description: 'Bad request',
        },
        404: {
          description: 'Usuário não encontrado',
        },
        500: {
          description: 'Internal server error',
        },
      },
    },
  },
};
