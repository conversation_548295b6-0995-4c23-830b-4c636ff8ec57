import 'dotenv/config';
import { z } from 'zod';

const envSchema = z.object({
  ENVIRONMENT: z.enum(['dev', 'test', 'prod']).default('dev'),
  JWT_SECRET: z.string(),
  TOKEN_DATABASE: z.string(),
  PORT: z.coerce.number().default(3000),
  DB_NAME: z.string(),
  DB_HOST: z.string(),
  DB_PORT: z.string(),
  DB_USERNAME: z.string(),
  DB_PASSWORD: z.string(),
  FIRST_CUSTOMER_TAX_NUMBER: z.string(),
  FIRST_CUSTOMER_DOMAIN: z.string(),
  SQS_QUEUE_SUBDOMAIN_ARN: z.string(),
  SQS_QUEUE_DOMAIN_ARN: z.string(),
  SUBNET_PRIVATE_ID_ONE: z.string(),
  SUBNET_PRIVATE_ID_TWO: z.string(),
  SECURITY_GROUP_ID: z.string(),
  ROUTE_53_ARN: z.string(),
  ACM_ARN: z.string(),
  CLOUD_FRONT_ARN: z.string(),
  SQS_URL_SUBDOMAIN: z.string(),
  SQS_URL_DOMAIN: z.string(),
  CUSTOM_AWS_REGION: z.string(),
  MAIN_DOMAIN: z.string(),
  HOSTED_ZONE_ID: z.string(),
  S3_BUCKET: z.string(),
  CLOUD_FRONT_DISTRIBUTION_ID: z.string(),
  AWS_S3_PUBLIC_BUCKET: z.string(),
  INTEGRATION_TOKEN: z.string(),
  INTERNAL_USER_EMAIL: z.string().default('<EMAIL>'),
  INTERNAL_USER_PASSWORD: z.string().default('Bq@12345678'),
  EMAIL_FROM: z.string(),
  EMAIL_DEBUG_MODE: z.enum(['true', 'false']).default('false'),
  EMAIL_REPORT_DEV: z.string(),
  MAX_EXCEL_ROWS: z.string().default('1000'),
  MAX_EXCEL_FILE_SIZE_MB: z.string().default('10'),
  BQ_API_URL: z.string(),
  BUCKET_FILES: z.string(),
  GOOGLE_CLIENT_ID: z.string(),
});
const _env = envSchema.safeParse(process.env);

if (!_env.success) {
  console.error('❌ Invalid environment variable', _env.error.format());
  throw new Error('Invalid environment variable');
}

export const env = _env.data;
