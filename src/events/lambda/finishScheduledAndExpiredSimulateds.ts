import { KnexAnswerSessionsRepository } from '../../repositories/knex/answerSessions.repositories';
import { KnexSimulatedsRepository } from '../../repositories/knex/simulated.repositories';
import { KnexSimulatedsAccessRepository } from '../../repositories/knex/simulatedsAccess.repositories';
import { FinishExpiredSimulatedsUseCase } from '../../use-case/simulateds/finishExpiredSimulateds/finishExpiredSimulateds.useCase';
import { OpenScheduledSimulatedsUseCase } from '../../use-case/simulateds/openScheduledSimulateds/openScheduledSimulateds.useCase';

export const handler = async () => {
  const start = Date.now();

  // Instanciar repositórios
  const simulatedsRepo = new KnexSimulatedsRepository();
  const simulatedsAccessRepo = new KnexSimulatedsAccessRepository();
  const answerSessionsRepo = new KnexAnswerSessionsRepository();

  // 1. Abrir simulados agendados
  const openScheduledSimulatedsUseCase = new OpenScheduledSimulatedsUseCase(simulatedsRepo);
  const openResult = await openScheduledSimulatedsUseCase.execute();
  console.log('=== Simulados agendados abertos ===');
  console.log(`Total de simulados abertos: ${openResult.totalSimuladosAbertos}`);
  if (openResult.erros && openResult.erros.length > 0) {
    console.log('Erros ao abrir simulados agendados:', openResult.erros);
  }

  // 2. Finalizar simulados expirados
  const finishExpiredSimulatedsUseCase = new FinishExpiredSimulatedsUseCase(
    simulatedsRepo,
    simulatedsAccessRepo,
    answerSessionsRepo
  );
  const finishResult = await finishExpiredSimulatedsUseCase.execute();
  console.log('=== Simulados expirados finalizados ===');
  console.log(`Total de simulados processados: ${finishResult.totalSimuladosProcessados}`);
  console.log(`Total de simulados finalizados: ${finishResult.simuladosDesativados.length}`);
  console.log(`Total de acessos finalizados: ${finishResult.totalAcessosFinalizados}`);
  if (finishResult.erros && finishResult.erros.length > 0) {
    console.log('Erros ao finalizar simulados expirados:', finishResult.erros);
  }

  const end = Date.now();
  const duration = ((end - start) / 1000).toFixed(2);
  console.log(`⏱️ Tempo total de execução da Lambda: ${duration} segundos`);
};
