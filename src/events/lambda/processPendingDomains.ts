import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';

import { factoryProcessPendingDomainsUseCase } from '../../use-case/factories/domains.factory';

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  console.log('Lambda de processamento de domínios pendentes iniciada');
  console.log('Event:', JSON.stringify(event));

  try {
    const useCase = factoryProcessPendingDomainsUseCase();
    const result = await useCase.execute();

    console.log('Resultado do processamento:', JSON.stringify(result));

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: 'Processamento de domínios pendentes concluído com sucesso',
        data: result,
      }),
    };
  } catch (error) {
    console.error('Erro ao processar domínios pendentes:', error);

    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: 'Erro ao processar domínios pendentes',
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      }),
    };
  }
};
