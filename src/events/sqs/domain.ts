import { SQ<PERSON><PERSON>and<PERSON> } from 'aws-lambda';
import AWS from 'aws-sdk';

import { env } from '../../env';

const acm = new AWS.ACM({ region: 'us-east-1' });
const cloudfront = new AWS.CloudFront();
const route53 = new AWS.Route53();
const s3 = new AWS.S3();
const sqs = new AWS.SQS({ region: 'us-east-1' });

interface ISQSMessage {
  domain: string;
  hostedZoneId: string;
  certificateArn: string;
}

export const handler: SQSHandler = async (event) => {
  console.log('Lambda SQS disparada! Event:', JSON.stringify(event));

  for (const record of event.Records) {
    try {
      console.log('Processando record:', record.messageId);
      console.log('Raw SQS message body:', record.body);
      const message: ISQSMessage = JSON.parse(record.body);
      const { domain, hostedZoneId, certificateArn } = message;

      console.log('Mensagem parseada:', { domain, hostedZoneId, certificateArn });

      // 1. Validar o certificado
      console.log('Chamando validateCertificate...');
      await validateCertificate(certificateArn, hostedZoneId, domain);
      console.log('validateCertificate concluído com sucesso');

      // 2. Criar a distribuição do CloudFront
      console.log('Chamando createCloudFrontDistribution...');
      await createCloudFrontDistribution(domain, certificateArn);
      console.log(`Processamento concluído para o domínio ${domain}`);

      // limpar da fila sqs
      console.log('Deletando mensagem da fila');

      await sqs
        .deleteMessage({
          QueueUrl: env.SQS_URL_DOMAIN!,
          ReceiptHandle: record.receiptHandle,
        })
        .promise();

      console.log(`Mensagem deletada da fila SQS para o domínio ${domain}`);
    } catch (error) {
      console.error(`Erro ao processar o registro:`, error);
    }
  }
};

async function validateCertificate(certArn: string, hostedZoneId: string, domain: string) {
  console.log('Iniciando validação do certificado...');
  console.log('Certificate ARN:', certArn);
  console.log('Hosted Zone ID:', hostedZoneId);
  console.log('Domain:', domain);

  const certDetails = await acm.describeCertificate({ CertificateArn: certArn }).promise();
  console.log('Detalhes do certificado obtidos:', certDetails.Certificate?.Status);

  const validationOptions = certDetails.Certificate?.DomainValidationOptions || [];
  console.log('Opções de validação encontradas:', validationOptions.length);

  for (const option of validationOptions) {
    console.log('Processando opção de validação:', option);

    if (option.ResourceRecord) {
      console.log('ResourceRecord encontrado:', option.ResourceRecord);

      const params = {
        HostedZoneId: hostedZoneId,
        ChangeBatch: {
          Changes: [
            {
              Action: 'UPSERT',
              ResourceRecordSet: {
                Name: option.ResourceRecord.Name,
                Type: option.ResourceRecord.Type,
                TTL: 300,
                ResourceRecords: [{ Value: option.ResourceRecord.Value }],
              },
            },
          ],
        },
      };

      console.log('Parâmetros para Route53:', JSON.stringify(params, null, 2));

      await route53.changeResourceRecordSets(params).promise();
      console.log(`Validação adicionada para ${domain}:`, option.ResourceRecord);
    }
  }
}

async function createCloudFrontDistribution(domain: string, certificateArn: string) {
  const s3BucketName = env.S3_BUCKET; // Pegando o nome do bucket da variável de ambiente

  if (!s3BucketName) {
    throw new Error('S3_BUCKET não está configurado');
  }

  console.log('Iniciando criação da CloudFront distribution para:', domain);
  console.log('Bucket S3:', s3BucketName);
  console.log('Certificate ARN:', certificateArn);

  // 1. Criar o Origin Access Identity (OAI) para o CloudFront
  console.log('Criando OAI...');
  const oai = await cloudfront
    .createCloudFrontOriginAccessIdentity({
      CloudFrontOriginAccessIdentityConfig: {
        CallerReference: `${Date.now()}`,
        Comment: 'OAI for ' + domain,
      },
    })
    .promise();

  const oaiId = oai.CloudFrontOriginAccessIdentity?.Id;
  const canonicalUserId = oai.CloudFrontOriginAccessIdentity?.S3CanonicalUserId;

  console.log('OAI criado - ID:', oaiId);
  console.log('OAI criado - Canonical User ID:', canonicalUserId);

  if (!oaiId || !canonicalUserId) {
    throw new Error('Falha ao criar OAI');
  }

  console.log(`OAI criado com sucesso: ${oaiId}`);
  console.log(`Canonical User ID: ${canonicalUserId}`);
  // 2. Adicionar política de acesso ao bucket para permitir o acesso do CloudFront (OAI)

  console.log('Verificando policy existente do bucket...');
  let existingPolicy = { Version: '2012-10-17', Statement: [] };

  try {
    // Tentar obter a policy existente do bucket
    const existingPolicyResult = await s3.getBucketPolicy({ Bucket: s3BucketName }).promise();
    existingPolicy = JSON.parse(
      existingPolicyResult.Policy || '{"Version": "2012-10-17", "Statement": []}'
    );
    console.log('Policy existente encontrada:', existingPolicy);
  } catch (error) {
    // Se não houver policy existente, usar uma policy vazia
    if (error instanceof Error && 'code' in error && error.code === 'NoSuchBucketPolicy') {
      console.log('Nenhuma policy existente encontrada, criando nova policy');
    } else {
      throw error;
    }
  }

  // Novos statements para adicionar
  const newStatements = [
    {
      Sid: `AllowCloudFrontListBucket-${oaiId}`,
      Effect: 'Allow',
      Principal: {
        CanonicalUser: canonicalUserId,
      },
      Action: 's3:ListBucket',
      Resource: `arn:aws:s3:::${s3BucketName}`,
    },
    {
      Sid: `AllowCloudFrontGetObject-${oaiId}`,
      Effect: 'Allow',
      Principal: {
        CanonicalUser: canonicalUserId,
      },
      Action: 's3:GetObject',
      Resource: `arn:aws:s3:::${s3BucketName}/*`,
    },
  ];

  console.log('Novos statements que serão adicionados:', JSON.stringify(newStatements, null, 2));

  // Verificar se os statements já existem para evitar duplicação
  const existingStatements = existingPolicy.Statement || [];
  const sidExists = (sid: string) =>
    existingStatements.some((stmt: { Sid: string }) => stmt.Sid === sid);

  const statementsToAdd = newStatements.filter((stmt) => !sidExists(stmt.Sid));

  console.log(
    'Statements que serão adicionados (após filtro):',
    JSON.stringify(statementsToAdd, null, 2)
  );

  if (statementsToAdd.length > 0) {
    // Adicionar novos statements aos existentes
    const updatedPolicy = {
      ...existingPolicy,
      Statement: [...existingStatements, ...statementsToAdd],
    };

    // Log detalhado da policy antes de aplicar
    console.log('Policy que será aplicada:', JSON.stringify(updatedPolicy, null, 2));

    const bucketPolicy = {
      Bucket: s3BucketName,
      Policy: JSON.stringify(updatedPolicy),
    };

    console.log('Policy atualizada:', bucketPolicy);

    await s3.putBucketPolicy(bucketPolicy).promise();
    console.log(
      `Política de acesso do bucket S3 atualizada para a OAI ${oaiId} (${statementsToAdd.length} novos statements adicionados)`
    );
  } else {
    console.log(`Statements para OAI ${oaiId} já existem na policy do bucket`);
  }

  // 3. Criar a distribuição do CloudFront com a OAI configurada
  const params = {
    DistributionConfig: {
      CallerReference: `${Date.now()}`,
      Aliases: {
        Quantity: 1,
        Items: [domain],
      },
      DefaultCacheBehavior: {
        TargetOriginId: 'S3-ORIGIN-ID',
        ViewerProtocolPolicy: 'redirect-to-https',
        ForwardedValues: {
          QueryString: false,
          Cookies: { Forward: 'none' },
        },
        MinTTL: 0,
      },
      Origins: {
        Quantity: 1,
        Items: [
          {
            Id: 'S3-ORIGIN-ID',
            DomainName: `${s3BucketName}.s3.amazonaws.com`,
            S3OriginConfig: {
              OriginAccessIdentity: `origin-access-identity/cloudfront/${oaiId}`, // OAI agora está configurado
            },
          },
        ],
      },
      ViewerCertificate: {
        ACMCertificateArn: certificateArn,
        SSLSupportMethod: 'sni-only',
        MinimumProtocolVersion: 'TLSv1.2_2021',
      },
      Enabled: true,
      Comment: 'CloudFront distribution for ' + domain,
    },
  };

  try {
    const result = await cloudfront.createDistribution(params).promise();
    console.log('CloudFront Distribution criada:', result);
    return result.Distribution;
  } catch (error) {
    console.error('Erro ao criar a distribuição do CloudFront:', error);
    throw error;
  }
}
