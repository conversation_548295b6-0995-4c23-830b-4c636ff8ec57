import { <PERSON>Q<PERSON><PERSON>and<PERSON> } from 'aws-lambda';
import AWS from 'aws-sdk';
const acm = new AWS.ACM({ region: 'us-east-1' });
const cloudfront = new AWS.CloudFront();
const route53 = new AWS.Route53();
interface ISQSMessage {
  subdomainName: string; // Subdomínio completo (ex.: "app.example.com")
  mainDomain: string; // Domínio principal (ex.: "example.com")
  hostedZoneId: string; // ID da Hosted Zone no Route 53
  cloudFrontDistributionId: string; // ID da distribuição CloudFront
}
export const handler: SQSHandler = async (event) => {
  for (const record of event.Records) {
    try {
      const message: ISQSMessage = JSON.parse(record.body);
      const { subdomainName, mainDomain, hostedZoneId, cloudFrontDistributionId } = message;
      console.log(`Garantindo certificado para: ${mainDomain} e *.${mainDomain}`);
      const certArn = await ensureCertificate(mainDomain, hostedZoneId);
      console.log(`Atualizando CloudFront para o subdomínio: ${subdomainName}`);
      await updateCloudFrontDistribution(subdomainName, cloudFrontDistributionId, certArn);
      console.log(`Criando registro no Route 53 para o subdomínio: ${subdomainName}`);
      if (hostedZoneId) {
        await createRoute53Record(subdomainName, hostedZoneId, cloudFrontDistributionId);
      }
      console.log(`Subdomínio configurado com sucesso: ${subdomainName}`);
    } catch (error) {
      console.error(`Erro ao processar o registro:`, error);
    }
  }
};

async function ensureCertificate(mainDomain: string, hostedZoneId: string): Promise<string> {
  const domains = [mainDomain, `*.${mainDomain}`];
  const existingCert = await findExistingCertificate(mainDomain);
  if (existingCert) {
    console.log(`Certificado existente encontrado: ${existingCert}`);
    return existingCert;
  }
  console.log(`Solicitando novo certificado para: ${domains.join(', ')}`);
  const request = await acm
    .requestCertificate({
      DomainName: mainDomain,
      SubjectAlternativeNames: domains,
      ValidationMethod: 'DNS',
    })
    .promise();
  const certArn = request.CertificateArn!;
  console.log(`Certificado solicitado: ${certArn}`);
  console.log(`Validando certificado: ${certArn}`);
  await validateCertificate(certArn, hostedZoneId);
  return certArn;
}

async function findExistingCertificate(mainDomain: string): Promise<string | undefined> {
  const certs = await acm.listCertificates({ CertificateStatuses: ['ISSUED'] }).promise();
  for (const certSummary of certs.CertificateSummaryList || []) {
    // Obter detalhes do certificado
    const certDetails = await acm
      .describeCertificate({ CertificateArn: certSummary.CertificateArn! })
      .promise();
    const { Certificate } = certDetails;
    if (!Certificate) continue;
    // Verificar se os dois domínios estão incluídos
    const domains = new Set(Certificate.SubjectAlternativeNames);
    if (domains.has(mainDomain) && domains.has(`*.${mainDomain}`)) {
      console.log(`Certificado encontrado com ambos os domínios: ${mainDomain} e *.${mainDomain}`);
      return Certificate.CertificateArn;
    }
  }
  console.log(`Nenhum certificado encontrado para os domínios: ${mainDomain} e *.${mainDomain}`);
  return undefined;
}

async function validateCertificate(certArn: string, hostedZoneId: string): Promise<void> {
  const certDetails = await acm.describeCertificate({ CertificateArn: certArn }).promise();
  const validationOptions = certDetails.Certificate?.DomainValidationOptions || [];
  for (const option of validationOptions) {
    if (option.ResourceRecord) {
      const { Name, Type, Value } = option.ResourceRecord;
      const params: AWS.Route53.ChangeResourceRecordSetsRequest = {
        HostedZoneId: hostedZoneId,
        ChangeBatch: {
          Changes: [
            {
              Action: 'UPSERT',
              ResourceRecordSet: {
                Name,
                Type,
                TTL: 60,
                ResourceRecords: [{ Value }],
              },
            },
          ],
        },
      };
      await route53.changeResourceRecordSets(params).promise();
      console.log(`Registro DNS criado para validar o certificado: ${Name}`);
    }
  }
  await waitForCertificateValidation(certArn);
}

async function waitForCertificateValidation(
  certArn: string,
  retries = 10,
  intervalMs = 30000
): Promise<void> {
  for (let i = 0; i < retries; i++) {
    const certDetails = await acm.describeCertificate({ CertificateArn: certArn }).promise();
    if (certDetails.Certificate?.Status === 'ISSUED') {
      console.log(`Certificado validado com sucesso: ${certArn}`);
      return;
    }
    console.log(`Certificado ainda não validado. Tentativa ${i + 1}/${retries}...`);
    await new Promise((resolve) => setTimeout(resolve, intervalMs));
  }
  throw new Error(`Certificado não foi validado após ${retries} tentativas: ${certArn}`);
}

async function updateCloudFrontDistribution(
  subdomainName: string,
  distributionId: string,
  certArn: string
): Promise<void> {
  const distributionConfig = await cloudfront
    .getDistributionConfig({ Id: distributionId })
    .promise();

  const { ETag, DistributionConfig } = distributionConfig;

  if (!DistributionConfig) {
    throw new Error(`Distribuição CloudFront ${distributionId} não encontrada`);
  }

  DistributionConfig.Aliases = DistributionConfig.Aliases || { Quantity: 0, Items: [] };
  DistributionConfig.Aliases.Items = DistributionConfig.Aliases.Items || [];

  if (!DistributionConfig.Aliases.Items.includes(subdomainName)) {
    DistributionConfig.Aliases.Items.push(subdomainName);
    DistributionConfig.Aliases.Quantity = DistributionConfig.Aliases.Items.length;
  }
  DistributionConfig.ViewerCertificate = {
    ACMCertificateArn: certArn,
    SSLSupportMethod: 'sni-only',
    MinimumProtocolVersion: 'TLSv1.2_2021',
  };
  const params: AWS.CloudFront.UpdateDistributionRequest = {
    Id: distributionId,
    IfMatch: ETag,
    DistributionConfig,
  };
  await cloudfront.updateDistribution(params).promise();
  console.log(`CloudFront atualizado para o subdomínio: ${subdomainName}`);
}

async function createRoute53Record(
  subdomainName: string,
  hostedZoneId: string,
  cloudFrontDistributionId: string
): Promise<void> {
  const cloudFrontDomain = await getCloudFrontDomain(cloudFrontDistributionId);
  const params: AWS.Route53.ChangeResourceRecordSetsRequest = {
    HostedZoneId: hostedZoneId,
    ChangeBatch: {
      Changes: [
        {
          Action: 'UPSERT',
          ResourceRecordSet: {
            Name: subdomainName,
            Type: 'A',
            AliasTarget: {
              HostedZoneId: 'Z2FDTNDATAQYW2', // Hosted Zone ID do CloudFront
              DNSName: cloudFrontDomain,
              EvaluateTargetHealth: false,
            },
          },
        },
      ],
    },
  };
  await route53.changeResourceRecordSets(params).promise();
  console.log(`Registro criado/atualizado no Route 53 para o subdomínio: ${subdomainName}`);
}

async function getCloudFrontDomain(distributionId: string): Promise<string> {
  const result = await cloudfront.getDistribution({ Id: distributionId }).promise();
  return result.Distribution?.DomainName || '';
}
