import { ZodIssue } from 'zod';

import { validateInstanceOfErrors } from '../services/validateInstanceOfErrors.service';

interface IValidateInstanceOfErrors {
  statusCode: number;
  message: string;
  error: string;
  issue?: ZodIssue[];
}
export function handleErrors(error: unknown): IValidateInstanceOfErrors {
  console.log(error);
  const validateError = validateInstanceOfErrors(error);
  console.log(validateError.message);
  return validateError;
}
