import { knexInstance } from '../config/connectionDatabase.config';

/**
 * Gera uma nova transação do Knex
 * @returns Promise com a transação
 *
 * @example
 * ```typescript
 * const trx = await generateTransaction();
 *
 * try {
 *   // Operações de banco de dados
 *   await repository.insert(data, trx);
 *   await trx.commit();
 * } catch (error) {
 *   await trx.rollback();
 *   throw error;
 * }
 * ```
 */
export async function generateTransaction() {
  return await knexInstance.transaction();
}
