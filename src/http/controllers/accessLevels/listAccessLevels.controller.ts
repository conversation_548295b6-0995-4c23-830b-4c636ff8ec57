import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { factoryListAccessLevels } from '../../../use-case/factories/accessLevels.factory';

export async function listAccessLevelsController(req: Request, res: Response) {
  const useCase = factoryListAccessLevels();

  const accessLevels = await useCase.execute();
  return httpResponse(res, 200, {
    message: 'Níveis de acesso listados com sucesso',
    data: accessLevels,
  });
}
