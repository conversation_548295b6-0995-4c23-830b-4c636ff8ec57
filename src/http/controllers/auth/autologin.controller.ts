import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { factoryAutologinUseCase } from '../../../use-case/factories/auth.factory';

export async function autologinController(req: Request, res: Response): Promise<Response> {
  const validatedData = req.user;

  const autologinUseCase = factoryAutologinUseCase();

  const { token } = await autologinUseCase.execute(validatedData);

  return httpResponse(res, 200, { message: 'Login realizado com sucesso!', token });
}
