import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { changePasswordSchema } from '../../../schema/auth.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { makeChangePasswordUseCase } from '../../../use-case/factories/auth.factory';

const changePasswordUseCase = makeChangePasswordUseCase();

export async function changePasswordController(req: Request, res: Response): Promise<Response> {
  const { id: userId } = genericIdSchema.parse(req.user);

  const body = changePasswordSchema.parse(req.body);

  await changePasswordUseCase.execute({ ...body, userId });

  return httpResponse(res, 200, { message: 'Senha alterada com sucesso' });
}
