import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { completeUserRegistrationSchema } from '../../../schema/auth.schema';
import { factoryCompleteUserRegistrationUseCase } from '../../../use-case/factories/auth.factory';

export async function completeUserRegistrationController(req: Request, res: Response) {
  const data = completeUserRegistrationSchema.parse(req.body);

  const useCase = factoryCompleteUserRegistrationUseCase();
  await useCase.execute(data);

  return httpResponse(res, 200, { message: 'Registro completado com sucesso' });
}
