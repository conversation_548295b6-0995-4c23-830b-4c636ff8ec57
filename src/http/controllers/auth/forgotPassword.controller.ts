import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { forgotPasswordSchema } from '../../../schema/auth.schema';
import { factoryForgotPasswordUseCase } from '../../../use-case/factories/auth.factory';

export async function forgotPassword(req: Request, res: Response) {
  const { email, customerId } = forgotPasswordSchema.parse(req.body);

  const useCase = factoryForgotPasswordUseCase();

  await useCase.execute({ email, customerId });
  return httpResponse(res, 200, { message: 'Email enviado com sucesso' });
}
