import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { KnexUserRepository } from '../../../repositories/knex/users.repositories';
import { googleAuthSchema } from '../../../schema/auth.schema';
import { GoogleAuthUseCase } from '../../../use-case/auth/googleAuth.useCase';

export async function googleAuthController(req: Request, res: Response) {
  const { googleToken } = googleAuthSchema.parse(req.body);
  const userRepository = new KnexUserRepository();
  const useCase = new GoogleAuthUseCase(userRepository);
  const { token } = await useCase.execute({ googleToken });
  return httpResponse(res, 200, { message: 'Login Google realizado com sucesso', token });
}
