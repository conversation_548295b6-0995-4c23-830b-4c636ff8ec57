import bcrypt from 'bcryptjs';
import { Request, Response } from 'express';

import { env } from '../../../../env';
import { httpResponse } from '../../../../helpers/httpResponse';
import { IUser } from '../../../../model/IUser';
import { KnexUserRepository } from '../../../../repositories/knex/users.repositories';
import { loginSchema } from '../../../../schema/customers.schema';
import { generateToken } from '../../../../services/jwt/generateToken.jwt';
import { ResourceNotFoundError } from '../../../../use-case/errors/ResourceNotFound';
import { UnauthorizedError } from '../../../../use-case/errors/UnauthorizedError.ts';

export async function loginUserController(req: Request, res: Response) {
  const { email, password } = loginSchema.parse(req.body);

  const userRepository = new KnexUserRepository();

  const user = await userRepository.findOneBy({
    email,
    deleted_at: null,
  } as IUser);

  if (!user || !user.password) {
    throw new UnauthorizedError('Credenciais inválidas');
  }

  const isPasswordValid = await bcrypt.compare(password, user.password);

  if (!isPasswordValid) {
    throw new UnauthorizedError('Credenciais inválidas');
  }

  if (!user.active) {
    throw new ResourceNotFoundError('Usuário inativo');
  }

  const payload = {
    id: user.id,
    customerId: user.customer_id,
    role: user.role,
  };

  const token = generateToken(payload, env.JWT_SECRET);

  return httpResponse(res, 200, {
    message: 'Login realizado com sucesso',
    token,
  });
}
