import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { redefinePasswordSchema } from '../../../schema/auth.schema';
import { factoryRedefinePasswordUseCase } from '../../../use-case/factories/auth.factory';

export async function redefinePasswordController(req: Request, res: Response) {
  const { password, token } = redefinePasswordSchema.parse(req.body);

  const useCase = factoryRedefinePasswordUseCase();
  await useCase.execute(token, password);

  return httpResponse(res, 200, { message: 'Senha redefinida com sucesso' });
}
