import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericSchemaToken } from '../../../schema/generic.schema';
import { factoryTokenInfoUseCase } from '../../../use-case/factories/auth.factory';

export async function tokenInfoController(req: Request, res: Response) {
  const { token } = genericSchemaToken.parse(req.query);

  const useCase = factoryTokenInfoUseCase();
  const response = await useCase.execute(token);

  return httpResponse(res, 200, { message: 'Usu<PERSON>rio encontrado', user: response });
}
