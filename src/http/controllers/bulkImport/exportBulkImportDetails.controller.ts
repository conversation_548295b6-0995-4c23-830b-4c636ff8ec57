import { Request, Response } from 'express';

import { importIdSchema } from '../../../schema/bulkImport.schema';
import { genericSchemaCustomerIdAndId } from '../../../schema/generic.schema';
import { factoryExportBulkImportDetailsUseCase } from '../../../use-case/factories/bulkImport.factory';

export async function exportBulkImportDetailsController(req: Request, res: Response) {
  const { importId } = importIdSchema.parse(req.params);
  const { customerId } = genericSchemaCustomerIdAndId.parse(req.user);

  const useCase = factoryExportBulkImportDetailsUseCase();

  const result = await useCase.execute({
    importId,
    customerId,
  });

  res.setHeader(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  );
  res.setHeader('Content-Disposition', `attachment; filename="${result.fileName}"`);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Allow-Headers', '*');

  return res.send(result.buffer);
}
