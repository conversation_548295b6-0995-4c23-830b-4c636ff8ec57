import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { importIdSchema } from '../../../schema/bulkImport.schema';
import { customerIdSchema } from '../../../schema/customers.schema';
import { factoryGetBulkImportResultsUseCase } from '../../../use-case/factories/bulkImport.factory';

export async function getBulkImportResultsController(
  req: Request,
  res: Response
): Promise<Response> {
  const { customerId } = customerIdSchema.parse(req.user);
  const { importId } = importIdSchema.parse(req.params);

  const getBulkImportResultsUseCase = factoryGetBulkImportResultsUseCase();

  const results = await getBulkImportResultsUseCase.execute({
    importId,
    customerId,
  });

  return httpResponse(res, 200, {
    message: 'Resultados da importação obtidos com sucesso',
    ...results,
  });
}
