import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { importIdSchema } from '../../../schema/bulkImport.schema';
import { genericSchemaCustomerIdAndId } from '../../../schema/generic.schema';
import { factoryImportValidatedQuestionsUseCase } from '../../../use-case/factories/bulkImport.factory';

export async function importValidatedQuestionsController(req: Request, res: Response) {
  const { id: userId, customerId } = genericSchemaCustomerIdAndId.parse(req.user);
  const { importId } = importIdSchema.parse(req.params);

  const useCase = factoryImportValidatedQuestionsUseCase();

  const result = await useCase.execute({
    importId,
    customerId,
    userId,
  });

  return httpResponse(res, 200, {
    message: 'Questões importadas com sucesso',
    ...result,
  });
}
