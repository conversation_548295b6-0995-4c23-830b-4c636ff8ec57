import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { listBulkImportsSchema } from '../../../schema/bulkImport.schema';
import { customerIdSchema } from '../../../schema/customers.schema';
import { factoryListBulkImportsUseCase } from '../../../use-case/factories/bulkImport.factory';

export async function listBulkImportsController(req: Request, res: Response): Promise<Response> {
  const { customerId } = customerIdSchema.parse(req.user);
  const filters = listBulkImportsSchema.parse(req.query);

  const listBulkImportsUseCase = factoryListBulkImportsUseCase();

  const result = await listBulkImportsUseCase.execute({
    customerId,
    ...filters,
  });

  return httpResponse(res, 200, {
    message: 'Importações em massa listadas com sucesso',
    ...result,
  });
}
