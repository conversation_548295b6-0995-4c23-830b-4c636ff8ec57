import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { importIdSchema } from '../../../schema/bulkImport.schema';
import { genericSchemaCustomerIdAndId } from '../../../schema/generic.schema';
import { factoryReuploadQuestionsExcelUseCase } from '../../../use-case/factories/bulkImport.factory';

export async function reuploadQuestionsExcelController(req: Request, res: Response) {
  const file = req.file;

  if (!file) {
    return httpResponse(res, 400, { message: 'Arquivo é obrigatório' });
  }

  const { id: userId, customerId } = genericSchemaCustomerIdAndId.parse(req.user);
  const { importId } = importIdSchema.parse(req.params);

  const useCase = factoryReuploadQuestionsExcelUseCase();

  const result = await useCase.execute({
    file,
    importId,
    customerId,
    userId,
  });

  return httpResponse(res, 200, {
    ...result,
  });
}
