import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericSchemaCustomerIdAndId } from '../../../schema/generic.schema';
import { factoryUploadQuestionsExcelUseCase } from '../../../use-case/factories/bulkImport.factory';

export async function uploadQuestionsExcelController(req: Request, res: Response) {
  const file = req.file;

  if (!file) {
    return httpResponse(res, 400, { message: 'Arquivo é obrigatório' });
  }

  const { id: userId, customerId } = genericSchemaCustomerIdAndId.parse(req.user);

  const useCase = factoryUploadQuestionsExcelUseCase();

  const result = await useCase.execute({
    file,
    customerId,
    userId,
  });

  return httpResponse(res, 200, {
    ...result,
  });
}
