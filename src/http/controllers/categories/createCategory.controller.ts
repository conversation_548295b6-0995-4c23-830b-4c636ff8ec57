import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { createCategoryBodySchema } from '../../../schema/categories.schema';
import { customerIdSchema } from '../../../schema/customers.schema';
import { factoryCreateCategoryUseCase } from '../../../use-case/factories/categories.factory';

export async function createCategoryController(req: Request, res: Response): Promise<Response> {
  const bodyData = createCategoryBodySchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);

  const createCategoryUseCase = factoryCreateCategoryUseCase();
  const category = await createCategoryUseCase.execute({ ...bodyData, customerId });

  return httpResponse(res, 201, { message: 'Categoria criada com sucesso', ...category });
}
