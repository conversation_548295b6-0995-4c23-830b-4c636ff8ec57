import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryDeleteCategoryUseCase } from '../../../use-case/factories/categories.factory';

export async function deleteCategoryController(req: Request, res: Response): Promise<Response> {
  const { id } = genericIdSchema.parse(req.params);
  const { customerId } = customerIdSchema.parse(req.user);

  const deleteCategoryUseCase = factoryDeleteCategoryUseCase();
  const deletedCategory = await deleteCategoryUseCase.execute({ id, customerId });

  return httpResponse(res, 200, { message: 'Categoria excluida com sucesso', ...deletedCategory });
}
