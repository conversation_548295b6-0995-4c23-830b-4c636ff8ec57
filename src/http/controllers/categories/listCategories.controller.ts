import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { listCategoriesQuerySchema } from '../../../schema/categories.schema';
import { customerIdSchema } from '../../../schema/customers.schema';
import { factoryListCategoriesUseCase } from '../../../use-case/factories/categories.factory';

export const listCategoriesController = async (req: Request, res: Response) => {
  const { customerId } = customerIdSchema.parse(req.user);
  const filters = listCategoriesQuerySchema.parse(req.query);

  const listCategoriesUseCase = factoryListCategoriesUseCase();
  const categories = await listCategoriesUseCase.execute({
    customerId,
    ...filters,
  });

  return httpResponse(res, 200, { categories });
};
