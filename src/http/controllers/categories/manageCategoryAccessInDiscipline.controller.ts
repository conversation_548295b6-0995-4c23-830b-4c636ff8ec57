import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { disciplineIdSchema } from '../../../schema/disciplines.schema';
import { manageCategoryAccessInDisciplineSchema } from '../../../schema/disciplinesCategoriesAccess.schema';
import { factoryManageCategoryAccessInDiscipline } from '../../../use-case/factories/disciplineCategoryAccess.factory';

export async function manageCategoryAccessInDisciplineController(
  req: Request,
  res: Response
): Promise<Response> {
  const { disciplineId } = disciplineIdSchema.parse(req.params);
  const bodyData = manageCategoryAccessInDisciplineSchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);

  const manageCategoryAccessInDisciplineUseCase = factoryManageCategoryAccessInDiscipline();
  const message = await manageCategoryAccessInDisciplineUseCase.execute({
    ...bodyData,
    customerId,
    disciplineId,
  });
  return httpResponse(res, 201, { message });
}
