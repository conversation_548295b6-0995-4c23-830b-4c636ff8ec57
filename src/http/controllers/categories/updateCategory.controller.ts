import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { paramsSchema, updateCategorySchema } from '../../../schema/categories.schema';
import { customerIdSchema } from '../../../schema/customers.schema';
import { factoryUpdateCategoryUseCase } from '../../../use-case/factories/categories.factory';

export async function updateCategoryController(req: Request, res: Response): Promise<Response> {
  const { id } = paramsSchema.parse(req.params);
  const { name } = updateCategorySchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);

  const updateCategoryUseCase = factoryUpdateCategoryUseCase();
  const updatedCategory = await updateCategoryUseCase.execute({ id, name, customerId });

  return httpResponse(res, 200, { message: 'Categoria atualizada com sucesso', updatedCategory });
}
