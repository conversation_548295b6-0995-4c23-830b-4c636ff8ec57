import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { createClassSchema } from '../../../schema/classes.schema';
import { customerIdSchema } from '../../../schema/customers.schema';
import { factoryCreateClassUseCase } from '../../../use-case/factories/classes.factory';

export async function createClassController(req: Request, res: Response): Promise<Response> {
  const { customerId } = customerIdSchema.parse(req.user);
  const body = createClassSchema.parse(req.body);
  const useCase = factoryCreateClassUseCase();
  const response = await useCase.execute({ ...body, customerId });
  return httpResponse(res, 201, { message: 'Turma criada com sucesso', data: response });
}
