import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { createExternalClassSchema } from '../../../schema/classes.schema';
import { factoryCreateExternalClassUseCase } from '../../../use-case/factories/classes.factory';

export async function createExternalClassController(req: Request, res: Response) {
  const bodyData = createExternalClassSchema.parse(req.body);

  const createExternalClassUseCase = factoryCreateExternalClassUseCase();

  const createdClass = await createExternalClassUseCase.execute({ ...bodyData });

  return httpResponse(res, 201, { ...createdClass });
}
