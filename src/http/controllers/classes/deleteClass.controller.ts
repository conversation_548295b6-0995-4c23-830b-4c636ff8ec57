import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryDeleteClassUseCase } from '../../../use-case/factories/classes.factory';

export async function deleteClassController(req: Request, res: Response): Promise<Response> {
  const { id } = genericIdSchema.parse(req.params);
  const useCase = factoryDeleteClassUseCase();
  await useCase.execute(id);
  return httpResponse(res, 200, { message: 'Turma deletada com sucesso' });
}
