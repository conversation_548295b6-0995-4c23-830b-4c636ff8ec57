import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import {
  genericStringCustomerIdchema,
  genericStringIdSchema,
} from '../../../schema/generic.schema';
import { factoryDeleteExternalClassUseCase } from '../../../use-case/factories/classes.factory';

export async function deleteExternalClassController(req: Request, res: Response) {
  const { id } = genericStringIdSchema.parse(req.params);
  const { customerId } = genericStringCustomerIdchema.parse(req.query);

  const deleteExternalClassUseCase = factoryDeleteExternalClassUseCase();

  const result = await deleteExternalClassUseCase.execute({ id, customerId });

  return httpResponse(res, 200, {
    message: 'Turma excluída com sucesso',
    deletedClass: result,
  });
}
