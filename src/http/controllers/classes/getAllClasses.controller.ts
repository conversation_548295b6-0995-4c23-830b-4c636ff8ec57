import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { courseIdRequiredSchema } from '../../../schema/courses.schema';
import { factoryListClassesByCourseUseCase } from '../../../use-case/factories/classes.factory';

export async function getAllClassesController(req: Request, res: Response): Promise<Response> {
  const { courseId } = courseIdRequiredSchema.parse(req.params);
  const useCase = factoryListClassesByCourseUseCase();

  const response = await useCase.execute(courseId);

  return httpResponse(res, 200, {
    message: 'Turmas listadas com sucesso.',
    data: response,
  });
}
