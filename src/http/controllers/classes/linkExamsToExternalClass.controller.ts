import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { linkExamsToExternalClassSchema } from '../../../schema/classes.schema';
import { factoryLinkExamsToExternalClassUseCase } from '../../../use-case/factories/examsClasses.factory';

export async function linkExamsToExternalClassController(req: Request, res: Response) {
  const bodyData = linkExamsToExternalClassSchema.parse(req.body);

  const linkExamsToExternalClassUseCase = factoryLinkExamsToExternalClassUseCase();

  const result = await linkExamsToExternalClassUseCase.execute({
    classIds: bodyData.classIds,
    examIds: bodyData.examIds,
    customerId: bodyData.customerId,
  });

  return httpResponse(res, 201, {
    message: 'Provas vinculadas às turmas com sucesso',
    ...result,
  });
}
