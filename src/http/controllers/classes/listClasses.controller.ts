import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { listClassesSchema } from '../../../schema/classes.schema';
import { customerIdSchema } from '../../../schema/customers.schema';
import { factoryListClassesUseCase } from '../../../use-case/factories/classes.factory';

export async function listClassesController(req: Request, res: Response) {
  const { customerId } = customerIdSchema.parse(req.user);
  const filters = listClassesSchema.parse(req.query);

  const listClassesUseCase = factoryListClassesUseCase();

  const classes = await listClassesUseCase.execute({
    customerId,
    ...filters,
  });

  return httpResponse(res, 200, {
    message: 'Turmas listadas com sucesso',
    ...classes,
  });
}
