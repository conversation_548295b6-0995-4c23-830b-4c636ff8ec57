import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { listClassesQuerySchema } from '../../../schema/classes.schema';
import { customerIdSchema } from '../../../schema/customers.schema';
import { simulatedIdSchema } from '../../../schema/simulateds.schema';
import { factoryListClassesWithUsersUseCase } from '../../../use-case/factories/classes.factory';

export async function listClassesWithUsersController(req: Request, res: Response) {
  const { customerId } = customerIdSchema.parse(req.user);
  const filters = listClassesQuerySchema.parse(req.query);
  const { simulatedId } = simulatedIdSchema.parse(req.query);

  const listClassesWithUsersUseCase = factoryListClassesWithUsersUseCase();

  const result = await listClassesWithUsersUseCase.execute({
    ...filters,
    customerId,
    simulatedId,
  });

  return httpResponse(res, 200, {
    message: 'Turmas listadas com sucesso',
    ...result,
  });
}
