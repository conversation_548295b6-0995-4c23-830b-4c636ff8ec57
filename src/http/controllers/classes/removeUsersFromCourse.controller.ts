import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { removeUsersFromCourseSchema } from '../../../schema/courses.schema';
import { customerIdSchema } from '../../../schema/customers.schema';
import { factoryRemoveUsersFromCourseUseCase } from '../../../use-case/factories/courses.factory';

export async function removeUsersFromCourseController(req: Request, res: Response) {
  const { userIds, courseId } = removeUsersFromCourseSchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);

  const useCase = factoryRemoveUsersFromCourseUseCase();
  const { totalRemoved } = await useCase.execute({ userIds, courseId, customerId });
  return httpResponse(res, 200, {
    message: 'Usuários removidos do curso com sucesso',
    totalRemoved,
  });
}
