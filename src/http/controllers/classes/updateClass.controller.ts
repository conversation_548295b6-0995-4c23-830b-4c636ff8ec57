import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { updateClassSchema } from '../../../schema/classes.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryUpdateClassUseCase } from '../../../use-case/factories/classes.factory';

export async function updateClassController(req: Request, res: Response): Promise<Response> {
  const { id } = genericIdSchema.parse(req.params);
  const body = updateClassSchema.parse(req.body);
  const useCase = factoryUpdateClassUseCase();
  const response = await useCase.execute({ id, ...body });
  return httpResponse(res, 200, { message: 'Turma atualizada com sucesso', data: response });
}
