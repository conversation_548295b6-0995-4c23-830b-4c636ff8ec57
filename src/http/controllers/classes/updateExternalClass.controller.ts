import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { updateExternalClassSchema } from '../../../schema/classes.schema';
import { genericStringIdSchema } from '../../../schema/generic.schema';
import { factoryUpdateExternalClassUseCase } from '../../../use-case/factories/classes.factory';

export async function updateExternalClassController(req: Request, res: Response) {
  const bodyData = updateExternalClassSchema.parse(req.body);

  const { id } = genericStringIdSchema.parse(req.params);

  const updateExternalClassUseCase = factoryUpdateExternalClassUseCase();

  const result = await updateExternalClassUseCase.execute({
    id,
    ...bodyData,
  });

  return httpResponse(res, 200, result);
}
