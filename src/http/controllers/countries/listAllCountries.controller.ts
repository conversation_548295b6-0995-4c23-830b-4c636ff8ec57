import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { listAllCountriesSchema } from '../../../schema/countries.schema';
import { listAllCountriesUseCase } from '../../../use-case/factories/countries.factory';

export async function listCountriesController(req: Request, res: Response) {
  const filter = listAllCountriesSchema.parse(req.query);

  const useCase = listAllCountriesUseCase();
  const countries = await useCase.execute(filter);
  return httpResponse(res, 200, countries);
}
