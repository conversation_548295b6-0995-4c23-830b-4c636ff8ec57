import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { createCourseSchema } from '../../../schema/courses.schema';
import { customerIdSchema } from '../../../schema/customers.schema';
import { factoryCreateCourse } from '../../../use-case/factories/courses.factory';

export async function createCourseController(req: Request, res: Response): Promise<Response> {
  const { customerId } = customerIdSchema.parse(req.user);
  const useCase = factoryCreateCourse();
  const bodyData = createCourseSchema.parse(req.body);

  const response = await useCase.execute({ ...bodyData, customerId });

  return httpResponse(res, 201, {
    message: 'Curso criado com sucesso.',
    data: response,
  });
}
