import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { factoryCreateCourseQuestionBankUseCase } from '../../../use-case/factories/coursesQuestionsGroup.factory';

export async function createCourseQuestionBankHandler(
  req: Request,
  res: Response
): Promise<Response> {
  const { courseId, questionGroupId } = req.body;
  const useCase = factoryCreateCourseQuestionBankUseCase();
  const result = await useCase.execute(courseId, questionGroupId);
  return httpResponse(res, 200, { message: 'ok', data: result });
}
