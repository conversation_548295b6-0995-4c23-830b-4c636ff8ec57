import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { courseIdRequiredSchema } from '../../../schema/courses.schema';
import { factoryGetCourse } from '../../../use-case/factories/courses.factory';

export async function getCourseController(req: Request, res: Response): Promise<Response> {
  const { courseId } = courseIdRequiredSchema.parse(req.params);

  const useCase = factoryGetCourse();
  const course = await useCase.execute(courseId);
  return httpResponse(res, 200, {
    message: 'Ok',
    data: course,
  });
}
