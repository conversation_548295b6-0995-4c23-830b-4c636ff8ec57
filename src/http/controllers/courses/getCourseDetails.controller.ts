import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { courseIdRequiredSchema } from '../../../schema/courses.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryGetCourseDetails } from '../../../use-case/factories/courses.factory';

export async function getCourseDetailsController(req: Request, res: Response): Promise<Response> {
  const { courseId } = courseIdRequiredSchema.parse(req.params);
  const { id: userId } = genericIdSchema.parse(req.user);

  const useCase = factoryGetCourseDetails();
  const courseDetails = await useCase.execute(courseId, userId);

  return httpResponse(res, 200, {
    message: 'Ok',
    data: courseDetails,
  });
}
