import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { courseIdRequiredSchema } from '../../../schema/users.schema';
import { factoryGetCourseQuestionBankUseCase } from '../../../use-case/factories/courses.factory';

export async function getCourseQuestionBankController(req: Request, res: Response) {
  const { courseId } = courseIdRequiredSchema.parse(req.params);
  const useCase = factoryGetCourseQuestionBankUseCase();
  const result = await useCase.execute(courseId);

  return httpResponse(res, 200, { message: 'Question bank encontrado com sucesso.', data: result });
}
