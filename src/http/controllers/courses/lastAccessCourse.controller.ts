import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryLastAccessCourseUseCase } from '../../../use-case/factories/courses.factory';

const lastAccessCourseUseCase = factoryLastAccessCourseUseCase();

export async function lastAccessCourse(req: Request, res: Response): Promise<Response> {
  const { id: userId } = genericIdSchema.parse(req.user);
  const response = await lastAccessCourseUseCase.execute(userId);

  if (!response) {
    return httpResponse(res, 200, { message: 'Usuario não iniciou nenhum curso.' });
  }

  return httpResponse(res, 200, {
    message: 'Curso mais recente listado com sucesso.',
    data: response,
  });
}
