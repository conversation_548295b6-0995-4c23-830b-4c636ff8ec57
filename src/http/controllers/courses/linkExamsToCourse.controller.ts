import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { factoryLinkExamsToCourseUseCase } from '../../../use-case/factories/courses.factory';

export async function linkExamsToCourseController(req: Request, res: Response): Promise<Response> {
  const { courseId, examIds } = req.body;
  const { customerId } = req.user;
  const useCase = factoryLinkExamsToCourseUseCase();
  const result = await useCase.execute({ courseId, examIds, customerId });
  return httpResponse(res, 200, result);
}
