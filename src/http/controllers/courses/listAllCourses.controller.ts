import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { factoryListAllCourses } from '../../../use-case/factories/courses.factory';

export async function listAllCoursesController(req: Request, res: Response) {
  const { customerId } = customerIdSchema.parse(req.user);

  const useCase = factoryListAllCourses();

  const courses = await useCase.execute(customerId);
  return httpResponse(res, 200, {
    message: 'Cursos listados com sucesso',
    data: courses,
  });
}
