import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { factoryListAllCoursesSimulationsUseCase } from '../../../use-case/factories/courses.factory';

export async function listAllCoursesSimulationsController(req: Request, res: Response) {
  const { id: userId, customerId } = req.user;
  const useCase = factoryListAllCoursesSimulationsUseCase();
  const data = await useCase.execute(userId, customerId);
  return httpResponse(res, 200, { message: 'Simulações dos cursos listadas com sucesso', data });
}
