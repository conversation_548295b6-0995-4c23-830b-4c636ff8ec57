import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryListCoursesByUserUseCase } from '../../../use-case/factories/courses.factory';

export async function listCoursesByUserController(req: Request, res: Response) {
  const { id: userId } = genericIdSchema.parse(req.user);

  const useCase = factoryListCoursesByUserUseCase();

  const courses = await useCase.execute(userId);
  return httpResponse(res, 200, { message: 'Cursos listados com sucesso', data: courses });
}

export default listCoursesByUserController;
