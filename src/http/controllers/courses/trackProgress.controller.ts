import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { trackProgressSchema } from '../../../schema/courses.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryTrackProgress } from '../../../use-case/factories/courses.factory';

export async function trackProgressController(req: Request, res: Response): Promise<Response> {
  const { courseId, lessonId, progress, timeWatched } = trackProgressSchema.parse(req.body);
  const { id: userId } = genericIdSchema.parse(req.user);

  const useCase = factoryTrackProgress();
  const result = await useCase.execute(courseId, lessonId, progress, userId, timeWatched);

  return httpResponse(res, 200, {
    message: 'Progresso atualizado com sucesso',
    data: result,
  });
}
