import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { updateCourseSchema } from '../../../schema/courses.schema';
import { courseIdRequiredSchema } from '../../../schema/users.schema';
import { factoryUpdateCourseUseCase } from '../../../use-case/factories/courses.factory';

export async function updateCourseController(req: Request, res: Response): Promise<Response> {
  const { courseId } = courseIdRequiredSchema.parse(req.params);
  const bodyData = updateCourseSchema.parse(req.body);
  const useCase = factoryUpdateCourseUseCase();
  const result = await useCase.execute({
    courseId,
    ...bodyData,
  });
  return httpResponse(res, 200, { message: 'Curso atualizado com sucesso.', data: result });
}
