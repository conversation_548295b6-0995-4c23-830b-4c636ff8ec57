import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { factoryGetAllCourseCategoriesUseCase } from '../../../use-case/factories/coursesCategories.factory';

export async function getAllCourseCategoriesController(
  req: Request,
  res: Response
): Promise<Response> {
  const filters = req.query;

  const coursesCategories = await factoryGetAllCourseCategoriesUseCase().execute(filters);
  return httpResponse(res, 200, {
    message: 'Categorias de cursos carregadas com sucesso',
    data: coursesCategories,
  });
}
