import { Request, Response } from 'express';

import { courseIdRequiredSchema } from '../../../schema/users.schema';
import { factoryGetExamIdsByCourseUseCase } from '../../../use-case/factories/coursesExams.factory';

export async function getExamIdsByCourseHandler(req: Request, res: Response): Promise<Response> {
  const { courseId } = courseIdRequiredSchema.parse(req.params);
  const getExamIdsByCourseUseCase = factoryGetExamIdsByCourseUseCase();
  const result = await getExamIdsByCourseUseCase.execute(courseId);
  return res.status(200).json(result);
}
