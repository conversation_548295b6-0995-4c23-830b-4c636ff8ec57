import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { createCustomerSchema } from '../../../schema/customers.schema';
import { factoryCreateExternalCustomerUseCase } from '../../../use-case/factories/customers.factory';

export async function createExternalCustomerController(
  req: Request,
  res: Response
): Promise<Response> {
  const validatedData = createCustomerSchema.parse(req.body);

  const createCustomerUseCase = factoryCreateExternalCustomerUseCase();

  const customer = await createCustomerUseCase.execute(validatedData);

  return httpResponse(res, 201, { message: 'Cliente cadastrado com sucesso!', data: customer });
}
