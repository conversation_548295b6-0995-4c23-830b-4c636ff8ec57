import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { factoryGetCustomerByIdUseCase } from '../../../use-case/factories/customers.factory';

export async function getCustomerController(req: Request, res: Response): Promise<Response> {
  const origin = req.headers.origin || '';
  const getCustomerByIdUseCase = factoryGetCustomerByIdUseCase();
  const customer = await getCustomerByIdUseCase.execute(origin);

  return httpResponse(res, 200, { message: 'Cliente buscado com sucesso!', customer });
}
