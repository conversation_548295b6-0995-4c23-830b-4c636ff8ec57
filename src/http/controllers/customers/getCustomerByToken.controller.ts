import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { getCustomersByTokenUseCase } from '../../../use-case/factories/customers.factory';

export async function getCustomerByTokenController(req: Request, res: Response): Promise<Response> {
  const { customerId } = req.user;

  const getCustomerByTokenUseCase = getCustomersByTokenUseCase();

  const customer = await getCustomerByTokenUseCase.execute(customerId);

  return httpResponse(res, 200, { message: 'Cliente buscado com sucesso!', customer });
}
