import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema, updateCustomerSchema } from '../../../schema/customers.schema';
import { factoryUpdateCustomerUseCase } from '../../../use-case/factories/customers.factory';

export async function updateCustomerController(req: Request, res: Response) {
  const { customerId } = customerIdSchema.parse(req.user);
  const bodyData = updateCustomerSchema.parse(req.body);

  const updateCustomerUseCase = factoryUpdateCustomerUseCase();

  const result = await updateCustomerUseCase.execute({
    customerId,
    ...bodyData,
  });

  return httpResponse(res, 200, {
    message: 'Cliente atualizado com sucesso',
    customer: result,
  });
}
