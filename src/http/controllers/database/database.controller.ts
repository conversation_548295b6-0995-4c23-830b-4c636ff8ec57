import { Request, Response } from 'express';

import { env } from '../../../env';
import { httpResponse } from '../../../helpers/httpResponse';
import { runMigrations, runMigrationsRemove } from '../../../services/migrations.service';
import { runSeeds } from '../../../services/seeds.service';

export class DatabaseController {
  async migrate(req: Request, res: Response) {
    await runMigrations();
    return httpResponse(res, 200, { message: 'Database migrated successfully.' });
  }

  async rollback(req: Request, res: Response) {
    if (env.ENVIRONMENT.toLocaleLowerCase().includes('prod')) {
      return httpResponse(res, 400, {
        message: 'This action is not allowed in production environment.',
      });
    }
    await runMigrationsRemove();
    return httpResponse(res, 200, { message: 'Database rolled back successfully.' });
  }

  async seed(req: Request, res: Response) {
    await runSeeds();
    return httpResponse(res, 200, { message: 'Database seeded successfully.' });
  }

  async reset(req: Request, res: Response) {
    if (env.ENVIRONMENT.toLocaleLowerCase().includes('prod')) {
      return httpResponse(res, 400, {
        message: 'This action is not allowed in production environment.',
      });
    }

    await runMigrationsRemove();
    await runMigrations();
    await runSeeds();
    return httpResponse(res, 200, {
      message: 'Database reset successfully (migrations and seeds).',
    });
  }
}
