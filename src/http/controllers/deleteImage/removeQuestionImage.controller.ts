import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { fileKeySchema } from '../../../schema/questions.schema';
import { factoryDeleteImageUseCase } from '../../../use-case/factories/questions.factory';

export async function removeQuestionImageController(req: Request, res: Response) {
  const dataParams = fileKeySchema.parse(req.query);

  const DeleteImageUseCase = factoryDeleteImageUseCase();

  await DeleteImageUseCase.execute(dataParams);

  return httpResponse(res, 200, { message: 'Imagem deletada com sucesso' });
}
