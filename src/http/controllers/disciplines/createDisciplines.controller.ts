import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { createDisciplineSchema } from '../../../schema/disciplines.schema';
import { factoryCreateDisciplineUseCase } from '../../../use-case/factories/disciplines.factory';
import { customerIdSchema } from './../../../schema/customers.schema';

export async function createDisciplinesController(req: Request, res: Response): Promise<Response> {
  createDisciplineSchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);

  const createDisciplineUseCase = factoryCreateDisciplineUseCase();

  const discipline = await createDisciplineUseCase.execute({ ...req.body, customerId });

  return httpResponse(res, 201, discipline);
}
