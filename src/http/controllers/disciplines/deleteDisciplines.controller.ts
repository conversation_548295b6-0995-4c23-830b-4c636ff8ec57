import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryDeleteDisciplineUseCase } from '../../../use-case/factories/disciplines.factory';

export async function deleteDisciplinesController(req: Request, res: Response): Promise<Response> {
  const { id } = genericIdSchema.parse(req.params);
  const { customerId } = customerIdSchema.parse(req.user);

  const deleteDisciplinesUseCase = factoryDeleteDisciplineUseCase();

  const deletedDiscipline = await deleteDisciplinesUseCase.execute({ id, customerId });

  return httpResponse(res, 200, {
    message: 'Disciplina excluida com sucesso',
    deletedDiscipline,
  });
}
