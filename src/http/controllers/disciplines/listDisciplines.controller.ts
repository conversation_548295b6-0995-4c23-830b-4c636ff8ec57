import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { factoryListDisciplinesUseCase } from '../../../use-case/factories/disciplines.factory';

export async function listDisciplinesController(req: Request, res: Response): Promise<Response> {
  const { customerId } = customerIdSchema.parse(req.user);
  const filter = req.query;
  const listDisciplinesUseCase = factoryListDisciplinesUseCase();
  const disciplines = await listDisciplinesUseCase.execute({ customerId, ...filter });

  return httpResponse(res, 200, disciplines);
}
