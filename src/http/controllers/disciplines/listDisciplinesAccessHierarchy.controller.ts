import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { factoryListDisciplinesWithAccessHierarchyUseCase } from '../../../use-case/factories/disciplines.factory';

export async function listDisciplinesAccessHierarchyController(
  req: Request,
  res: Response
): Promise<Response> {
  const { customerId } = customerIdSchema.parse(req.user);

  const disciplinesAccessHierarchyUseCase =
    await factoryListDisciplinesWithAccessHierarchyUseCase();

  const disciplines = await disciplinesAccessHierarchyUseCase.execute({ customerId });

  return httpResponse(res, 200, {
    message: 'Disciplinas e acessos listados com sucesso!',
    disciplines,
  });
}
