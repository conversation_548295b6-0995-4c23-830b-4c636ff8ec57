import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { listDisciplinesWithQuestionCountSchema } from '../../../schema/disciplines.schema';
import { factoryListDisciplinesWithQuestionCountUseCase } from '../../../use-case/factories/disciplines.factory';

export async function listDisciplinesWithQuestionCountController(req: Request, res: Response) {
  const parsedData = listDisciplinesWithQuestionCountSchema.parse({
    ...req.query,
    ...req.params,
    customerId: req.user.customerId,
  });

  const useCase = await factoryListDisciplinesWithQuestionCountUseCase();

  const response = await useCase.execute(parsedData);

  return httpResponse(res, 200, {
    message: 'Disciplinas listadas com sucesso!',
    ...response,
  });
}
