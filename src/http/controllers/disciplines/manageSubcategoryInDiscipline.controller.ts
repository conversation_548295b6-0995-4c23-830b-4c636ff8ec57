import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { manageSubcategoryInDisciplineSchema } from '../../../schema/disciplinesSubcategoriesAccess.schema';
import { factoryManageSubcategoryInDisciplineUseCase } from '../../../use-case/factories/disciplineSubcategoriesAccess.factory';
import { customerIdSchema } from './../../../schema/customers.schema';

export async function manageSubcategoryInDisciplineController(
  req: Request,
  res: Response
): Promise<Response> {
  const { disciplineId } = req.params;
  const bodyData = manageSubcategoryInDisciplineSchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);

  const manageSubcategoryInDisciplineUseCase = factoryManageSubcategoryInDisciplineUseCase();

  const result = await manageSubcategoryInDisciplineUseCase.execute({
    ...bodyData,
    customerId,
    disciplineId,
  });

  return httpResponse(res, 200, { ...result });
}
