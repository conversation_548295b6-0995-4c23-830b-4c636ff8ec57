import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { updateDisciplineSchema } from '../../../schema/disciplines.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryUpdateDisciplineUseCase } from '../../../use-case/factories/disciplines.factory';

export async function updateDisciplinesController(req: Request, res: Response): Promise<Response> {
  const { name } = updateDisciplineSchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);
  const { id } = genericIdSchema.parse(req.params);

  const updateDisciplineUseCase = factoryUpdateDisciplineUseCase();

  const updatedDiscipline = await updateDisciplineUseCase.execute({ id, name, customerId });

  return httpResponse(res, 200, {
    message: 'Disciplina atualizada com sucesso!',
    updatedDiscipline,
  });
}
