import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { createDomainSchema } from '../../../schema/domains.schema';
import { factoryCreateDomainCustomerUseCase } from '../../../use-case/factories/domains.factory';

export async function createDomainController(req: Request, res: Response): Promise<Response> {
  const { domain, customerId } = createDomainSchema.parse(req.body);

  const useCase = factoryCreateDomainCustomerUseCase();
  const result = await useCase.execute({ domain, customerId });

  return httpResponse(res, 200, {
    message: 'Domínio criado com sucesso',
    data: {
      id: result.id,
      domain: result.domain,
      hosted_zone_id: result.hosted_zone_id,
      certificate_arn: result.certificate_arn,
      status: result.status,
    },
  });
}
