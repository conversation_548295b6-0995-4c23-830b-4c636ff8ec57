import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { factoryProcessPendingDomainsUseCase } from '../../../use-case/factories/domains.factory';

export async function processPendingDomainsController(
  req: Request,
  res: Response
): Promise<Response> {
  const useCase = factoryProcessPendingDomainsUseCase();
  const result = await useCase.execute();

  return httpResponse(res, 200, {
    message: 'Processamento de domínios pendentes concluído',
    data: result,
  });
}
