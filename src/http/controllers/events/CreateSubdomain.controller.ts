import { Request, Response } from 'express';

import { env } from '../../../env';
import { httpResponse } from '../../../helpers/httpResponse';
import { factoryCreateSubdomainUseCase } from '../../../use-case/factories/events.factory';

export async function createSubdomainController(req: Request, res: Response) {
  const {
    subdomain,
    customerName,
    email,
    firstName,
    lastName,
    cpf,
    birthDate,
    gender,
    phoneNumber,
  } = req.body;

  const createSubdomainUseCase = factoryCreateSubdomainUseCase();

  const result = await createSubdomainUseCase.execute({
    subdomainName: subdomain,
    customerName,
    email,
    firstName,
    lastName,
    cpf,
    birthDate,
    gender,
    phoneNumber,
    mainDomain: env.MAIN_DOMAIN,
    hostedZoneId: env.HOSTED_ZONE_ID,
    cloudFrontDistributionId: env.CLOUD_FRONT_DISTRIBUTION_ID,
  });

  return httpResponse(res, 200, { message: 'Ok', result });
}
