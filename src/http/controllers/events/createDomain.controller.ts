import AWS from 'aws-sdk';
import { Request, Response } from 'express';

import { env } from '../../../env';
import { httpResponse } from '../../../helpers/httpResponse';

const acm = new AWS.ACM({ region: 'us-east-1' });
const sqs = new AWS.SQS({ region: 'us-east-1' });

const route53 = new AWS.Route53();

export async function domainEventHttp(req: Request, res: Response) {
  try {
    const { domain } = req.body;

    const updatedDomain = domain.replace(/\.$/, '');
    const existingHostedZone = await getExistingHostedZone(updatedDomain);

    if (existingHostedZone) {
      return httpResponse(res, 200, {
        Ns: existingHostedZone.DelegationSet.NameServers,
        message: 'Hosted zone already exists',
      });
    }

    console.log(`Processando domínio: ${domain}`);
    // Criar Hosted Zone
    const hostedZone = await createHostedZone(updatedDomain);
    // Solicitar Certificado
    const certificateArn = await requestCertificate(updatedDomain);

    console.log(`Configuração concluída para o domínio ${domain}:`, {
      hostedZoneId: hostedZone,
      certificateArn,
    });

    console.log(`Enviar para a fila o domain`);
    const queueUrl = env.SQS_URL_DOMAIN;
    if (!queueUrl) {
      throw new Error('SQS_URL_DOMAIN não está configurado');
    }
    await sendMessageToQueue(queueUrl, {
      domain: updatedDomain,
      hostedZoneId: hostedZone.HostedZone.Id,
      certificateArn,
    });

    return httpResponse(res, 200, {
      Ns: hostedZone.DelegationSet.NameServers,
      message: 'Hosted zone created',
    });
  } catch (error) {
    console.error(`Erro ao processar o registro:`, error);
  }
}

async function createHostedZone(domain: string) {
  const params = {
    Name: domain,
    CallerReference: Date.now().toString(),
  };

  const result = await route53.createHostedZone(params).promise();

  return result;
}

async function getExistingHostedZone(domain: string) {
  const params = {
    DNSName: domain,
    MaxItems: '1',
  };

  const result = await route53.listHostedZonesByName(params).promise();

  if (result.HostedZones?.length > 0 && result.HostedZones[0]?.Name === `${domain}.`) {
    const hostedZoneId = result.HostedZones[0].Id;
    const ns = await getNsRecords(hostedZoneId, domain);
    console.log(ns);

    const hostedZone = {
      DelegationSet: {
        NameServers: ns,
      },
    };
    return hostedZone;
  }

  return null;
}

async function getNsRecords(hostedZoneId: string, domain: string) {
  const params = {
    HostedZoneId: hostedZoneId,
    StartRecordName: domain,
    StartRecordType: 'NS',
    MaxItems: '1',
  };

  const result = await route53.listResourceRecordSets(params).promise();
  const nsRecordSet = result.ResourceRecordSets?.find((recordSet) => recordSet.Type === 'NS');

  if (nsRecordSet) {
    return nsRecordSet.ResourceRecords?.map((record) => record.Value);
  }
  return [];
}

async function requestCertificate(domain: string) {
  const params = {
    DomainName: domain,
    ValidationMethod: 'DNS',
  };
  const result = await acm.requestCertificate(params).promise();
  console.log('Certificado solicitado:', result);
  return result.CertificateArn;
}

async function sendMessageToQueue(queueUrl: string, messageBody: object) {
  const params = {
    QueueUrl: queueUrl,
    MessageBody: JSON.stringify(messageBody),
  };

  const result = await sqs.sendMessage(params).promise();
  console.log('Mensagem enviada para a fila:', result);
  return result;
}
