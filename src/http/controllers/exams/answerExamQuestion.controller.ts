import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { alternativeIdSchema } from '../../../schema/alternatives.schema';
import { answerExamQuestionParamsSchema } from '../../../schema/examsAccess.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryAnswerExamQuestionUseCase } from '../../../use-case/factories/exams.factory';

export async function answerExamQuestionController(req: Request, res: Response): Promise<Response> {
  const { examAccessId, questionId } = answerExamQuestionParamsSchema.parse(req.params);

  const { alternativeId } = alternativeIdSchema.parse(req.body);

  const { id: userId } = genericIdSchema.parse(req.user);

  const answerExamQuestionUseCase = factoryAnswerExamQuestionUseCase();

  const result = await answerExamQuestionUseCase.execute({
    examAccessId,
    questionId,
    userId,
    alternativeId,
  });

  return httpResponse(res, 200, {
    message: 'Resposta registrada com sucesso',
    result,
  });
}
