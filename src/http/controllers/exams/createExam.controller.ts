import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { createExamSchema } from '../../../schema/exams.schema';
import { factoryCreateExamUseCase } from '../../../use-case/factories/exams.factory';

export async function createExamController(req: Request, res: Response): Promise<Response> {
  const bodyData = createExamSchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);

  const createExamUseCase = factoryCreateExamUseCase();

  const exam = await createExamUseCase.execute({
    ...bodyData,
    customerId,
  });

  return httpResponse(res, 201, {
    message: 'Prova criada com sucesso',
    exam,
  });
}
