import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryDeleteExamUseCase } from '../../../use-case/factories/exams.factory';

export async function deleteExamController(req: Request, res: Response): Promise<Response> {
  const { id } = genericIdSchema.parse(req.params);
  const { customerId } = customerIdSchema.parse(req.user);

  const deleteExamUseCase = factoryDeleteExamUseCase();

  const deletedExam = await deleteExamUseCase.execute({
    id,
    customerId,
  });

  return httpResponse(res, 200, {
    message: 'Prova excluída com sucesso',
    deletedExam,
  });
}
