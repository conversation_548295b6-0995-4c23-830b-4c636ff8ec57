import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { externalListExamsSchema } from '../../../schema/exams.schema';
import { factoryExternalListExamsUseCase } from '../../../use-case/factories/exams.factory';

export async function externalListExamsController(req: Request, res: Response): Promise<Response> {
  const filters = externalListExamsSchema.parse(req.query);

  const externalListExamsUseCase = factoryExternalListExamsUseCase();
  const result = await externalListExamsUseCase.execute({
    ...filters,
  });

  return httpResponse(res, 200, {
    message: 'Provas listadas com sucesso',
    ...result,
  });
}
