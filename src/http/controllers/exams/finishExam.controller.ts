import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { finishExamParamsSchema } from '../../../schema/exams.schema';
import { genericSchemaCustomerIdAndId } from '../../../schema/generic.schema';
import { factoryFinishExamUseCase } from '../../../use-case/factories/exams.factory';

export async function finishExamController(req: Request, res: Response): Promise<Response> {
  const { examAccessId } = finishExamParamsSchema.parse(req.params);
  const { id: userId, customerId } = genericSchemaCustomerIdAndId.parse(req.user);

  const finishExamUseCase = factoryFinishExamUseCase();
  const result = await finishExamUseCase.execute({
    examAccessId,
    userId,
    customerId,
  });

  return httpResponse(res, 200, {
    message: 'Prova finalizada com sucesso',
    ...result,
  });
}
