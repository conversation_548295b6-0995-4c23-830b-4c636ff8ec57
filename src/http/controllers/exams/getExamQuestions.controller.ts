import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { examAccessIdSchema } from '../../../schema/examsAccess.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryGetExamQuestionsUseCase } from '../../../use-case/factories/exams.factory';

export async function getExamQuestionsController(req: Request, res: Response): Promise<Response> {
  const { examAccessId } = examAccessIdSchema.parse(req.params);
  const { id: userId } = genericIdSchema.parse(req.user);

  const getExamQuestionsUseCase = factoryGetExamQuestionsUseCase();

  const result = await getExamQuestionsUseCase.execute({
    examAccessId,
    userId,
  });

  return httpResponse(res, 200, {
    message: '<PERSON><PERSON><PERSON> da prova listadas com sucesso',
    ...result,
  });
}
