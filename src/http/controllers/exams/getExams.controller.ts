import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { factoryGetExamsUseCase } from '../../../use-case/factories/exams.factory';

export async function getExamsHandler(req: Request, res: Response): Promise<Response> {
  const { customerId } = customerIdSchema.parse(req.user);

  const getExamsUseCase = factoryGetExamsUseCase();

  const exams = await getExamsUseCase.execute(customerId);

  return httpResponse(res, 200, { message: 'Exames listados com sucesso', data: exams });
}
