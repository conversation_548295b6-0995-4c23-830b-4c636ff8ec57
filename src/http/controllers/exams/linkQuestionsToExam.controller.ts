import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { examIdSchema } from '../../../schema/exams.schema';
import { linkQuestionsToExamBodySchema } from '../../../schema/examsQuestions.schema';
import { factoryLinkQuestionsToExamUseCase } from '../../../use-case/factories/exams.factory';

export async function linkQuestionsToExamController(
  req: Request,
  res: Response
): Promise<Response> {
  const { examId } = examIdSchema.parse(req.params);
  const { questionIds } = linkQuestionsToExamBodySchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);

  const linkQuestionsToExamUseCase = factoryLinkQuestionsToExamUseCase();

  const result = await linkQuestionsToExamUseCase.execute({
    examId,
    questionIds,
    customerId,
  });

  return httpResponse(res, 201, {
    message: 'Questões vinculadas à prova com sucesso',
    ...result,
  });
}
