import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { listExamsSchema } from '../../../schema/exams.schema';
import { factoryListExamsUseCase } from '../../../use-case/factories/exams.factory';

export async function listExamsController(req: Request, res: Response): Promise<Response> {
  const { customerId } = customerIdSchema.parse(req.user);
  const filters = listExamsSchema.parse(req.query);

  const listExamsUseCase = factoryListExamsUseCase();
  const exams = await listExamsUseCase.execute({
    customerId,
    ...filters,
  });

  return httpResponse(res, 200, {
    message: 'Provas listadas com sucesso',
    ...exams,
  });
}
