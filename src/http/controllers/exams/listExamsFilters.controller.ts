import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { listExamsFiltersSchema } from '../../../schema/exams.schema';
import { factoryListExamsFiltersUseCase } from '../../../use-case/factories/exams.factory';

export async function listExamsFiltersController(req: Request, res: Response): Promise<Response> {
  const { customerId } = customerIdSchema.parse(req.user);
  const { courseId } = listExamsFiltersSchema.parse(req.query);

  const listExamsFiltersUseCase = factoryListExamsFiltersUseCase();

  const filters = await listExamsFiltersUseCase.execute({
    courseId,
    customerId,
  });

  return httpResponse(res, 200, {
    message: 'Filtros obtidos com sucesso',
    ...filters,
  });
}
