import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { listPublishedExamsFiltersSchema } from '../../../schema/exams.schema';
import { factoryListPublishedExamsPaginatedUseCase } from '../../../use-case/factories/exams.factory';

export async function listPublishedExamsPaginatedController(
  req: Request,
  res: Response
): Promise<Response> {
  const { customerId } = req.user;
  const filters = listPublishedExamsFiltersSchema.parse(req.query);

  const listPublishedExamsPaginatedUseCase = factoryListPublishedExamsPaginatedUseCase();

  const result = await listPublishedExamsPaginatedUseCase.execute({
    customerId,
    ...filters,
  });

  return httpResponse(res, 200, result);
}
