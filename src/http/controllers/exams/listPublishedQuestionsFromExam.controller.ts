import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { examIdSchema } from '../../../schema/exams.schema';
import { factoryListPublishedQuestionsFromExamUseCase } from '../../../use-case/factories/exams.factory';

export async function listPublishedQuestionsFromExamController(req: Request, res: Response) {
  const { examId } = examIdSchema.parse(req.params);
  const { customerId } = customerIdSchema.parse(req.user);

  const listPublishedQuestionsFromExamUseCase = factoryListPublishedQuestionsFromExamUseCase();

  const result = await listPublishedQuestionsFromExamUseCase.execute({
    examId,
    customerId,
  });

  return httpResponse(res, 200, {
    message: '<PERSON><PERSON><PERSON> da prova listadas com sucesso',
    ...result,
  });
}
