import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { listUserExamsSchema } from '../../../schema/exams.schema';
import { factoryListUserExamsUseCase } from '../../../use-case/factories/exams.factory';

export async function listUserExamsController(req: Request, res: Response): Promise<Response> {
  const { id: userId, customerId } = req.user;

  const { courseId } = listUserExamsSchema.parse(req.query);

  const listUserExamsUseCase = factoryListUserExamsUseCase();
  const userExams = await listUserExamsUseCase.execute({
    userId,
    customerId,
    courseId,
  });

  return httpResponse(res, 200, userExams);
}
