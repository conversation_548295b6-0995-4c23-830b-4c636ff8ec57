import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { ExamSessionActionEnum } from '../../../model/enums/examsStatus.enum';
import {
  examSessionActionBodySchema,
  examSessionActionParamsSchema,
} from '../../../schema/exams.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryManageExamSessionUseCase } from '../../../use-case/factories/exams.factory';

export async function pauseResumeExamController(req: Request, res: Response): Promise<Response> {
  const { examAccessId } = examSessionActionParamsSchema.parse(req.params);

  const { action } = examSessionActionBodySchema.parse(req.body);

  const { id: userId } = genericIdSchema.parse(req.user);

  const useCase = factoryManageExamSessionUseCase();

  const result = await useCase.execute({
    examAccessId,
    userId,
    action,
  });

  return httpResponse(res, 200, {
    message:
      action === ExamSessionActionEnum.PAUSE
        ? 'Prova pausada com sucesso'
        : 'Prova iniciada com sucesso',
    ...result,
  });
}
