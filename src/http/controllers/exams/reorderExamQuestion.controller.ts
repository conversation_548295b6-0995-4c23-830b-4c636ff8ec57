import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { examIdSchema } from '../../../schema/exams.schema';
import { reorderExamQuestionsSchema } from '../../../schema/examsQuestions.schema';
import { factoryReorderExamQuestionUseCase } from '../../../use-case/factories/exams.factory';

export async function reorderExamQuestionsController(
  req: Request,
  res: Response
): Promise<Response> {
  const { examId } = examIdSchema.parse(req.params);
  const { questions } = reorderExamQuestionsSchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);

  const reorderExamQuestionsUseCase = factoryReorderExamQuestionUseCase();

  const result = await reorderExamQuestionsUseCase.execute({
    examId,
    customerId,
    questions,
  });

  return httpResponse(res, 200, {
    message: 'Questõ<PERSON> reordenadas com sucesso',
    ...result,
  });
}
