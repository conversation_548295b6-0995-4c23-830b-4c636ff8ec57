import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { examIdSchema } from '../../../schema/exams.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryStartExamSessionUseCase } from '../../../use-case/factories/exams.factory';

export async function startExamSessionController(req: Request, res: Response): Promise<Response> {
  const { examId } = examIdSchema.parse(req.params);
  const { id: userId } = genericIdSchema.parse(req.user);

  const startExamSessionUseCase = factoryStartExamSessionUseCase();

  const examSession = await startExamSessionUseCase.execute({
    examId,
    userId,
  });

  return httpResponse(res, 201, {
    message: 'Sessão de prova iniciada com sucesso',
    examSession,
  });
}
