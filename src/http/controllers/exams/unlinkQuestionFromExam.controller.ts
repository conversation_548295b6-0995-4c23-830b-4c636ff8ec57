import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { examIdSchema } from '../../../schema/exams.schema';
import { questionIdSchema } from '../../../schema/questions.schema';
import { factoryUnlinkQuestionFromExamUseCase } from '../../../use-case/factories/exams.factory';

export async function unlinkQuestionFromExamController(
  req: Request,
  res: Response
): Promise<Response> {
  const { examId } = examIdSchema.parse(req.params);
  const { questionId } = questionIdSchema.parse(req.params);
  const { customerId } = customerIdSchema.parse(req.user);

  const unlinkQuestionFromExamUseCase = factoryUnlinkQuestionFromExamUseCase();

  const result = await unlinkQuestionFromExamUseCase.execute({
    examId,
    questionId,
    customerId,
  });

  return httpResponse(res, 200, {
    message: 'Questão removida da prova com sucesso',
    ...result,
  });
}
