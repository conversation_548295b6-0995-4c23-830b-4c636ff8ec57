import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { updateExamSchema } from '../../../schema/exams.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryUpdateExamUseCase } from '../../../use-case/factories/exams.factory';

export async function updateExamController(req: Request, res: Response): Promise<Response> {
  const { id } = genericIdSchema.parse(req.params);
  const bodyData = updateExamSchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);

  const updateExamUseCase = factoryUpdateExamUseCase();

  const updatedExam = await updateExamUseCase.execute({
    id,
    customerId,
    ...bodyData,
  });

  return httpResponse(res, 200, {
    message: 'Prova atualizada com sucesso',
    exam: updatedExam,
  });
}
