import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { getSignedFileUrlSchema } from '../../../schema/files.schema';
import { factoryGetSignedFileUrlUseCase } from '../../../use-case/factories/files.factory';

export async function getSignedFileUrlController(req: Request, res: Response): Promise<Response> {
  const { fileId } = getSignedFileUrlSchema.parse(req.params);

  const useCase = factoryGetSignedFileUrlUseCase();
  const result = await useCase.execute(fileId);

  return httpResponse(res, 200, {
    message: 'URL assinada gerada com sucesso',
    data: result,
  });
}
