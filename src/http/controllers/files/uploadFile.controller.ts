import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { uploadFileSchema } from '../../../schema/files.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryUploadFileUseCase } from '../../../use-case/factories/files.factory';

export const uploadFileController = async (req: Request, res: Response) => {
  const bodyData = uploadFileSchema.parse(req.body);

  const { id: userId } = genericIdSchema.parse(req.user);

  const file = req.file as Express.Multer.File;

  const uploadFileUseCase = factoryUploadFileUseCase();

  const result = await uploadFileUseCase.execute({
    file,
    bodyData,
    userId,
    customerId: req.user.id,
  });
  return httpResponse(res, result.statusCode, { message: result.message, data: result.data });
};
