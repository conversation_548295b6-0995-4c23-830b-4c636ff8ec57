import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { listGraduationsSchema } from '../../../schema/graduations.schema';
import { factoryListPaginatedGraduationsUseCase } from '../../../use-case/factories/graduations.factory';

export async function listPaginatedGraduationsController(req: Request, res: Response) {
  const filters = listGraduationsSchema.parse(req.query);
  const usecase = factoryListPaginatedGraduationsUseCase();

  const result = await usecase.execute(filters);

  return httpResponse(res, 200, { message: 'Graduações listadas com sucesso', data: result });
}
