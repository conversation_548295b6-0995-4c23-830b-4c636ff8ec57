import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { createInstitutionSchema } from '../../../schema/institutions.schema';
import { factoryCreateInstitutionUseCase } from '../../../use-case/factories/institutions.factory';

export async function createInstitutionController(req: Request, res: Response): Promise<Response> {
  const bodyData = createInstitutionSchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);

  const createInstitutionUseCase = factoryCreateInstitutionUseCase();

  const institution = await createInstitutionUseCase.execute({
    ...bodyData,
    customerId,
  });

  return httpResponse(res, 201, {
    message: 'Instituição criada com sucesso',
    institution,
  });
}
