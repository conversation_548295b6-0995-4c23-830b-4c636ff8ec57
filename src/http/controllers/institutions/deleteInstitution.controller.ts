import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryDeleteInstitutionUseCase } from '../../../use-case/factories/institutions.factory';

export async function deleteInstitutionController(req: Request, res: Response): Promise<Response> {
  const { id } = genericIdSchema.parse(req.params);
  const { customerId } = customerIdSchema.parse(req.user);

  const deleteInstitutionUseCase = factoryDeleteInstitutionUseCase();

  const deletedInstitution = await deleteInstitutionUseCase.execute({
    id,
    customerId,
  });

  return httpResponse(res, 200, {
    message: 'Instituição excluída com sucesso',
    deletedInstitution,
  });
}
