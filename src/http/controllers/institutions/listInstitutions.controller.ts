import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { factoryListInstitutionsUseCase } from '../../../use-case/factories/institutions.factory';

export async function listInstitutionsController(req: Request, res: Response): Promise<Response> {
  const { customerId } = customerIdSchema.parse(req.user);

  const listInstitutionsUseCase = factoryListInstitutionsUseCase();

  const institutions = await listInstitutionsUseCase.execute({
    customerId,
  });

  return httpResponse(res, 200, {
    message: 'Instituições listadas com sucesso',
    institutions,
  });
}
