import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { listInstitutionsPublicSchema } from '../../../schema/institutions.schema';
import { factoryListInstitutionsPublicUseCase } from '../../../use-case/factories/institutions.factory';

export async function listInstitutionsPublicController(
  req: Request,
  res: Response
): Promise<Response> {
  const filters = listInstitutionsPublicSchema.parse(req.query);

  const listInstitutionsPublicUseCase = factoryListInstitutionsPublicUseCase();

  const result = await listInstitutionsPublicUseCase.execute({
    ...filters,
  });

  return httpResponse(res, 200, {
    message: 'Instituições listadas com sucesso',
    data: result,
  });
}
