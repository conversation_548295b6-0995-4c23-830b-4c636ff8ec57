import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { updateInstitutionSchema } from '../../../schema/institutions.schema';
import { factoryUpdateInstitutionUseCase } from '../../../use-case/factories/institutions.factory';

export async function updateInstitutionController(req: Request, res: Response): Promise<Response> {
  const bodyData = updateInstitutionSchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);
  const { id } = genericIdSchema.parse(req.params);

  const updateInstitutionUseCase = factoryUpdateInstitutionUseCase();

  const updatedInstitution = await updateInstitutionUseCase.execute({
    ...bodyData,
    customerId,
    id,
  });

  return httpResponse(res, 200, {
    message: 'Instituição atualizada com sucesso',
    institution: updatedInstitution,
  });
}
