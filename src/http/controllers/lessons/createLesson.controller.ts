import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { createLessonSchema } from '../../../schema/lessons.schema';
import { factoryCreateLessonUseCase } from '../../../use-case/factories/lessons.factory';

export async function createLessonController(req: Request, res: Response) {
  const bodyData = createLessonSchema.parse(req.body);
  const files = req.files as Express.Multer.File[] | undefined;
  const useCase = factoryCreateLessonUseCase();
  const lesson = await useCase.execute({ ...bodyData, files, customerId: req.user.id });
  return httpResponse(res, 201, { message: 'Aula criada com sucesso.', lesson });
}
