import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryDeleteLessonUseCase } from '../../../use-case/factories/lessons.factory';

export async function deleteLessonController(req: Request, res: Response) {
  const { id } = genericIdSchema.parse(req.params);

  const useCase = factoryDeleteLessonUseCase();

  await useCase.execute(id);

  return httpResponse(res, 200, { message: 'Aula excluída com sucesso.' });
}
