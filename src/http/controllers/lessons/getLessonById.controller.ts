import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryGetLessonByIdUseCase } from '../../../use-case/factories/lessons.factory';

export async function getLessonByIdController(req: Request, res: Response) {
  const { id } = genericIdSchema.parse(req.params);

  const useCase = factoryGetLessonByIdUseCase();

  const lesson = await useCase.execute(id);

  return httpResponse(res, 200, { message: 'Aula encontrada com sucesso', lesson });
}
