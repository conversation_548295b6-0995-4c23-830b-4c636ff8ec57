import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { reorderLessonsSchema } from '../../../schema/lessons.schema';
import { factoryReorderLessonsUseCase } from '../../../use-case/factories/lessons.factory';

export async function reorderLessonsController(req: Request, res: Response) {
  const data = reorderLessonsSchema.parse(req.body);
  const useCase = factoryReorderLessonsUseCase();
  const result = await useCase.execute(data);
  return httpResponse(res, 200, result);
}
