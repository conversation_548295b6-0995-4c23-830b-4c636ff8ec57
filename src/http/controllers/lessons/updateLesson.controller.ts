import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericIdSchema } from '../../../schema/generic.schema';
import { updateLessonSchema } from '../../../schema/lessons.schema';
import { factoryUpdateLessonUseCase } from '../../../use-case/factories/lessons.factory';

export async function updateLessonController(req: Request, res: Response) {
  const { id } = genericIdSchema.parse(req.params);

  if (req.body.removeFiles && !Array.isArray(req.body.removeFiles)) {
    req.body.removeFiles = [req.body.removeFiles];
  }
  const bodyData = updateLessonSchema.parse(req.body);
  const files = req.files as Express.Multer.File[] | undefined;
  const useCase = factoryUpdateLessonUseCase();
  const updated = await useCase.execute({ id, ...bodyData, files, customerId: req.user.id });
  return httpResponse(res, 200, { message: 'Aula atualizada com sucesso.', lesson: updated });
}
