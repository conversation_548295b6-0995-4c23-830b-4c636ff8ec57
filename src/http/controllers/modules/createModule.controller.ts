import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { createModuleSchema } from '../../../schema/modules.schema';
import { factoryCreateModuleUseCase } from '../../../use-case/factories/modules.factory';

export async function createModuleController(req: Request, res: Response): Promise<Response> {
  const bodyData = createModuleSchema.parse(req.body);
  const useCase = factoryCreateModuleUseCase();
  await useCase.execute(bodyData);
  return httpResponse(res, 201, {
    message: 'Modules created or updated successfully.',
  });
}
