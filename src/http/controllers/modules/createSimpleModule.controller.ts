import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { createSimpleModuleSchema } from '../../../schema/modules.schema';
import { factoryCreateSimpleModuleUseCase } from '../../../use-case/factories/modules.factory';

export async function createSimpleModuleController(req: Request, res: Response) {
  const { customerId } = customerIdSchema.parse(req.user);

  const bodyData = createSimpleModuleSchema.parse(req.body);

  const useCase = factoryCreateSimpleModuleUseCase();

  const module = await useCase.execute({ ...bodyData, customerId });

  return httpResponse(res, 201, { message: 'Módulo criado com sucesso.', module });
}
