import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryDeleteModuleUseCase } from '../../../use-case/factories/modules.factory';

export async function deleteModuleController(req: Request, res: Response) {
  const { id } = genericIdSchema.parse(req.params);

  const useCase = factoryDeleteModuleUseCase();

  await useCase.execute(id);
  return httpResponse(res, 200, { message: 'Módulo excluído com sucesso.' });
}
