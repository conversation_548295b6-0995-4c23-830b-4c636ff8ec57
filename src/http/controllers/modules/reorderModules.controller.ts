import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { reorderModulesSchema } from '../../../schema/modules.schema';
import { factoryReorderModulesUseCase } from '../../../use-case/factories/modules.factory';

export async function reorderModulesController(req: Request, res: Response) {
  const data = reorderModulesSchema.parse(req.body);
  const useCase = factoryReorderModulesUseCase();
  const result = await useCase.execute(data);
  return httpResponse(res, 200, result);
}
