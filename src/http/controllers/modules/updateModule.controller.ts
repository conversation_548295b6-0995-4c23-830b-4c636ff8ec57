import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericIdSchema } from '../../../schema/generic.schema';
import { updateModuleSchema } from '../../../schema/modules.schema';
import { factoryUpdateModuleUseCase } from '../../../use-case/factories/modules.factory';

export async function updateModuleController(req: Request, res: Response) {
  const { id } = genericIdSchema.parse(req.params);
  const bodyData = updateModuleSchema.parse(req.body);
  const useCase = factoryUpdateModuleUseCase();
  const updated = await useCase.execute({ ...bodyData, id });
  return httpResponse(res, 200, { message: 'Módulo atualizado com sucesso.', module: updated });
}
