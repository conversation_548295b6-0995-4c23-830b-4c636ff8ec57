import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { factoryListQuestionTypesUseCase } from '../../../use-case/factories/questionTypes.factory';

export async function listQuestionTypesController(req: Request, res: Response) {
  const { customerId } = customerIdSchema.parse(req.user);

  const listQuestionTypesUseCase = factoryListQuestionTypesUseCase();

  const questionTypes = await listQuestionTypesUseCase.execute({ customerId });

  return httpResponse(res, 200, {
    message: 'Tipos de questões listados com sucesso!',
    questionTypes,
  });
}
