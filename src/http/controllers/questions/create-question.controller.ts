import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse'; // Importação adicionada
import { customerIdSchema } from '../../../schema/customers.schema';
import { createQuestionBodySchema } from '../../../schema/questions.schema';
import { factoryCreateQuestionUseCase } from '../../../use-case/factories/questions.factory';

export async function createQuestionController(req: Request, res: Response) {
  const dataBody = createQuestionBodySchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);

  const CreateQuestionUseCase = factoryCreateQuestionUseCase();

  const question = await CreateQuestionUseCase.execute({
    ...dataBody,
    customerId,
  });

  return httpResponse(res, 201, { message: 'Questão criada com sucesso', question });
}
