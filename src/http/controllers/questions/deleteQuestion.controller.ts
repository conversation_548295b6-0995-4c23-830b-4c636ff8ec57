import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryDeleteQuestionUseCase } from '../../../use-case/factories/questions.factory';

export async function deleteQuestionController(req: Request, res: Response) {
  const { id } = genericIdSchema.parse(req.params);
  const { customerId } = customerIdSchema.parse(req.user);

  const DeleteQuestionUseCase = factoryDeleteQuestionUseCase();

  const deletedQuestion = await DeleteQuestionUseCase.execute({
    id,
    customerId,
  });

  return httpResponse(res, 200, { message: 'Questão deletada com sucesso', deletedQuestion });
}
