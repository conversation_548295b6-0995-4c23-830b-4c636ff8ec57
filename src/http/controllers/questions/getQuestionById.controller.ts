import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { factoryGetQuestionByIdUseCase } from '../../../use-case/factories/questions.factory';

export async function getQuestionByIdController(req: Request, res: Response) {
  const { questionId } = req.params;
  const { customerId } = customerIdSchema.parse(req.user);

  const getQuestionByIdUseCase = factoryGetQuestionByIdUseCase();

  const question = await getQuestionByIdUseCase.execute({ questionId, customerId });

  return httpResponse(res, 200, { message: 'Questão obtida com sucesso.', question });
}
