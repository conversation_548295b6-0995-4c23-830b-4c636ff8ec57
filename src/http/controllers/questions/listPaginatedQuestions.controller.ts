import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { listPaginatedQuestionsSchema } from '../../../schema/questions.schema';
import { factoryListPaginatedQuestionsUseCase } from '../../../use-case/factories/questions.factory';

export async function listPaginatedQuestionsController(req: Request, res: Response) {
  const { customerId } = customerIdSchema.parse(req.user);
  const filters = listPaginatedQuestionsSchema.parse(req.query);

  const listQuestionPagination = factoryListPaginatedQuestionsUseCase();

  const questions = await listQuestionPagination.execute({
    customerId,
    ...filters,
  });

  return httpResponse(res, 200, { message: 'Questões listadas com sucesso', ...questions });
}
