import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { listQuestionInGroupQuerySchema } from '../../../schema/questions.schema';
import { factoryListQuestionInGroupUseCase } from '../../../use-case/factories/questions.factory';

export async function listQuestionInGroupController(req: Request, res: Response) {
  const { customerId } = customerIdSchema.parse(req.user);
  const filters = listQuestionInGroupQuerySchema.parse(req.query);
  const { id } = genericIdSchema.parse(req.params);

  const listQuestionInGroupUseCase = factoryListQuestionInGroupUseCase();

  const result = await listQuestionInGroupUseCase.execute({
    customerId,
    id,
    ...filters,
  });

  return httpResponse(res, 200, result);
}
