import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericSchemaCustomerIdAndId } from '../../../schema/generic.schema';
import { questionHighlightsSchema, questionIdSchema } from '../../../schema/questions.schema';
import { factoryManageQuestionHighlightsUseCase } from '../../../use-case/factories/questions.factory';

export async function manageQuestionHighlightsController(req: Request, res: Response) {
  const { questionId } = questionIdSchema.parse(req.params);
  const { id: userId, customerId } = genericSchemaCustomerIdAndId.parse(req.user);
  const highlightsData = questionHighlightsSchema.parse(req.body);

  const manageQuestionHighlightsUseCase = factoryManageQuestionHighlightsUseCase();

  const result = await manageQuestionHighlightsUseCase.execute({
    questionId,
    userId,
    customerId,
    ...highlightsData,
  });

  return httpResponse(res, 200, {
    message: 'Destaques da questão atualizados com sucesso',
    result,
  });
}
