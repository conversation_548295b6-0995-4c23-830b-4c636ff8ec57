import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { factoryGenerateCodeQuestionService } from '../../../use-case/factories/questions.factory';

export async function questionCodeController(req: Request, res: Response) {
  const { customerId } = customerIdSchema.parse(req.user);

  const questionCodeGenerator = factoryGenerateCodeQuestionService();

  const questionCode = await questionCodeGenerator.execute({ customerId });

  return httpResponse(res, 200, { message: 'Código gerado com sucesso!', code: questionCode });
}
