import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { updateQuestionBodySchema } from '../../../schema/questions.schema';
import { factoryUpdateQuestionUseCase } from '../../../use-case/factories/questions.factory';

export async function updateQuestionController(req: Request, res: Response) {
  const { id } = genericIdSchema.parse(req.params);
  const { customerId } = customerIdSchema.parse(req.user);
  const bodyData = updateQuestionBodySchema.parse(req.body);

  const updateQuestionUseCae = factoryUpdateQuestionUseCase();

  const question = await updateQuestionUseCae.execute({
    customerId,
    questionId: id,
    ...bodyData,
  });

  return httpResponse(res, 200, { message: 'Questão atualizada com sucesso', ...question });
}
