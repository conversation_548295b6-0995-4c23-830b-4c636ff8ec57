import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { uploadQuestionImageSchema } from '../../../schema/upload.schema';
import { fatoryUploadQuestionImageUseCase } from '../../../use-case/factories/questions.factory';

export async function uploadQuestionImageController(req: Request, res: Response) {
  const file = req.file as Express.Multer.File;
  console.log('file -- Controller', file);
  const dataBody = uploadQuestionImageSchema.parse(req.body);

  console.log('dataBody -- Controller', dataBody);
  const { customerId } = customerIdSchema.parse(req.user);

  console.log('contentType -- Controller', req.headers['content-type']);
  console.log('ContentType -- Controller', req.headers['Content-Type']);
  const uploadQuestionImageUrl = fatoryUploadQuestionImageUseCase();

  const imageUrl = await uploadQuestionImageUrl.execute({
    file,
    customerId,
    ...dataBody,
  });

  return httpResponse(res, 200, { message: 'Imagem enviada com sucesso!', imageUrl });
}
