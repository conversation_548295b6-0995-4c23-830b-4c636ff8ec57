import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { ExternalListQuestionsGroupsSchema } from '../../../schema/questionsGroups.schema';
import { factoryListQuestionGroupUseCase } from '../../../use-case/factories/questionsGroups.factory';

export async function ExternalListQuestionGroupController(req: Request, res: Response) {
  const { customerId, ...filters } = ExternalListQuestionsGroupsSchema.parse(req.query);

  const listQuestionGroupUseCase = factoryListQuestionGroupUseCase();

  const questionsGroups = await listQuestionGroupUseCase.execute({
    customerId,
    ...filters,
    isExternal: true,
  });

  return httpResponse(res, 200, {
    message: 'Grupos de questões listados com sucesso',
    ...questionsGroups,
  });
}
