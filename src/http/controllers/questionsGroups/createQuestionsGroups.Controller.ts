import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { createQuestionGroupSchema } from '../../../schema/questionsGroups.schema';
import { factoryCreateQuestionGroupUseCase } from '../../../use-case/factories/questionsGroups.factory';

export async function createQuestionGroupController(req: Request, res: Response) {
  const { name } = createQuestionGroupSchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);

  const createQuestionGroupUseCase = factoryCreateQuestionGroupUseCase();

  const groupCreated = await createQuestionGroupUseCase.execute({ name, customerId });

  return httpResponse(res, 201, {
    message: 'Grupo de questões criado com sucesso!',
    data: groupCreated,
  });
}
