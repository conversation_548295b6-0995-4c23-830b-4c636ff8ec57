import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryDeleteQuestionGroupUseCase } from '../../../use-case/factories/questionsGroups.factory';

export async function deleteQuestionGroupController(req: Request, res: Response) {
  const { id } = genericIdSchema.parse(req.params);
  const { customerId } = customerIdSchema.parse(req.user);

  const deleteQuestionGroupUseCase = factoryDeleteQuestionGroupUseCase();

  const result = await deleteQuestionGroupUseCase.execute({ id, customerId });

  return httpResponse(res, 200, {
    message: 'Grupo de questões deletado com sucesso.',
    ...result,
  });
}
