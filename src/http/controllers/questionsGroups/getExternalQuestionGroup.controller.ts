import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import {
  genericStringCustomerIdchema,
  genericStringIdSchema,
} from '../../../schema/generic.schema';
import { factoryGetExternalQuestionGroupUseCase } from '../../../use-case/factories/questionsGroups.factory';

export async function getExternalQuestionGroupController(req: Request, res: Response) {
  const { id } = genericStringIdSchema.parse(req.params);
  const { customerId } = genericStringCustomerIdchema.parse(req.query);

  const getExternalQuestionGroupUseCase = factoryGetExternalQuestionGroupUseCase();

  const result = await getExternalQuestionGroupUseCase.execute({
    id,
    customerId,
  });

  return httpResponse(res, 200, {
    message: 'Grupo de questões obtido com sucesso',
    ...result,
  });
}
