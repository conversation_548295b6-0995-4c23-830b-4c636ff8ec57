import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { questionIdSchema } from '../../../schema/questionsGroups.schema';
import { factoryListQuestionsGroupsUseCase } from '../../../use-case/factories/questionsGroups.factory';

export async function getQuestionsGroupsWithAccessController(req: Request, res: Response) {
  const { questionId } = questionIdSchema.parse(req.params);
  const { customerId } = customerIdSchema.parse(req.user);

  const listQuestionsGroupsUseCase = factoryListQuestionsGroupsUseCase();
  const questionsGroups = await listQuestionsGroupsUseCase.execute({
    customerId,
    questionId: questionId as string,
  });

  return httpResponse(res, 200, {
    message: 'Grupos de questões listados com sucesso!',
    questionsGroups,
  });
}
