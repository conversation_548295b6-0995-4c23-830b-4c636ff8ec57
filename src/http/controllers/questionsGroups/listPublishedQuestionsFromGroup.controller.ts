import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { questionGroupIdSchema } from '../../../schema/questionsGroups.schema';
import { factoryListPublishedQuestionsFromGroupUseCase } from '../../../use-case/factories/questionsGroups.factory';

export async function listPublishedQuestionsFromGroupController(req: Request, res: Response) {
  const { questionGroupId } = questionGroupIdSchema.parse(req.params);
  const { customerId } = customerIdSchema.parse(req.user);

  const listPublishedQuestionsFromGroupUseCase = factoryListPublishedQuestionsFromGroupUseCase();

  const result = await listPublishedQuestionsFromGroupUseCase.execute({
    questionGroupId,
    customerId,
  });

  return httpResponse(res, 200, {
    message: 'Questões listadas com sucesso',
    ...result,
  });
}
