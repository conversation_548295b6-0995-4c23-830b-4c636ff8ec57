import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { listQuestionsGroupsSchema } from '../../../schema/questionsGroups.schema';
import { factoryListQuestionGroupWithCountUseCase } from '../../../use-case/factories/questionsGroups.factory';

export async function ListQuestionsGroupWithCountController(req: Request, res: Response) {
  const { customerId } = customerIdSchema.parse(req.user);
  const { ...filters } = listQuestionsGroupsSchema.parse(req.query);

  const listQuestionGroupWithCountUseCase = factoryListQuestionGroupWithCountUseCase();

  const questionsGroups = await listQuestionGroupWithCountUseCase.execute({
    customerId,
    ...filters,
  });

  return httpResponse(res, 200, {
    message: 'Grupos de questões listados com sucesso',
    ...questionsGroups,
  });
}
