import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { listQuestionsGroupsSchema } from '../../../schema/questionsGroups.schema';
import { factoryListQuestionGroupUseCase } from '../../../use-case/factories/questionsGroups.factory';

export async function ListQuestionGroupController(req: Request, res: Response) {
  const { customerId } = customerIdSchema.parse(req.user);

  const filters = listQuestionsGroupsSchema.parse(req.query);

  const listQuestionGroupUseCase = factoryListQuestionGroupUseCase();

  const questionsGroups = await listQuestionGroupUseCase.execute({ customerId, ...filters });

  return httpResponse(res, 200, {
    message: 'Grupos de questões listados com sucesso',
    ...questionsGroups,
  });
}
