import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { updateQuestionGroupSchema } from '../../../schema/questionsGroups.schema';
import { factoryUpdateQuestionGroupUseCase } from '../../../use-case/factories/questionsGroups.factory';

export async function updateQuestionGroupController(req: Request, res: Response) {
  const { name } = updateQuestionGroupSchema.parse(req.body);
  const { id } = genericIdSchema.parse(req.params);
  const { customerId } = customerIdSchema.parse(req.user);

  const createQuestionGroupUseCase = factoryUpdateQuestionGroupUseCase();

  const updatedQuestionGroup = await createQuestionGroupUseCase.execute({ name, customerId, id });

  return httpResponse(res, 201, {
    message: 'Grupo de questões atualizado com sucesso!',
    ...updatedQuestionGroup,
  });
}
