import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { questionGroupAccessIdentifierSchema } from '../../../schema/questionsGroupsAccess.schema';
import { factoryManageQuestionGroupAccessUseCase } from '../../../use-case/factories/questionsGroupsAccess.factory';

export async function manageQuestionGroupAccessController(req: Request, res: Response) {
  const requestData = questionGroupAccessIdentifierSchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);

  const manageQuestionGroupAccessUseCase = factoryManageQuestionGroupAccessUseCase();

  const questionGroupAccess = await manageQuestionGroupAccessUseCase.execute({
    ...requestData,
    customerId,
  });

  return httpResponse(res, 200, { ...questionGroupAccess });
}
