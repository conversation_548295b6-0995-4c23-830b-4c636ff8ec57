import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericSchemaCustomerIdAndId } from '../../../schema/generic.schema';
import { createReportSchema } from '../../../schema/reports.schema';
import { factoryCreateQuestionReportUseCase } from '../../../use-case/factories/reports.factory';

export async function createQuestionReportController(req: Request, res: Response) {
  const reportData = createReportSchema.parse(req.body);
  const { id: userId, customerId } = genericSchemaCustomerIdAndId.parse(req.user);
  const headers = req.headers;

  const createQuestionReportUseCase = factoryCreateQuestionReportUseCase();

  const report = await createQuestionReportUseCase.execute({
    ...reportData,
    userId,
    customerId,
    headers,
  });

  return httpResponse(res, 201, { message: 'Reporte enviado com sucesso!', report });
}
