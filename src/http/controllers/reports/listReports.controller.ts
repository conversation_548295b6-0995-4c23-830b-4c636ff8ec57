import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { IReportListParams } from '../../../model/DTO/IReport.dto';
import { customerIdSchema } from '../../../schema/customers.schema';
import { listReportsSchema } from '../../../schema/reports.schema';
import { factoryListReportsUseCase } from '../../../use-case/factories/reports.factory';

export async function listReportsController(req: Request, res: Response): Promise<Response> {
  const { customerId } = customerIdSchema.parse(req.user);
  const filters = listReportsSchema.parse(req.query);

  const listReportsUseCase = factoryListReportsUseCase();
  const params: IReportListParams = {
    customerId,
    ...filters,
  };

  const reports = await listReportsUseCase.execute(params);

  return httpResponse(res, 200, {
    message: 'Reportes listados com sucesso',
    ...reports,
  });
}
