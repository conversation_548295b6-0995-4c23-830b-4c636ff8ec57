import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericSchemaCustomerIdAndId } from '../../../schema/generic.schema';
import { createSimulatedTemplateSchema } from '../../../schema/simulatedTemplates.schema';
import { factoryCreateSimulatedTemplateUseCase } from '../../../use-case/factories/simulatedTemplates.factory';

export async function createSimulatedTemplateController(req: Request, res: Response) {
  const { customerId, id } = genericSchemaCustomerIdAndId.parse(req.user);
  const body = createSimulatedTemplateSchema.parse(req.body);

  const createSimulatedTemplateUseCase = factoryCreateSimulatedTemplateUseCase();

  const result = await createSimulatedTemplateUseCase.execute({
    ...body,
    customerId,
    id,
  });

  return httpResponse(res, 201, {
    message: 'Template de simulado criado com sucesso',
    ...result,
  });
}
