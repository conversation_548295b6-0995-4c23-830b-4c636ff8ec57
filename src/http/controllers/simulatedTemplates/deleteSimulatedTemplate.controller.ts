import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryDeleteSimulatedTemplateUseCase } from '../../../use-case/factories/simulatedTemplates.factory';

export async function deleteSimulatedTemplateController(req: Request, res: Response) {
  const { id } = genericIdSchema.parse(req.params);
  const { id: userId } = genericIdSchema.parse(req.user);

  const useCase = factoryDeleteSimulatedTemplateUseCase();

  const deletedTemplate = await useCase.execute({
    id,
    userId,
  });

  return httpResponse(res, 200, {
    message: 'Template de simulado excluído com sucesso',
    deletedTemplate,
  });
}
