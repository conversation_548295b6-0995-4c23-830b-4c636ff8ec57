import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import {
  genericSchemaCustomerIdAndId,
  genericSchemaQuestionGroupId,
} from '../../../schema/generic.schema';
import { factoryGetSimulatedTemplatesCountUseCase } from '../../../use-case/factories/simulatedTemplates.factory';

export async function getSimulatedTemplatesCountController(req: Request, res: Response) {
  const { customerId, id } = genericSchemaCustomerIdAndId.parse(req.user);

  const { questionGroupId } = genericSchemaQuestionGroupId.parse(req.query);

  const useCase = factoryGetSimulatedTemplatesCountUseCase();
  const count = await useCase.execute({
    id,
    customerId,
    questionGroupId,
  });

  return httpResponse(res, 200, {
    count,
  });
}
