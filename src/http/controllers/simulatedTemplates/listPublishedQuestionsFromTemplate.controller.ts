import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericIdSchema, genericSchemaCustomerIdAndId } from '../../../schema/generic.schema';
import { factoryListPublishedQuestionsFromTemplateUseCase } from '../../../use-case/factories/simulatedTemplates.factory';

export async function listPublishedQuestionsFromTemplateController(req: Request, res: Response) {
  const { id: templateId } = genericIdSchema.parse(req.params);
  const { customerId, id: userId } = genericSchemaCustomerIdAndId.parse(req.user);

  const useCase = factoryListPublishedQuestionsFromTemplateUseCase();

  const templateDetails = await useCase.execute({
    templateId,
    userId,
    customerId,
  });

  return httpResponse(res, 200, {
    message: 'Questões do template listadas com sucesso',
    ...templateDetails,
  });
}
