import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import {
  genericSchemaCustomerIdAndId,
  genericSchemaQuestionGroupId,
} from '../../../schema/generic.schema';
import { factoryListSimulatedTemplatesUseCase } from '../../../use-case/factories/simulatedTemplates.factory';

export async function listSimulatedTemplatesController(req: Request, res: Response) {
  const { customerId, id } = genericSchemaCustomerIdAndId.parse(req.user);
  const { questionGroupId } = genericSchemaQuestionGroupId.parse(req.query);

  const listSimulatedTemplatesUseCase = factoryListSimulatedTemplatesUseCase();

  const result = await listSimulatedTemplatesUseCase.execute({
    id,
    customerId,
    questionGroupId,
  });

  return httpResponse(res, 200, {
    message: 'Templates de simulados listados com sucesso',
    ...result,
  });
}
