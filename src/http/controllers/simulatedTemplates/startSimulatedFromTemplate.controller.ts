import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryStartSimulatedFromTemplateUseCase } from '../../../use-case/factories/simulatedTemplates.factory';

export async function startSimulatedFromTemplateController(req: Request, res: Response) {
  const { id: templateId } = genericIdSchema.parse(req.params);
  const { id: userId } = genericIdSchema.parse(req.user);

  const startSimulatedFromTemplateUseCase = factoryStartSimulatedFromTemplateUseCase();

  const result = await startSimulatedFromTemplateUseCase.execute({
    templateId,
    userId,
  });

  return httpResponse(res, 201, {
    message: 'Simulado iniciado com sucesso',
    simulated: result,
  });
}
