import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericIdSchema } from '../../../schema/generic.schema';
import { questionSimulatedAnsweredSchema } from '../../../schema/questionSimulated.schema';
import { answerQuestionSimulatedSchema } from '../../../schema/simulateds.schema';
import { factoryAnswerQuestionSimulatedUseCase } from '../../../use-case/factories/questionsSimulated.factory';

export async function answerQuestionSimulatedController(
  req: Request,
  res: Response
): Promise<Response> {
  const { simulatedAccessId, questionSimulatedId } = questionSimulatedAnsweredSchema.parse(
    req.params
  );
  const { id: userId } = genericIdSchema.parse(req.user);

  const { alternativeId } = answerQuestionSimulatedSchema.parse(req.body);

  const answerQuestionSimulatedUseCase = factoryAnswerQuestionSimulatedUseCase();

  const answered = await answerQuestionSimulatedUseCase.execute({
    simulatedAccessId,
    questionSimulatedId,
    userId,
    alternativeId,
  });

  return httpResponse(res, 200, {
    message: 'Resposta registrada com sucesso',
    answered,
  });
}
