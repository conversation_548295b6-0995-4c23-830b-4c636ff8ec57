import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { createExclusiveSimulatedSchema } from '../../../schema/simulateds.schema';
import { factoryCreateExclusiveSimulatedUseCase } from '../../../use-case/factories/simulateds.factory';

export async function createExclusiveSimulatedController(
  req: Request,
  res: Response
): Promise<Response> {
  const body = createExclusiveSimulatedSchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);

  const createExclusiveSimulatedUseCase = factoryCreateExclusiveSimulatedUseCase();

  const result = await createExclusiveSimulatedUseCase.execute({
    ...body,
    customerId,
  });

  return httpResponse(res, 201, {
    message: 'Simulado exclusivo criado com sucesso',
    ...result,
  });
}
