import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericSchemaCustomerIdAndId } from '../../../schema/generic.schema';
import { finishSimulatedParamsSchema } from '../../../schema/simulatedAccess.schema';
import { finishSimulatedBodySchema } from '../../../schema/simulateds.schema';
import { factoryFinishSimulatedUseCase } from '../../../use-case/factories/simulateds.factory';

export async function finishSimulatedController(req: Request, res: Response): Promise<Response> {
  const { simulatedAccessId } = finishSimulatedParamsSchema.parse(req.params);
  const { simulatedId } = finishSimulatedBodySchema.parse(req.body);
  const { id: userId, customerId } = genericSchemaCustomerIdAndId.parse(req.user);

  const finishSimulatedUseCase = factoryFinishSimulatedUseCase();
  const result = await finishSimulatedUseCase.execute({
    simulatedAccessId,
    userId,
    simulatedId,
    customerId,
  });

  return httpResponse(res, 200, {
    message: 'Simulado finalizado com sucesso',
    ...result,
  });
}
