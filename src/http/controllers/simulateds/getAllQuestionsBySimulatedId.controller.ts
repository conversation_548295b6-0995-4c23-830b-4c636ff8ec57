import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { simulatedIdSchema } from '../../../schema/simulateds.schema';
import { factoryGetAllQuestionsBySimulatedIdUseCase } from '../../../use-case/factories/simulateds.factory';

export async function getAllQuestionsBySimulatedIdController(
  req: Request,
  res: Response
): Promise<Response> {
  const { simulatedId } = simulatedIdSchema.parse(req.params);
  const getAllQuestionsBySimulatedIdUseCase = factoryGetAllQuestionsBySimulatedIdUseCase();
  const simulatedQuestions = await getAllQuestionsBySimulatedIdUseCase.execute({
    simulatedId,
  });
  return httpResponse(res, 200, simulatedQuestions);
}
