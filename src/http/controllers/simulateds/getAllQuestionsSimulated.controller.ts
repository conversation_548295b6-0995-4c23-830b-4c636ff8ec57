import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { factoryGetAllQuestionsSimulatedUseCase } from '../../../use-case/factories/questionsSimulated.factory';

export async function getAllQuestionsSimulatedController(
  req: Request,
  res: Response
): Promise<Response> {
  const { simulatedId, userId } = req.params;

  const getSimulatedQuestionsUseCase = factoryGetAllQuestionsSimulatedUseCase();
  const simulatedQuestions = await getSimulatedQuestionsUseCase.getAllQuestions(
    simulatedId,
    userId
  );

  return httpResponse(res, 200, simulatedQuestions);
}
