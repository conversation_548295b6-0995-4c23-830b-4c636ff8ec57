import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { simulatedIdSchema } from '../../../schema/simulateds.schema';
import { factoryGetExclusiveSimulatedStatsUseCase } from '../../../use-case/factories/simulateds.factory';

export async function getExclusiveSimulatedStatsController(req: Request, res: Response) {
  const { simulatedId } = simulatedIdSchema.parse(req.params);

  const useCase = factoryGetExclusiveSimulatedStatsUseCase();

  const stats = await useCase.execute(simulatedId);
  return httpResponse(res, 200, {
    message: 'Estatísticas do simulado exclusivo',
    stats,
  });
}
