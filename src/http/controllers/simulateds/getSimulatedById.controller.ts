import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { factoryGetSimulatedByIdUseCase } from '../../../use-case/factories/simulateds.factory';

export async function getSimulatedByIdController(req: Request, res: Response): Promise<Response> {
  const { simulatedId } = req.params;

  console.log('simulatedId:', simulatedId);

  const getSimulatedByIdUseCase = factoryGetSimulatedByIdUseCase();
  const simulatedQuestions = await getSimulatedByIdUseCase.getSimulatedById(simulatedId);

  return httpResponse(res, 200, simulatedQuestions);
}
