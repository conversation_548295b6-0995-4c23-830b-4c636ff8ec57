import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericIdSchema, genericSchemaSimulatedAccessId } from '../../../schema/generic.schema';
import { factoryGetSimulatedWithQuestionsByIdUseCase } from '../../../use-case/factories/simulatedsAccess.factory';

export async function getSimulatedWithQuestionsByIdController(
  req: Request,
  res: Response
): Promise<Response> {
  const { simulatedAccessId } = genericSchemaSimulatedAccessId.parse(req.params);
  const { id: userId } = genericIdSchema.parse(req.user);

  const getSimulatedWithQuestionsUseCase = factoryGetSimulatedWithQuestionsByIdUseCase();

  const simulatedWithQuestions = await getSimulatedWithQuestionsUseCase.execute(
    simulatedAccessId,
    userId
  );

  return httpResponse(res, 200, simulatedWithQuestions);
}
