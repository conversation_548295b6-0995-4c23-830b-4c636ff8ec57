import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import {
  linkQuestionsToSimulatedBodySchema,
  linkQuestionsToSimulatedParamsSchema,
} from '../../../schema/simulateds.schema';
import { factoryLinkQuestionsToSimulatedUseCase } from '../../../use-case/factories/simulateds.factory';

export async function linkQuestionsToSimulatedController(
  req: Request,
  res: Response
): Promise<Response> {
  const { simulatedId } = linkQuestionsToSimulatedParamsSchema.parse(req.params);
  const { questionIds } = linkQuestionsToSimulatedBodySchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);

  const linkQuestionsToSimulatedUseCase = await factoryLinkQuestionsToSimulatedUseCase();

  const result = await linkQuestionsToSimulatedUseCase.execute({
    simulatedId,
    questionIds,
    customerId,
  });

  return httpResponse(res, 201, {
    message: 'Questões vinculadas ao simulado com sucesso',
    ...result,
  });
}
