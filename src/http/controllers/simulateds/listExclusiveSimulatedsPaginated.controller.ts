import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { listExclusiveSimulatedsPaginatedSchema } from '../../../schema/simulateds.schema';
import { factoryListExclusiveSimulatedsPaginatedUseCase } from '../../../use-case/factories/simulateds.factory';

export async function listExclusiveSimulatedsPaginatedController(req: Request, res: Response) {
  const { customerId } = customerIdSchema.parse(req.user);

  const filters = listExclusiveSimulatedsPaginatedSchema.parse({ ...req.query });

  const useCase = factoryListExclusiveSimulatedsPaginatedUseCase();

  const result = await useCase.execute({ ...filters, customerId });
  return httpResponse(res, 200, {
    message: 'Simulados exclusivos listados com sucesso',
    ...result,
  });
}
