import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericSchemaCustomerIdAndId } from '../../../schema/generic.schema';
import { listUserExclusiveSimulatedsQuerySchema } from '../../../schema/simulateds.schema';
import { factoryListUserExclusiveSimulatedsUseCase } from '../../../use-case/factories/simulateds.factory';

export async function listUserExclusiveSimulatedsController(
  req: Request,
  res: Response
): Promise<Response> {
  const { id: userId, customerId } = genericSchemaCustomerIdAndId.parse(req.user);
  const filters = listUserExclusiveSimulatedsQuerySchema.parse(req.query);

  const listUserExclusiveSimulatedsUseCase = factoryListUserExclusiveSimulatedsUseCase();
  const userExclusiveSimulateds = await listUserExclusiveSimulatedsUseCase.execute({
    userId,
    customerId,
    ...filters,
  });

  return httpResponse(res, 200, userExclusiveSimulateds);
}
