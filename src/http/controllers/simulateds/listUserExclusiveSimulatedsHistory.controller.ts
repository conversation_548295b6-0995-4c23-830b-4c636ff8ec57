import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericSchemaCustomerIdAndId } from '../../../schema/generic.schema';
import { courseIdRequiredSchema } from '../../../schema/users.schema';
import { factoryListUserExclusiveSimulatedsUserHistoryUseCase } from '../../../use-case/factories/simulateds.factory';

export async function listUserExclusiveSimulatedsUserHistoryController(
  req: Request,
  res: Response
): Promise<Response> {
  const { id: userId, customerId } = genericSchemaCustomerIdAndId.parse(req.user);
  const { courseId } = courseIdRequiredSchema.parse(req.query);

  const listUserExclusiveSimulatedsUserHistoryUseCase =
    factoryListUserExclusiveSimulatedsUserHistoryUseCase();
  const userExclusiveSimulatedsHistory =
    await listUserExclusiveSimulatedsUserHistoryUseCase.execute({
      userId,
      customerId,
      courseId,
    });

  return httpResponse(res, 200, userExclusiveSimulatedsHistory);
}
