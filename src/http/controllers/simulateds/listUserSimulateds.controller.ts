import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { questionGroupIdSchema } from '../../../schema/questionsGroups.schema';
import { factoryListUserSimulatedsUseCase } from '../../../use-case/factories/simulateds.factory';

export async function listUserSimulatedsController(req: Request, res: Response): Promise<Response> {
  const { id: userId, customerId } = req.user;

  const { questionGroupId } = questionGroupIdSchema.parse(req.query);

  const listUserSimulatedsUseCase = factoryListUserSimulatedsUseCase();
  const userSimulateds = await listUserSimulatedsUseCase.execute({
    userId,
    customerId,
    questionGroupId,
  });

  return httpResponse(res, 200, userSimulateds);
}
