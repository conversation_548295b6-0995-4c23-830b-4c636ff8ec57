import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { simulatedIdSchema } from '../../../schema/simulateds.schema';
import { factoryListUsersInExclusiveSimulatedUseCase } from '../../../use-case/factories/simulateds.factory';

export async function listUsersInExclusiveSimulatedController(req: Request, res: Response) {
  const { simulatedId } = simulatedIdSchema.parse(req.params);

  const useCase = factoryListUsersInExclusiveSimulatedUseCase();
  const users = await useCase.execute(simulatedId);

  return httpResponse(res, 200, {
    message: 'Usuários do simulado exclusivo listados com sucesso',
    users,
  });
}
