import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import {
  simulatedClassesBodySchema,
  simulatedClassesParamsSchema,
} from '../../../schema/simulateds.schema';
import { factoryManageClassesInExclusiveSimulatedUseCase } from '../../../use-case/factories/simulateds.factory';

export async function manageClassesInExclusiveSimulatedController(req: Request, res: Response) {
  const { simulatedId } = simulatedClassesParamsSchema.parse(req.params);
  const { classIds } = simulatedClassesBodySchema.parse(req.body);

  const useCase = factoryManageClassesInExclusiveSimulatedUseCase();
  const result = await useCase.execute({ simulatedId, classIds });

  return httpResponse(res, 200, {
    message: 'Turmas gerenciadas com sucesso',
    ...result,
  });
}
