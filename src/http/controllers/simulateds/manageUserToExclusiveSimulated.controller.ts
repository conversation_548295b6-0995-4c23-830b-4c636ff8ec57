import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import {
  manageUserToExclusiveSimulatedBodySchema,
  manageUserToExclusiveSimulatedParamsSchema,
} from '../../../schema/simulateds.schema';
import { factoryManageUserToExclusiveSimulatedUseCase } from '../../../use-case/factories/simulateds.factory';

export async function manageUserToExclusiveSimulatedController(req: Request, res: Response) {
  const { simulatedId } = manageUserToExclusiveSimulatedParamsSchema.parse(req.params);
  const { data } = manageUserToExclusiveSimulatedBodySchema.parse(req.body);

  const useCase = factoryManageUserToExclusiveSimulatedUseCase();
  const result = await useCase.execute({ simulatedId, data });

  return httpResponse(res, 200, {
    message: 'Usuários adicionados ao simulado exclusivo com sucesso',
    ...result,
  });
}
