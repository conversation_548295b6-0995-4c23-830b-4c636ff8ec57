import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { StatusSimulatedEnum } from '../../../model/enums/statusSimulated.enum';
import { genericIdSchema } from '../../../schema/generic.schema';
import {
  pauseResumeSimulatedBodySchema,
  pauseResumeSimulatedParamsSchema,
} from '../../../schema/simulateds.schema';
import { factoryPauseResumeSimulatedUseCase } from '../../../use-case/factories/simulateds.factory';

export async function pauseResumeSimulatedController(
  req: Request,
  res: Response
): Promise<Response> {
  const { simulatedAccessId } = pauseResumeSimulatedParamsSchema.parse(req.params);

  const { action } = pauseResumeSimulatedBodySchema.parse(req.body);

  const { id: userId } = genericIdSchema.parse(req.user);

  const useCase = factoryPauseResumeSimulatedUseCase();

  const result = await useCase.execute({
    simulatedAccessId,
    userId,
    action,
  });

  return httpResponse(res, 200, {
    message:
      action === StatusSimulatedEnum.PAUSE
        ? 'Simulado pausado com sucesso'
        : 'Simulado retomado com sucesso',
    ...result,
  });
}
