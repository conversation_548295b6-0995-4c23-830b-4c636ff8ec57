import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import {
  reorderSimulatedQuestionsSchema,
  simulatedIdSchema,
} from '../../../schema/simulateds.schema';
import { factoryReorderSimulatedQuestionsUseCase } from '../../../use-case/factories/simulateds.factory';

export async function reorderSimulatedQuestionsController(
  req: Request,
  res: Response
): Promise<Response> {
  const { simulatedId } = simulatedIdSchema.parse(req.params);
  const { questions } = reorderSimulatedQuestionsSchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);

  const reorderSimulatedQuestionsUseCase = factoryReorderSimulatedQuestionsUseCase();

  const result = await reorderSimulatedQuestionsUseCase.execute({
    simulatedId,
    customerId,
    questions,
  });

  return httpResponse(res, 200, {
    message: 'Questões reordenadas com sucesso',
    ...result,
  });
}
