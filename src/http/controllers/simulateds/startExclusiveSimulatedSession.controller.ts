import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericIdSchema } from '../../../schema/generic.schema';
import { simulatedAccessIdSchema } from '../../../schema/simulateds.schema';
import { factoryStartExclusiveSimulatedSessionUseCase } from '../../../use-case/factories/simulateds.factory';

export async function startExclusiveSimulatedSessionController(
  req: Request,
  res: Response
): Promise<Response> {
  const { simulatedAccessId } = simulatedAccessIdSchema.parse(req.params);
  const { id: userId } = genericIdSchema.parse(req.user);

  const useCase = factoryStartExclusiveSimulatedSessionUseCase();
  const result = await useCase.execute({ simulatedAccessId, userId });

  return httpResponse(res, 201, {
    message: 'Sessão do simulado exclusivo iniciada com sucesso',
    ...result,
  });
}
