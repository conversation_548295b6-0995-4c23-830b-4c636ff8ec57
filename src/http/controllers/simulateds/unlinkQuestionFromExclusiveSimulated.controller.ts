import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { questionIdSchema } from '../../../schema/questions.schema';
import { simulatedIdSchema } from '../../../schema/simulateds.schema';
import { factoryUnlinkQuestionFromExclusiveSimulatedUseCase } from '../../../use-case/factories/simulateds.factory';

export async function unlinkQuestionFromExclusiveSimulatedController(
  req: Request,
  res: Response
): Promise<Response> {
  const { simulatedId } = simulatedIdSchema.parse(req.params);
  const { questionId } = questionIdSchema.parse(req.params);
  const { customerId } = customerIdSchema.parse(req.user);

  const useCase = factoryUnlinkQuestionFromExclusiveSimulatedUseCase();
  await useCase.execute({ simulatedId, questionId, customerId });

  return httpResponse(res, 200, {
    message: 'Questão desvinculada do simulado com sucesso',
  });
}
