import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { updateExclusiveSimulatedSchema } from '../../../schema/simulateds.schema';
import { factoryUpdateExclusiveSimulatedUseCase } from '../../../use-case/factories/simulateds.factory';

export async function updateExclusiveSimulatedController(
  req: Request,
  res: Response
): Promise<Response> {
  const body = updateExclusiveSimulatedSchema.parse(req.body);
  const { id } = genericIdSchema.parse(req.params);

  const updateExclusiveSimulatedUseCase = factoryUpdateExclusiveSimulatedUseCase();

  const { customerId } = customerIdSchema.parse(req.user);

  const result = await updateExclusiveSimulatedUseCase.execute(id, body, customerId);

  return httpResponse(res, 200, {
    message: 'Simulado exclusivo atualizado com sucesso',
    result,
  });
}
