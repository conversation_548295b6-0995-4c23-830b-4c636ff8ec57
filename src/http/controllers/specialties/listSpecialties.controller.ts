import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { listSpecialtiesSchema } from '../../../schema/specialties.schema';
import { factoryListSpecialtiesUseCase } from '../../../use-case/factories/specialties.factory';

export async function listSpecialtiesController(req: Request, res: Response) {
  const filters = listSpecialtiesSchema.parse(req.query);
  const usecase = factoryListSpecialtiesUseCase();

  const result = await usecase.execute(filters);

  return httpResponse(res, 200, { message: 'Especialidades listadas com sucesso', data: result });
}
