import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { addStudentsByEmailSchema } from '../../../schema/students.schema';
import { factoryAddStudentsByEmailUseCase } from '../../../use-case/factories/users.factory';

export async function addStudentsByEmailController(req: Request, res: Response) {
  const { customerId } = customerIdSchema.parse(req.user);
  const { courseId, emails, classId } = addStudentsByEmailSchema.parse(req.body);
  const useCase = factoryAddStudentsByEmailUseCase();
  const result = await useCase.execute({ courseId, customerId, emails, classId });
  return httpResponse(res, 200, result);
}
