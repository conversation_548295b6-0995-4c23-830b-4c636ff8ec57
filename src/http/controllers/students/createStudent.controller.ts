import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { createStudentSchema } from '../../../schema/createStudent.schema';
import { factoryCreateStudentUseCase } from '../../../use-case/factories/users.factory';

export async function createStudentController(req: Request, res: Response) {
  const body = createStudentSchema.parse(req.body);
  const useCase = factoryCreateStudentUseCase();
  const result = await useCase.execute(body);
  return httpResponse(res, 201, { message: 'Aluno cadastrado com sucesso', users: result });
}
