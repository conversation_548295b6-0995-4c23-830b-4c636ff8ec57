import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { listStudentsQuerySchema } from '../../../schema/students.schema';
import { factoryListStudentsUseCase } from '../../../use-case/factories/users.factory';

export async function listStudentsController(req: Request, res: Response) {
  const { customerId } = customerIdSchema.parse(req.user);
  const { orderByColumn, orderByDirection, courseId } = listStudentsQuerySchema.parse(req.query);

  const useCase = factoryListStudentsUseCase();
  const students = await useCase.execute({
    customerId,
    orderByColumn,
    orderByDirection,
    courseId,
  });

  return httpResponse(res, 200, { message: 'Alunos listados com sucesso', users: students });
}
