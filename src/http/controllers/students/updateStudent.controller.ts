import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { updateStudentBodySchema, userIdRequeridSchema } from '../../../schema/users.schema';
import { factoryUpdateStudentUseCase } from '../../../use-case/factories/users.factory';

export async function updateStudentController(req: Request, res: Response) {
  const { userId } = userIdRequeridSchema.parse(req.params);
  const data = updateStudentBodySchema.parse(req.body);

  const useCase = factoryUpdateStudentUseCase();
  const updatedUser = await useCase.execute(userId, data);

  return httpResponse(res, 200, { message: 'Aluno atualizado com sucesso', data: updatedUser });
}
