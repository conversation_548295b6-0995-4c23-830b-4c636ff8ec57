import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { addSubcategoryBodySchema } from '../../../schema/subcategories.schema';
import { factoryAddSubcategoryUseCase } from '../../../use-case/factories/subcategories.factory';

export const addSubcategoryController = async (req: Request, res: Response): Promise<Response> => {
  const { categoryId } = req.params;
  const { name } = addSubcategoryBodySchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);

  const addSubcategoryToCategoryUseCase = factoryAddSubcategoryUseCase();

  const addedSubcategory = await addSubcategoryToCategoryUseCase.execute({
    categoryId,
    name,
    customerId,
  });

  return httpResponse(res, 201, {
    message: 'Subcategoria adicionada com sucesso!',
    ...addedSubcategory,
  });
};
