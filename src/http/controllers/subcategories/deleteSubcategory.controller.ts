import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { factoryDeleteSubcategoryUseCase } from '../../../use-case/factories/subcategories.factory';

export async function deleteSubcategoryController(req: Request, res: Response): Promise<Response> {
  const { id } = req.params;
  const { customerId } = customerIdSchema.parse(req.user);

  const deleteSubcategoryUseCase = factoryDeleteSubcategoryUseCase();
  const result = await deleteSubcategoryUseCase.execute({ id, customerId });

  return httpResponse(res, 200, { message: 'Subcategoria excluida com sucesso', ...result });
}
