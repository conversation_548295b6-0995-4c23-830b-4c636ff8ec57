import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { paramsSchema } from '../../../schema/categories.schema';
import { customerIdSchema } from '../../../schema/customers.schema';
import { factoryUpdateSubcategoryUseCase } from '../../../use-case/factories/subcategories.factory';
import { updateSubcategoryBodySchema } from './../../../schema/categories.schema';
export async function updateSubcategoryController(req: Request, res: Response): Promise<Response> {
  const { id } = paramsSchema.parse(req.params);
  const bodyData = updateSubcategoryBodySchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);

  const editSubcategoryUseCase = factoryUpdateSubcategoryUseCase();
  const updatedSubcategory = await editSubcategoryUseCase.execute({ ...bodyData, id, customerId });
  return httpResponse(res, 200, {
    message: 'Subcategoria atualizada com sucesso',
    updatedSubcategory,
  });
}
