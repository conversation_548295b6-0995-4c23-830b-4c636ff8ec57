import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { createUserSchema } from '../../../schema/users';
import { factoryCreateInitialUserUseCase } from '../../../use-case/factories/users.factory';

export async function createInitialUserController(req: Request, res: Response) {
  const data = req.body;

  const caseCreateInitialUser = factoryCreateInitialUserUseCase();
  createUserSchema.parse(data);

  const result = await caseCreateInitialUser.execute(data);

  httpResponse(res, 200, result);
}
