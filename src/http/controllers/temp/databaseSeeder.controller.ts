import bcrypt from 'bcryptjs';
import { randomUUID } from 'crypto';
import 'dotenv/config';
import { Request, Response } from 'express';
import fs from 'fs';
import path from 'path';

import { knexInstance } from '../../../config/connectionDatabase.config';
import { env } from '../../../env';
import { httpResponse } from '../../../helpers/httpResponse';
import { IAlternative } from '../../../model/IAlternative';
import { ICategory } from '../../../model/ICategory';
import { IClasses } from '../../../model/IClasses';
import { ICustomer } from '../../../model/ICustomer';
import { IDiscipline } from '../../../model/IDiscipline';
import { IExam } from '../../../model/IExam';
import { IInstitution } from '../../../model/IInstitution';
import { IQuestion } from '../../../model/IQuestion';
import { IQuestionGroup } from '../../../model/IQuestionGroup';
import { ISubCategory } from '../../../model/ISubcategory';
import { IUser } from '../../../model/IUser';
import { KnexAlternativesRepository } from '../../../repositories/knex/alternatives.repositories';
import { KnexCategoriesRepository } from '../../../repositories/knex/categories.repositories';
import { KnexClassesRepository } from '../../../repositories/knex/classes.repositories';
import { KnexCustomerRepository } from '../../../repositories/knex/customers.repositories';
import { KnexDisciplinesRepository } from '../../../repositories/knex/disciplines.repositories';
import { KnexDisciplinesCategoriesRepository } from '../../../repositories/knex/disciplinesCategoriesAccess.repositories';
import { KnexDisciplinesSubcategoriesAccessRepository } from '../../../repositories/knex/disciplinesSubcategoriesAccess.repositories';
import { KnexExamsRepository } from '../../../repositories/knex/exams.repositories';
import { KnexExamsAccessRepository } from '../../../repositories/knex/examsAccess.repositories';
import { KnexExamsClassesRepository } from '../../../repositories/knex/examsClasses.repositories';
import { KnexExamsQuestionsRepository } from '../../../repositories/knex/examsQuestions.repositories';
import { KnexInstitutionsRepository } from '../../../repositories/knex/institutions.repositories';
import { KnexQuestionsRepository } from '../../../repositories/knex/questions.repositories';
import { KnexQuestionsGroupsRepository } from '../../../repositories/knex/questionsGroups.repositories';
import { KnexQuestionsGroupsAccessRepository } from '../../../repositories/knex/questionsGroupsAccess.repositories';
import { KnexQuestionsTypesRepository } from '../../../repositories/knex/questionsTypes.repositories';
import { KnexSubcategoriesRepository } from '../../../repositories/knex/subcategories.repositories';
import { KnexUserRepository } from '../../../repositories/knex/users.repositories';
import { KnexUsersTypesRepository } from '../../../repositories/knex/userTypes.repositories';

// Repositórios
const customerRepository = new KnexCustomerRepository();
const questionTypesRepository = new KnexQuestionsTypesRepository();
const userTypesRepository = new KnexUsersTypesRepository();
const disciplineRepository = new KnexDisciplinesRepository();
const categoriesRepository = new KnexCategoriesRepository();
const subcategoriesRepository = new KnexSubcategoriesRepository();
const questionsRepository = new KnexQuestionsRepository();
const alternativesRepository = new KnexAlternativesRepository();
const disciplinesCategoriesRepository = new KnexDisciplinesCategoriesRepository();
const disciplinesSubcategoriesRepository = new KnexDisciplinesSubcategoriesAccessRepository();
const questionsGroupsRepository = new KnexQuestionsGroupsRepository();
const questionsGroupsAccessRepository = new KnexQuestionsGroupsAccessRepository();
const institutionsRepository = new KnexInstitutionsRepository();
const examsRepository = new KnexExamsRepository();
const examsQuestionsRepository = new KnexExamsQuestionsRepository();
const classesRepository = new KnexClassesRepository();
const userRepository = new KnexUserRepository();
const examsClassesRepository = new KnexExamsClassesRepository();
const examsAccessRepository = new KnexExamsAccessRepository();

// Mapa global para armazenar nomes de categorias por ID
const categoryNamesMap = new Map<string, string>();

// Interfaces específicas para este controlador
interface IQuestionData {
  title: string;
  description?: string;
  difficulty?: string;
  type?: string;
  image_url?: string | null;
  explanation_video?: string | null;
  explanation_text?: string;
  explanation_image?: string | null;
  published?: boolean;
  status?: string;
  correct_text?: string;
  reference?: string;
  institution?: string;
  year?: number;
  alternatives?: IAlternative[];
}

// Usar o tipo Omit para garantir compatibilidade com IQuestion
type IQuestionInsert = Omit<IQuestion, 'id' | 'created_at' | 'updated_at' | 'deleted_at'>;

interface IAlternativeInsert {
  question_id: string;
  correct: boolean;
  option: string;
  description: string;
}

interface IQuestionGroupInsert {
  name: string;
  customer_id: string;
}

interface IQuestionGroupAccess {
  customer_id: string;
  question_id: string;
  question_group_id: string;
}

interface IDisciplineCategoryAccess {
  customer_id: string;
  discipline_id: string;
  category_id: string;
}

interface IDisciplineSubcategoryAccess {
  customer_id: string;
  discipline_id: string;
  category_id: string;
  subcategory_id: string;
}

interface IUserType {
  name: string;
  customer_id: string;
}

/**
 * DatabaseSeeder Controller
 *
 * Este controlador é responsável por popular o banco de dados com dados iniciais para desenvolvimento e testes.
 * Ele cria uma estrutura completa de dados relacionados, incluindo:
 *
 * - Instituições de ensino
 * - Exames/provas
 * - Disciplinas de desenvolvimento de software
 * - Categorias para cada disciplina
 * - Subcategorias para cada categoria
 * - Questões com alternativas
 * - Grupos de questões
 * - Vínculos entre questões e grupos
 * - Vínculos entre questões e exames
 *
 * IMPORTANTE:
 * - Este seeder só funciona em ambientes de desenvolvimento ('dev') ou teste ('test')
 * - Ele verifica se já existem questões no banco antes de executar, para evitar duplicação
 * - As questões são importadas de um arquivo JSON localizado em src/database/questions.json
 *
 * Fluxo de execução:
 * 1. Verifica ambiente e se o banco já foi populado
 * 2. Obtém o cliente inicial (definido em variáveis de ambiente)
 * 3. Cria instituições e exames
 * 4. Cria tipos de usuários
 * 5. Cria disciplinas, categorias e subcategorias
 * 6. Estabelece os relacionamentos entre disciplinas, categorias e subcategorias
 * 7. Importa questões do arquivo JSON
 * 8. Cria grupos de questões e vincula as questões aos grupos
 * 9. Vincula questões aos exames
 *
 * Para adicionar novos dados:
 * - Para novas disciplinas: modifique a função insertDevDisciplines()
 * - Para novas categorias: modifique a função insertDevCategories()
 * - Para novas subcategorias: modifique a função insertDevSubcategories()
 * - Para novos tipos de questões: adicione-os via seed de tipos de questões
 * - Para novas questões: adicione-as ao arquivo questions.json
 */
export async function populateDatabase(_req: Request, res: Response): Promise<Response> {
  try {
    // Verificar se estamos em ambiente de produção
    if (env.ENVIRONMENT !== 'dev' && env.ENVIRONMENT !== 'test') {
      return httpResponse(res, 403, {
        message: 'Esta operação não é permitida em ambiente de produção',
      });
    }

    // Verificar se o banco já foi populado anteriormente
    const existingQuestions = await questionsRepository.count();
    if (existingQuestions > 0) {
      return httpResponse(res, 409, {
        message: 'O banco de dados já foi populado anteriormente',
        count: existingQuestions,
      });
    }

    // Continuar com a população do banco de dados
    const initialCustomer = await getInitialCustomer();

    // Mapeando os tipos de questão
    const questionTypesMap = await getQuestionTypesMap();

    // Inserindo instituições (com customer_id null)
    const createdInstitutions = await insertInstitutions();

    // Inserindo exames para as instituições
    const createdExams = await insertExams(initialCustomer.id, createdInstitutions);

    // Inserindo tipos de usuários
    await insertUserTypes(initialCustomer.id);

    // Inserindo turmas
    const createdClasses = await insertDevClasses(initialCustomer.id);

    // Inserindo alunos e vinculando às turmas
    await insertDevStudents(initialCustomer.id, createdClasses);

    // Inserindo disciplinas de desenvolvimento de software
    const insertedDisciplines = await insertDevDisciplines(initialCustomer.id);

    // Inserindo categorias para cada disciplina
    const disciplineCategoriesMap = new Map<string, ICategory[]>();
    for (const discipline of insertedDisciplines) {
      const categories = await insertDevCategories(initialCustomer.id, discipline);
      disciplineCategoriesMap.set(discipline.id, categories);

      // Armazenar nomes de categorias no mapa global
      for (const category of categories) {
        categoryNamesMap.set(category.id, category.name);
      }
    }

    // Vinculando categorias às disciplinas (disciplines_categories_access)
    await createDisciplinesCategoriesAccess(initialCustomer.id, disciplineCategoriesMap);

    // Inserindo subcategorias para cada categoria
    const categorySubcategoriesMap = new Map<string, ISubCategory[]>();
    for (const [, categories] of disciplineCategoriesMap.entries()) {
      for (const category of categories) {
        const subcategories = await insertDevSubcategories(
          initialCustomer.id,
          category.id,
          category.name
        );
        categorySubcategoriesMap.set(category.id, subcategories);
      }
    }

    // Vinculando subcategorias às disciplinas (disciplines_subcategories_access)
    await createDisciplinesSubcategoriesAccess(
      initialCustomer.id,
      disciplineCategoriesMap,
      categorySubcategoriesMap
    );

    // Importar questões do arquivo JSON
    const questionsData = await importQuestionsFromJson();

    // Inserindo questões e alternativas do arquivo JSON
    const createdQuestions = await importQuestionsToDatabase(
      initialCustomer.id,
      insertedDisciplines,
      disciplineCategoriesMap,
      categorySubcategoriesMap,
      questionTypesMap,
      questionsData,
      createdInstitutions
    );

    // Criando grupos de questões para cada disciplina
    const createdGroups = await createQuestionGroups(initialCustomer.id, insertedDisciplines);

    // Vinculando questões aos grupos
    const groupAccessCount = await createQuestionGroupsAccess(
      initialCustomer.id,
      createdQuestions,
      createdGroups,
      insertedDisciplines
    );

    // Vinculando questões às provas
    const examQuestionsCount = await linkQuestionsToExams(createdQuestions, createdExams);

    // Vinculando provas às turmas e criando acessos para os alunos
    const examAccessCount = await createExamsClassesAndAccess(
      initialCustomer.id,
      createdExams,
      createdClasses
    );

    return httpResponse(res, 201, {
      message: 'Banco de dados populado com sucesso!',
      stats: {
        institutions: createdInstitutions.length,
        exams: createdExams.length,
        classes: createdClasses.length,
        disciplines: insertedDisciplines.length,
        categories: Array.from(disciplineCategoriesMap.values()).flat().length,
        subcategories: Array.from(categorySubcategoriesMap.values()).flat().length,
        questions: createdQuestions.length,
        disciplinesCategoriesAccess: Array.from(disciplineCategoriesMap.entries()).reduce(
          (acc, [, categories]) => acc + categories.length,
          0
        ),
        disciplinesSubcategoriesAccess: Array.from(categorySubcategoriesMap.entries()).reduce(
          (acc, [, subcategories]) => acc + subcategories.length,
          0
        ),
        questionGroups: createdGroups.length,
        questionGroupsAccess: groupAccessCount,
        examQuestionsCount,
        examAccessCount,
      },
    });
  } catch (error) {
    console.error('Erro ao popular banco de dados:', error);
    return httpResponse(res, 500, {
      message: 'Erro ao popular banco de dados',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Cria grupos de questões gerais e específicos para cada disciplina
 * @param customerId ID do cliente para associação
 * @param disciplines Lista de disciplinas para criar grupos específicos
 * @returns Lista de grupos criados
 */
async function createQuestionGroups(
  customerId: string,
  disciplines: IDiscipline[]
): Promise<IQuestionGroup[]> {
  console.log('Criando grupos de questões...');
  const groups: IQuestionGroupInsert[] = [];

  // Criar grupos gerais
  groups.push(
    { name: 'Questões Essenciais', customer_id: customerId },
    { name: 'Questões Avançadas', customer_id: customerId },
    { name: 'Questões para Iniciantes', customer_id: customerId }
  );

  // Criar grupos específicos para cada disciplina
  for (const discipline of disciplines) {
    groups.push(
      { name: `Fundamentos de ${discipline.name}`, customer_id: customerId },
      { name: `${discipline.name} Avançado`, customer_id: customerId }
    );
  }

  const createdGroups = await questionsGroupsRepository.insertAll(groups);
  console.log(`${createdGroups.length} grupos de questões criados`);

  return createdGroups;
}

/**
 * Vincula questões aos grupos criados, seguindo regras específicas:
 * - Questões Essenciais: 30% de todas as questões aleatoriamente
 * - Questões para Iniciantes: todas as questões fáceis
 * - Fundamentos de [Disciplina]: questões fáceis da disciplina
 * - [Disciplina] Avançado: questões médias da disciplina
 *
 * @param customerId ID do cliente
 * @param questions Lista de todas as questões
 * @param groups Grupos de questões criados
 * @param disciplines Lista de disciplinas
 * @returns Número total de vínculos criados
 */
async function createQuestionGroupsAccess(
  customerId: string,
  questions: IQuestion[],
  groups: IQuestionGroup[],
  disciplines: IDiscipline[]
): Promise<number> {
  console.log('Vinculando questões aos grupos...');
  const accessRecords: IQuestionGroupAccess[] = [];

  // Mapa para agrupar questões por disciplina
  const questionsByDiscipline = new Map<string, IQuestion[]>();
  for (const question of questions) {
    if (question.discipline_id) {
      if (!questionsByDiscipline.has(question.discipline_id)) {
        questionsByDiscipline.set(question.discipline_id, []);
      }
      const disciplineQuestions = questionsByDiscipline.get(question.discipline_id);
      if (disciplineQuestions) {
        disciplineQuestions.push(question);
      }
    }
  }

  // Mapa para agrupar questões por dificuldade
  const questionsByDifficulty: Record<string, IQuestion[]> = {
    easy: questions.filter((q) => q.difficulty === 'easy'),
    medium: questions.filter((q) => q.difficulty === 'medium'),
  };

  // Encontrar grupos por nome
  const findGroupByName = (name: string): IQuestionGroup | undefined =>
    groups.find((g) => g.name === name);

  // Grupo "Questões Essenciais" - adicionar 30% das questões aleatoriamente
  const essentialsGroup = findGroupByName('Questões Essenciais');
  if (essentialsGroup) {
    const randomQuestions = getRandomSubset(questions, Math.floor(questions.length * 0.3));
    for (const question of randomQuestions) {
      accessRecords.push({
        customer_id: customerId,
        question_id: question.id,
        question_group_id: essentialsGroup.id,
      });
    }
  }

  // Grupo "Questões para Iniciantes" - adicionar questões fáceis
  const beginnersGroup = findGroupByName('Questões para Iniciantes');
  if (beginnersGroup) {
    for (const question of questionsByDifficulty.easy) {
      accessRecords.push({
        customer_id: customerId,
        question_id: question.id,
        question_group_id: beginnersGroup.id,
      });
    }
  }

  // Grupo "Questões Avançadas" - adicionar questões médias
  const advancedGroup = findGroupByName('Questões Avançadas');
  if (advancedGroup) {
    for (const question of questionsByDifficulty.medium) {
      accessRecords.push({
        customer_id: customerId,
        question_id: question.id,
        question_group_id: advancedGroup.id,
      });
    }
  }

  // Grupos específicos por disciplina
  for (const discipline of disciplines) {
    const disciplineQuestions = questionsByDiscipline.get(discipline.id) || [];

    // Grupo "Fundamentos de [Disciplina]"
    const fundamentalsGroup = findGroupByName(`Fundamentos de ${discipline.name}`);
    if (fundamentalsGroup && disciplineQuestions.length > 0) {
      // Adicionar questões fáceis da disciplina
      const easyQuestions = disciplineQuestions.filter((q) => q.difficulty === 'easy');
      for (const question of easyQuestions) {
        accessRecords.push({
          customer_id: customerId,
          question_id: question.id,
          question_group_id: fundamentalsGroup.id,
        });
      }
    }

    const disciplineAdvancedGroup = findGroupByName(`${discipline.name} Avançado`);

    if (disciplineAdvancedGroup && disciplineQuestions.length > 0) {
      const mediumQuestions = disciplineQuestions.filter((q) => q.difficulty === 'medium');

      for (const question of mediumQuestions) {
        accessRecords.push({
          customer_id: customerId,
          question_id: question.id,
          question_group_id: disciplineAdvancedGroup.id,
        });
      }
    }
  }

  if (accessRecords.length > 0) {
    await questionsGroupsAccessRepository.insertAll(accessRecords);
    console.log(`${accessRecords.length} vínculos entre questões e grupos criados`);
  }

  return accessRecords.length;
}

function getRandomSubset<T>(array: T[], size: number): T[] {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, size);
}

function getRandomItem<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

async function createDisciplinesCategoriesAccess(
  customerId: string,
  disciplineCategoriesMap: Map<string, ICategory[]>
): Promise<void> {
  console.log('Criando vínculos entre disciplinas e categorias...');
  const accessRecords: IDisciplineCategoryAccess[] = [];

  for (const [disciplineId, categories] of disciplineCategoriesMap.entries()) {
    for (const category of categories) {
      accessRecords.push({
        customer_id: customerId,
        discipline_id: disciplineId,
        category_id: category.id,
      });
    }
  }

  if (accessRecords.length > 0) {
    await disciplinesCategoriesRepository.insertAll(accessRecords);
    console.log(`${accessRecords.length} vínculos entre disciplinas e categorias criados`);
  }
}

async function createDisciplinesSubcategoriesAccess(
  customerId: string,
  disciplineCategoriesMap: Map<string, ICategory[]>,
  categorySubcategoriesMap: Map<string, ISubCategory[]>
): Promise<void> {
  console.log('Criando vínculos entre disciplinas e subcategorias...');
  const accessRecords: IDisciplineSubcategoryAccess[] = [];

  for (const [disciplineId, categories] of disciplineCategoriesMap.entries()) {
    for (const category of categories) {
      const subcategories = categorySubcategoriesMap.get(category.id) || [];

      for (const subcategory of subcategories) {
        accessRecords.push({
          customer_id: customerId,
          discipline_id: disciplineId,
          category_id: category.id,
          subcategory_id: subcategory.id,
        });
      }
    }
  }

  if (accessRecords.length > 0) {
    await disciplinesSubcategoriesRepository.insertAll(accessRecords);
    console.log(`${accessRecords.length} vínculos entre disciplinas e subcategorias criados`);
  }
}

/**
 * Obtém o cliente inicial a partir do CNPJ definido nas variáveis de ambiente
 * @returns Cliente inicial
 * @throws Erro se o cliente não for encontrado
 */
async function getInitialCustomer(): Promise<ICustomer> {
  const taxNumber = env.FIRST_CUSTOMER_TAX_NUMBER;

  if (!taxNumber) {
    throw new Error('FIRST_CUSTOMER_TAX_NUMBER não definido no ambiente');
  }

  const firstCustomer = await customerRepository.getTaxNumber(taxNumber);

  if (!firstCustomer) {
    throw new Error('Cliente inicial não encontrado!');
  }
  return firstCustomer;
}

/**
 * Cria um mapa de tipos de questões (ID por tipo)
 * @returns Mapa de tipos de questões
 */
async function getQuestionTypesMap(): Promise<Record<string, string>> {
  const questionTypes = await questionTypesRepository.findAll();

  const typesMap: Record<string, string> = {};

  for (const type of questionTypes) {
    typesMap[type.type] = type.id;
  }

  return typesMap;
}

/**
 * Insere tipos de usuários para desenvolvimento de software
 * @param customerId ID do cliente
 * @returns Lista de tipos de usuários criados
 */
async function insertUserTypes(customerId: string): Promise<IUserType[]> {
  const userTypes: IUserType[] = [
    { name: 'Desenvolvedor Junior', customer_id: customerId },
    { name: 'Desenvolvedor Pleno', customer_id: customerId },
    { name: 'Desenvolvedor Senior', customer_id: customerId },
    { name: 'Tech Lead', customer_id: customerId },
    { name: 'Arquiteto de Software', customer_id: customerId },
  ];

  await userTypesRepository.insertAll(userTypes);
  return userTypes;
}

/**
 * Insere disciplinas relacionadas ao desenvolvimento de software
 * @param customerId ID do cliente
 * @returns Lista de disciplinas criadas
 */
async function insertDevDisciplines(customerId: string): Promise<IDiscipline[]> {
  const disciplines = [
    { name: 'Backend', code: 'BACKEND', customer_id: customerId },
    { name: 'Frontend', code: 'FRONTEND', customer_id: customerId },
    { name: 'DevOps', code: 'DEVOPS', customer_id: customerId },
    { name: 'Mobile', code: 'MOBILE', customer_id: customerId },
    { name: 'Banco de Dados', code: 'DATABASE', customer_id: customerId },
  ];

  return await disciplineRepository.insertAll(disciplines);
}

async function insertDevCategories(
  customerId: string,
  discipline: IDiscipline
): Promise<ICategory[]> {
  let categories: { name: string; customer_id: string }[] = [];

  switch (discipline.name) {
    case 'Backend':
      categories = [
        { name: 'Node.js', customer_id: customerId },
        { name: 'Python', customer_id: customerId },
        { name: 'Java', customer_id: customerId },
        { name: 'C#', customer_id: customerId },
      ];
      break;
    case 'Frontend':
      categories = [
        { name: 'JavaScript', customer_id: customerId },
        { name: 'React', customer_id: customerId },
        { name: 'Angular', customer_id: customerId },
        { name: 'Vue', customer_id: customerId },
      ];
      break;
    case 'DevOps':
      categories = [
        { name: 'Docker', customer_id: customerId },
        { name: 'Kubernetes', customer_id: customerId },
        { name: 'CI/CD', customer_id: customerId },
        { name: 'Cloud', customer_id: customerId },
      ];
      break;
    case 'Mobile':
      categories = [
        { name: 'React Native', customer_id: customerId },
        { name: 'Flutter', customer_id: customerId },
        { name: 'iOS', customer_id: customerId },
        { name: 'Android', customer_id: customerId },
      ];
      break;
    case 'Banco de Dados':
      categories = [
        { name: 'SQL', customer_id: customerId },
        { name: 'NoSQL', customer_id: customerId },
        { name: 'ORM', customer_id: customerId },
        { name: 'Data Modeling', customer_id: customerId },
      ];
      break;
    default:
      categories = [
        { name: 'Fundamentos', customer_id: customerId },
        { name: 'Avançado', customer_id: customerId },
        { name: 'Práticas Recomendadas', customer_id: customerId },
      ];
  }

  return await categoriesRepository.insertAll(categories);
}

async function insertDevSubcategories(
  customerId: string,
  categoryId: string,
  categoryName: string
): Promise<ISubCategory[]> {
  console.log(`Inserindo subcategorias para a categoria: ${categoryName}`);
  const subcategories: { name: string; category_id: string; customer_id: string }[] = [];

  switch (categoryName) {
    case 'Node.js':
      subcategories.push(
        { name: 'Express', category_id: categoryId, customer_id: customerId },
        { name: 'NestJS', category_id: categoryId, customer_id: customerId },
        { name: 'TypeScript', category_id: categoryId, customer_id: customerId }
      );
      break;
    case 'Python':
      subcategories.push(
        { name: 'Django', category_id: categoryId, customer_id: customerId },
        { name: 'Flask', category_id: categoryId, customer_id: customerId },
        { name: 'FastAPI', category_id: categoryId, customer_id: customerId }
      );
      break;
    case 'Java':
      subcategories.push(
        { name: 'Spring Boot', category_id: categoryId, customer_id: customerId },
        { name: 'Hibernate', category_id: categoryId, customer_id: customerId },
        { name: 'Jakarta EE', category_id: categoryId, customer_id: customerId }
      );
      break;
    case 'C#':
      subcategories.push(
        { name: 'ASP.NET Core', category_id: categoryId, customer_id: customerId },
        { name: 'Entity Framework', category_id: categoryId, customer_id: customerId },
        { name: 'LINQ', category_id: categoryId, customer_id: customerId }
      );
      break;
    case 'JavaScript':
      subcategories.push(
        { name: 'ES6+', category_id: categoryId, customer_id: customerId },
        { name: 'TypeScript', category_id: categoryId, customer_id: customerId },
        { name: 'Promises/Async', category_id: categoryId, customer_id: customerId }
      );
      break;
    case 'React':
      subcategories.push(
        { name: 'Hooks', category_id: categoryId, customer_id: customerId },
        { name: 'Redux', category_id: categoryId, customer_id: customerId },
        { name: 'Next.js', category_id: categoryId, customer_id: customerId }
      );
      break;
    case 'Angular':
      subcategories.push(
        { name: 'Components', category_id: categoryId, customer_id: customerId },
        { name: 'Services', category_id: categoryId, customer_id: customerId },
        { name: 'RxJS', category_id: categoryId, customer_id: customerId }
      );
      break;
    case 'Vue':
      subcategories.push(
        { name: 'Vue 3 Composition API', category_id: categoryId, customer_id: customerId },
        { name: 'Vuex', category_id: categoryId, customer_id: customerId },
        { name: 'Nuxt.js', category_id: categoryId, customer_id: customerId }
      );
      break;
    case 'Docker':
      subcategories.push(
        { name: 'Containers', category_id: categoryId, customer_id: customerId },
        { name: 'Docker Compose', category_id: categoryId, customer_id: customerId },
        { name: 'Dockerfile', category_id: categoryId, customer_id: customerId }
      );
      break;
    case 'Kubernetes':
      subcategories.push(
        { name: 'Pods', category_id: categoryId, customer_id: customerId },
        { name: 'Services', category_id: categoryId, customer_id: customerId },
        { name: 'Deployments', category_id: categoryId, customer_id: customerId }
      );
      break;
    case 'CI/CD':
      subcategories.push(
        { name: 'GitHub Actions', category_id: categoryId, customer_id: customerId },
        { name: 'Jenkins', category_id: categoryId, customer_id: customerId },
        { name: 'GitLab CI', category_id: categoryId, customer_id: customerId }
      );
      break;
    case 'Cloud':
      subcategories.push(
        { name: 'AWS', category_id: categoryId, customer_id: customerId },
        { name: 'Azure', category_id: categoryId, customer_id: customerId },
        { name: 'Google Cloud', category_id: categoryId, customer_id: customerId }
      );
      break;
    case 'React Native':
      subcategories.push(
        { name: 'Componentes', category_id: categoryId, customer_id: customerId },
        { name: 'Navegação', category_id: categoryId, customer_id: customerId },
        { name: 'Expo', category_id: categoryId, customer_id: customerId }
      );
      break;
    case 'Flutter':
      subcategories.push(
        { name: 'Widgets', category_id: categoryId, customer_id: customerId },
        { name: 'State Management', category_id: categoryId, customer_id: customerId },
        { name: 'Dart', category_id: categoryId, customer_id: customerId }
      );
      break;
    case 'iOS':
      subcategories.push(
        { name: 'Swift', category_id: categoryId, customer_id: customerId },
        { name: 'UIKit', category_id: categoryId, customer_id: customerId },
        { name: 'SwiftUI', category_id: categoryId, customer_id: customerId }
      );
      break;
    case 'Android':
      subcategories.push(
        { name: 'Kotlin', category_id: categoryId, customer_id: customerId },
        { name: 'Jetpack Compose', category_id: categoryId, customer_id: customerId },
        { name: 'Android SDK', category_id: categoryId, customer_id: customerId }
      );
      break;
    case 'SQL':
      subcategories.push(
        { name: 'PostgreSQL', category_id: categoryId, customer_id: customerId },
        { name: 'MySQL', category_id: categoryId, customer_id: customerId },
        { name: 'SQL Server', category_id: categoryId, customer_id: customerId }
      );
      break;
    case 'NoSQL':
      subcategories.push(
        { name: 'MongoDB', category_id: categoryId, customer_id: customerId },
        { name: 'Redis', category_id: categoryId, customer_id: customerId },
        { name: 'Cassandra', category_id: categoryId, customer_id: customerId }
      );
      break;
    case 'ORM':
      subcategories.push(
        { name: 'Sequelize', category_id: categoryId, customer_id: customerId },
        { name: 'TypeORM', category_id: categoryId, customer_id: customerId },
        { name: 'Prisma', category_id: categoryId, customer_id: customerId }
      );
      break;
    case 'Data Modeling':
      subcategories.push(
        { name: 'Normalização', category_id: categoryId, customer_id: customerId },
        { name: 'Modelagem ER', category_id: categoryId, customer_id: customerId },
        { name: 'Índices', category_id: categoryId, customer_id: customerId }
      );
      break;
    default:
      subcategories.push(
        { name: 'Conceitos Básicos', category_id: categoryId, customer_id: customerId },
        { name: 'Práticas Avançadas', category_id: categoryId, customer_id: customerId },
        { name: 'Ferramentas', category_id: categoryId, customer_id: customerId }
      );
  }

  return await subcategoriesRepository.insertAll(subcategories);
}

/**
 * Importa questões do arquivo JSON
 * @returns Lista de dados de questões
 */
async function importQuestionsFromJson(): Promise<IQuestionData[]> {
  console.log('Importando questões do arquivo JSON...');
  try {
    const filePath = path.resolve(__dirname, '../../../../src/database/questions.json');
    const fileContent = fs.readFileSync(filePath, 'utf-8');
    const questionsData = JSON.parse(fileContent) as IQuestionData[];
    console.log(`${questionsData.length} questões encontradas no arquivo JSON`);
    return questionsData;
  } catch (error) {
    console.error('Erro ao importar questões do arquivo JSON:', error);
    return [];
  }
}

async function insertExams(customerId: string, institutions: IInstitution[]): Promise<IExam[]> {
  console.log('Inserindo exames...');

  if (institutions.length === 0) {
    console.warn('Nenhuma instituição disponível para criar exames');
    return [];
  }

  const currentYear = new Date().getFullYear();
  const exams = [];

  // Para cada instituição, criar alguns exames de anos diferentes
  for (const institution of institutions) {
    // Criar exames para os últimos 5 anos
    for (let yearOffset = 0; yearOffset < 5; yearOffset++) {
      const year = currentYear - yearOffset;

      exams.push({
        name: `${institution.acronym || institution.name} ${year}`,
        institution_id: institution.id,
        year,
        published: true,
        status: 'published',
        exam_time: 240, // 4 horas em minutos
        customer_id: customerId,
      });

      // Para algumas instituições, adicionar também exames de meio de ano
      if (yearOffset < 3 && Math.random() > 0.5) {
        exams.push({
          name: `${institution.acronym || institution.name} ${year}`,
          institution_id: institution.id,
          year,
          published: true,
          status: 'published',
          exam_time: 240,
          customer_id: customerId,
        });
      }
    }
  }

  const createdExams = await examsRepository.insertAll(exams);
  console.log(`${createdExams.length} exames criados`);

  return createdExams;
}

/**
 * Insere instituições de ensino no banco de dados
 * @returns Lista de instituições criadas
 */
async function insertInstitutions(): Promise<IInstitution[]> {
  console.log('Inserindo instituições...');

  const institutions = [
    { name: 'Instituto Tecnológico de Aeronáutica', acronym: 'ITA', customer_id: null },
    { name: 'Instituto Militar de Engenharia', acronym: 'IME', customer_id: null },
    { name: 'Universidade de São Paulo', acronym: 'USP', customer_id: null },
    { name: 'Universidade Estadual de Campinas', acronym: 'UNICAMP', customer_id: null },
    { name: 'Universidade Federal do Rio de Janeiro', acronym: 'UFRJ', customer_id: null },
    { name: 'Universidade Federal de Minas Gerais', acronym: 'UFMG', customer_id: null },
    { name: 'Universidade Federal do Rio Grande do Sul', acronym: 'UFRGS', customer_id: null },
    { name: 'Universidade de Brasília', acronym: 'UnB', customer_id: null },
    {
      name: 'Pontifícia Universidade Católica do Rio de Janeiro',
      acronym: 'PUC-Rio',
      customer_id: null,
    },
    { name: 'Pontifícia Universidade Católica de São Paulo', acronym: 'PUC-SP', customer_id: null },
    { name: 'Instituto Nacional de Telecomunicações', acronym: 'INATEL', customer_id: null },
    { name: 'Fundação Getúlio Vargas', acronym: 'FGV', customer_id: null },
    { name: 'Instituto Nacional de Pesquisas Espaciais', acronym: 'INPE', customer_id: null },
    {
      name: 'Centro Federal de Educação Tecnológica de Minas Gerais',
      acronym: 'CEFET-MG',
      customer_id: null,
    },
    {
      name: 'Instituto Federal de Educação, Ciência e Tecnologia de São Paulo',
      acronym: 'IFSP',
      customer_id: null,
    },
    { name: 'Escola Politécnica da USP', acronym: 'POLI-USP', customer_id: null },
    { name: 'Faculdade de Tecnologia de São Paulo', acronym: 'FATEC', customer_id: null },
    {
      name: 'Instituto de Ciências Matemáticas e de Computação',
      acronym: 'ICMC-USP',
      customer_id: null,
    },
    { name: 'Instituto de Matemática e Estatística da USP', acronym: 'IME-USP', customer_id: null },
    {
      name: 'Faculdade de Informática e Administração Paulista',
      acronym: 'FIAP',
      customer_id: null,
    },
  ];

  const createdInstitutions = await institutionsRepository.insertAll(institutions);
  console.log(`${createdInstitutions.length} instituições criadas`);

  return createdInstitutions;
}

async function importQuestionsToDatabase(
  customerId: string,
  disciplines: IDiscipline[],
  disciplineCategoriesMap: Map<string, ICategory[]>,
  categorySubcategoriesMap: Map<string, ISubCategory[]>,
  questionTypesMap: Record<string, string>,
  questionsData: IQuestionData[],
  institutions: IInstitution[] = []
): Promise<IQuestion[]> {
  console.log('Importando questões do JSON para o banco de dados...');
  const createdQuestions: IQuestion[] = [];

  for (const questionData of questionsData) {
    try {
      const discipline = getRandomItem(disciplines);

      const categories = disciplineCategoriesMap.get(discipline.id) || [];
      if (categories.length === 0) continue;

      const category = getRandomItem(categories);

      const subcategories = categorySubcategoriesMap.get(category.id) || [];
      if (subcategories.length === 0) continue;

      const subcategory = getRandomItem(subcategories);

      const questionType = questionData.type || 'alternatives';
      const questionTypeId = questionTypesMap[questionType];

      if (!questionTypeId) {
        console.warn(`Tipo de questão não encontrado: ${questionType}`);
        continue;
      }

      let institutionName = questionData.institution || '';

      if (institutions.length > 0 && (!institutionName || institutionName.trim() === '')) {
        const randomInstitution = getRandomItem(institutions);
        institutionName = randomInstitution.name;
      }

      const questionInsertData: IQuestionInsert = {
        title: questionData.title,
        description: questionData.description || '',
        difficulty: questionData.difficulty || 'medium',
        image_url: questionData.image_url || '',
        explanation_video: questionData.explanation_video || '',
        explanation_text: questionData.explanation_text || 'Sem explicação disponível.',
        explanation_image: questionData.explanation_image || '',
        published: questionData.published !== undefined ? questionData.published : true,
        published_at: new Date(),
        status: questionData.status || 'published',
        correct_text: questionData.correct_text || '',
        reference: questionData.reference || '',
        institution: institutionName,
        year: questionData.year || null,
        customer_id: customerId,
        discipline_id: discipline.id,
        category_id: category.id,
        subcategory_id: subcategory.id,
        question_type_id: questionTypeId,
      };

      const createdQuestion = await questionsRepository.insert(questionInsertData);
      createdQuestions.push(createdQuestion);

      if (questionData.alternatives && Array.isArray(questionData.alternatives)) {
        for (const alt of questionData.alternatives) {
          const alternativeData: IAlternativeInsert = {
            question_id: createdQuestion.id,
            correct: alt.correct || false,
            option: alt.option || '',
            description: alt.description || '',
          };
          await alternativesRepository.insert(alternativeData);
        }
      }

      console.log(`Questão importada: ${questionData.title}`);
    } catch (error) {
      console.error(`Erro ao importar questão: ${questionData.title}`, error);
    }
  }

  console.log(`${createdQuestions.length} questões importadas com sucesso`);
  return createdQuestions;
}

// Função para vincular questões às provas
async function linkQuestionsToExams(questions: IQuestion[], exams: IExam[]): Promise<number> {
  console.log('Vinculando questões às provas...');
  let totalLinked = 0;

  // Para cada prova, vincular um conjunto de questões
  for (const exam of exams) {
    try {
      // Obter as questões já vinculadas à prova
      const existingLinks = await examsQuestionsRepository.findByExamId(exam.id);

      // Determinar a próxima ordem a partir da maior ordem existente
      let startingOrder = 1;
      if (existingLinks.length > 0) {
        // Encontrar a maior ordem existente e adicionar 1
        startingOrder = Math.max(...existingLinks.map((link) => link.order_by)) + 1;
      }

      // Selecionar um subconjunto aleatório de questões para cada prova
      const questionsCount = Math.floor(Math.random() * 21) + 10; // Entre 10 e 30
      const selectedQuestions = getRandomSubset(questions, questionsCount);

      // Filtrar questões que já estão vinculadas à prova
      const existingQuestionIds = new Set(existingLinks.map((link) => link.question_id));
      const newQuestions = selectedQuestions.filter((q) => !existingQuestionIds.has(q.id));

      if (newQuestions.length === 0) {
        console.log(`Nenhuma nova questão para vincular à prova ${exam.name}`);
        continue;
      }

      // Preparar os dados para inserção com ordem incremental
      const now = new Date();
      const questionsToInsert = newQuestions.map((question, index) => ({
        id: randomUUID(),
        exam_id: exam.id,
        question_id: question.id,
        order_by: startingOrder + index,
        created_at: now,
        updated_at: now,
        deleted_at: null,
      }));

      // Inserir todas as questões de uma vez
      if (questionsToInsert.length > 0) {
        await examsQuestionsRepository.insertAll(questionsToInsert);
        totalLinked += questionsToInsert.length;

        console.log(
          `${questionsToInsert.length} questões vinculadas à prova ${exam.name} (ordem inicial: ${startingOrder})`
        );
      }
    } catch (error) {
      console.error(`Erro ao vincular questões à prova ${exam.name}:`, error);
    }
  }

  console.log(`Total de ${totalLinked} vínculos entre questões e provas criados`);
  return totalLinked;
}

/**
 * Insere turmas para desenvolvimento e testes
 * @param customerId ID do cliente
 * @returns Lista de turmas criadas
 */
async function insertDevClasses(customerId: string): Promise<IClasses[]> {
  console.log('Inserindo turmas...');

  // Obter um instrutor para associar às turmas
  const instructors = await userRepository.findByRole('instructor', customerId);
  const instructorId = instructors.length > 0 ? instructors[0].id : undefined;

  const today = new Date();
  const endDate = new Date();
  endDate.setMonth(today.getMonth() + 6); // Turmas com duração de 6 meses

  // Gerar 30 turmas com nomes e external_class_id únicos
  const classesToInsert = Array.from({ length: 30 }, (_, i) => ({
    name: `Turma ${i + 1 < 10 ? '0' : ''}${i + 1}`,
    description: `Turma de desenvolvimento  ${i + 1 < 10 ? '0' : ''}${i + 1}`,
    status: 'active',
    start_date: today,
    end_date: endDate,
    instructor_id: instructorId,
    external_class_id: `CLASS-${(i + 1).toString().padStart(3, '0')}`,
    customer_id: customerId,
  }));

  const createdClasses = await classesRepository.insertAll(classesToInsert);
  console.log(`${createdClasses.length} turmas criadas`);

  return createdClasses;
}

/**
 * Insere usuários alunos para desenvolvimento e testes
 * @param customerId ID do cliente
 * @param classes Lista de turmas
 * @returns Lista de alunos criados
 */
async function insertDevStudents(customerId: string, classes: IClasses[]): Promise<IUser[]> {
  console.log('Inserindo alunos...');

  // Verificar se já existem alunos
  const existingStudents = await userRepository.findByRole('student', customerId);

  if (existingStudents.length > 0) {
    console.log(`${existingStudents.length} alunos já existem no banco de dados`);
    return existingStudents;
  }

  // Criar 5 alunos para cada turma
  const studentsToInsert = [];
  const hashedPassword = await bcrypt.hash('senha123', 8);

  // Nomes para gerar alunos aleatórios
  const firstNames = [
    'João',
    'Maria',
    'Pedro',
    'Ana',
    'Carlos',
    'Juliana',
    'Lucas',
    'Mariana',
    'Rafael',
    'Fernanda',
    'Bruno',
    'Camila',
    'Diego',
    'Patrícia',
    'Gustavo',
    'Eduardo',
    'Larissa',
    'Vinícius',
    'Beatriz',
    'Fábio',
    'Aline',
    'Ricardo',
    'Tatiane',
    'Felipe',
    'Amanda',
    'Leandro',
    'Paula',
    'André',
    'Letícia',
    'Roberta',
  ];
  const lastNames = [
    'Silva',
    'Santos',
    'Oliveira',
    'Souza',
    'Pereira',
    'Costa',
    'Rodrigues',
    'Almeida',
    'Nascimento',
    'Lima',
    'Araújo',
    'Fernandes',
    'Carvalho',
    'Gomes',
    'Martins',
    'Barbosa',
    'Rocha',
    'Dias',
    'Teixeira',
    'Moreira',
    'Moura',
    'Cavalcante',
    'Freitas',
    'Ribeiro',
    'Mendes',
    'Batista',
    'Farias',
    'Pinto',
    'Campos',
    'Cardoso',
  ];

  let studentIndex = 0;
  for (let i = 0; i < classes.length; i++) {
    for (let j = 0; j < 5; j++) {
      // 5 alunos por turma
      const firstName = firstNames[studentIndex % firstNames.length];
      const lastName = lastNames[studentIndex % lastNames.length];
      const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}${i}_${j}@aluno.exemplo.com`;
      studentsToInsert.push({
        first_name: firstName,
        last_name: lastName,
        email,
        password: hashedPassword,
        active: true,
        terms_accept: true,
        customer_id: customerId,
        role: 'student',
        created_by: 'seeder',
      });
      studentIndex++;
    }
  }

  // Inserir todos os alunos de uma vez
  const createdStudents = await userRepository.insertAll(studentsToInsert);
  console.log(`${createdStudents.length} alunos criados`);

  // Vincular alunos às turmas (5 por turma)
  await linkStudentsToClasses(createdStudents, classes);

  return createdStudents;
}

/**
 * Vincula alunos às turmas
 * @param students Lista de alunos
 * @param classes Lista de turmas
 */
async function linkStudentsToClasses(students: IUser[], classes: IClasses[]): Promise<void> {
  console.log('Vinculando alunos às turmas...');

  // Garantir 5 alunos por turma
  const usersClassesLinks = [];
  let studentIdx = 0;
  for (let i = 0; i < classes.length; i++) {
    for (let j = 0; j < 5; j++) {
      if (studentIdx < students.length) {
        usersClassesLinks.push({
          id: randomUUID(),
          class_id: classes[i].id,
          user_id: students[studentIdx].id,
          status: 'active',
          apply_date: new Date(),
          created_at: new Date(),
          updated_at: new Date(),
          deleted_at: null,
        });
        studentIdx++;
      }
    }
  }

  // Inserir os vínculos no banco de dados
  if (usersClassesLinks.length > 0) {
    await knexInstance('users_classes').insert(usersClassesLinks);
    console.log(`${usersClassesLinks.length} vínculos entre alunos e turmas criados`);
  }
}

/**
 * Vincula provas às turmas e cria acessos para os alunos
 * @param customerId ID do cliente
 * @param exams Lista de provas
 * @param classes Lista de turmas
 * @returns Número total de acessos criados
 */
async function createExamsClassesAndAccess(
  customerId: string,
  exams: IExam[],
  classes: IClasses[]
): Promise<number> {
  console.log('Vinculando provas às turmas e criando acessos para alunos...');

  // Obter alguns usuários com papel de aluno para simular alunos nas turmas
  const students = await userRepository.findByRole('student', customerId);

  if (students.length === 0) {
    console.log('Nenhum aluno encontrado para criar acessos às provas');
    return 0;
  }

  let totalAccessCreated = 0;

  // Para cada turma, vincular algumas provas aleatórias
  for (const classEntity of classes) {
    // Selecionar 2-4 provas aleatórias para cada turma
    const examCount = Math.floor(Math.random() * 3) + 2; // Entre 2 e 4
    const selectedExams = getRandomSubset(exams, examCount);

    console.log(`Vinculando ${selectedExams.length} provas à turma ${classEntity.name}`);

    // Criar vínculos entre turma e provas
    const examsClassesLinks = selectedExams.map((exam) => ({
      id: randomUUID(),
      class_id: classEntity.id,
      exam_id: exam.id,
      created_at: new Date(),
      updated_at: new Date(),
      deleted_at: null,
    }));

    if (examsClassesLinks.length > 0) {
      await examsClassesRepository.insertAll(examsClassesLinks);
      console.log(`${examsClassesLinks.length} provas vinculadas à turma ${classEntity.name}`);
    }

    // Buscar alunos da turma através da tabela users_classes
    const classStudents = await knexInstance('users')
      .join('users_classes', 'users.id', '=', 'users_classes.user_id')
      .where('users_classes.class_id', classEntity.id)
      .whereNull('users.deleted_at')
      .whereNull('users_classes.deleted_at')
      .select('users.*');

    if (classStudents.length === 0) {
      // Se não houver alunos vinculados à turma, selecionar alguns aleatoriamente
      const studentCount = Math.floor(Math.random() * 11) + 5; // Entre 5 e 15
      const randomStudents = getRandomSubset(students, studentCount);
      console.log(
        `Selecionando ${randomStudents.length} alunos aleatórios para a turma ${classEntity.name}`
      );

      // Para cada prova vinculada, criar acesso para os alunos selecionados
      for (const exam of selectedExams) {
        console.log(
          `Criando acessos para ${randomStudents.length} alunos na prova ${exam.name} da turma ${classEntity.name}`
        );

        // Criar acessos para os alunos selecionados
        const accessRecords = randomStudents.map((student) => ({
          id: randomUUID(),
          exam_id: exam.id,
          user_id: student.id,
          active: true,
          start_exams: null,
          end_exams: null,
          time_limit: exam.exam_time,
          total_seconds: 0,
          created_at: new Date(),
          updated_at: new Date(),
          deleted_at: null,
        }));

        if (accessRecords.length > 0) {
          await examsAccessRepository.insertAll(accessRecords);
          totalAccessCreated += accessRecords.length;
        }
      }
    } else {
      console.log(`Encontrados ${classStudents.length} alunos na turma ${classEntity.name}`);

      // Para cada prova vinculada, criar acesso para os alunos da turma
      for (const exam of selectedExams) {
        console.log(
          `Criando acessos para ${classStudents.length} alunos na prova ${exam.name} da turma ${classEntity.name}`
        );

        // Criar acessos para os alunos da turma
        const accessRecords = classStudents.map((student) => ({
          id: randomUUID(),
          exam_id: exam.id,
          user_id: student.id,
          active: true,
          start_exams: null,
          end_exams: null,
          time_limit: exam.exam_time,
          total_seconds: 0,
          created_at: new Date(),
          updated_at: new Date(),
          deleted_at: null,
        }));

        if (accessRecords.length > 0) {
          await examsAccessRepository.insertAll(accessRecords);
          totalAccessCreated += accessRecords.length;
        }
      }
    }
  }

  console.log(`Total de ${totalAccessCreated} acessos a provas criados`);
  return totalAccessCreated;
}
