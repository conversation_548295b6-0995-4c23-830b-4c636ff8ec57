import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { KnexCustomerRepository } from '../../../repositories/knex/customers.repositories';

export async function getFirstCustomerController(req: Request, res: Response) {
  const customersRepository = new KnexCustomerRepository();
  const firstCustomer = await customersRepository.findAll().then((customers) => customers[0]);

  if (!firstCustomer) {
    return httpResponse(res, 404, { message: 'Nenhum cliente encontrado.' });
  }
  return httpResponse(res, 200, firstCustomer);
}
