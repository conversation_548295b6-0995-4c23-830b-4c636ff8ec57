import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { IQuestion } from '../../../model/IQuestion';
import { KnexQuestionsRepository } from '../../../repositories/knex/questions.repositories';
import { KnexQuestionsSimulatedsRepository } from '../../../repositories/knex/questionsSimulateds.repositories';
import { KnexSimulatedsRepository } from '../../../repositories/knex/simulated.repositories';
import { KnexSimulatedsAccessRepository } from '../../../repositories/knex/simulatedsAccess.repositories';
import { KnexSimulatedTypesRepository } from '../../../repositories/knex/simulatedsTypes.repositories';
import { KnexUserRepository } from '../../../repositories/knex/users.repositories';

export const createSimulated = async (req: Request, res: Response): Promise<Response> => {
  const { type, quantity, name } = req.body;

  const simulatedTypeRepository = new KnexSimulatedTypesRepository();
  const questionsRepository = new KnexQuestionsRepository();
  const usersRepository = new KnexUserRepository();
  const questionsSimulatedRepository = new KnexQuestionsSimulatedsRepository();
  const simulatedsRepository = new KnexSimulatedsRepository();
  const simulatedsAccessRepository = new KnexSimulatedsAccessRepository();

  const simulatedType = await getSimulatedTypeByName(simulatedTypeRepository, type);

  if (!simulatedType) {
    return httpResponse(res, 400, { message: 'Tipo de simulado não encontrado.' });
  }

  const allQuestions = await questionsRepository.findAll();
  if (quantity > allQuestions.length) {
    return httpResponse(res, 400, { message: `Selecione até ${allQuestions.length} questões.` });
  }

  const randomQuestions = getRandomQuestions(allQuestions, quantity);

  const firstUserId = await getFirstUserId(usersRepository);
  if (!firstUserId) {
    return httpResponse(res, 400, { message: 'Usuário inicial não encontrado.' });
  }

  const createdSimulated = await createSimulatedRecord(
    simulatedsRepository,
    name,
    simulatedType.id
  );

  await saveQuestionsSimulated(questionsSimulatedRepository, createdSimulated.id, randomQuestions);

  await createSimulatedAccessForUser(simulatedsAccessRepository, firstUserId, createdSimulated.id);

  return httpResponse(res, 201, {
    message: 'Simulado criado com sucesso!',
    simulatedId: createdSimulated.id,
  });
};

const getSimulatedTypeByName = async (
  simulatedTypeRepository: KnexSimulatedTypesRepository,
  type: string
) => {
  const simulatedTypes = await simulatedTypeRepository.findAll();
  return simulatedTypes.find((simType) => simType.name === type);
};

const getRandomQuestions = (allQuestions: IQuestion[], quantity: number): IQuestion[] => {
  const shuffled = [...allQuestions].sort(() => Math.random() - 0.5);
  return shuffled.slice(0, quantity);
};

const getFirstUserId = async (usersRepository: KnexUserRepository): Promise<string | null> => {
  const firstUser = await usersRepository.findAll();
  return firstUser.length > 0 ? firstUser[0].id : null;
};

const createSimulatedRecord = async (
  simulatedsRepository: KnexSimulatedsRepository,
  name: string,
  simulatedTypeId: string
) => {
  const simulated = {
    name,
    simulated_type_id: simulatedTypeId,
    active: true,
  };

  const result = await simulatedsRepository.insert(simulated);
  console.log(`Criando simulado ${name}`);
  return result;
};

const saveQuestionsSimulated = async (
  questionsSimulatedRepository: KnexQuestionsSimulatedsRepository,
  simulatedId: string,
  questions: IQuestion[]
): Promise<void> => {
  const questionsToInsert = questions.map((question) => ({
    simulated_id: simulatedId,
    question_id: question.id,
  }));
  await questionsSimulatedRepository.insertAll(questionsToInsert);
  console.log(`Criando questões do simulado`);
};

const createSimulatedAccessForUser = async (
  simulatedsAccessRepository: KnexSimulatedsAccessRepository,
  userId: string,
  simulatedId: string
): Promise<void> => {
  const accessDate = {
    user_id: userId,
    simulated_id: simulatedId,
    active: true,
    start_simulated: null,
    end_simulated: null,
  };

  await simulatedsAccessRepository.insert(accessDate);
  console.log(`Criando acesso do simulado para o aluno`);
};
