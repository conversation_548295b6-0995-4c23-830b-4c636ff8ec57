import bcrypt from 'bcryptjs';
import { randomUUID } from 'crypto';
import { Request, Response } from 'express';

import { knexInstance } from '../../../config/connectionDatabase.config';
import { env } from '../../../env';
import { httpResponse } from '../../../helpers/httpResponse';
import { IUser } from '../../../model/IUser';
import { KnexClassesRepository } from '../../../repositories/knex/classes.repositories';
import { KnexCustomerRepository } from '../../../repositories/knex/customers.repositories';
import { KnexUserRepository } from '../../../repositories/knex/users.repositories';

// Função utilitária para buscar o customer inicial
async function getInitialCustomer() {
  const customerRepository = new KnexCustomerRepository();
  const taxNumber = env.FIRST_CUSTOMER_TAX_NUMBER;
  if (!taxNumber) throw new Error('FIRST_CUSTOMER_TAX_NUMBER não definido no ambiente');
  const firstCustomer = await customerRepository.getTaxNumber(taxNumber);
  if (!firstCustomer) throw new Error('Cliente inicial não encontrado!');
  return firstCustomer;
}

export async function userSeederController(_req: Request, res: Response) {
  try {
    // Buscar customer inicial
    const customer = await getInitialCustomer();
    // criar turma exclusiva de teste
    const classesRepository = new KnexClassesRepository();
    let testClass = await classesRepository.findByExternalClassId({
      externalClassId: 'TURMA-TESTE-EXCLUSIVA',
      customerId: customer.id,
    });
    if (!testClass) {
      const today = new Date();
      const endDate = new Date();
      endDate.setMonth(today.getMonth() + 6);
      const classData = {
        name: 'Turma Teste Exclusiva',
        description: 'Turma exclusiva para testes manuais',
        status: 'active',
        start_date: today,
        end_date: endDate,
        customer_id: customer.id,
        external_class_id: 'TURMA-TESTE-EXCLUSIVA',
      };
      testClass = await classesRepository.createExternalClass(classData);
    }
    // Criar usuários fictícios
    const userRepository = new KnexUserRepository();
    const hashedPassword = await bcrypt.hash('senha123', 8);
    const nomes = ['Cleber', 'Rafael', 'Dessa', 'Pedro', 'Lee', 'Manu'];
    const usersToInsert: Partial<IUser>[] = nomes.map((nome) => ({
      first_name: nome,
      last_name: 'Teste',
      email: `${nome.toLowerCase()}@gmail.com`,
      password: hashedPassword,
      active: true,
      terms_accept: true,
      customer_id: customer.id,
      role: 'student',
      created_by: 'userSeeder',
    }));
    // Inserir usuários
    const createdUsers = await userRepository.insertAll(usersToInsert);
    // Vincular à turma exclusiva
    const now = new Date();
    const usersClassesLinks = createdUsers.map((user) => ({
      id: randomUUID(),
      class_id: testClass.id,
      user_id: user.id,
      status: 'active',
      apply_date: now,
      created_at: now,
      updated_at: now,
      deleted_at: null,
    }));
    await knexInstance('users_classes').insert(usersClassesLinks);
    return httpResponse(res, 201, {
      message: 'Usuários criados e vinculados à turma exclusiva de teste com sucesso!',
      usersCreated: createdUsers.length,
      classId: testClass.id,
      externalClassId: testClass.external_class_id,
      usersLinked: usersClassesLinks.length,
    });
  } catch (error) {
    console.error('Erro no userSeeder:', error);
    return httpResponse(res, 500, {
      message: 'Erro ao criar usuários e vincular à turma exclusiva de teste',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

export async function createExclusiveTestClassController(_req: Request, res: Response) {
  try {
    const customer = await getInitialCustomer();
    const classesRepository = new KnexClassesRepository();
    const today = new Date();
    const endDate = new Date();
    endDate.setMonth(today.getMonth() + 6);
    const classData = {
      name: 'Turma Teste Exclusiva',
      description: 'Turma exclusiva para testes manuais',
      status: 'active',
      start_date: today,
      end_date: endDate,
      customer_id: customer.id,
      external_class_id: 'TURMA-TESTE-EXCLUSIVA',
    };
    const createdClass = await classesRepository.createExternalClass(classData);
    return httpResponse(res, 201, {
      message: 'Turma exclusiva de teste criada com sucesso!',
      class: createdClass,
    });
  } catch (error) {
    console.error('Erro ao criar turma exclusiva de teste:', error);
    return httpResponse(res, 500, {
      message: 'Erro ao criar turma exclusiva de teste',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
