import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { factoryCreateTermUseCase } from '../../../use-case/factories/terms.factory';

export async function createTermController(req: Request, res: Response) {
  const { customerId } = customerIdSchema.parse(req.user);
  const useCase = factoryCreateTermUseCase();
  const term = await useCase.execute({
    customerId,
    file: req.file as Express.Multer.File,
  });
  return httpResponse(res, 201, { message: 'Termo criado com sucesso', term });
}
