import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { genericIdSchema } from '../../../schema/generic.schema';
import { factoryGetTermUseCase } from '../../../use-case/factories/terms.factory';

export async function getTermController(req: Request, res: Response) {
  const { id } = genericIdSchema.parse(req.params);
  const { customerId } = customerIdSchema.parse(req.user);
  const useCase = factoryGetTermUseCase();
  const result = await useCase.execute(id, customerId);
  return httpResponse(res, 200, { message: 'Termo encontrado com sucesso', ...result });
}
