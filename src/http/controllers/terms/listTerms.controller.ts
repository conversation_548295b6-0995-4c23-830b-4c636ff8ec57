import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { filterListTermsSchema } from '../../../schema/terms.schema';
import { factoryListAllTermsUseCase } from '../../../use-case/factories/terms.factory';

export async function listAllTermsController(req: Request, res: Response) {
  const { customerId } = customerIdSchema.parse(req.user);
  const filter = filterListTermsSchema.parse(req.query);
  const useCase = factoryListAllTermsUseCase();
  const terms = await useCase.execute(customerId, filter);
  return httpResponse(res, 200, { message: 'Termos de uso listados com sucesso', terms });
}
