import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { updateTermSchema } from '../../../schema/terms.schema';
import { factoryUpdateTermUseCase } from '../../../use-case/factories/terms.factory';

export async function updateTermController(req: Request, res: Response) {
  const { id } = req.params;
  const { customerId } = req.user;
  const data = updateTermSchema.parse(req.body);
  const useCase = factoryUpdateTermUseCase();
  const term = await useCase.execute({ id, customerId, ...data });
  return httpResponse(res, 200, { message: 'Termo atualizado com sucesso', term });
}
