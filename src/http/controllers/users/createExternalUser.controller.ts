import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { createExternalUserSchema } from '../../../schema/users.schema';
import { factoryCreateExternalUserUseCase } from '../../../use-case/factories/users.factory';

export async function createExternalUserController(req: Request, res: Response) {
  const bodyData = createExternalUserSchema.parse(req.body);

  const createExternalUser = factoryCreateExternalUserUseCase();

  const createdUser = await createExternalUser.execute({ ...bodyData });

  return httpResponse(res, 201, { user: createdUser });
}
