import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { createUserAdminSchema } from '../../../schema/users.schema';
import { factoryCreateUserAdminUseCase } from '../../../use-case/factories/users.factory';

export async function createUserAdminController(req: Request, res: Response): Promise<Response> {
  const bodyData = createUserAdminSchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);
  const useCase = factoryCreateUserAdminUseCase();
  const response = await useCase.execute({ ...bodyData, customerId });
  return httpResponse(res, 200, {
    message: 'Usuário criado com sucesso',
    data: response,
  });
}
