import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import {
  genericStringCustomerIdchema,
  genericStringIdSchema,
} from '../../../schema/generic.schema';
import { factoryDeleteExternalUserUseCase } from '../../../use-case/factories/users.factory';

export async function deleteExternalUserController(req: Request, res: Response) {
  const { id } = genericStringIdSchema.parse(req.params);

  const { customerId } = genericStringCustomerIdchema.parse(req.query);
  const hardDelete = req.query.hardDelete === 'true';

  const deleteExternalUserUseCase = factoryDeleteExternalUserUseCase();

  const result = await deleteExternalUserUseCase.execute({
    id,
    customerId,
    hardDelete,
  });

  return httpResponse(res, 200, {
    message: hardDelete ? 'Usuário removido permanentemente' : 'Usuário excluído com sucesso',
    deletedUser: result,
  });
}
