import { Request, Response } from 'express';

import { customerIdSchema } from '../../../schema/customers.schema';
import { userIdRequeridSchema } from '../../../schema/users.schema';
import { factoryDeleteUserAdminUseCase } from '../../../use-case/factories/users.factory';

export async function deleteUserAdminController(req: Request, res: Response) {
  const { userId } = userIdRequeridSchema.parse(req.params);
  const { customerId } = customerIdSchema.parse(req.user);

  const useCase = factoryDeleteUserAdminUseCase();
  const deletedUser = await useCase.execute(userId, customerId);

  return res.status(200).json({ message: 'Usuário excluído com sucesso.', data: deletedUser });
}
