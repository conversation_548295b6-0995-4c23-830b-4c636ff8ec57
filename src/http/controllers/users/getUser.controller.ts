import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericIdSchema } from '../../../schema/generic.schema';
import { UsersFactory } from '../../../use-case/factories/users.factory';

export const getUserController = async (req: Request, res: Response) => {
  const { id: userId } = genericIdSchema.parse(req.user);

  const getUserUseCase = UsersFactory.getUserUseCase();
  const user = await getUserUseCase.execute(userId);
  return httpResponse(res, 200, { message: 'Usuário encontrado com sucesso', user });
};
