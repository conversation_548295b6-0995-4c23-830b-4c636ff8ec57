import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { userIdRequeridSchema } from '../../../schema/users.schema';
import { factoryGetUserByIdUseCase } from '../../../use-case/factories/users.factory';

export async function getUserByIdController(req: Request, res: Response) {
  const { userId } = userIdRequeridSchema.parse(req.params);

  const useCase = factoryGetUserByIdUseCase();
  const user = await useCase.execute(userId);

  return httpResponse(res, 200, { message: 'Usuário encontrado com sucesso', user });
}
