import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { userIdRequeridSchema } from '../../../schema/users.schema';
import { factoryGetUserByIdAndCourseIdUseCase } from '../../../use-case/factories/users.factory';

export async function getUserByIdAndCourseIdController(req: Request, res: Response) {
  const { userId } = userIdRequeridSchema.parse(req.params);
  const { customerId } = customerIdSchema.parse(req.user);

  const useCase = factoryGetUserByIdAndCourseIdUseCase();
  const user = await useCase.execute(userId, customerId);

  return httpResponse(res, 200, { message: 'Usuário encontrado com sucesso', data: user });
}
