import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { usersListAdminsSchema } from '../../../schema/users.schema';
import { factoryListUsersAdmByCustomerUseCase } from '../../../use-case/factories/users.factory';

export async function listUsersAdmByCustomerController(req: Request, res: Response) {
  const { customerId } = customerIdSchema.parse(req.user);
  const { orderByColumn, orderByDirection } = usersListAdminsSchema.parse(req.query);
  const useCase = factoryListUsersAdmByCustomerUseCase();
  const users = await useCase.execute(
    customerId,
    orderByColumn as string,
    orderByDirection as string
  );
  return httpResponse(res, 200, {
    message: 'Usuários administradores listados com sucesso',
    users,
  });
}
