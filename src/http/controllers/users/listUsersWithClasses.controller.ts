import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { listUsersWithClassesSchema } from '../../../schema/users.schema';
import { factoryListUsersWithClassesUseCase } from '../../../use-case/factories/users.factory';

export async function listUsersWithClassesController(req: Request, res: Response) {
  const useCase = factoryListUsersWithClassesUseCase();
  const filters = listUsersWithClassesSchema.parse(req.query);
  const { customerId } = customerIdSchema.parse(req.user);
  const result = await useCase.execute({
    customerId,
    ...filters,
  });
  return httpResponse(res, 200, { ...result });
}
