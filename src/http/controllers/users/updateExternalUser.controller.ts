import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericStringIdSchema } from '../../../schema/generic.schema';
import { updateExternalUserSchema } from '../../../schema/users.schema';
import { factoryUpdateExternalUserUseCase } from '../../../use-case/factories/users.factory';

export async function updateExternalUserController(req: Request, res: Response) {
  const bodyData = updateExternalUserSchema.parse(req.body);
  const { id } = genericStringIdSchema.parse(req.params);

  const updateExternalUserUseCase = factoryUpdateExternalUserUseCase();

  const updatedUser = await updateExternalUserUseCase.execute({
    id,
    ...bodyData,
  });

  return httpResponse(res, 200, { message: 'Usuário atualizado com sucesso', user: updatedUser });
}
