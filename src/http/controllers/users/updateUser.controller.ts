import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { genericIdSchema } from '../../../schema/generic.schema';
import { updateUserSchema } from '../../../schema/users.schema';
import { UsersFactory } from '../../../use-case/factories/users.factory';

export async function updateUserController(req: Request, res: Response) {
  const bodyData = updateUserSchema.parse(req.body);
  const { id: userId } = genericIdSchema.parse(req.user);
  const updateUserUseCase = UsersFactory.updateUserUseCase();

  const result = await updateUserUseCase.execute({
    userId,
    ...bodyData,
  });
  return httpResponse(res, 200, {
    message: 'Usuário atualizado com sucesso',
    data: result,
  });
}
