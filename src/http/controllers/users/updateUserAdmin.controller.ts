import { Request, Response } from 'express';

import { httpResponse } from '../../../helpers/httpResponse';
import { customerIdSchema } from '../../../schema/customers.schema';
import { updateUserAdminSchema } from '../../../schema/users.schema';
import { factoryUpdateUserAdmin } from '../../../use-case/factories/users.factory';

export async function updateUserAdminController(req: Request, res: Response) {
  const bodyData = updateUserAdminSchema.parse(req.body);
  const { customerId } = customerIdSchema.parse(req.user);

  const { id: requesterId } = req.user;

  const useCase = factoryUpdateUserAdmin();

  const response = await useCase.execute(bodyData, requesterId, customerId);
  return httpResponse(res, 200, {
    message: 'Usuário atualizado com sucesso',
    ...response,
  });
}
