import { NextFunction, Request, Response } from 'express';

import { env } from '../../env';
import { httpResponse } from '../../helpers/httpResponse';
import { KnexUserRepository } from '../../repositories/knex/users.repositories';
import { verifyToken } from '../../services/jwt/verify.jwt';
import { verifyUser } from '../../services/verifyUser.service';

// Middleware de autenticação da plataforma
export const authenticateToken = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response | void> => {
  const authHeader = req.headers.authorization;
  const token = authHeader?.split(' ')[1];

  if (!token) {
    return httpResponse(res, 401, { message: 'Token inválido' });
  }

  const decoded = verifyToken(token, env.JWT_SECRET);

  if (!decoded) {
    return httpResponse(res, 401, { message: 'Token inválido' });
  }
  const userRepository = new KnexUserRepository();
  const userExist = await verifyUser(userRepository, decoded.customerId, decoded.id);

  if (!userExist) {
    return httpResponse(res, 401, { message: 'Token inválido' });
  }

  req.user = {
    id: decoded.id,
    customerId: decoded.customerId,
    role: userExist.role,
  };

  next();
};
