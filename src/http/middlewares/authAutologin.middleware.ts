import { NextFunction, Request, Response } from 'express';
import jwt from 'jsonwebtoken';

import { httpResponse } from '../../helpers/httpResponse';

export const authenticateAutologinToken = (
  req: Request,
  res: Response,
  next: NextFunction
): Response | void => {
  const token = req.query.token as string;

  if (!token) {
    return httpResponse(res, 401, { message: 'Token inválido' });
  }

  const decoded = jwt.decode(token);

  if (!decoded || typeof decoded !== 'object' || !decoded.customerId) {
    return httpResponse(res, 401, { message: 'Token inválido' });
  }

  req.user = {
    id: decoded.id,
    customerId: decoded.customerId,
  };

  next();
};
