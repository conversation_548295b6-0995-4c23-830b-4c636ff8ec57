import { NextFunction, Request, Response } from 'express';

import { env } from '../../env';
import { httpResponse } from '../../helpers/httpResponse';

export const authDatabase = (req: Request, res: Response, next: NextFunction): Response | void => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return httpResponse(res, 401, { message: 'Token inválido' });
  }

  if (token !== env.TOKEN_DATABASE) {
    return httpResponse(res, 401, { message: 'Token inválido' });
  }

  next();
};
