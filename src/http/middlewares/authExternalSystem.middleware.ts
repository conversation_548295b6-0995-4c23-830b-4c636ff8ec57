import { NextFunction, Request, Response } from 'express';

import { env } from '../../env';
import { httpResponse } from '../../helpers/httpResponse';

export const authenticateExternalSystem = (
  req: Request,
  res: Response,
  next: NextFunction
): Response | void => {
  try {
    const apiKey = req.headers['x-api-key'];

    if (typeof apiKey !== 'string' || !apiKey.trim()) {
      return httpResponse(res, 401, { message: 'Não autorizado' });
    }

    if (
      Buffer.from(apiKey).length !== Buffer.from(env.INTEGRATION_TOKEN).length ||
      apiKey !== env.INTEGRATION_TOKEN
    ) {
      console.warn(`Tentativa de acesso com API key inválida. IP: ${req.ip}`);
      return httpResponse(res, 401, { message: 'Não autorizado' });
    }

    console.info(`Sistema externo autenticado com sucesso. IP: ${req.ip}`);
    next();
  } catch (error) {
    console.error('Erro na autenticação:', error);
    return httpResponse(res, 500, { message: 'Erro interno do servidor' });
  }
};
