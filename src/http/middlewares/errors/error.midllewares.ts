import { NextFunction, Request, Response } from 'express';

import { handleErrors } from '../../../helpers/handleErrors';
import { httpResponse } from '../../../helpers/httpResponse';

export function globalErrorHandler(err: unknown, _req: Request, res: Response, next: NextFunction) {
  const { error, message, statusCode, issue } = handleErrors(err);

  if (statusCode) {
    return httpResponse(res, statusCode, { message, error, issue });
  } else {
    return next(err);
  }
}
