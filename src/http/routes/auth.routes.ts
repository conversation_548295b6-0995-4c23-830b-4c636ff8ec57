import { Router } from 'express';

import { autologinController } from '../controllers/auth/autologin.controller';
import { changePasswordController } from '../controllers/auth/changePassword.controller';
import { completeUserRegistrationController } from '../controllers/auth/completeUserRegistration.controller';
import { forgotPassword } from '../controllers/auth/forgotPassword.controller';
import { redefinePasswordController } from '../controllers/auth/redefinePassword.controller';
import { tokenInfoController } from '../controllers/auth/tokenInfo.controller';
import { authenticateToken } from '../middlewares/auth.middleware';
import { authenticateAutologinToken } from '../middlewares/authAutologin.middleware';
import { authenticateExternalSystem } from '../middlewares/authExternalSystem.middleware';

export const autologinRoutes = Router();

autologinRoutes.get(
  '/autologin',
  authenticateExternalSystem,
  authenticateAutologinToken,
  autologinController
);
autologinRoutes.post('/auth/change-password', authenticateToken, changePasswordController);
autologinRoutes.post('/forgot-password', forgotPassword);
autologinRoutes.get('/token-info', tokenInfoController);
autologinRoutes.post('/redefine-password', redefinePasswordController);
autologinRoutes.post('/complete-registration', completeUserRegistrationController);
