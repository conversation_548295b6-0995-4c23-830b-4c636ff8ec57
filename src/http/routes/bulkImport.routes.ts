import { Router } from 'express';
import multer from 'multer';

import { exportBulkImportDetailsController } from '../controllers/bulkImport/exportBulkImportDetails.controller';
import { getBulkImportResultsController } from '../controllers/bulkImport/getBulkImportResults.controller';
import { importValidatedQuestionsController } from '../controllers/bulkImport/importValidatedQuestions.controller';
import { listBulkImportsController } from '../controllers/bulkImport/listBulkImports.controller';
import { reuploadQuestionsExcelController } from '../controllers/bulkImport/reuploadQuestionsExcel.controller';
import { uploadQuestionsExcelController } from '../controllers/bulkImport/uploadQuestionsExcel.controller';
import { authenticateToken } from '../middlewares/auth.middleware';

export const bulkImportRoutes = Router();

const upload = multer({
  storage: multer.memoryStorage(),
});

bulkImportRoutes.get('/list-bulk-imports', authenticateToken, listBulkImportsController);

bulkImportRoutes.post(
  '/questions/bulk-import/upload',
  authenticateToken,
  upload.single('file'),
  uploadQuestionsExcelController
);

bulkImportRoutes.post(
  '/questions/bulk-import/:importId/reupload',
  authenticateToken,
  upload.single('file'),
  reuploadQuestionsExcelController
);

bulkImportRoutes.get(
  '/questions/:importId/results',
  authenticateToken,
  getBulkImportResultsController
);

bulkImportRoutes.get(
  '/questions/bulk-import/:importId/export',
  authenticateToken,
  exportBulkImportDetailsController
);

bulkImportRoutes.post(
  '/questions/bulk-import/:importId/import',
  authenticateToken,
  importValidatedQuestionsController
);
