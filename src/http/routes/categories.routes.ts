import { Router } from 'express';

import { createCategoryController } from '../controllers/categories/createCategory.controller';
import { deleteCategoryController } from '../controllers/categories/deleteCategory.controller';
import { listCategoriesController } from '../controllers/categories/listCategories.controller';
import { manageCategoryAccessInDisciplineController } from '../controllers/categories/manageCategoryAccessInDiscipline.controller';
import { updateCategoryController } from '../controllers/categories/updateCategory.controller';
import { authenticateToken } from '../middlewares/auth.middleware';

export const routesCategories = Router();

routesCategories.post('/categories', authenticateToken, createCategoryController);
routesCategories.put('/categories/:id', authenticateToken, updateCategoryController);
routesCategories.delete('/categories/:id', authenticateToken, deleteCategoryController);
routesCategories.get('/categories', authenticateToken, listCategoriesController);
routesCategories.post(
  '/categories/:disciplineId',
  authenticateToken,
  manageCategoryAccessInDisciplineController
);
