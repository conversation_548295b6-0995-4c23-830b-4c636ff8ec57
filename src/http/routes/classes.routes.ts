import { Router } from 'express';

import { createClassController } from '../controllers/classes/createClass.controller';
import { createExternalClassController } from '../controllers/classes/createExternalClass.controller';
import { deleteClassController } from '../controllers/classes/deleteClass.controller';
import { deleteExternalClassController } from '../controllers/classes/deleteExternalClass.controller';
import { getAllClassesController } from '../controllers/classes/getAllClasses.controller';
import { linkExamsToExternalClassController } from '../controllers/classes/linkExamsToExternalClass.controller';
import { listClassesController } from '../controllers/classes/listClasses.controller';
import { listClassesWithUsersController } from '../controllers/classes/listClassesWithUsers.controller';
import { updateClassController } from '../controllers/classes/updateClass.controller';
import { updateExternalClassController } from '../controllers/classes/updateExternalClass.controller';
import { authenticateToken } from '../middlewares/auth.middleware';
import { authenticateExternalSystem } from '../middlewares/authExternalSystem.middleware';

export const classesRoutes = Router();

classesRoutes.post('/external/classes', authenticateExternalSystem, createExternalClassController);
classesRoutes.put(
  '/external/classes/:id',
  authenticateExternalSystem,
  updateExternalClassController
);
classesRoutes.delete(
  '/external/classes/:id',
  authenticateExternalSystem,
  deleteExternalClassController
);
classesRoutes.post(
  '/external/classes/exams',
  authenticateExternalSystem,
  linkExamsToExternalClassController
);

classesRoutes.post('/classes', authenticateToken, createClassController);
classesRoutes.put('/classes/:id', authenticateToken, updateClassController);
classesRoutes.delete('/classes/:id', authenticateToken, deleteClassController);
classesRoutes.get('/classes/course/:courseId', authenticateToken, getAllClassesController);

classesRoutes.get('/classes-with-users', authenticateToken, listClassesWithUsersController);
classesRoutes.get('/classes', authenticateToken, listClassesController);
