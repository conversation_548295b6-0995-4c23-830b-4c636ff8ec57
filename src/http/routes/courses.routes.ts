import { Router } from 'express';

import { removeUsersFromCourseController } from '../controllers/classes/removeUsersFromCourse.controller';
import { createCourseController } from '../controllers/courses/createCourse.controller';
import { createCourseQuestionBankHandler } from '../controllers/courses/createCourseQuestionBank.controller';
import { getCourseController } from '../controllers/courses/getCourse.controller';
import { getCourseDetailsController } from '../controllers/courses/getCourseDetails.controller';
import { getCourseQuestionBankController } from '../controllers/courses/getCourseQuestionBank.controller';
import { lastAccessCourse } from '../controllers/courses/lastAccessCourse.controller';
import { linkExamsToCourseController } from '../controllers/courses/linkExamsToCourse.controller';
import { listAllCoursesController } from '../controllers/courses/listAllCourses.controller';
import { listAllCoursesSimulationsController } from '../controllers/courses/listAllCoursesSimulations.controller';
import listCoursesByUserController from '../controllers/courses/listCoursesByUser.controller';
import { trackProgressController } from '../controllers/courses/trackProgress.controller';
import { updateCourseController } from '../controllers/courses/updateCourse.controller';
import { authenticateToken } from '../middlewares/auth.middleware';

export const coursesRoutes = Router();

coursesRoutes.get('/courses/last-access', authenticateToken, lastAccessCourse);
coursesRoutes.get('/courses/list-by-user', authenticateToken, listCoursesByUserController);
coursesRoutes.get(
  '/courses/list-simulations',
  authenticateToken,
  listAllCoursesSimulationsController
);
coursesRoutes.get('/courses', authenticateToken, listAllCoursesController);
coursesRoutes.post('/courses', authenticateToken, createCourseController);
coursesRoutes.post('/courses/question-bank', authenticateToken, createCourseQuestionBankHandler);
coursesRoutes.put('/courses/:courseId', authenticateToken, updateCourseController);
coursesRoutes.get('/courses/info/:courseId', authenticateToken, getCourseController);
coursesRoutes.get('/courses/:courseId', authenticateToken, getCourseDetailsController);
coursesRoutes.get(
  '/courses/:courseId/question-bank',
  authenticateToken,
  getCourseQuestionBankController
);
coursesRoutes.post('/courses/link-exams', authenticateToken, linkExamsToCourseController);
coursesRoutes.post('/courses/remove-users', authenticateToken, removeUsersFromCourseController);
coursesRoutes.post('/track-progress', authenticateToken, trackProgressController);
