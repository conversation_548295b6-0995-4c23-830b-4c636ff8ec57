import { Router } from 'express';

import { createExternalCustomerController } from '../controllers/customers/createExternalCustomer.controller';
import { getCustomerController } from '../controllers/customers/getCustomer.controller';
import { updateCustomerController } from '../controllers/customers/updateCustomer.controller';
import { authenticateToken } from '../middlewares/auth.middleware';

export const customersRoutes = Router();

customersRoutes.get('/customers', getCustomerController);
// customersRoutes.get('/customers', authenticateToken, getCustomerByTokenController);
customersRoutes.post('/external/customers', createExternalCustomerController);
customersRoutes.put('/customers', authenticateToken, updateCustomerController);
