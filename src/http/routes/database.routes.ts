import { Request, Response, Router } from 'express';

import { env } from '../../env';
import { httpResponse } from '../../helpers/httpResponse';
import { DatabaseController } from '../controllers/database/database.controller';
import { authDatabase } from '../middlewares/authDatabase.middleware';

const routesDatabaseComands = Router();
const databaseController = new DatabaseController();

routesDatabaseComands.post('/database/migrate', authDatabase, databaseController.migrate);
routesDatabaseComands.post('/database/migrate/remove', authDatabase, databaseController.rollback);
routesDatabaseComands.post('/database/seed', authDatabase, databaseController.seed);
routesDatabaseComands.post('/database/reset', authDatabase, databaseController.reset);

routesDatabaseComands.get('/healthCheck-prod', async (req: Request, res: Response) => {
  const response = await fetch(`${env.BQ_API_URL}/v2/healthCheck`);

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();

  return httpResponse(res, 200, { message: 'Ok', viaCepStatus: 'Connected', testData: data });
});

routesDatabaseComands.get('/healthCheck', async (req: Request, res: Response) => {
  const response = await fetch('https://viacep.com.br/ws/01310-100/json/');
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();

  return httpResponse(res, 200, { message: 'Ok', viaCepStatus: 'Connected', testData: data });
});
export { routesDatabaseComands };
