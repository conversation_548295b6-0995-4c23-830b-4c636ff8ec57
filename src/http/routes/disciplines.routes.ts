import { Router } from 'express';

import { createDisciplinesController } from '../controllers/disciplines/createDisciplines.controller';
import { deleteDisciplinesController } from '../controllers/disciplines/deleteDisciplines.controller';
import { listDisciplinesController } from '../controllers/disciplines/listDisciplines.controller';
import { listDisciplinesAccessHierarchyController } from '../controllers/disciplines/listDisciplinesAccessHierarchy.controller';
import { listDisciplinesWithQuestionCountController } from '../controllers/disciplines/listDisciplinesWithQuestionCount.controller';
import { updateDisciplinesController } from '../controllers/disciplines/updateDisciplines.controller';
import { authenticateToken } from '../middlewares/auth.middleware';

export const routesDisciplines = Router();

routesDisciplines.post('/disciplines', authenticateToken, createDisciplinesController);

routesDisciplines.put('/disciplines/:id', authenticateToken, updateDisciplinesController);
routesDisciplines.delete('/disciplines/:id', authenticateToken, deleteDisciplinesController);
routesDisciplines.get('/disciplines', authenticateToken, listDisciplinesController);
routesDisciplines.get(
  '/disciplines/access/hierarchy',
  authenticateToken,
  listDisciplinesAccessHierarchyController
);
routesDisciplines.get(
  '/disciplines/count/by-question-group/:questionGroupId',
  authenticateToken,
  listDisciplinesWithQuestionCountController
);
