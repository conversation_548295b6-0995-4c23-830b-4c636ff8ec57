import { Router } from 'express';

import { createDomainController } from '../controllers/domains/createDomain.controller';
import { processPendingDomainsController } from '../controllers/domains/processPendingDomains.controller';
import { authenticateToken } from '../middlewares/auth.middleware';

const domainsRoutes = Router();

domainsRoutes.post('/domains', authenticateToken, createDomainController);
domainsRoutes.post('/domains/process-pending', authenticateToken, processPendingDomainsController);

export { domainsRoutes };
