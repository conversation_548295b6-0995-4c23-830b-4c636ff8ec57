import { Router } from 'express';

import { domainEventHttp } from '../controllers/events/createDomain.controller';
import { createSubdomainController } from '../controllers/events/CreateSubdomain.controller';
import { authenticateToken } from '../middlewares/auth.middleware';

export const routesEvents = Router();

routesEvents.post('/events/subdomain', createSubdomainController);
routesEvents.options('/subdomain', (req, res) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
  res.header('Access-Control-Allow-Headers', '*');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.sendStatus(200);
});

routesEvents.post('/events/domain', authenticateToken, domainEventHttp);
