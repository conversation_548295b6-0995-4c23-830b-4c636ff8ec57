import { Router } from 'express';

import { answerExamQuestionController } from '../controllers/exams/answerExamQuestion.controller';
import { createExamController } from '../controllers/exams/createExam.controller';
import { deleteExamController } from '../controllers/exams/deleteExam.controller';
import { externalListExamsController } from '../controllers/exams/externalListExams.controller';
import { finishExamController } from '../controllers/exams/finishExam.controller';
import { getExamQuestionsController } from '../controllers/exams/getExamQuestions.controller';
import { getExamsHandler } from '../controllers/exams/getExams.controller';
import { linkQuestionsToExamController } from '../controllers/exams/linkQuestionsToExam.controller';
import { listExamsController } from '../controllers/exams/listExams.controller';
import { listExamsFiltersController } from '../controllers/exams/listExamsFilters.controller';
import { listPublishedExamsPaginatedController } from '../controllers/exams/listPublishedExamsPaginated.controller';
import { listPublishedQuestionsFromExamController } from '../controllers/exams/listPublishedQuestionsFromExam.controller';
import { listUserExamsController } from '../controllers/exams/listUserExams.controller';
import { pauseResumeExamController } from '../controllers/exams/pauseResumeExam.controller';
import { reorderExamQuestionsController } from '../controllers/exams/reorderExamQuestion.controller';
import { startExamSessionController } from '../controllers/exams/startExamSession.controller';
import { unlinkQuestionFromExamController } from '../controllers/exams/unlinkQuestionFromExam.controller';
import { updateExamController } from '../controllers/exams/updateExam.controller';
import { authenticateToken } from '../middlewares/auth.middleware';
import { authenticateExternalSystem } from '../middlewares/authExternalSystem.middleware';

const examsRoutes = Router();

examsRoutes.get('/exams', authenticateToken, listExamsController);
examsRoutes.get('/exams/:examAccessId/questions', authenticateToken, getExamQuestionsController);
examsRoutes.get(
  '/exams/published/:examId/questions',
  authenticateToken,
  listPublishedQuestionsFromExamController
);
examsRoutes.get(
  '/exams/published/paginated',
  authenticateToken,
  listPublishedExamsPaginatedController
);
examsRoutes.get('/exams/filters', authenticateToken, listExamsFiltersController);
examsRoutes.get('/exams/user', authenticateToken, listUserExamsController);
examsRoutes.get('/ref-data/exams', authenticateToken, getExamsHandler);
examsRoutes.post('/exams', authenticateToken, createExamController);
examsRoutes.post('/exams/:examId/questions', authenticateToken, linkQuestionsToExamController);
examsRoutes.post('/exams/:examId/start', authenticateToken, startExamSessionController);
examsRoutes.post('/exams/:examAccessId/session', authenticateToken, pauseResumeExamController);
examsRoutes.post('/exams/:examAccessId/finish', authenticateToken, finishExamController);
examsRoutes.post(
  '/exams/:examAccessId/questions/:questionId/answer',
  authenticateToken,
  answerExamQuestionController
);
examsRoutes.put('/exams/:id', authenticateToken, updateExamController);
examsRoutes.delete('/exams/:id', authenticateToken, deleteExamController);
examsRoutes.delete(
  '/exams/:examId/questions/:questionId',
  authenticateToken,
  unlinkQuestionFromExamController
);
examsRoutes.post(
  '/exams/:examId/questions/order',
  authenticateToken,
  reorderExamQuestionsController
);
examsRoutes.get('/external/exams', authenticateExternalSystem, externalListExamsController);

export { examsRoutes };
