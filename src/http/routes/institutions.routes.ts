import { Router } from 'express';

import { createInstitutionController } from '../controllers/institutions/createInstitution.controller';
import { deleteInstitutionController } from '../controllers/institutions/deleteInstitution.controller';
import { listInstitutionsController } from '../controllers/institutions/listInstitutions.controller';
import { listInstitutionsPublicController } from '../controllers/institutions/listInstitutionsPublic.controller';
import { updateInstitutionController } from '../controllers/institutions/updateInstitution.controller';
import { authenticateToken } from '../middlewares/auth.middleware';

export const institutionsRoutes = Router();

institutionsRoutes.post('/institutions', authenticateToken, createInstitutionController);
institutionsRoutes.get('/institutions/public', listInstitutionsPublicController);
institutionsRoutes.get('/institutions', authenticateToken, listInstitutionsController);
institutionsRoutes.put('/institutions/:id', authenticateToken, updateInstitutionController);
institutionsRoutes.delete('/institutions/:id', authenticateToken, deleteInstitutionController);
