import { Router } from 'express';
import multer from 'multer';

import { createLessonController } from '../controllers/lessons/createLesson.controller';
import { deleteLessonController } from '../controllers/lessons/deleteLesson.controller';
import { getLessonByIdController } from '../controllers/lessons/getLessonById.controller';
import { reorderLessonsController } from '../controllers/lessons/reorderLessons.controller';
import { updateLessonController } from '../controllers/lessons/updateLesson.controller';
import { authenticateToken } from '../middlewares/auth.middleware';

export const lessonRoutes = Router();

const upload = multer({
  storage: multer.memoryStorage(),
});

lessonRoutes.post('/lessons', authenticateToken, upload.array('files', 3), createLessonController);
lessonRoutes.put('/lessons/order', authenticateToken, reorderLessonsController);
lessonRoutes.get('/lessons/:id', authenticateToken, getLessonByIdController);
lessonRoutes.put(
  '/lessons/:id',
  authenticateToken,
  upload.array('files', 3),
  updateLessonController
);
lessonRoutes.delete('/lessons/:id', authenticateToken, deleteLessonController);
