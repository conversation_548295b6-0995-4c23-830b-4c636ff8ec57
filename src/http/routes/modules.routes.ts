import { Router } from 'express';

import { createSimpleModuleController } from '../controllers/modules/createSimpleModule.controller';
import { deleteModuleController } from '../controllers/modules/deleteModule.controller';
import { reorderModulesController } from '../controllers/modules/reorderModules.controller';
import { updateModuleController } from '../controllers/modules/updateModule.controller';
import { authenticateToken } from '../middlewares/auth.middleware';

export const moduleRoutes = Router();

// moduleRoutes.post('/modules/deprecated', authenticateToken, createModuleController);
moduleRoutes.post('/modules', authenticateToken, createSimpleModuleController);
moduleRoutes.put('/modules/order', authenticateToken, reorderModulesController);
moduleRoutes.put('/modules/:id', authenticateToken, updateModuleController);
moduleRoutes.delete('/modules/:id', authenticateToken, deleteModuleController);
