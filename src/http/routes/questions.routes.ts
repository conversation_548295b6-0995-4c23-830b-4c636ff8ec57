import { Router } from 'express';

import { createQuestionController } from '../controllers/questions/create-question.controller';
import { deleteQuestionController } from '../controllers/questions/deleteQuestion.controller';
import { getQuestionByIdController } from '../controllers/questions/getQuestionById.controller';
import { listPaginatedQuestionsController } from '../controllers/questions/listPaginatedQuestions.controller';
import { listQuestionInGroupController } from '../controllers/questions/listQuestionInGroup.controller';
import { manageQuestionHighlightsController } from '../controllers/questions/manageQuestionHighlights.controller';
import { questionCodeController } from '../controllers/questions/questionCode.controller';
import { updateQuestionController } from '../controllers/questions/updateQuestion.controller';
import { authenticateToken } from '../middlewares/auth.middleware';

export const routesQuestions = Router();

routesQuestions.post('/questions', authenticateToken, createQuestionController);

routesQuestions.get('/questions', authenticateToken, listPaginatedQuestionsController);
routesQuestions.get('/code/questions', authenticateToken, questionCodeController);
routesQuestions.get('/questions/:questionId', authenticateToken, getQuestionByIdController);
routesQuestions.put('/questions/:id', authenticateToken, updateQuestionController);
routesQuestions.delete('/questions/:id', authenticateToken, deleteQuestionController);
routesQuestions.get(
  '/questions/questions-groups/:id',
  authenticateToken,
  listQuestionInGroupController
);

routesQuestions.post(
  '/questions/:questionId/highlights',
  authenticateToken,
  manageQuestionHighlightsController
);
