import { Router } from 'express';

import { createQuestionGroupController } from '../controllers/questionsGroups/createQuestionsGroups.Controller';
import { deleteQuestionGroupController } from '../controllers/questionsGroups/deleteQuestionGroups.Controller';
import { ExternalListQuestionGroupController } from '../controllers/questionsGroups/ExternalListQuestionsGroups.Controller';
import { getExternalQuestionGroupController } from '../controllers/questionsGroups/getExternalQuestionGroup.controller';
import { getQuestionsGroupsWithAccessController } from '../controllers/questionsGroups/getQuestionsGroupsWithAccess.controller';
import { listPublishedQuestionsFromGroupController } from '../controllers/questionsGroups/listPublishedQuestionsFromGroup.controller';
import { ListQuestionGroupController } from '../controllers/questionsGroups/listQuestionsGroups.Controller';
import { ListQuestionsGroupWithCountController } from '../controllers/questionsGroups/listQuestionsGroupWithCount.Controller';
import { updateQuestionGroupController } from '../controllers/questionsGroups/updateQuestionsGroups.Controller';
import { manageQuestionGroupAccessController } from '../controllers/questionsGroupsAccess/manageQuestionGroupAccess.controller';
import { authenticateToken } from '../middlewares/auth.middleware';
import { authenticateExternalSystem } from '../middlewares/authExternalSystem.middleware';

export const routesQuestionsGroups = Router();

routesQuestionsGroups.post('/questions-groups', authenticateToken, createQuestionGroupController);
routesQuestionsGroups.put(
  '/questions-groups/:id',
  authenticateToken,
  updateQuestionGroupController
);
routesQuestionsGroups.delete(
  '/questions-groups/:id',
  authenticateToken,
  deleteQuestionGroupController
);

routesQuestionsGroups.get(
  '/external/questions-groups',
  authenticateExternalSystem,
  ExternalListQuestionGroupController
);

routesQuestionsGroups.get(
  '/ref-data/question-groups',
  authenticateToken,
  ListQuestionsGroupWithCountController
);

routesQuestionsGroups.get('/questions-groups', authenticateToken, ListQuestionGroupController);

routesQuestionsGroups.get(
  '/questions-groups/:questionId',
  authenticateToken,
  getQuestionsGroupsWithAccessController
);

routesQuestionsGroups.post(
  '/question-group-access/manage',
  authenticateToken,
  manageQuestionGroupAccessController
);

routesQuestionsGroups.get(
  '/questions-groups/:questionGroupId/questions',
  authenticateToken,
  listPublishedQuestionsFromGroupController
);

routesQuestionsGroups.get(
  '/external/questions-groups/:id',
  authenticateExternalSystem,
  getExternalQuestionGroupController
);
