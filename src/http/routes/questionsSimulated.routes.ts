import { Router } from 'express';

import { answerQuestionSimulatedController } from '../controllers/simulateds/answerQuestionSimulated.controller';
import { getAllQuestionsSimulatedController } from '../controllers/simulateds/getAllQuestionsSimulated.controller';
import { authenticateToken } from '../middlewares/auth.middleware';

export const routesQuestionsSimulated = Router();

routesQuestionsSimulated.get(
  '/simulated/:simulatedId/:userId',
  authenticateToken,
  getAllQuestionsSimulatedController
);

routesQuestionsSimulated.post(
  '/simulated/:simulatedAccessId/questions/:questionSimulatedId/answer',
  authenticateToken,
  answerQuestionSimulatedController
);
