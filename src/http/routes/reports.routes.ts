import { Router } from 'express';

import { createQuestionReportController } from '../controllers/reports/createQuestionReport.controller';
import { listReportsController } from '../controllers/reports/listReports.controller';
import { authenticateToken } from '../middlewares/auth.middleware';

export const reportsRoutes = Router();

reportsRoutes.post('/reports/questions', authenticateToken, createQuestionReportController);
reportsRoutes.get('/reports', authenticateToken, listReportsController);
