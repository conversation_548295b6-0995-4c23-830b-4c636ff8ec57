import { Router } from 'express';
import swaggerUi from 'swagger-ui-express';

import swaggerExternalSpec from '../../config/swagger-external.config';
import swaggerSpec from '../../config/swagger.config';
import { globalErrorHandler } from '../middlewares/errors/error.midllewares';
import accessLevelsRoutes from './accessLevels.routes';
import { autologinRoutes } from './auth.routes';
import { bulkImportRoutes } from './bulkImport.routes';
import { routesCategories } from './categories.routes';
import { classesRoutes } from './classes.routes';
import { countriesRouter } from './countries.routes';
import { coursesRoutes } from './courses.routes';
import { coursesCategoriesRoutes } from './coursesCategories.routes';
import { coursesExamsRoutes } from './coursesExams.routes';
import { customersRoutes } from './customers.routes';
import { routesDatabaseComands } from './database.routes';
import { routesDisciplines } from './disciplines.routes';
import { domainsRoutes } from './domains.routes';
import { routesEvents } from './events.routes';
import { examsRoutes } from './exams.routes';
import { filesRoutes } from './files.routes';
import { routesGraduations } from './graduations.routes';
import { institutionsRoutes } from './institutions.routes';
import { lessonRoutes } from './lesson.routes';
import { moduleRoutes } from './modules.routes';
import { routesQuestions } from './questions.routes';
import { routesQuestionsGroups } from './questionsGroups.routes';
import { routesQuestionsSimulated } from './questionsSimulated.routes';
import { routesQuestionsTypes } from './questionsTypes.routes';
import { reportsRoutes } from './reports.routes';
import { routesSimulateds } from './simulateds.routes';
import { simulatedTemplatesRoutes } from './simulatedTemplates.routes';
import { routesSpecialties } from './specialties.routes';
import { routesSubcategories } from './subcategories.routes';
import { routesDatabase } from './temporary.routes';
import { termsRoutes } from './terms.routes';
import { uploadRoutes } from './upload.routes';
import { routesUsers } from './users.routes';

export const routes = Router();

routes.use(routesUsers);
routes.use(routesDisciplines);
routes.use(routesCategories);
routes.use(routesSubcategories);
routes.use(routesDatabase);
routes.use(routesDatabaseComands);
routes.use(routesQuestionsSimulated);
routes.use(routesEvents);
routes.use(routesSimulateds);
routes.use(routesQuestionsGroups);
routes.use(routesQuestions);
routes.use(routesQuestionsTypes);
routes.use(customersRoutes);
routes.use(autologinRoutes);
routes.use(classesRoutes);
routes.use(simulatedTemplatesRoutes);
routes.use(reportsRoutes);
routes.use(uploadRoutes);
routes.use(filesRoutes);
routes.use(domainsRoutes);
routes.use(examsRoutes);
routes.use(institutionsRoutes);
routes.use(bulkImportRoutes);
routes.use(coursesRoutes);
routes.use(moduleRoutes);
routes.use(coursesExamsRoutes);
routes.use(coursesCategoriesRoutes);
routes.use(lessonRoutes);
routes.use(termsRoutes);
routes.use(countriesRouter);
routes.use(accessLevelsRoutes);
routes.use(routesGraduations);
routes.use(routesSpecialties);
routes.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
routes.use('/external-api-docs', swaggerUi.serve, swaggerUi.setup(swaggerExternalSpec));
routes.use(globalErrorHandler);
