import { Router } from 'express';

import { createSimulatedTemplateController } from '../controllers/simulatedTemplates/createSimulatedTemplate.controller';
import { deleteSimulatedTemplateController } from '../controllers/simulatedTemplates/deleteSimulatedTemplate.controller';
import { getSimulatedTemplatesCountController } from '../controllers/simulatedTemplates/getSimulatedTemplatesCount.controller';
import { listPublishedQuestionsFromTemplateController } from '../controllers/simulatedTemplates/listPublishedQuestionsFromTemplate.controller';
import { listSimulatedTemplatesController } from '../controllers/simulatedTemplates/listSimulatedTemplates.controller';
import { startSimulatedFromTemplateController } from '../controllers/simulatedTemplates/startSimulatedFromTemplate.controller';
import { authenticateToken } from '../middlewares/auth.middleware';

export const simulatedTemplatesRoutes = Router();

simulatedTemplatesRoutes.post(
  '/simulated-templates',
  authenticateToken,
  createSimulatedTemplateController
);

simulatedTemplatesRoutes.get(
  '/simulated-templates/count',
  authenticateToken,
  getSimulatedTemplatesCountController
);

simulatedTemplatesRoutes.get(
  '/simulated-templates',
  authenticateToken,
  listSimulatedTemplatesController
);

simulatedTemplatesRoutes.delete(
  '/simulated-templates/:id',
  authenticateToken,
  deleteSimulatedTemplateController
);

simulatedTemplatesRoutes.get(
  '/simulated-templates/:id/questions',
  authenticateToken,
  listPublishedQuestionsFromTemplateController
);

simulatedTemplatesRoutes.post(
  '/simulated-templates/:id/start',
  authenticateToken,
  startSimulatedFromTemplateController
);
