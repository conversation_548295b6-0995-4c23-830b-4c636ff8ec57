import { Router } from 'express';

import { createExclusiveSimulatedController } from '../controllers/simulateds/createExclusiveSimulated.controller';
import { finishSimulatedController } from '../controllers/simulateds/finishSimulated.controller';
import { getAllQuestionsBySimulatedIdController } from '../controllers/simulateds/getAllQuestionsBySimulatedId.controller';
import { getExclusiveSimulatedStatsController } from '../controllers/simulateds/getExclusiveSimulatedStats.controller';
import { getSimulatedByIdController } from '../controllers/simulateds/getSimulatedById.controller';
import { getSimulatedWithQuestionsByIdController } from '../controllers/simulateds/getSimulatedWithQuestionsById.controller';
import { linkQuestionsToSimulatedController } from '../controllers/simulateds/linkQuestionsToSimulated.controller';
import { listExclusiveSimulatedsPaginatedController } from '../controllers/simulateds/listExclusiveSimulatedsPaginated.controller';
import { listUserExclusiveSimulatedsController } from '../controllers/simulateds/listUserExclusiveSimulateds.controller';
import { listUserExclusiveSimulatedsUserHistoryController } from '../controllers/simulateds/listUserExclusiveSimulatedsHistory.controller';
import { listUserSimulatedsController } from '../controllers/simulateds/listUserSimulateds.controller';
import { listUsersInExclusiveSimulatedController } from '../controllers/simulateds/listUsersInExclusiveSimulated.controller';
import { manageClassesInExclusiveSimulatedController } from '../controllers/simulateds/manageClassesInExclusiveSimulated.controller';
import { manageUserToExclusiveSimulatedController } from '../controllers/simulateds/manageUserToExclusiveSimulated.controller';
import { pauseResumeSimulatedController } from '../controllers/simulateds/pauseResumeSimulated.controller';
import { reorderSimulatedQuestionsController } from '../controllers/simulateds/reorderSimulatedQuestions.controller';
import { startExclusiveSimulatedSessionController } from '../controllers/simulateds/startExclusiveSimulatedSession.controller';
import { unlinkQuestionFromExclusiveSimulatedController } from '../controllers/simulateds/unlinkQuestionFromExclusiveSimulated.controller';
import { updateExclusiveSimulatedController } from '../controllers/simulateds/updateExclusiveSimulated.controller';
import { authenticateToken } from '../middlewares/auth.middleware';

const routesSimulateds = Router();

routesSimulateds.get('/simulateds/user', authenticateToken, listUserSimulatedsController);
routesSimulateds.get(
  '/simulateds/exclusive',
  authenticateToken,
  listExclusiveSimulatedsPaginatedController
);
routesSimulateds.post(
  '/simulateds/exclusive',
  authenticateToken,
  createExclusiveSimulatedController
);
routesSimulateds.put(
  '/simulateds/exclusive/:id',
  authenticateToken,
  updateExclusiveSimulatedController
);
routesSimulateds.get('/simulateds/:simulatedId', authenticateToken, getSimulatedByIdController);
routesSimulateds.get(
  '/simulateds/:simulatedAccessId/questions',
  authenticateToken,
  getSimulatedWithQuestionsByIdController
);
routesSimulateds.post(
  '/simulateds/:simulatedAccessId/finish',
  authenticateToken,
  finishSimulatedController
);
routesSimulateds.post(
  '/simulateds/:simulatedAccessId/session',
  authenticateToken,
  pauseResumeSimulatedController
);

routesSimulateds.post(
  '/simulateds/exclusive/:simulatedId/classes',
  authenticateToken,
  manageClassesInExclusiveSimulatedController
);

routesSimulateds.post(
  '/simulateds/exclusive/:simulatedId/questions',
  authenticateToken,
  linkQuestionsToSimulatedController
);

routesSimulateds.post(
  '/simulateds/exclusive/:simulatedId/users',
  authenticateToken,
  manageUserToExclusiveSimulatedController
);

routesSimulateds.post(
  '/simulateds/exclusive/:simulatedId/questions/order',
  authenticateToken,
  reorderSimulatedQuestionsController
);

routesSimulateds.get(
  '/simulateds/:simulatedId/questions-all',
  authenticateToken,
  getAllQuestionsBySimulatedIdController
);

routesSimulateds.get(
  '/simulateds/exclusive/:simulatedId/users',
  authenticateToken,
  listUsersInExclusiveSimulatedController
);

routesSimulateds.get(
  '/simulateds/user/exclusive',
  authenticateToken,
  listUserExclusiveSimulatedsController
);

routesSimulateds.get(
  '/simulateds/user/exclusive/history',
  authenticateToken,
  listUserExclusiveSimulatedsUserHistoryController
);

routesSimulateds.delete(
  '/simulateds/exclusive/:simulatedId/questions/:questionId',
  authenticateToken,
  unlinkQuestionFromExclusiveSimulatedController
);

routesSimulateds.post(
  '/simulateds/exclusive/:simulatedAccessId/start',
  authenticateToken,
  startExclusiveSimulatedSessionController
);

routesSimulateds.get(
  '/simulateds/exclusive/:simulatedId/stats',
  authenticateToken,
  getExclusiveSimulatedStatsController
);

export { routesSimulateds };
