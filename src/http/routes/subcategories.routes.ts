import { Router } from 'express';

import { addSubcategoryController } from '../controllers/subcategories/addSubcategory.controller';
import { deleteSubcategoryController } from '../controllers/subcategories/deleteSubcategory.controller';
import { manageSubcategoryInDisciplineController } from '../controllers/subcategories/manageSubcategoryInDiscipline.controller';
import { updateSubcategoryController } from '../controllers/subcategories/updateSubcategory.controller';
import { authenticateToken } from '../middlewares/auth.middleware';

export const routesSubcategories = Router();

routesSubcategories.put('/subcategories/:id', authenticateToken, updateSubcategoryController);
routesSubcategories.post('/subcategories/:categoryId', authenticateToken, addSubcategoryController);
routesSubcategories.delete('/subcategories/:id', authenticateToken, deleteSubcategoryController);
routesSubcategories.post(
  '/categories/subcategories/:disciplineId',
  authenticateToken,
  manageSubcategoryInDisciplineController
);
routesSubcategories.post(
  '/categories/subcategories/:disciplineId',
  authenticateToken,
  manageSubcategoryInDisciplineController
);
