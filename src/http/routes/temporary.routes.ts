import { Request, Response, Router } from 'express';

import { httpResponse } from '../../helpers/httpResponse';
import { populateDatabase } from '../controllers/temp/databaseSeeder.controller';
import { getFirstCustomerController } from '../controllers/temp/listFirstCustomer.controller';
import { createSimulated } from '../controllers/temp/simulatedSeeder.controller';
import { userSeederController } from '../controllers/temp/userSeeder.controller';

export const routesDatabase = Router();

routesDatabase.get('/', (req: Request, res: Response) =>
  httpResponse(res, 200, {
    title: 'Conexão bem-sucedida!',
    description:
      'A conexão entre o front-end e o back-end foi estabelecida com sucesso. Tudo está funcionando como esperado!',
  })
);

routesDatabase.post('/populate-database', populateDatabase);
routesDatabase.post('/populate-simulated', createSimulated);
routesDatabase.post('/populate-users', userSeederController);
routesDatabase.get('/customers/first', getFirstCustomerController);
