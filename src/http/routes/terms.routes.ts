import { Router } from 'express';
import multer from 'multer';

import { createTermController } from '../controllers/terms/createTerm.controller';
import { getTermController } from '../controllers/terms/getTerm.controller';
import { listAllTermsController } from '../controllers/terms/listTerms.controller';
import { updateTermController } from '../controllers/terms/updateTerm.controller';
import { authenticateToken } from '../middlewares/auth.middleware';

export const termsRoutes = Router();

const upload = multer({
  storage: multer.memoryStorage(),
});

termsRoutes.post('/terms', authenticateToken, upload.single('file'), createTermController);
termsRoutes.put('/terms/:id', authenticateToken, updateTermController);
termsRoutes.get('/terms', authenticateToken, listAllTermsController);
termsRoutes.get('/terms/:id', authenticateToken, getTermController);
