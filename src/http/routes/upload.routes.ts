import { Router } from 'express';
import multer from 'multer';

import { removeQuestionImageController } from '../controllers/deleteImage/removeQuestionImage.controller';
import { uploadFileController } from '../controllers/files/uploadFile.controller';
import { uploadQuestionImageController } from '../controllers/questions/uploadQuestionImage.controller';
import { authenticateToken } from '../middlewares/auth.middleware';

export const uploadRoutes = Router();

const upload = multer({
  storage: multer.memoryStorage(),
});

uploadRoutes.post(
  '/questions-image',
  authenticateToken,
  upload.single('file'),
  uploadQuestionImageController
);
uploadRoutes.post('/files/upload', authenticateToken, upload.single('file'), uploadFileController);
uploadRoutes.delete('/questions-image', authenticateToken, removeQuestionImageController);
