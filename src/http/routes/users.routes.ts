import { Router } from 'express';

import { googleAuthController } from '../controllers/auth/googleAuth.controller';
import { loginUserController } from '../controllers/auth/login/loginUser.controller';
import { addStudentsByEmailController } from '../controllers/students/addStudentsByEmail.controller';
import { createStudentController } from '../controllers/students/createStudent.controller';
import { listStudentsController } from '../controllers/students/listStudents.controller';
import { updateStudentController } from '../controllers/students/updateStudent.controller';
import { createInitialUserController } from '../controllers/temp/createInitialUser.controller';
import { createExternalUserController } from '../controllers/users/createExternalUser.controller';
import { createUserAdminController } from '../controllers/users/createUserAdmin.controller';
import { deleteExternalUserController } from '../controllers/users/deleteExternalUser.controller';
import { deleteUserAdminController } from '../controllers/users/deleteUserAdmin.controller';
import { getUserController } from '../controllers/users/getUser.controller';
import { getUserByIdController } from '../controllers/users/getUserById.controller';
import { getUserByIdAndCourseIdController } from '../controllers/users/getUserByIdAndCourseId.controller';
import { listUsersAdmByCustomerController } from '../controllers/users/listUsersAdmByCustomer.controller';
import { listUsersWithClassesController } from '../controllers/users/listUsersWithClasses.controller';
import { updateExternalUserController } from '../controllers/users/updateExternalUser.controller';
import { updateUserController } from '../controllers/users/updateUser.controller';
import { updateUserAdminController } from '../controllers/users/updateUserAdmin.controller';
import { authenticateToken } from '../middlewares/auth.middleware';
import { authenticateExternalSystem } from '../middlewares/authExternalSystem.middleware';

export const routesUsers = Router();

routesUsers.post('/users', createInitialUserController);
routesUsers.post('/login', loginUserController);
routesUsers.post('/auth/google', googleAuthController);
routesUsers.post('/users/register', authenticateExternalSystem, createExternalUserController);
routesUsers.post('/users/adm', authenticateToken, createUserAdminController);
routesUsers.put('/external/users/:id', authenticateExternalSystem, updateExternalUserController);
routesUsers.delete('/external/users/:id', authenticateExternalSystem, deleteExternalUserController);
routesUsers.get('/users-with-classes', authenticateToken, listUsersWithClassesController);
routesUsers.get('/users', authenticateToken, getUserController);
routesUsers.put('/users', authenticateToken, updateUserController);
routesUsers.put('/users/adm', authenticateToken, updateUserAdminController);
routesUsers.get('/users/admins', authenticateToken, listUsersAdmByCustomerController);
routesUsers.get('/users/adm/:userId', authenticateToken, getUserByIdController);
routesUsers.delete('/users/adm/:userId', authenticateToken, deleteUserAdminController);
routesUsers.get('/users/students', authenticateToken, listStudentsController);
routesUsers.post('/user', authenticateToken, createStudentController);
routesUsers.put('/student/:userId', authenticateToken, updateStudentController);
routesUsers.post('/students/add-by-email', authenticateToken, addStudentsByEmailController);
routesUsers.get('/users/:userId/user-details', authenticateToken, getUserByIdAndCourseIdController);
