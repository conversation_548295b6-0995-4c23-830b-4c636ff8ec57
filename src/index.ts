import cors from 'cors';
import 'dotenv/config';
import express from 'express';
import serverless from 'serverless-http';

import 'express-async-errors';
import { env } from './env';
import { routes } from './http/routes/routes';

const app = express();

app.use(express.json());
app.use(
  cors({
    origin: process.env.CORS_ORIGIN,
  })
);
app.use(express.urlencoded({ extended: true }));

app.use('/v2', routes);
app.all('*', (req: express.Request, res: express.Response) => {
  const requestedRoute = req.originalUrl;
  return res.status(404).send(`ROUTE: ${requestedRoute} NOT FOUND.`);
});

app.listen(env.PORT, () => {
  console.log(`Server is running in port ${env.PORT}`);
});

export const handler = serverless(app);
