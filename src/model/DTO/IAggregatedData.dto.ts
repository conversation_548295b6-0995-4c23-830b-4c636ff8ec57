import { IAnswerSession } from '../IAnswerSession';
import { ICategory } from '../ICategory';
import { IDiscipline } from '../IDiscipline';
import { IQuestion } from '../IQuestion';
import { IQuestionGroup } from '../IQuestionGroup';
import { IQuestionType } from '../IQuestionType';
import { ISimulated } from '../ISimulated';
import { ISimulatedAccess } from '../ISimulatedAcces';
import { ISubCategory } from '../ISubcategory';
import { IQuestionWithAlternativesDTO } from './IQuestion.dto';

export interface IRequestedEntitiesDTO {
  questionId?: string;
  customerId: string;
  disciplineId?: string;
  categoryId?: string;
  subcategoryId?: string;
  questionTypeId?: string;
  questionGroupId?: string;
  simulatedId?: string;
  userId?: string;
  simulatedAccessId?: string;
  answerSessions?: boolean;
  questionWithAlternatives?: boolean;
}

export interface IRetrievedEntitiesDTO {
  question: IQuestion | null;
  discipline: IDiscipline | null;
  category: ICategory | null;
  subcategory: ISubCategory | null;
  questionType: IQuestionType | null;
  questionGroup: IQuestionGroup | null;
  simulated: ISimulated | null;
  simulatedAccess: ISimulatedAccess | null;
  questionWithAlternatives: IQuestionWithAlternativesDTO | null;
  answerSessionsData: IAnswerSession[] | null;
}
