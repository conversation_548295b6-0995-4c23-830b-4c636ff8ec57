import { IClasses } from '../IClasses';
import { IUserInClass } from './IUsers.dto';

export type IClassesDTO = Omit<IClasses, 'id' | 'created_at' | 'updated_at' | 'deleted_at'>;

export type IClassDataForCreation = Omit<
  IClasses,
  'id' | 'created_at' | 'updated_at' | 'deleted_at'
>;

export interface ISoftDeleteClassDTO {
  id: string;
  customerId: string;
}

export interface IClassWithUsers {
  id: string;
  name: string;
  totalUsers: number;
  users: IUserInClass[];
}

export interface IUserRowDTO {
  id: string;
  first_name: string;
  last_name: string;
}

export interface IClassRowDTO {
  id: string;
  name: string;
  users: string | IUserRowDTO[];
}
