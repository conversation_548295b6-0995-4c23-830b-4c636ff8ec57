import { ICourse } from '../ICourse';
import { IModule } from '../IModule';

export interface ICourseLastAccess {
  userName: string;
  courseId: string;
  courseName: string;
  logoUrl?: string;
  moduleName: string;
  lessonTitle: string;
  lessonId: string;
  lessonDuration: string;
  currentPosition: string;
  progressPercent: number;
  remainingTime: string;
  lastAccessedAt: Date | string;
  status: string;
}

export interface ICourseDetail extends ICourse {
  modules: IModule[];
  categories: { id: string; name: string }[];
}

export interface ICourseDetailsResponse {
  id: string;
  title: string;
  status: string;
  lastLessonAccessed: string | null;
  lastModuleAccessed: string | null;
  modules: {
    id: string;
    title: string;
    description: string;
    status: string;
    totalLessons: number;
    completedLessons: number;
    lessons: {
      id: string;
      title: string;
      description: string;
      url: string;
      questionGroupId: string | null;
      type: string;
      duration: string;
      currentPosition: string;
      progressPercent: number;
      currentPositionSeconds: number;
      status: string;
      files: {
        id: string;
        url: string;
        file_name: string;
        file_type: string;
        file_size: number;
        created_at: Date;
        updated_at: Date;
      }[];
      classes: {
        id: string;
        name: string;
      }[];
    }[];
  }[];
}
