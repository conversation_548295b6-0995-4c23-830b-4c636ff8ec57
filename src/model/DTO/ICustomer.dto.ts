import { ICustomer } from '../ICustomer';

export interface ICustomerWithoutMetadata
  extends Omit<ICustomer, 'id' | 'created_at' | 'updated_at' | 'deleted_at'> {}

export interface ICustomerInsertDTO
  extends Omit<ICustomer, 'created_at' | 'updated_at' | 'deleted_at'> {}

export interface ICustomerWithTokenDTO {
  customerName: string;
  email?: string;
  taxNumber?: string;
  logoUrl?: string;
  primaryColor?: string;
  secondaryColor?: string;
  status?: boolean;
  subdomain?: string;
  websiteUrl?: string;
  id?: string;
  supportEmail?: string;
}

export type ICustomerPublicDTO = ICustomer;

export interface ICustomerWithId extends ICustomerWithoutMetadata {
  id: string;
  supportEmail?: string | null;
}
