import z from 'zod';

import { listDisciplinesWithQuestionCountSchema } from '../../schema/disciplines.schema';
import { IDiscipline } from '../IDiscipline';
import { IDisciplineCategoriesAccess } from '../IDisciplineCategoryAccess';
import { IDisciplineSubcategoriesAccess } from '../IDisciplineSubcategoryAccess';

export interface IDisciplineDTO {
  name: string;
  code: string;
  customer_id: string;
}

export interface IUpdateDiscipline extends Partial<IDiscipline> {
  id: string;
}

export interface ICategoryAccessDTO {
  categoryId: string;
  categoryName: string;
  subcategories: Array<{
    subcategoryId: string;
    subcategoryName: string;
  }>;
}

export interface IDisciplinesAccessHierarchyDTO {
  discipline: IDiscipline;
  category: IDisciplineCategoriesAccess;
  subcategory: IDisciplineSubcategoriesAccess;
}

export type ListDisciplinesWithQuestionCountDTO = z.infer<
  typeof listDisciplinesWithQuestionCountSchema
>;

interface IQuestionCountByDifficulty {
  totalQuestions: number;
  totalQuestionsEasy: number;
  totalQuestionsMedium: number;
  totalQuestionsHard: number;
}

export interface ISubcategoryWithQuestionCount extends IQuestionCountByDifficulty {
  id: string;
  name: string;
}

export interface ICategoryWithQuestionCount extends IQuestionCountByDifficulty {
  id: string;
  name: string;
  subcategories: ISubcategoryWithQuestionCount[];
}

export interface IDisciplineWithQuestionCount extends IQuestionCountByDifficulty {
  id: string;
  name: string;
  categories: ICategoryWithQuestionCount[];
}
