import { ISubcategoryAccessDTO } from './IDisciplineSubcategoryAccess.dto';

export interface IAddCategoryInDisciplineDTO {
  customer_id: string;
  discipline_id: string;
  category_id: string;
}

export interface IDisciplineSubcategoriesAccessCreate {
  discipline_id: string;
  subcategory_id: string;
  customer_id: string;
  category_id: string;
}

export interface ICategoryAccessDTO {
  categoryId: string;
  categoryName: string;
  subcategories: ISubcategoryAccessDTO[];
}
