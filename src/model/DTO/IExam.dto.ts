import { IExam } from '../IExam';
import { IQuestionWithAlternativesDTO } from './IQuestion.dto';

export interface IExamWithProgressDTO {
  id: string;
  name: string;
  active: boolean;
  institution_id: string;
  institution_name: string;
  created_at: string;
  updated_at: string;
  start_exams: string | null;
  end_exams: string | null;
  access_id: string;
  total_questions?: number;
  answered_questions?: number;
  correct_answers?: number;
}

export interface FindByUserWithProgressParams {
  userId: string;
  customerId: string;
  courseId?: string;
}

export interface IGetPublishedQuestionsFromExamDTO {
  examId: string;
  customerId: string;
}

export interface IPublishedQuestionsFromExamDTO {
  exam: IExam;
  questions: IQuestionWithAlternativesDTO[];
  customer: {
    id: string;
    name: string;
    primary_color: string;
    secondary_color: string;
    website: string;
    subdomain: string;
  };
}
