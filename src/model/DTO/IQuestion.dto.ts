import { QuestionStatusEnum } from '../enums/questions.enum';
import { IAlternative } from '../IAlternative';
import { IQuestion } from '../IQuestion';
import { IBaseAlternativeDTO } from './IAlternative.dto';

export type IQuestionDTO = Omit<IQuestion, 'id' | 'created_at' | 'updated_at' | 'deleted_at'>;

export interface IQuestionCreateDTO {
  title: string;
  description?: string;
  difficulty?: string;
  imageUrl?: string;
  explanationVideo?: string;
  explanationText?: string;
  explanationImage?: string;
  published?: boolean;
  status?: QuestionStatusEnum;
  correctText?: string;
  questionTypeId?: string;
  disciplineId?: string;
  categoryId?: string;
  subcategoryId?: string;
  reference?: string;
  institution?: string;
  year?: number | null;
  customerId: string;
  alternatives?: IBaseAlternativeDTO[];
}

export interface QuestionValidationDTO {
  title?: string;
  customerId?: string;
  status?: QuestionStatusEnum;
  description?: string;
  difficulty?: string;
  explanationText?: string;
  correctText?: string;
  questionTypeId?: string;
  published_at?: Date;
  disciplineId?: string;
  categoryId?: string;
  subcategoryId?: string;
  alternatives?: { option: string; description: string }[];
  published?: boolean;
}

export interface IQuestionUpdateDTO {
  id: string;
  title: string;
  description?: string;
  difficulty?: string;
  image_url?: string;
  explanation_video?: string;
  explanation_text?: string;
  explanation_image?: string;
  published?: boolean;
  published_at?: Date;
  status?: QuestionStatusEnum;
  correct_text?: string;
  question_type_id?: string | null | undefined;
  discipline_id?: string | null | undefined;
  category_id?: string | null | undefined;
  subcategory_id?: string | null | undefined;
  reference?: string;
  institution?: string;
  year?: number | null;
  customer_id: string;
  alternatives?: IBaseAlternativeDTO[];
}

export interface IQuestionRequestDTO {
  publishedAt?: Date;
  title: string;
  description?: string;
  difficulty?: string;
  imageUrl?: string;
  explanationVideo?: string;
  explanationText?: string;
  explanationImage?: string;
  published: boolean;
  status?: QuestionStatusEnum;
  correctText?: string;
  questionTypeId?: string;
  disciplineId?: string;
  categoryId?: string;
  questionId: string;
  subcategoryId?: string;
  reference?: string;
  institution?: string;
  year?: number | null;
  customerId: string;
  alternatives?: (Omit<IBaseAlternativeDTO, 'question_id'> &
    Partial<Pick<IBaseAlternativeDTO, 'question_id'>>)[];
}

export interface IlistQuestionFilterDTO {
  customerId: string;
  page?: number;
  limit?: number;
  difficulty?: string[];
  status?: string[];
  disciplineIds?: string[];
  categoryIds?: string[];
  subcategoryIds?: string[];
  search?: string;
  orderByColumn?: string;
  orderDirection?: string;
  questionGroupId?: string;
  examId?: string;
  allQuestions?: string;
}

export interface IListQuestionInGroupFilterDTO {
  customerId: string;
  difficulty?: string[];
  disciplineIds?: string[];
  categoryIds?: string[];
  subcategoryIds?: string[];
  questionGroupId: string;
}

export interface IQuestionWithAlternativesDTO {
  id: string;
  published_at: Date;
  title: string;
  description: string;
  difficulty: string;
  image_url?: string;
  explanation_video?: string;
  explanation_text: string;
  explanation_image?: string;
  published: boolean;
  status: QuestionStatusEnum;
  correct_text?: string;
  question_type_id?: string;
  discipline_id: string;
  category_id: string;
  subcategory_id: string;
  reference?: string;
  institution?: string;
  year?: number | null;
  alternatives: IAlternative[];
}

export interface IFindSimilarCodesDTO {
  baseCode: string;
  customerId: string;
}

export interface IQuestionTitleDTO {
  title: string;
}

export interface IQuestionIdentifierDTO {
  questionId: string;
  customerId: string;
}

export interface IUploadQuestionImageDTO {
  imageUrl: string;
  questionId: string;
  customerId: string;
}

export interface ListQuestionsWithFiltersParams extends IListQuestionInGroupFilterDTO {
  quantity?: number;
}

export interface QuestionCountResult {
  exam_id: string;
  count: string | number;
}
