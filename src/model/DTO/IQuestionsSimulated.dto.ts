import { IBaseAlternativeDTO } from './IAlternative.dto';

export interface IQuestionSimulatedWithAlternativesDTO {
  question_id: string;
  title: string;
  description: string;
  category_id: string;
  question_simulated_id: string;
  order_by: number;
  simulated_id: string;
  published: boolean;
  explanation_text: string | null;
  explanation_video: string | null;
  explanation_image: string | null;
  alternatives: IBaseAlternativeDTO[];
}

export interface IQuestionSimulatedWithExtendedDataDTO
  extends IQuestionSimulatedWithAlternativesDTO {
  saved: boolean;
  highlight_description: string[];
  highlight_explanation_text: string[];
}
