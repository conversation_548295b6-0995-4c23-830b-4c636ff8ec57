import { IReport } from '../IReport';

export interface IReportDTO
  extends Omit<IReport, 'id' | 'created_at' | 'updated_at' | 'deleted_at'> {}

export interface IReportCreateDTO {
  description?: string;
  category: string;
  platform?: string;
  web_browser?: string;
  isMobile?: boolean;
  device?: string;
  browser?: string;
  operational_system?: string;
  user_agent?: string;
  status?: string;
  viewed?: boolean;
  finished?: boolean;
  send_email?: boolean;
  user_id: string;
  question_id: string;
}

export interface IReportListDTO extends IReport {
  question_title?: string;
  user_first_name?: string;
  user_last_name?: string;
  user_email?: string;
}

export interface IReportListParams {
  customerId: string;
  userId?: string;
  questionId?: string;
  examId?: string;
  simulatedAccessId?: string;
  questionGroupId?: string;
  status?: string[];
  orderByColumn?: string;
  orderDirection?: 'asc' | 'desc';
}

export interface IReportListResult {
  reports: IReportListDTO[];
}

export interface IReportPaginatedResponse {
  reports: IReportListDTO[];
}
