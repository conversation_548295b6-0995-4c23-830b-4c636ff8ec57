import { ISimulated } from '../ISimulated';

export type ISimulatedDTO = Omit<ISimulated, 'id' | 'created_at' | 'updated_at' | 'deleted_at'>;
export interface ISimulatedByIdDTO {
  id: string;
  name: string;
  active: boolean;
  simulated_type_id: string;
  time_limit?: number | null;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
}

export interface ISimulatedWithProgressDTO {
  id: string;
  name: string;
  active: boolean;
  simulated_type_id: string;
  simulated_type_name: string;
  created_at: string;
  updated_at: string;
  access_id: string;
  start_simulated: string | null;
  end_simulated: string | null;
  access_active?: boolean;
}

export interface IFindByUserWithProgressParams {
  userId: string;
  customerId: string;
  questionGroupId: string;
}

export interface ISimulatedWithProgressAndStatsDTO extends ISimulatedWithProgressDTO {
  total_questions: number;
  answered_questions: number;
  correct_answers: number;
}

export interface IListExclusiveSimulatedsPaginatedResponse {
  simulateds: ISimulated[];
  paginationInfo?: {
    currentPage: number;
    itemsPerPage: number;
    totalItems: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface SimulatedInfoDTO {
  id: string;
  name: string;
  active?: boolean;
  simulated_type_id: string;
  simulated_type_name: string;
  question_group_id?: string;
  status?: string;
  time_limit?: number;
  start_date?: string | null;
  end_date?: string | null;
  customer_id?: string;
  total_questions?: number;
}

export interface UserSimulatedAccessDTO {
  access_id: string;
  start_simulated: string | null;
  end_simulated: string | null;
}

export interface IExclusiveSimulatedStatsDTO {
  totalFinished: number;
  avgScorePercentage: number;
  avgTimeSeconds: number;
}
