import { ICustomer } from '../ICustomer';
import { ISimulatedTemplate } from '../ISimulatedTemplate';
import { IQuestionWithAlternativesDTO } from './IQuestion.dto';

export interface CreateTemplateInput {
  name: string;
  user_id: string;
  simulated_type_id?: string;
  question_group_id?: string;
  time_limit?: number;
}

export type CreateSimulatedTemplateInput = Omit<
  ISimulatedTemplate,
  'id' | 'created_at' | 'updated_at' | 'deleted_at'
>;

export interface IUserCustomerParams {
  userId: string;
  customerId: string;
}

export interface IGetSimulatedTemplatesCountDTO extends IUserCustomerParams {
  questionGroupId: string;
}

export interface IListSimulatedTemplatesDTO extends IUserCustomerParams {
  questionGroupId: string;
}

export interface ISimulatedTemplatesResult {
  templates: ISimulatedTemplate[];
}

export interface IPublishedQuestionsFromTemplateDTO {
  template: ISimulatedTemplate;
  questions: IQuestionWithAlternativesDTO[];
  customer: ICustomer;
}

export interface IGetSimulatedTemplatesDTO {
  templateId: string;
  customerId: string;
}
