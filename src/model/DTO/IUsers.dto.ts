import { IUser } from '../IUser';

export type IUserDTO = Omit<IUser, 'id' | 'created_at' | 'updated_at' | 'deleted_at'>;

export interface ICreateUserExternal extends Partial<IUserDTO> {
  external_user_id: string;
  customer_id: string;
  first_name: string;
  last_name: string;
  email: string;
  created_by: string;
  terms_accept?: boolean;
  gender?: string;
  birth_date?: string;
  phone_number?: string;
  cpf?: string;
  password?: string;
  active?: boolean;
  user_type_id?: string;
}

export interface ISoftDeleteUserDTO {
  id: string;
  customerId: string;
}

export type UserPublicDTO = Omit<
  Partial<IUser>,
  'created_by' | 'user_type_id' | 'terms_accept' | 'external_user_id'
>;

export interface IUserInClass
  extends Pick<IUser, 'id' | 'first_name' | 'last_name' | 'email' | 'customer_id' | 'role'> {
  marked?: boolean;
}

export interface IListStudentsParamsDTO {
  customerId: string;
  orderByColumn?: string;
  orderByDirection?: string;
  courseId?: string;
  accessLevelId?: string;
}

export interface IListUsersAdmParamsDTO extends IUser {
  firstName: string;
  lastName: string;
  createdAt: string;
  birthDate: string;
  phoneNumber: string;
  termsAccept: boolean;
  customerId: string;
  accessLevelName?: string;
  accessLevelDescription?: string;
  accessLevelId?: string;
  lastAccessedAt?: Date | null;
}

export interface ICreateUserFromGoogleDTO {
  email: string;
  first_name: string;
  last_name: string;
  customer_id: string | null;
  created_by: string;
  active: boolean;
  image: string | null;
  role: string;
  terms_accept: boolean;
}

export interface ICreateUserFromEmailDTO {
  email: string;
  customer_id: string;
}
