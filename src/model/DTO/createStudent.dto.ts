export interface CreateStudentRequestDto {
  firstName: string;
  lastName: string;
  email: string;
  birthDate?: string;
  gender?: string;
  cpf: string;
  phoneNumber?: string;
  customerId: string;
  classesId?: string;
}

export interface CreateStudentResponseDto {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  birthDate?: string;
  gender?: string;
  phoneNumber?: string;
  cpf: string;
  customerId: string;
  classId?: string;
}
