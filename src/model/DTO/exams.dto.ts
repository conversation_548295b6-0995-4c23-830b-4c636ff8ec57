import { OrderDirection } from '../../services/sorting.service';
import { IExam } from '../IExam';
import { IPaginationDTO } from './IGeneric.dto';

export interface IExamCreateDTO {
  name: string;
  institution_id?: string | null;
  year?: number | null;
  published?: boolean;
  status: string;
  customer_id?: string;
  exam_time: number | null;
}

export interface ListExamsParams {
  customerId: string;
  search?: string;
  orderByColumn?: string;
  orderDirection?: OrderDirection;
}

export interface ListPublishedExamsPaginatedParams {
  examIds?: string[];
  customerId: string;
  search?: string;
  institution?: string;
  year?: number;
  page?: number;
  limit?: number;
  orderByColumn?: string;
  orderDirection?: OrderDirection;
}

export interface PublishedExamDTO extends IExam {
  totalQuestions: number;
}

export interface PublishedExamsWithPaginationDTO {
  exams: PublishedExamDTO[];
  paginationInfo: IPaginationDTO;
}

export interface ExamsFilters {
  institutions: { id: string; name: string; acronym: string | null }[];
  years: (number | null)[];
}

export interface GetExamsFiltersParams {
  examIds: string[];
  customerId: string;
}
