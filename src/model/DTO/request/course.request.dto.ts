export interface ICourseDetailsRawData {
  course_name: string;
  course_id: string;
  module_id: number;
  module_name: string;
  module_description: string;
  lesson_id: number;
  lesson_title: string;
  lesson_description: string;
  lesson_url: string;
  question_group_id: number | null;
  lesson_duration: string;
  course_status: string;
  module_status: string;
  lesson_status: string;
  lesson_current_position: number;
  lesson_files: Array<{
    id: string;
    url: string;
    file_name: string;
    file_type: string;
    file_size: number;
    created_at: Date;
    updated_at: Date;
  }>;
}
