import { ProgressStatus } from '../../enums/progressStatus.enum';

export interface ITrackProgressRequest {
  courseId: string;
  moduleId: string;
  lessonId: string;
  progress: number;
  userId: string;
  timeWatched?: number;
}

export interface IUpdateProgressRequest {
  userId: string;
  lessonId: string;
  progress: number;
  status: ProgressStatus;
}

export interface IGetProgressRequest {
  userId: string;
  courseId?: string;
  moduleId?: string;
  lessonId?: string;
}

export interface IGetUserProgressRequest {
  userId: string;
  courseId: string;
}

export interface IGetModuleProgressRequest {
  userId: string;
  moduleId: string;
}

export interface IGetLessonProgressRequest {
  userId: string;
  lessonId: string;
}

export interface IProgressFilterRequest {
  userId: string;
  status?: ProgressStatus;
  startDate?: Date;
  endDate?: Date;
  courseId?: string;
  moduleId?: string;
}

export interface IProgressStatsRequest {
  userId: string;
  courseId?: string;
  period?: 'day' | 'week' | 'month' | 'year';
  startDate?: Date;
  endDate?: Date;
}

export interface IResetProgressRequest {
  userId: string;
  courseId?: string;
  moduleId?: string;
  lessonId?: string;
  confirmReset: boolean;
}
