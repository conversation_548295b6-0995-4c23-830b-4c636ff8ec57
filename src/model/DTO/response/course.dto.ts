export interface CreateCourseResponseDto {
  id: number;
  name: string;
  description_course: string;
  photo_url: string;
  customer_id: string;
}

export interface ListCoursesByUserAndClassResponseDto extends CreateCourseResponseDto {
  class_name: string;
  classes_id: number;
  lastLessonAccess?: {
    id: string;
    title: string;
    moduleId: string;
    moduleName: string;
  };
  questionGroupId?: string;
}

export interface ICourseWithoutMetadata {
  name: string;
  description_course: string;
  photo_url?: string | null;
  customer_id: string;
}
