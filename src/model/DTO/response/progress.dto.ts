import { ProgressStatus } from '../../enums/progressStatus.enum';
import { ILessonResponse } from './lesson.dto';

export interface ILessonProgress {
  current_position: number;
  status: ProgressStatus;
  started_at?: Date;
  last_accessed_at?: Date;
  completed_at?: Date;
}

export interface IModuleProgress {
  status: ProgressStatus;
  started_at?: Date;
  last_accessed_at?: Date;
  completed_at?: Date;
}

export interface ICourseProgress {
  status: ProgressStatus;
  started_at?: Date;
  last_accessed_at?: Date;
  completed_at?: Date;
}

export interface ILastModuleAccess {
  module_id: string;
  last_accessed_at: Date;
}

export interface ILastLessonAccess {
  lesson_id: string;
  last_accessed_at: Date;
}

export interface IModuleProgressData {
  totalLessons: number;
  completedLessons: number;
  progressPercentage: number;
  progress: IModuleProgress | undefined;
}

export interface ICourseProgressData {
  totalModules: number;
  completedModules: number;
  progressPercentage: number;
  progress: ICourseProgress | undefined;
}

export interface IProgressHierarchyData {
  moduleData: IModuleProgressData;
  courseData: ICourseProgressData;
}

export interface ILessonWithProgress {
  lesson: ILessonResponse | undefined;
  currentProgress: ILessonProgress | undefined;
  progressPercentage: number;
}

export interface IModuleWithProgress {
  moduleId: string;
  moduleName: string;
  totalLessons: number;
  completedLessons: number;
  progressPercentage: number;
  status: ProgressStatus;
}

export interface ICourseWithProgress {
  courseId: string;
  courseName: string;
  totalModules: number;
  completedModules: number;
  progressPercentage: number;
  status: ProgressStatus;
}

export interface IProgressSummary {
  totalLessons: number;
  completedLessons: number;
  totalModules: number;
  completedModules: number;
  overallProgress: number;
  estimatedTimeRemaining: number; // em segundos
}

export interface ITrackingData {
  userId: string;
  lessonId: string;
  moduleId: string;
  courseId: string;
  progress: number;
  timeWatched?: number;
  timestamp: Date;
}
