import { ProgressStatus } from '../../enums/progressStatus.enum';
import {
  ICourseWithProgress,
  ILessonWithProgress,
  IModuleWithProgress,
  IProgressSummary,
} from './progress.dto';

export interface ITrackProgressResponse {
  success: boolean;
  data: {
    lessonProgress: number;
    lessonStatus: ProgressStatus;
    moduleCompleted: boolean;
    courseCompleted: boolean;
  };
  message?: string;
}

export interface IGetProgressResponse {
  success: boolean;
  data: ILessonWithProgress;
  message?: string;
}

export interface IGetUserProgressResponse {
  success: boolean;
  data: {
    courseProgress: ICourseWithProgress;
    modulesProgress: IModuleWithProgress[];
    summary: IProgressSummary;
  };
  message?: string;
}

export interface IGetModuleProgressResponse {
  success: boolean;
  data: {
    moduleProgress: IModuleWithProgress;
    lessonsProgress: ILessonWithProgress[];
  };
  message?: string;
}

export interface IProgressStatsResponse {
  success: boolean;
  data: {
    totalTimeWatched: number; // em segundos
    averageSessionTime: number; // em segundos
    lessonsCompletedToday: number;
    lessonsCompletedThisWeek: number;
    lessonsCompletedThisMonth: number;
    progressTrend: {
      date: string;
      lessonsCompleted: number;
    }[];
  };
  message?: string;
}

export interface IProgressHierarchyResponse {
  success: boolean;
  data: {
    course: {
      id: string;
      name: string;
      progress: number;
      status: ProgressStatus;
    };
    modules: {
      id: string;
      name: string;
      progress: number;
      status: ProgressStatus;
      lessons: {
        id: string;
        title: string;
        progress: number;
        status: ProgressStatus;
      }[];
    }[];
  };
  message?: string;
}

export interface IUpdateProgressResponse {
  success: boolean;
  data: {
    updated: boolean;
    previousStatus: ProgressStatus;
    newStatus: ProgressStatus;
    progressChange: number;
  };
  message?: string;
}

export interface IResetProgressResponse {
  success: boolean;
  data: {
    resetItems: string[];
    resetCount: number;
  };
  message?: string;
}

export interface IProgressErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: unknown;
  };
}
