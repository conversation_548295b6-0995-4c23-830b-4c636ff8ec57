export interface CreateUserAdminResponseDto {
  userId: string;
  customerId: string;
  email: string;
  fullName: string;
  accessLevelId: string;
}

export interface UpdateUserAdminResponseDto {
  userId: string;
  customerId: string;
  email: string;
  fullName: string;
  accessLevelId: string;
}

export interface ListStudentsResponseDto {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  class_id: string | null;
  class_name: string | null;
  last_accessed_at: string | null;
  customer_id: string;
  access_level_id: number;
  image: string | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface UpdateStudentResponseDto {
  id: string;
  firstName: string;
  lastName?: string;
  email: string;
  birthDate?: string;
  gender?: string;
  phoneNumber?: string;
  class_id: string | null;
  cpf?: string;
}

export interface TokenInfoResponseDto {
  id: string;
  email: string;
  expiresAt: string;
  used_at?: string | null;
  recoveryId?: string;
}
