import { BaseModel } from 'bq-knex-base-repository';

import { BulkImportStatus } from './enums/bulkImportStatus.enum';

export interface IBulkImport extends BaseModel {
  id: string;
  file_name: string;
  file_key: string | null;
  total_rows: number;
  valid_count: number;
  invalid_count: number;
  success_percentage: number;
  status: BulkImportStatus;
  customer_id: string;
  created_by: string;
  updated_by?: string;
  imported_at?: Date;
  created_at: Date;
  updated_at: Date;
}
