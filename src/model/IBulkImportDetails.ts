import { BaseModel } from 'bq-knex-base-repository';

import { ExcelRow } from '../services/excelProcessor.service';
import { ExcelRowData } from '../use-case/bulkImportQuestions/uploadQuestionsExcel.useCase';

export interface IBulkImportDetail extends BaseModel {
  id: string;
  bulk_import_id: string;
  row_data: ExcelRow | ExcelRowData | null;
  is_valid: boolean;
  errors?: string[];
  row_index: number;
  question_id?: string;
  created_at: Date;
  updated_at: Date;
}
