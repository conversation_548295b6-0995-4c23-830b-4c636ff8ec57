import { BaseModel } from 'bq-knex-base-repository';

export interface ICustomer extends BaseModel {
  name: string;
  email?: string;
  tax_number?: string;
  logo_url?: string;
  primary_color?: string;
  secondary_color?: string;
  status?: boolean;
  subdomain?: string;
  website?: string;
  external_customer_id?: string;
  support_email?: string | null;
  image?: string;
  additional_info?: string;
  access_level_id?: string;
}
