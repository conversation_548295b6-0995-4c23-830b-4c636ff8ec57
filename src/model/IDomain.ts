import { BaseModel } from 'bq-knex-base-repository';

export interface IDomain extends BaseModel {
  domain: string;
  hosted_zone_id: string;
  certificate_arn: string;
  customer_id?: string;
  status: 'pending' | 'validating' | 'validated' | 'cloudfront_created' | 'error';
  error_message?: string;
  cloudfront_distribution_id?: string;
  cloudfront_distribution_url?: string;
  validated_at?: Date;
  cloudfront_created_at?: Date;
  last_processed_at?: Date;
}
