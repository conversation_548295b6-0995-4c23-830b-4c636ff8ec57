import { BaseModel } from 'bq-knex-base-repository';

export interface IQuestion extends BaseModel {
  title: string;
  description: string;
  difficulty: string;
  image_url?: string;
  explanation_video?: string;
  explanation_text: string;
  explanation_image?: string;
  published: boolean;
  published_at: Date | null;
  status: string;
  correct_text?: string;
  reference?: string;
  institution?: string;
  year?: number | null;
  category_id: string | null;
  question_type_id?: string | null;
  discipline_id: string | null;
  subcategory_id: string | null;
  customer_id: string;
}
