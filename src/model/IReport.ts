import { BaseModel } from 'bq-knex-base-repository';

export interface IReport extends BaseModel {
  description?: string;
  category?: string;
  platform?: string;
  web_browser?: string;
  isMobile?: boolean;
  device?: string;
  browser?: string;
  operational_system?: string;
  user_agent?: string;
  status?: string;
  viewed?: boolean;
  finished?: boolean;
  send_email?: boolean;
  user_id: string;
  question_id: string;
}
