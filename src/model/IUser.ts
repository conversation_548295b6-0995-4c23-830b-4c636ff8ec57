import { BaseModel } from 'bq-knex-base-repository';

export interface IUser extends BaseModel {
  first_name: string;
  last_name?: string;
  gender?: string;
  birth_date?: string;
  phone_number?: string;
  cpf?: string;
  email: string;
  password?: string;
  active?: boolean;
  terms_accept?: boolean;
  customer_id: string;
  user_type_id?: string;
  external_user_id?: string;
  created_by?: string;
  role: string;
  image?: string;
  access_level_id?: string;
  additional_info?: Record<string, unknown>;
  last_accessed_at?: Date;
  country_id?: string | null;
  ddi?: string | null;
  accept_marketing?: boolean | null;
  accept_newsletter?: boolean | null;
}
