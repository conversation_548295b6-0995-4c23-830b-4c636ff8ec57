import { BaseModel } from 'bq-knex-base-repository';

export interface IUserAdditionalInfo extends BaseModel {
  user_id: string;
  referral_source?: string | null;
  formation?: string | null;
  institution_id?: string | null;
  graduation_id?: string | null;
  completion_year?: number | null;
  has_specialty?: boolean | null;
  primary_specialty_id?: string | null;
  secondary_specialty_id?: string | null;
  currently_attending?: boolean | null;
  attendance_location?: string | null;
}
