export enum QuestionStatusEnum {
  Draft = 'draft',
  Published = 'published',
  InReview = 'inReview',
}

export enum DifficultyEnum {
  Easy = 'easy',
  Medium = 'medium',
  Hard = 'hard',
}

export enum QuestionOrderByColumnEnum {
  Id = 'id',
  Title = 'title',
  Description = 'description',
  CreatedAt = 'created_at',
  UpdatedAt = 'updated_at',
  DeletedAt = 'deleted_at',
  Difficulty = 'difficulty',
  Status = 'status',
}
