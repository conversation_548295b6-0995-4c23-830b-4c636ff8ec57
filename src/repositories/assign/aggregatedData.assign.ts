import { IRequestedEntitiesDTO, IRetrievedEntitiesDTO } from '../../model/DTO/IAggregatedData.dto';
import { IAlternative } from '../../model/IAlternative';

export interface IAggregatedDataAssignRepository {
  findEntities(data: IRequestedEntitiesDTO): Promise<IRetrievedEntitiesDTO>;
  findAlternativesAnsweredByQuestionId(data: {
    questionId: string;
    customerId: string;
    tablesName: string[];
  }): Promise<Record<string, IAlternative[]>>;
}
