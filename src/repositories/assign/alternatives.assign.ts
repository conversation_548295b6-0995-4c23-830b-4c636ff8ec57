import { IAlternativeWithIdDTO, IBaseAlternativeDTO } from '../../model/DTO/IAlternative.dto';
import { IAlternative } from '../../model/IAlternative';

export interface IAlternativesRepository {
  insertAll(data: IBaseAlternativeDTO[]): Promise<IAlternative[]>;
  deleteByQuestionId(data: { questionId: string }): Promise<void>;
  findByQuestionId(data: { questionId: string }): Promise<IAlternative[] | undefined>;
  update(item: IAlternativeWithIdDTO): Promise<IAlternative>;
  updateAll(data: IAlternativeWithIdDTO[]): Promise<IAlternative[]>;
  deleteAllPermanent(ids: string[]): Promise<void>;
}
