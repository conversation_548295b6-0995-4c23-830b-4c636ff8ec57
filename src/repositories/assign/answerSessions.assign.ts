import { K<PERSON> } from 'knex';

import { IAnswerSession } from '../../model/IAnswerSession';

export interface IAnswerSessionsRepository {
  findBySimulatedAccessId(simulatedAccessId: string): Promise<IAnswerSession[]>;
  findByExamAccessId(examAccessId: string): Promise<IAnswerSession[]>;
  insert(data: Partial<IAnswerSession>): Promise<IAnswerSession>;
  update(data: Partial<IAnswerSession>, transaction?: Knex.Transaction): Promise<IAnswerSession>;
  findOneBy(data: Partial<IAnswerSession>): Promise<IAnswerSession | undefined>;
}
