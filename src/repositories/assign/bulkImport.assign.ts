import { BulkImportsWithPaginationDTO } from '../../model/DTO/bulkImport.dto';
import { IBulkImport } from '../../model/IBulkImport';
import { ListBulkImportsParams } from '../../schema/bulkImport.schema';

export interface IBulkImportRepository {
  insert(data: Partial<IBulkImport>): Promise<IBulkImport>;
  update(data: Partial<IBulkImport>): Promise<IBulkImport>;
  findOneBy(data: Partial<IBulkImport>): Promise<IBulkImport | undefined>;
  listBulkImports(params: ListBulkImportsParams): Promise<BulkImportsWithPaginationDTO>;
  updateStatus(importId: string, status: string): Promise<void>;
  updateFileKey(importId: string, fileKey: string): Promise<void>;
}
