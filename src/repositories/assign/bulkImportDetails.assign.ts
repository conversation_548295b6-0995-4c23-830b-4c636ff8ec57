import { BulkImportDetailInput } from '../../model/DTO/bulkImportDetails.dto';
import { IBulkImportDetail } from '../../model/IBulkImportDetails';

export interface IBulkImportDetailRepository {
  insert(data: Partial<IBulkImportDetail>): Promise<IBulkImportDetail>;
  update(data: Partial<IBulkImportDetail>): Promise<IBulkImportDetail>;
  updateBatch(updates: Partial<IBulkImportDetail>[]): Promise<IBulkImportDetail[]>;
  findOneBy(data: Partial<IBulkImportDetail>): Promise<IBulkImportDetail | undefined>;
  insertBatch(details: BulkImportDetailInput[]): Promise<void>;
  findByImportId(importId: string): Promise<IBulkImportDetail[]>;
  findValidByImportId(importId: string): Promise<IBulkImportDetail[]>;
  findInvalidByImportId(importId: string): Promise<IBulkImportDetail[]>;
  countByImportId(importId: string): Promise<{
    totalValid: number;
    totalInvalid: number;
  }>;
  deleteByImportId(importId: string): Promise<void>;
  findAllRowsByBulkImportId(importId: string): Promise<IBulkImportDetail[]>;
}
