import { ICategoryFilterDTO, ICreateCategoryDTO } from '../../model/DTO/ICategory.dto';
import { ICategory } from '../../model/ICategory';

export interface ICategoriesRepository {
  // TODO: Futuramente passar os parâmetros como objeto
  findByNameAndCustomerId(name: string, customerId: string): Promise<ICategory | undefined>;
  insert(data: ICreateCategoryDTO): Promise<ICategory | undefined>;
  get(id: string): Promise<ICategory | undefined>;
  findCategoryIdAndCustomerId(id: string, customerId: string): Promise<ICategory>;
  deleteById(id: string): Promise<void>;
  findAllByCustomerId(data: ICategoryFilterDTO): Promise<ICategory[]>;
  getTotalCatregories(customerId: string): Promise<number>;
}
