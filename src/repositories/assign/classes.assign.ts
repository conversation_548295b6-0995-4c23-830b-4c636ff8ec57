import { <PERSON><PERSON> } from 'knex';

import {
  IClassDataForCreation,
  IClassWithUsers,
  ISoftDeleteClassDTO,
} from '../../model/DTO/IClasses.dto';
import { IPaginationDTO } from '../../model/DTO/IGeneric.dto';
import { IClasses } from '../../model/IClasses';
import { ListClassesQueryInput, ListClassesWithUsersInput } from '../../schema/classes.schema';

export interface IClassesRepository {
  findByExternalClassId(data: {
    externalClassId: string;
    customerId: string;
  }): Promise<IClasses | undefined>;
  createExternalClass(data: IClassDataForCreation): Promise<IClasses>;
  findOneBy(data: Partial<IClasses>): Promise<IClasses | undefined>;
  updateWithTrx(item: Partial<IClasses>, trx?: Knex.Transaction): Promise<IClasses>;
  softDelete(data: ISoftDeleteClassDTO): Promise<IClasses | undefined>;
  findByExternalClassIds(data: {
    externalClassIds: string[];
    customerId: string;
  }): Promise<IClasses[]>;
  findByIds(ids: string[]): Promise<IClasses[]>;
  findClassesWithUsersPaginated(data: ListClassesWithUsersInput): Promise<{
    classes: IClassWithUsers[];
    paginationInfo: IPaginationDTO;
  }>;
  listByCustomerId(data: ListClassesQueryInput): Promise<IClasses[]>;
  getAllClasses(courseId: string): Promise<IClasses[]>;
  createClass(data: IClassDataForCreation, trx?: Knex.Transaction): Promise<IClasses>;
  update(item: Partial<IClasses>): Promise<IClasses>;
  countUsersInClass(classId: string): Promise<number>;
  deleteClass(id: string): Promise<void>;
  insert(data: Partial<IClasses>, trx?: Knex.Transaction): Promise<IClasses>;
  findByClassesByCoursesId(coursesId: string[]): Promise<IClasses[]>;
}
