import { Knex } from 'knex';

import { ICountry } from '../../model/countries';
import { ListAllCountriesDto } from '../../schema/countries.schema';

export interface ICountriesRepository {
  insertManyCountries(countries: Partial<ICountry>[], trx?: Knex.Transaction): Promise<ICountry[]>;
  findAllCountries(data: ListAllCountriesDto): Promise<ICountry[]>;
  findOneBy(data: Partial<ICountry>): Promise<ICountry | undefined>;
}
