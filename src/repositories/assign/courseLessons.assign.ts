import { <PERSON><PERSON> } from 'knex';

import { ILessonResponse } from '../../model/DTO/response/lesson.dto';
import { ICourseLesson } from '../../model/ICourseLesson';

export interface ICourseLessonRepository {
  createLesson(data: Partial<ICourseLesson>): Promise<ICourseLesson>;
  updateLessonById(id: string, data: Partial<ICourseLesson>): Promise<void>;
  insertLesson(data: Partial<ICourseLesson>, trx?: Knex.Transaction): Promise<ICourseLesson>;
  countPublishedLessonsByModuleId(moduleId: string): Promise<number>;
  updateLesson(data: Partial<ICourseLesson>, trx?: Knex.Transaction): Promise<ICourseLesson>;
  findOneBy(data: Partial<ICourseLesson>): Promise<ICourseLesson | undefined>;
  updateLessonsOrderBatch(
    lessons: { id: string; order_by: number }[],
    trx?: Knex.Transaction
  ): Promise<void>;
  findByModuleId(moduleId: string): Promise<ICourseLesson[]>;
  getLessonById(id: string): Promise<ILessonResponse | undefined>;
  softDeleteLessonsByModuleId(moduleId: string, trx?: Knex.Transaction): Promise<ICourseLesson[]>;
  softDeleteLesson(id: string, trx?: Knex.Transaction): Promise<ICourseLesson | undefined>;
}
