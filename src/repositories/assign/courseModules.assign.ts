import { <PERSON><PERSON> } from 'knex';

import { ICourseModule } from '../../model/ICourseModule';

export interface ICourseModuleRepository {
  createModule(data: Partial<ICourseModule>): Promise<ICourseModule>;
  updateModuleById(id: string, data: Partial<ICourseModule>): Promise<void>;
  updateModule(data: Partial<ICourseModule>, trx?: Knex.Transaction): Promise<ICourseModule>;
  updateModulesOrderBatch(
    modules: { id: string; order_by: number }[],
    trx?: Knex.Transaction
  ): Promise<void>;
  insertModule(data: Partial<ICourseModule>, trx?: Knex.Transaction): Promise<ICourseModule>;
  countModulesByCourseId(courseId: string): Promise<number>;
  findByCourseId(courseId: string): Promise<ICourseModule[]>;
  findOneBy(data: Partial<ICourseModule>): Promise<ICourseModule | undefined>;
  softDeleteModule(id: string, trx?: Knex.Transaction): Promise<ICourseModule>;
}
