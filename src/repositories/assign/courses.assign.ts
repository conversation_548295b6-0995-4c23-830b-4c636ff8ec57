import { K<PERSON> } from 'knex';

import { ICourseLastAccess } from '../../model/DTO/ICourse.dto';
import { ICourseDetailsRawData } from '../../model/DTO/request/course.request.dto';
import {
  ICourseWithoutMetadata,
  ListCoursesByUserAndClassResponseDto,
} from '../../model/DTO/response/course.dto';
import { ILastLessonAccess, ILastModuleAccess } from '../../model/DTO/response/progress.dto';
import { ICourse } from '../../model/ICourse';

export interface ICourseRepository {
  lastAccess(userId: string): Promise<ICourseLastAccess | undefined>;
  listAllCoursesUser(userId: string): Promise<ListCoursesByUserAndClassResponseDto[]>;
  listAllCoursesSimulations(customerId: string, userId: string): Promise<ICourse[]>;
  listAllCourses(customerId: string): Promise<ICourse[]>;
  findOneBy(data: Partial<ICourse>): Promise<ICourse | undefined>;
  insert(data: ICourseWithoutMetadata, trx?: Knex.Transaction): Promise<ICourse>;
  findCourseById(courseId: string): Promise<ICourse | undefined>;
  getCourseQuestionBankByCourseId(courseId: string): Promise<ICourse | null>;
  update(data: Partial<ICourse>, trx?: Knex.Transaction): Promise<ICourse | undefined>;
  findCourseUser(data: { user_id: string }): Promise<ICourse[] | undefined>;
  getCourseDetails(courseId: string, userId: string): Promise<ICourseDetailsRawData[]>;
  getLastModuleAccess(userId: string, courseId: string): Promise<ILastModuleAccess | undefined>;
  getLastLessonAccess(userId: string, courseId: string): Promise<ILastLessonAccess | undefined>;
}
