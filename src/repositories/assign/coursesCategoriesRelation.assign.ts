import { Knex } from 'knex';

import { ICoursesCategoriesRelation } from '../../model/coursesCategoriesRelation';

export interface ICoursesCategoriesRelationRepository {
  findAllByCourseId(data: { course_id: string }): Promise<ICoursesCategoriesRelation[]>;
  deleteByCourseIdAndCategoryIds(
    courseId: string,
    categoryIds: string[],
    trx?: Knex.Transaction
  ): Promise<void>;
  insertAll(
    data: ICoursesCategoriesRelation[],
    trx?: Knex.Transaction
  ): Promise<ICoursesCategoriesRelation[]>;
}
