import { ICourseExams } from '../../model/ICourseExams';

export interface ICourseExamsRepository {
  getExamIdsByCourseId(courseId: string): Promise<string[]>;
  insertAll(courseExams: ICourseExams[]): Promise<ICourseExams[]>;
  findByCourseId(courseId: string): Promise<ICourseExams[]>;
  deleteByCourseIdAndExamIds(courseId: string, examIds: string[]): Promise<void>;
  deleteByCourseId(courseId: string): Promise<ICourseExams[]>;
}
