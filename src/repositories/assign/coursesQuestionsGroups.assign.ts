import { <PERSON><PERSON> } from 'knex';

import { ICourseQuestionsGroups } from '../../model/ICourseQuestionsGroups';

export interface ICourseQuestionsGroupsRepository {
  findOneBy(data: Partial<ICourseQuestionsGroups>): Promise<ICourseQuestionsGroups | undefined>;
  insert(data: ICourseQuestionsGroups, trx?: Knex.Transaction): Promise<ICourseQuestionsGroups>;
  update(
    data: Partial<ICourseQuestionsGroups>,
    trx?: Knex.Transaction
  ): Promise<ICourseQuestionsGroups>;
}
