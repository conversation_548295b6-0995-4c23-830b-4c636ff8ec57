import { ICustomerWithId, ICustomerWithoutMetadata } from '../../model/DTO/ICustomer.dto';
import { ICustomer } from '../../model/ICustomer';

export interface ICustomerRepository {
  get(id: string): Promise<ICustomer | undefined>;
  getTaxNumber(taxNumber: string): Promise<ICustomer | undefined>;
  findOneBy(data: Partial<ICustomer>): Promise<ICustomer | undefined>;
  findById(id: string): Promise<ICustomer | undefined>;
  insert(item: ICustomerWithoutMetadata): Promise<ICustomer>;
  findByExternalId(externalCustomerId: string): Promise<ICustomer | undefined>;
  update(item: ICustomerWithId): Promise<ICustomer | undefined>;
  getDefaultCustomer(): Promise<ICustomer | undefined>;
}
