import { ICustomerIdDTO } from '../../model/DTO/ICustumers.dto';
import {
  IDisciplineDTO,
  IDisciplinesAccessHierarchyDTO,
  IDisciplineWithQuestionCount,
  IUpdateDiscipline,
  ListDisciplinesWithQuestionCountDTO,
} from '../../model/DTO/IDiscipline.dto';
import { IDiscipline } from '../../model/IDiscipline';

export interface IDisciplinesRepository {
  insert(data: IDisciplineDTO): Promise<IDiscipline | undefined>;
  findAll(): Promise<IDiscipline[]>;
  findByNameAndCustomerId(name: string, customerId: string): Promise<IDiscipline | undefined>;
  findById(id: string): Promise<IDiscipline | undefined>;
  update(item: IUpdateDiscipline): Promise<IDiscipline>;
  findAllCodesByCustomerId(customerId: string): Promise<string[]>;
  deleteById(id: string, customerId: string): Promise<IDiscipline>;

  listByCustomerId(data: {
    customerId: string;
    search?: string;
    orderByColumn?: string;
    orderDirection?: string;
  }): Promise<IDiscipline[]>;
  listDisciplinesWithAccessHierarchy(
    data: ICustomerIdDTO
  ): Promise<IDisciplinesAccessHierarchyDTO[]>;
  listDisciplinesWithAccessHierarchyForImport(
    data: ICustomerIdDTO
  ): Promise<IDisciplinesAccessHierarchyDTO[]>;
  listDisciplinesWithQuestionCount(
    data: ListDisciplinesWithQuestionCountDTO
  ): Promise<IDisciplineWithQuestionCount[]>;
}
