import { IAddCategoryInDisciplineDTO } from '../../model/DTO/IDisciplineCategoryAccess.dto';
import { IDisciplineCategoriesAccess } from '../../model/IDisciplineCategoryAccess';
export interface IDisciplinesCategoriesAccessRepository {
  insert(data: IAddCategoryInDisciplineDTO): Promise<IAddCategoryInDisciplineDTO | undefined>;
  findByDisciplineIdAndCategoryId(data: {
    disciplineId: string;
    categoryId: string;
    customerId: string;
  }): Promise<IAddCategoryInDisciplineDTO | undefined>;
  deleteByDisciplineIdAndCategoryId(data: {
    disciplineId: string;
    categoryId: string;
    customerId: string;
  }): Promise<void>;
  findAllById(id: string): Promise<IAddCategoryInDisciplineDTO[]>;
  deleteAll(id: string, customerId: string): Promise<object>;
  deleteAllByDisciplineId(disciplineId: string, customerId: string): Promise<void>;
  findAllByDisciplineId(disciplineId: string): Promise<IDisciplineCategoriesAccess[]>;
}
