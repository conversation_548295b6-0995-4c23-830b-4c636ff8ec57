import { ICategoryIdentifierDTO } from '../../model/DTO/ICategory.dto';
import { IDisciplineSubcategoriesAccessCreate } from '../../model/DTO/IDisciplineCategoryAccess.dto';
import { IDisciplineSubcategoriesAccess } from '../../model/IDisciplineSubcategoryAccess';

export interface IDisciplinesSubcategoriesAccessRepository {
  deleteByDisciplineId(disciplineId: string): Promise<void>;

  getBysubcategoryId(id: string, customerId: string): Promise<IDisciplineSubcategoriesAccess[]>;

  deleteBySubcategoryId(subcategoryId: string): Promise<void>;

  deleteAll(subcategoryId: string, customerId: string): Promise<void>;

  deleteAllByDisciplineId(disciplineId: string, customerId: string): Promise<void>;

  findAllByDisciplineId(
    disciplineId: string,
    customerId: string
  ): Promise<IDisciplineSubcategoriesAccess[]>;

  findByDisciplineIdAndSubcategoryId(data: {
    disciplineId: string;
    subcategoryId: string;
    customerId: string;
  }): Promise<IDisciplineSubcategoriesAccess | undefined>;

  insert(data: IDisciplineSubcategoriesAccessCreate): Promise<IDisciplineSubcategoriesAccess>;

  deleteByDisciplineIdAndSubcategoryId(data: {
    disciplineId: string;
    subcategoryId: string;
    customerId: string;
  }): Promise<void>;
  deleteAllByCategoryId(data: ICategoryIdentifierDTO): Promise<void>;
}
