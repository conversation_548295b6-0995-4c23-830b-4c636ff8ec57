import { K<PERSON> } from 'knex';

import { IDomain } from '../../model/IDomain';

export interface IDomainsRepository {
  insertDomainWithTransaction(data: Partial<IDomain>, trx?: Knex.Transaction): Promise<IDomain>;
  findOneBy(data: Partial<IDomain>): Promise<IDomain | undefined>;
  findByPendingValidation(): Promise<IDomain[]>;
  updateDomainWithTransaction(data: Partial<IDomain>, trx?: Knex.Transaction): Promise<IDomain>;
  findByValidatedNotCloudFront(): Promise<IDomain[]>;
}
