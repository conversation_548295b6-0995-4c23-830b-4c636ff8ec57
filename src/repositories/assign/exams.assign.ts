import {
  ExamsFilters,
  GetExamsFiltersParams,
  IExamCreateDTO,
  ListExamsParams,
  ListPublishedExamsPaginatedParams,
  PublishedExamsWithPaginationDTO,
} from '../../model/DTO/exams.dto';
import {
  FindByUserWithProgressParams,
  IExamWithProgressDTO,
  IGetPublishedQuestionsFromExamDTO,
  IPublishedQuestionsFromExamDTO,
} from '../../model/DTO/IExam.dto';
import { IExam } from '../../model/IExam';
import { IInstitution } from '../../model/IInstitution';

export interface IExamsRepository {
  findOneBy(data: Partial<IExam>): Promise<IExam | undefined>;
  findAllBy(filter?: Partial<IExam>): Promise<IExam[]>;
  insert(data: IExamCreateDTO): Promise<IExam>;
  update(data: Partial<IExam>): Promise<IExam>;
  listExams(data: ListExamsParams): Promise<IExam[]>;
  softDelete(data: { id: string; customerId: string }): Promise<IExam>;
  listPublishedExamsPaginated(
    params: ListPublishedExamsPaginatedParams
  ): Promise<PublishedExamsWithPaginationDTO>;
  getExamsFilters(params: GetExamsFiltersParams): Promise<ExamsFilters>;
  findByEntities(data: Partial<IExam>): Promise<IExam[]>;
  findByUserWithProgress(params: FindByUserWithProgressParams): Promise<IExamWithProgressDTO[]>;
  findByExam(data: Partial<IExam>): Promise<IExam & IInstitution>;
  getPublishedQuestionsFromExam(
    params: IGetPublishedQuestionsFromExamDTO
  ): Promise<IPublishedQuestionsFromExamDTO>;
  findByIds(ids: string[], customerId: string): Promise<IExam[]>;
}
