import { IExamClass } from '../../model/IExamClass';

export interface IExamsClassesRepository {
  findOneBy(data: Partial<IExamClass>): Promise<IExamClass | undefined>;
  insert(data: Partial<IExamClass>): Promise<IExamClass>;
  findByClassId(classId: string): Promise<IExamClass[]>;
  findByClassIdAndExamIds(classId: string, examIds: string[]): Promise<IExamClass[]>;
  insertAll(data: Partial<IExamClass>[]): Promise<IExamClass[]>;
  deleteByClassIdAndExamIds(classId: string, examIds: string[]): Promise<IExamClass[]>;
  deleteBy(data: Partial<IExamClass>): Promise<IExamClass[]>;
}
