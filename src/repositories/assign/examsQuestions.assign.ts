import { BaseRepository } from 'bq-knex-base-repository';

import { IExamQuestion } from '../../model/IExamQuestion';

export interface IExamsQuestionsRepository extends BaseRepository<IExamQuestion> {
  findByExamId(examId: string): Promise<IExamQuestion[]>;
  findByQuestionId(questionId: string): Promise<IExamQuestion[]>;
  insertAll(data: Partial<IExamQuestion>[]): Promise<IExamQuestion[]>;
  deleteById(id: string): Promise<IExamQuestion>;
  updateBatch(updates: Partial<IExamQuestion>[]): Promise<IExamQuestion[]>;
  softDeleteByQuestionId(questionId: string, examId?: string): Promise<string[]>;
  findOneBy(data: Partial<IExamQuestion>): Promise<IExamQuestion | undefined>;
  // reorderQuestionsForExam(examId: string): Promise<void>;
}
