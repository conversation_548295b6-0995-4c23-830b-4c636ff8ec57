import { IPaginationDTO } from '../../model/DTO/IGeneric.dto';
import { findByNameAndCustomerIdParams } from '../../model/DTO/IInstitution.dto';
import { IInstitution } from '../../model/IInstitution';
import { ListInstitutionsPublicDTO } from '../../schema/institutions.schema';

export interface IInstitutionsRepository {
  insert(data: Partial<IInstitution>): Promise<IInstitution | undefined>;
  findOneBy(filters: Partial<IInstitution>): Promise<IInstitution | undefined>;
  listAllInstitutions(customerId: string): Promise<IInstitution[]>;
  listInstitutionsPublicPaginated(data: ListInstitutionsPublicDTO): Promise<{
    paginationInfo: IPaginationDTO;
    institutions: IInstitution[];
  }>;
  update(data: Partial<IInstitution>): Promise<IInstitution | undefined>;
  softDelete(data: { id: string; customerId: string }): Promise<IInstitution | undefined>;
  findByNameAndCustomerId(data: findByNameAndCustomerIdParams): Promise<IInstitution | undefined>;
  findAllBy(filter: Partial<IInstitution>): Promise<IInstitution[]>;
}
