import { IPaginationDTO } from '../../model/DTO/IGeneric.dto';
import {
  IFindSimilarCodesDTO,
  IlistQuestionFilterDTO,
  IListQuestionInGroupFilterDTO,
  IQuestionDTO,
  IQuestionIdentifierDTO,
  IQuestionTitleDTO,
  IQuestionWithAlternativesDTO,
  ListQuestionsWithFiltersParams,
} from '../../model/DTO/IQuestion.dto';
import { ListQuestionsByGroupInput } from '../../model/DTO/IQuestionGroup.dto';
import { IQuestion } from '../../model/IQuestion';

export interface IQuestionsRepository {
  findByDisciplineId(disciplineId: string, customerId: string): Promise<IQuestion[] | undefined>;
  findBySubcategoryId(params: {
    subcategoryId: string;
    customerId: string;
  }): Promise<IQuestion[] | undefined>;
  insert(data: IQuestionDTO): Promise<IQuestion>;
  deleteById(data: { id: string }): Promise<IQuestion | undefined>;
  findById(data: { id: string; customerId: string }): Promise<IQuestion | undefined>;
  findOneBy(filter: Partial<IQuestion>): Promise<IQuestion | undefined>;
  findByIds(ids: string[], customerId: string): Promise<IQuestion[]>;
  listPaginatedQuestions(
    data: IlistQuestionFilterDTO
  ): Promise<{ questionList: IQuestionWithAlternativesDTO[]; paginationInfo: IPaginationDTO }>;
  update(data: IQuestion): Promise<IQuestion>;
  findSimilarCodes(data: IFindSimilarCodesDTO): Promise<IQuestionTitleDTO[]>;
  findQuestionFullData(
    data: IQuestionIdentifierDTO
  ): Promise<{ question: IQuestionWithAlternativesDTO }>;
  getQuestionsByCategoryId(data: { categoryId: string; customerId: string }): Promise<IQuestion[]>;
  getPublishedQuestionsByGroupId(
    data: ListQuestionsByGroupInput
  ): Promise<IQuestionWithAlternativesDTO[]>;
  listQuestionsInGroup(
    data: IListQuestionInGroupFilterDTO
  ): Promise<{ items: IQuestionWithAlternativesDTO[] }>;
  listQuestionsWithFilters(filters: ListQuestionsWithFiltersParams): Promise<{
    items: IQuestionWithAlternativesDTO[];
    total: number;
  }>;
  insertAll(data: IQuestionDTO[]): Promise<IQuestion[]>;
  findQuestionsWithAlternativesByIds(
    examId: string,
    userId: string,
    examAccessId: string
  ): Promise<IQuestionWithAlternativesDTO[]>;
}
