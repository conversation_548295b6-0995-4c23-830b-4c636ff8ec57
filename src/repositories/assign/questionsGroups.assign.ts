import { IQuestionGroupQueryParamsDTO } from '../../model/DTO/IQuestionGroup.dto';
import { IQuestionGroup } from '../../model/IQuestionGroup';

export interface IQuestionsGroupsRepository {
  findQuestionGroup(data: {
    name: string;
    customerId: string;
  }): Promise<IQuestionGroup | undefined>;
  insert(data: IQuestionGroup): Promise<IQuestionGroup>;
  update(data: IQuestionGroup): Promise<IQuestionGroup | undefined>;
  findById(data: { id: string; customerId: string }): Promise<IQuestionGroup>;
  deleteById(data: { id: string; customerId: string }): Promise<IQuestionGroup | undefined>;
  listAllQuestionsGroups(data: {
    customerId: string;
    search?: string;
    orderByColumn?: string;
    orderDirection?: string;
  }): Promise<IQuestionGroup[]>;
  listSelectableQuestionGroups(data: IQuestionGroupQueryParamsDTO): Promise<IQuestionGroup[]>;
  findOneBy(data: Partial<IQuestionGroup>): Promise<IQuestionGroup | undefined>;
}
