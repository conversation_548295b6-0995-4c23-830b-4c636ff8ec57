import {
  IQuestionGroupAccessIdentifiersDTO,
  IQuestionGroupAccessWithoutMetadata,
} from '../../model/DTO/IQuestionGroupAccess.dto';
import { IQuestionGroup } from '../../model/IQuestionGroup';
import { IQuestionGroupAccess } from '../../model/IQuestionGroupAccess';

export interface IQuestionsGroupsAccessRepository {
  findAllAccessByGroupId(data: {
    questionGroupId: string;
    customerId: string;
  }): Promise<IQuestionGroup[]>;
  findByQuestionId(data: {
    questionId: string;
    customerId: string;
  }): Promise<IQuestionGroup[] | undefined>;
  deleteByQuestionId(data: { questionId: string; customerId: string }): Promise<void>;
  insert(data: IQuestionGroupAccessWithoutMetadata): Promise<IQuestionGroupAccess>;
  removeQuestionFromGroup(data: IQuestionGroupAccessIdentifiersDTO): Promise<IQuestionGroupAccess>;
  getQuestionGroupAccess(data: IQuestionGroupAccessIdentifiersDTO): Promise<IQuestionGroupAccess>;
}
