import { IQuestionSimulatedAnswered } from '../../model/IQuestionSimulatedAnswered';

export interface IQuestionsSimulatedAnsweredRepository {
  insert(data: Partial<IQuestionSimulatedAnswered>): Promise<IQuestionSimulatedAnswered>;
  update(data: Partial<IQuestionSimulatedAnswered>): Promise<IQuestionSimulatedAnswered>;
  findOneBy(
    data: Partial<IQuestionSimulatedAnswered>
  ): Promise<IQuestionSimulatedAnswered | undefined>;
}
