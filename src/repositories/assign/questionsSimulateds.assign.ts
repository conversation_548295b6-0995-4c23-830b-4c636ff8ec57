import { <PERSON><PERSON> } from 'knex';

import {
  IQuestionSimulatedWithAlternativesDTO,
  IQuestionSimulatedWithExtendedDataDTO,
} from '../../model/DTO/IQuestionsSimulated.dto';
import { IQuestionSimulated } from '../../model/IQuestionSimulated';

export interface IQuestionsSimulatedsRepository {
  getAllQuestionsSimulated(simulatedId: string, userId: string): Promise<object>;
  insertAll(
    item: IQuestionSimulated[],
    transaction?: Knex.Transaction
  ): Promise<IQuestionSimulated[]>;
  getQuestionsWithExtendedData(
    simulatedId: string,
    userId: string
  ): Promise<{ questions: IQuestionSimulatedWithExtendedDataDTO[]; totalQuestions: number }>;
  findOneBy(data: Partial<IQuestionSimulated>): Promise<IQuestionSimulated | undefined>;
  findAllBy(filters: Partial<IQuestionSimulated>): Promise<IQuestionSimulated[]>;
  updateBatch(updates: Partial<IQuestionSimulated>[]): Promise<IQuestionSimulated[]>;
  getAllQuestionsBySimulatedId(
    simulatedId: string
  ): Promise<IQuestionSimulatedWithAlternativesDTO[]>;
  softDeleteByQuestionId(questionId: string): Promise<void>;
  update(data: Partial<IQuestionSimulated>): Promise<IQuestionSimulated>;
}
