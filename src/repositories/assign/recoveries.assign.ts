import { K<PERSON> } from 'knex';

import { TokenInfoResponseDto } from '../../model/DTO/response/user.dto';
import { IRecovery } from '../../model/IRecovery';

export interface IRecoveryRepository {
  createRecovery(data: Partial<IRecovery>, trx?: Knex.Transaction): Promise<IRecovery>;
  createManyRecoveries(data: Partial<IRecovery>[], trx?: Knex.Transaction): Promise<void>;
  findUserByRecoveryToken(token: string): Promise<TokenInfoResponseDto | undefined>;
  findRecoveryByUserId(userId: string): Promise<IRecovery | undefined>;
  updateRecovery(data: Partial<IRecovery>, trx?: Knex.Transaction): Promise<IRecovery>;
}
