import {
  IReportCreateDTO,
  IReportListParams,
  IReportListResult,
} from '../../model/DTO/IReport.dto';
import { IReport } from '../../model/IReport';

export interface IReportsRepository {
  insert(data: IReportCreateDTO): Promise<IReport>;
  update(data: Partial<IReport>): Promise<IReport>;
  findOneBy(data: Partial<IReport>): Promise<IReport | undefined>;
  listReports(params: IReportListParams): Promise<IReportListResult>;
}
