import { K<PERSON> } from 'knex';

import {
  IFindByUserWithProgressParams,
  IListExclusiveSimulatedsPaginatedResponse,
  ISimulatedByIdDTO,
  ISimulatedWithProgressAndStatsDTO,
  ISimulatedWithProgressDTO,
} from '../../model/DTO/ISimulated.dto';
import { ISimulated } from '../../model/ISimulated';
import { IListExclusiveSimulatedsPaginatedInput } from '../../schema/simulateds.schema';

export interface ISimulatedsRepository {
  get(id: string): Promise<ISimulated | undefined>;
  getSimulatedById(id: string): Promise<ISimulatedByIdDTO | undefined>;
  findByUser(userId: string, customerId: string): Promise<ISimulatedWithProgressDTO[]>;
  findByUserWithProgress(
    params: IFindByUserWithProgressParams
  ): Promise<ISimulatedWithProgressAndStatsDTO[]>;
  insert(data: Partial<ISimulated>): Promise<ISimulated>;
  update(data: Partial<ISimulated>, transaction?: Knex.Transaction): Promise<ISimulated>;
  findOneBy(data: Partial<ISimulated>): Promise<ISimulated | undefined>;
  listExclusiveSimulatedsPaginated(
    params: IListExclusiveSimulatedsPaginatedInput
  ): Promise<IListExclusiveSimulatedsPaginatedResponse>;
  findExpiredExclusiveSimulateds(): Promise<{ id: string }[]>;
  findScheduledExclusiveSimulatedsToOpen(now: Date): Promise<ISimulated[]>;
  updateManyStatusAndActive(
    ids: string[],
    status: string,
    active: boolean,
    transaction?: Knex.Transaction
  ): Promise<number>;
}
