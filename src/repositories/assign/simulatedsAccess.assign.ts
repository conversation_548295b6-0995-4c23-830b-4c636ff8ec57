import { <PERSON><PERSON> } from 'knex';

import { ISimulatedWithProgressDTO } from '../../model/DTO/ISimulated.dto';
import { ISimulatedAccessWithSimulatedName } from '../../model/DTO/ISimulatedAccess.dto';
import { IUserSimulatedAccessDTO } from '../../model/DTO/IUserSimulatedAccess.dto';
import { ISimulatedAccess } from '../../model/ISimulatedAcces';
import { IListUserExclusiveSimulatedsQueryInput } from '../../schema/simulateds.schema';
import { ListUserExclusiveSimulatedsUserHistoryInput } from '../../use-case/simulateds/listUserExclusiveSimulateds/listUserExclusiveSimulatedsUserHistory.useCase';

export interface ISimulatedsAccessRepository {
  insert(data: Partial<ISimulatedAccess>): Promise<ISimulatedAccess>;
  update(
    data: Partial<ISimulatedAccess>,
    transaction?: Knex.Transaction
  ): Promise<ISimulatedAccess>;
  findOneBy(data: Partial<ISimulatedAccess>): Promise<ISimulatedAccess | undefined>;
  findBy(data: Partial<ISimulatedAccess>): Promise<ISimulatedAccess[]>;
  getSimulatedAccessWithSimulatedName(
    data: Partial<ISimulatedAccess>
  ): Promise<ISimulatedAccessWithSimulatedName | undefined>;
  insertAll(
    data: Partial<ISimulatedAccess>[],
    transaction?: Knex.Transaction
  ): Promise<ISimulatedAccess[]>;
  deleteByUserIdsAndSimulatedId(
    userIds: string[],
    simulatedId: string,
    transaction?: Knex.Transaction
  ): Promise<number>;
  deleteAllBySimulatedId(simulatedId: string, transaction?: Knex.Transaction): Promise<number>;
  findUsersBySimulatedId(simulatedId: string): Promise<IUserSimulatedAccessDTO[]>;
  findExclusiveSimulatedsByUserId({
    userId,
    customerId,
    courseId,
    isClosed,
  }: IListUserExclusiveSimulatedsQueryInput): Promise<ISimulatedWithProgressDTO[]>;
  findExclusiveSimulatedsAccessHistoryByUserId({
    userId,
    customerId,
    courseId,
  }: ListUserExclusiveSimulatedsUserHistoryInput): Promise<ISimulatedWithProgressDTO[]>;
  updateTimeLimitBySimulatedId(simulatedId: string, timeLimit: number): Promise<number>;
  getExclusiveSimulatedStats(simulatedId: string): Promise<{
    totalFinished: number;
    avgScorePercentage: number;
    avgTimeSeconds: number;
  }>;
}
