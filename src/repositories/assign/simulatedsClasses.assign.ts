import { <PERSON><PERSON> } from 'knex';

import { ISimulatedClass } from '../../model/ISimulatedClass';

export interface ISimulatedsClassesRepository {
  findOneBy(data: Partial<ISimulatedClass>): Promise<ISimulatedClass | undefined>;
  insert(data: Partial<ISimulatedClass>): Promise<ISimulatedClass>;
  findByClassId(classId: string): Promise<ISimulatedClass[]>;
  findByClassIdAndSimulatedIds(classId: string, simulatedIds: string[]): Promise<ISimulatedClass[]>;
  insertAll(
    data: Partial<ISimulatedClass>[],
    transaction?: Knex.Transaction
  ): Promise<ISimulatedClass[]>;
  deleteBySimulatedIdAndClassIds(
    simulatedId: string,
    classIds: string[],
    transaction?: Knex.Transaction
  ): Promise<ISimulatedClass[]>;
  findBy(data: Partial<ISimulatedClass>): Promise<ISimulatedClass[]>;
  deleteAllBySimulatedId(simulatedId: string, transaction?: Knex.Transaction): Promise<number>;
}
