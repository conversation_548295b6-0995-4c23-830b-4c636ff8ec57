import {
  CreateSimulatedTemplateInput,
  IGetSimulatedTemplatesCountDTO,
  IGetSimulatedTemplatesDTO,
  IListSimulatedTemplatesDTO,
  IPublishedQuestionsFromTemplateDTO,
  ISimulatedTemplatesResult,
} from '../../model/DTO/ISimulatedTemplate.dto';
import { ISimulatedTemplate } from '../../model/ISimulatedTemplate';

export interface ISimulatedTemplateRepository {
  insert(data: CreateSimulatedTemplateInput): Promise<ISimulatedTemplate>;
  findOneBy(data: Partial<ISimulatedTemplate>): Promise<ISimulatedTemplate | undefined>;
  countByUserId(data: IGetSimulatedTemplatesCountDTO): Promise<number>;
  findTemplatesWithQuestionCountByUser(
    data: IListSimulatedTemplatesDTO
  ): Promise<ISimulatedTemplatesResult>;
  deleteById(id: string): Promise<ISimulatedTemplate>;
  getPublishedQuestionsFromTemplate(
    data: IGetSimulatedTemplatesDTO
  ): Promise<IPublishedQuestionsFromTemplateDTO>;
  findOneBy(data: Partial<ISimulatedTemplate>): Promise<ISimulatedTemplate | undefined>;
}
