import { ICategoryIdentifierDTO } from '../../model/DTO/ICategory.dto';
import { IInsertSubcategoryDTO, IUpdateSubcategoryDTO } from '../../model/DTO/ISubcategory.dto';
import { ISubCategory } from '../../model/ISubcategory';

export interface ISubCategoriesRepository {
  // TODO: Futuramente passar os parâmetros como objeto
  getSubcategoryCustomer(data: {
    name: string;
    categoryId: string;
    customerId: string;
  }): Promise<ISubCategory | undefined>;
  update(item: IUpdateSubcategoryDTO): Promise<ISubCategory>;
  get(id: string): Promise<ISubCategory | undefined>;
  insert(data: IInsertSubcategoryDTO): Promise<ISubCategory | undefined>;
  deleteById(id: string): Promise<object>;
  findAllByCustomerId(customerId: string, categoryId: string): Promise<ISubCategory[]>;
  findByName(data: { name: string; customerId: string }): Promise<ISubCategory | undefined>;
  deleteAllByCategoryId(data: ICategoryIdentifierDTO): Promise<object>;
}
