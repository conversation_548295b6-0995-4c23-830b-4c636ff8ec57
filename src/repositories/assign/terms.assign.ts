import { K<PERSON> } from 'knex';

import { ITerm } from '../../model/ITerm';
import { FilterListTermsInput } from '../../schema/terms.schema';

export interface ITermsRepository {
  createTerm(data: Partial<ITerm>, trx?: Knex.Transaction): Promise<ITerm | undefined>;
  findOneBy(data: Partial<ITerm>): Promise<ITerm | undefined>;
  updateTerm(data: Partial<ITerm>, trx?: Knex.Transaction): Promise<ITerm | undefined>;
  listAllTerms(customerId: string, filter: FilterListTermsInput): Promise<ITerm[]>;
}
