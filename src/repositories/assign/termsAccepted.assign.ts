import { K<PERSON> } from 'knex';

import { ITermsAccepted } from '../../model/ITermsAccepted';

export interface ITermsAcceptedRepository {
  createTermsAccept(data: Partial<ITermsAccepted>, trx?: Knex.Transaction): Promise<ITermsAccepted>;
  findOneBy(data: Partial<ITermsAccepted>): Promise<ITermsAccepted | undefined>;
  updateTermsAccept(data: Partial<ITermsAccepted>, trx?: Knex.Transaction): Promise<ITermsAccepted>;
  findByUserIdAndTermsId(userId: string, termsId: string): Promise<ITermsAccepted | undefined>;
}
