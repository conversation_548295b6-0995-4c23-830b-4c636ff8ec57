import { <PERSON><PERSON> } from 'knex';

import { IAddUserToClassDTO } from '../../model/DTO/IUserClasses.dto';
import { IUserClasses } from '../../model/IUserClasses';

export interface IUserClassesRepository {
  insert(data: IAddUserToClassDTO): Promise<IUserClasses>;
  insertWithTrx(data: IAddUserToClassDTO, trx: Knex.Transaction): Promise<IUserClasses>;
  insertAll(data: IAddUserToClassDTO[]): Promise<IUserClasses[]>;
  findOneBy(data: Partial<IUserClasses>): Promise<IUserClasses | undefined>;
  findByUserId(userId: string): Promise<IUserClasses[]>;
  softDeleteByUserId(userId: string): Promise<IUserClasses>;
  softDeleteByUserIdAndClassIds(userId: string, classIds: string[]): Promise<void>;
  softDeleteByClassId(classId: string): Promise<void>;
  findByClassIds(classIds: string[]): Promise<IUserClasses[]>;
  hardDeleteByUserId(userId: string): Promise<void>;
  findUserClassesInCourse(userId: string, courseId: string): Promise<IUserClasses[]>;
  findByUserIdAndCourseIdExcludingClass(
    userId: string,
    courseId: string,
    excludeClassId: string
  ): Promise<IUserClasses | undefined>;
  update(data: Partial<IUserClasses>, trx?: Knex.Transaction): Promise<IUserClasses>;
  insertAllWithTrx(data: IAddUserToClassDTO[], trx: Knex.Transaction): Promise<IUserClasses[]>;
  findByUserIdsAndClassId(userIds: string[], classId: string): Promise<IUserClasses[]>;
  findByUserIdsAndClassIds(userIds: string[], courseId: string): Promise<IUserClasses[]>;
  softDeleteByUserIdsAndCourseId(
    userIds: string[],
    courseId: string,
    trx?: Knex.Transaction
  ): Promise<void>;
}
