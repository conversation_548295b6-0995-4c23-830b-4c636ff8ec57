import { K<PERSON> } from 'knex';

import { ProgressStatus } from '../../model/enums/progressStatus.enum';
import { IUserCourseProgress } from '../../model/IUserCourseProgress';

export interface IUserCourseProgressRepository {
  insertCourseProgress(
    trx: Knex.Transaction,
    userId: string,
    courseId: string,
    status: ProgressStatus
  ): Promise<IUserCourseProgress | undefined>;
  updateCourseProgress(
    trx: Knex.Transaction,
    userId: string,
    courseId: string,
    status: ProgressStatus
  ): Promise<IUserCourseProgress | undefined>;
  getCourseProgress(userId: string, courseId: string): Promise<IUserCourseProgress | undefined>;
}
