import { K<PERSON> } from 'knex';

import { ILessonWithProgress } from '../../model/DTO/response/progress.dto';
import { ProgressStatus } from '../../model/enums/progressStatus.enum';
import { IUserLessonProgress } from '../../model/IUserLessonProgress';

export interface IUserLessonsProgressRepository {
  insertLessonProgress(
    trx: Knex.Transaction,
    userId: string,
    lessonId: string,
    progress: number,
    status: ProgressStatus
  ): Promise<IUserLessonProgress | undefined>;
  updateLessonProgress(
    trx: Knex.Transaction,
    userId: string,
    lessonId: string,
    progress: number,
    status: ProgressStatus
  ): Promise<IUserLessonProgress | undefined>;
  getLessonProgress(userId: string, lessonId: string): Promise<IUserLessonProgress | undefined>;
  getLessonWithProgress(userId: string, lessonId: string): Promise<ILessonWithProgress>;
}
