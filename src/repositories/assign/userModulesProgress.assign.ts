import { <PERSON><PERSON> } from 'knex';

import { IProgressHierarchyData } from '../../model/DTO/response/progress.dto';
import { ProgressStatus } from '../../model/enums/progressStatus.enum';
import { IUserModuleProgress } from '../../model/IUserModuleProgress';

export interface IUserModulesProgressRepository {
  insertModuleProgress(
    data: Partial<IUserModuleProgress>,
    trx: Knex.Transaction
  ): Promise<IUserModuleProgress | undefined>;
  updateModuleProgress(
    trx: Knex.Transaction,
    userId: string,
    moduleId: string,
    status: ProgressStatus
  ): Promise<IUserModuleProgress | undefined>;
  getModuleProgress(userId: string, moduleId: string): Promise<IUserModuleProgress | undefined>;

  getProgressHierarchyData(
    userId: string,
    moduleId: string,
    courseId: string
  ): Promise<IProgressHierarchyData>;
}
