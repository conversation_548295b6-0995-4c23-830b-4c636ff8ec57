import { K<PERSON> } from 'knex';

import {
  ICreateUserExternal,
  ICreateUserFromEmailDTO,
  ICreateUserFromGoogleDTO,
  IListStudentsParamsDTO,
  IListUsersAdmParamsDTO,
  ISoftDeleteUserDTO,
  IUserDTO,
  UserPublicDTO,
} from '../../model/DTO/IUsers.dto';
import { IUserWithCoursesAndClasses } from '../../model/DTO/request/user.request.dto';
import { ListStudentsResponseDto } from '../../model/DTO/response/user.dto';
import { IUser } from '../../model/IUser';
import {
  ListUsersWithClassesParams,
  ListUsersWithClassesResponse,
} from '../../use-case/users/listUsers/listUsersWithClasses.useCase';

export interface IUsersRepository {
  getUsersByEmail(email: string, customerId: string): Promise<IUser | undefined>;
  insert(data: IUserDTO): Promise<IUser | undefined>;
  insertWithTrx(data: IUser, trx: Knex.Transaction): Promise<IUser | undefined>;
  findByEmailAndExternalCustomerId(data: {
    email: string;
    externalCustomerId: string;
  }): Promise<IUser | undefined>;
  createUserFromExternalSystem(data: ICreateUserExternal): Promise<UserPublicDTO>;
  findOneBy(data: Partial<IUser>): Promise<IUser | undefined>;
  update(data: Partial<UserPublicDTO>): Promise<IUser | undefined>;
  softDelete(data: ISoftDeleteUserDTO): Promise<IUser>;
  findByRole(role: string, customerId: string): Promise<IUser[]>;
  findAllWithUserAndClass(
    filters: ListUsersWithClassesParams
  ): Promise<ListUsersWithClassesResponse>;
  hardDelete(id: string, customerId: string): Promise<void>;
  changePassword(
    userId: string,
    password: string,
    trx?: Knex.Transaction
  ): Promise<{ id: string | undefined }>;
  findUsersByCustomerAdm(
    customerId: string,
    orderByColumn?: string,
    orderByDirection?: string,
    accessLevelIds?: string[]
  ): Promise<IListUsersAdmParamsDTO[]>;
  getUserWithAccessLevel(userId: string): Promise<IUser | undefined>;
  findUsersStudents(data: IListStudentsParamsDTO): Promise<ListStudentsResponseDto[]>;
  updateWithTrx(data: Partial<UserPublicDTO>, trx?: Knex.Transaction): Promise<IUser | undefined>;
  findUserByEmailOrCpfAndCustomerId(
    email: string,
    cpf: string,
    customerId: string,
    id: string
  ): Promise<IUser | undefined>;
  createUserFromGoogle(data: ICreateUserFromGoogleDTO): Promise<IUser | undefined>;
  createUserFromEmails(data: ICreateUserFromEmailDTO[], trx?: Knex.Transaction): Promise<IUser[]>;
  findManyByEmailsAndCustomerId(emails: string[], customerId: string): Promise<IUser[]>;
  findAllByIds(ids: string[], customerId: string): Promise<IUser[]>;
  findUserAndUpdateLastAccess(userId: string): Promise<IUser | undefined>;
  getUserWithCoursesAndClasses(
    userId: string,
    customerId: string
  ): Promise<IUserWithCoursesAndClasses[]>;
}
