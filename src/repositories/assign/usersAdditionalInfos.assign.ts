import { K<PERSON> } from 'knex';

import { IUserAdditionalInfo } from '../../model/IUserAdditionalInfo';

export interface IUsersAdditionalInfosRepository {
  createUserAdditionalInfo(
    data: IUserAdditionalInfo,
    trx?: Knex.Transaction
  ): Promise<IUserAdditionalInfo | undefined>;
  findOneBy(data: Partial<IUserAdditionalInfo>): Promise<IUserAdditionalInfo | undefined>;
  updateUserAdditionalInfo(
    data: Partial<IUserAdditionalInfo>,
    trx?: Knex.Transaction
  ): Promise<IUserAdditionalInfo | undefined>;
}
