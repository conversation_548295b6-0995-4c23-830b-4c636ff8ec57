import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IAccessLevel } from '../../model/IAccessLevel';
import { IAccessLevelsRepository } from '../assign/accessLevels.assign';

export class KnexAccessLevelsRepository
  extends BaseRepository<IAccessLevel>
  implements IAccessLevelsRepository
{
  constructor() {
    super(knexInstance, 'access_levels');
  }

  async findByRole(role: string): Promise<IAccessLevel | undefined> {
    return knexInstance.select('*').from('access_levels').where({ role }).first();
  }

  // async insertAll(items: IAccessLevel[]): Promise<void> {
  //   await knexInstance(this.table).insert(items);
  // }

  async findByRoles(roles: string[]): Promise<IAccessLevel[]> {
    return knexInstance.select('*').from('access_levels').whereIn('role', roles);
  }
}
