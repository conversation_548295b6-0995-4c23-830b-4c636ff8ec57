import { knexInstance } from '../../config/connectionDatabase.config';
import { IRequestedEntitiesDTO, IRetrievedEntitiesDTO } from '../../model/DTO/IAggregatedData.dto';
import { IQuestionWithAlternativesDTO } from '../../model/DTO/IQuestion.dto';
import { IAlternative } from '../../model/IAlternative';
import { IAnswerSession } from '../../model/IAnswerSession';
import { ICategory } from '../../model/ICategory';
import { IDiscipline } from '../../model/IDiscipline';
import { IQuestion } from '../../model/IQuestion';
import { IQuestionGroup } from '../../model/IQuestionGroup';
import { IQuestionType } from '../../model/IQuestionType';
import { ISimulated } from '../../model/ISimulated';
import { ISimulatedAccess } from '../../model/ISimulatedAcces';
import { ISubCategory } from '../../model/ISubcategory';
import { IAggregatedDataAssignRepository } from '../assign/aggregatedData.assign';

export class KnexAggregateDataRepository implements IAggregatedDataAssignRepository {
  constructor() {}

  async findEntities(data: IRequestedEntitiesDTO): Promise<IRetrievedEntitiesDTO> {
    const {
      questionId,
      customerId,
      userId,
      disciplineId,
      categoryId,
      subcategoryId,
      questionTypeId,
      questionGroupId,
      simulatedId,
      answerSessions,
      simulatedAccessId,
      questionWithAlternatives,
    } = data;

    type Row = { entityType: string } & Partial<
      IQuestion &
        IDiscipline &
        ICategory &
        ISubCategory &
        IQuestionType &
        IQuestionGroup &
        ISimulated &
        ISimulatedAccess &
        IAnswerSession[] &
        IQuestionWithAlternativesDTO
    >;
    const queries: Promise<{ rows: Row[] }>[] = [];

    if (simulatedAccessId && userId && answerSessions) {
      queries.push(
        knexInstance.raw(
          `SELECT 'answerSessions' AS "entityType", 
           json_agg(
             json_build_object(
               'id', ans.id,
               'started_at', ans.started_at,
               'paused_at', ans.paused_at,
               'simulated_access_id', ans.simulated_access_id,
               'duration_seconds', ans.duration_seconds,
               'created_at', ans.created_at,
               'updated_at', ans.updated_at
             )
           ) as sessions
           FROM answer_sessions ans
           WHERE ans.simulated_access_id = ? AND ans.deleted_at IS NULL`,
          [simulatedAccessId]
        )
      );
    }

    if (questionId && customerId && questionWithAlternatives === true) {
      queries.push(
        knexInstance.raw(
          `SELECT 'questionWithAlternatives' AS "entityType", q.*, 
           COALESCE(
             json_agg(
               json_build_object(
                 'id', a.id,
                 'question_id', a.question_id,
                 'option', a.option,
                 'description', a.description,
                 'correct', a.correct
               )
             ) FILTER (WHERE a.id IS NOT NULL), '[]'
           ) as alternatives
           FROM questions q
           LEFT JOIN alternatives a ON q.id = a.question_id
           WHERE q.id = ? AND q.customer_id = ?
           GROUP BY q.id`,
          [questionId, customerId]
        )
      );
    }

    if (questionId && customerId) {
      queries.push(
        knexInstance.raw(
          `SELECT 'question' AS "entityType", q.* FROM questions q WHERE q.id = ? AND q.customer_id = ?`,
          [questionId, customerId]
        )
      );
    }

    if (disciplineId && customerId) {
      queries.push(
        knexInstance.raw(
          `SELECT 'discipline' AS "entityType", d.* FROM disciplines d WHERE d.id = ? AND d.customer_id = ?`,
          [disciplineId, customerId]
        )
      );
    }

    if (categoryId && customerId) {
      queries.push(
        knexInstance.raw(
          `SELECT 'category' AS "entityType", c.* FROM categories c WHERE c.id = ? AND c.customer_id = ?`,
          [categoryId, customerId]
        )
      );
    }

    if (subcategoryId && customerId) {
      queries.push(
        knexInstance.raw(
          `SELECT 'subcategory' AS "entityType", sc.* FROM subcategories sc WHERE sc.id = ? AND sc.customer_id = ?`,
          [subcategoryId, customerId]
        )
      );
    }

    if (questionTypeId) {
      queries.push(
        knexInstance.raw(
          `SELECT 'questionType' AS "entityType", qt.*
           FROM questions_types qt WHERE qt.id = ?`,
          [questionTypeId]
        )
      );
    }

    if (questionGroupId && customerId) {
      queries.push(
        knexInstance.raw(
          `SELECT 'questionGroup' AS "entityType", qg.*
           FROM questions_groups qg WHERE qg.id = ? AND qg.customer_id = ?`,
          [questionGroupId, customerId]
        )
      );
    }

    if (simulatedId) {
      queries.push(
        knexInstance.raw(
          `SELECT 'simulated' AS "entityType", s.*
           FROM simulateds s WHERE s.id = ?`,
          [simulatedId]
        )
      );
    }

    if (simulatedAccessId && userId) {
      queries.push(
        knexInstance.raw(
          `SELECT 'simulatedAccess' AS "entityType", sa.*
           FROM simulateds_access sa WHERE sa.id = ? AND sa.user_id = ?`,
          [simulatedAccessId, userId]
        )
      );
    }

    const results = await Promise.all(queries);

    const formattedData: IRetrievedEntitiesDTO = {
      question: null,
      discipline: null,
      category: null,
      subcategory: null,
      questionType: null,
      questionGroup: null,
      simulated: null,
      simulatedAccess: null,
      questionWithAlternatives: null,
      answerSessionsData: null,
    };
    interface QueryResult {
      rows: Row[];
    }
    (results as unknown as QueryResult[]).forEach((result) => {
      result.rows.forEach((row) => {
        const { entityType, ...rest } = row;

        switch (entityType) {
          case 'question':
            formattedData.question = rest as IQuestion;
            break;
          case 'discipline':
            formattedData.discipline = rest as IDiscipline;
            break;
          case 'category':
            formattedData.category = rest as ICategory;
            break;
          case 'subcategory':
            formattedData.subcategory = rest as ISubCategory;
            break;
          case 'questionType':
            formattedData.questionType = rest as IQuestionType;
            break;
          case 'questionGroup':
            formattedData.questionGroup = rest as IQuestionGroup;
            break;
          case 'simulated':
            formattedData.simulated = rest as ISimulated;
            break;
          case 'simulatedAccess':
            formattedData.simulatedAccess = rest as ISimulatedAccess;
            break;
          case 'questionWithAlternatives':
            formattedData.questionWithAlternatives = rest as IQuestionWithAlternativesDTO;
            break;
          case 'answerSessions':
            formattedData.answerSessionsData = rest as IAnswerSession[];
            break;
          default:
            console.warn(`⚠️ Tipo desconhecido encontrado: ${entityType}`);
        }
      });
    });

    return formattedData;
  }

  // TODO: Não está sendo utilizado
  async findAlternativesAnsweredByQuestionId(data: {
    questionId: string;
    customerId: string;
    tablesName: string[];
  }): Promise<Record<string, IAlternative[]>> {
    const { questionId, customerId, tablesName } = data;

    const queries = tablesName.map((table) => {
      return `
        SELECT alt.*, '${table}' AS type
        FROM ${table} AS resp
        JOIN alternatives AS alt ON resp.alternative_id = alt.id
        JOIN questions AS q ON alt.question_id = q.id
        WHERE alt.question_id = ?
        AND q.customer_id = ?
      `;
    });

    const dependencies = tablesName.flatMap(() => [questionId, customerId]);

    const sqlQuery = queries.join(' UNION ALL ');

    const result = await knexInstance.raw(sqlQuery, dependencies);

    const rows: Array<IAlternative & { type: string }> = result.rows || [];

    const formattedData: Record<string, IAlternative[]> = {};

    rows.forEach((row) => {
      if (!formattedData[row.type]) {
        formattedData[row.type] = [];
      }
      formattedData[row.type].push(row);
    });
    return formattedData;
  }
}
