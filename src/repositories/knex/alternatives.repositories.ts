import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IAlternativeWithIdDTO } from '../../model/DTO/IAlternative.dto';
import { IAlternative } from '../../model/IAlternative';
import { IAlternativesRepository } from '../assign/alternatives.assign';

export class KnexAlternativesRepository
  extends BaseRepository<IAlternative>
  implements IAlternativesRepository
{
  constructor() {
    super(knexInstance, 'alternatives');
  }

  async updateAll(data: IAlternativeWithIdDTO[]): Promise<IAlternative[]> {
    if (!data.length) return [];

    const validData = data.filter(
      ({ id, description, option, correct, question_id: questionId }) =>
        id && description && option && typeof correct === 'boolean' && questionId
    );

    const query = knexInstance.raw(
      `
        UPDATE alternatives AS a
        SET
            description = d.description,
            option = d.option,
            correct = d.correct,
            question_id = d.question_id,
            updated_at = NOW()
        FROM (
            VALUES ${validData.map(() => `(?::UUID, ?, ?, ?::BOOLEAN, ?::UUID)`).join(', ')}
        ) AS d(id, description, option, correct, question_id)
        WHERE a.id = d.id
        AND a.deleted_at IS NULL
        RETURNING a.*;
        `,
      validData.flatMap(({ id, description, option, correct, question_id: questionId }) => [
        id,
        description,
        option,
        correct,
        questionId,
      ])
    );

    const { rows } = await query;

    return rows;
  }

  async findByQuestionId(data: { questionId: string }): Promise<IAlternative[] | undefined> {
    const { questionId } = data;

    return await knexInstance('alternatives')
      .where('question_id', questionId)
      .whereNull('deleted_at');
  }

  async deleteByQuestionId(data: { questionId: string }): Promise<void> {
    const { questionId } = data;

    return await knexInstance('alternatives')
      .where('question_id', questionId)
      .whereNull('deleted_at')
      .update({ deleted_at: new Date() });
  }

  async deleteAllPermanent(ids: string[]): Promise<void> {
    return await knexInstance('alternatives').whereIn('id', ids).del();
  }
}
