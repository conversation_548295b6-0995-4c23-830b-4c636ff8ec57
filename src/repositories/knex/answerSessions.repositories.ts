import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IAnswerSession } from '../../model/IAnswerSession';
import { IAnswerSessionsRepository } from '../assign/answerSessions.assign';

export class KnexAnswerSessionsRepository
  extends BaseRepository<IAnswerSession>
  implements IAnswerSessionsRepository
{
  constructor() {
    super(knexInstance, 'answer_sessions');
  }

  async findBySimulatedAccessId(simulatedAccessId: string): Promise<IAnswerSession[]> {
    const result = await knexInstance('answer_sessions')
      .where({
        simulated_access_id: simulatedAccessId,
        deleted_at: null,
      })
      .orderBy('started_at', 'asc');

    return result;
  }

  async findByExamAccessId(examAccessId: string): Promise<IAnswerSession[]> {
    const result = await knexInstance('answer_sessions')
      .where({
        exam_access_id: examAccessId,
        deleted_at: null,
      })
      .orderBy('started_at', 'asc');

    return result;
  }
}
