import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { BulkImportsWithPaginationDTO } from '../../model/DTO/bulkImport.dto';
import { IPaginationDTO } from '../../model/DTO/IGeneric.dto';
import { IBulkImport } from '../../model/IBulkImport';
import { ListBulkImportsParams } from '../../schema/bulkImport.schema';
import { translateFormated } from '../../services/translateFormatted.service';
import { IBulkImportRepository } from '../assign/bulkImport.assign';

export class KnexBulkImportRepository
  extends BaseRepository<IBulkImport>
  implements IBulkImportRepository
{
  constructor() {
    super(knexInstance, 'bulk_imports');
  }

  async listBulkImports({
    page = 1,
    limit = 10,
    startDate,
    endDate,
    search,
    status,
    orderBy = 'created_at',
    orderDirection = 'desc',
    customerId,
  }: ListBulkImportsParams): Promise<BulkImportsWithPaginationDTO> {
    const baseQuery = knexInstance('bulk_imports as bi')
      .leftJoin('users as u', 'bi.created_by', 'u.id')
      .where('bi.customer_id', customerId);

    if (startDate) {
      baseQuery.where('bi.created_at', '>=', new Date(startDate));
    }

    if (endDate) {
      baseQuery.where('bi.created_at', '<=', new Date(endDate));
    }

    if (status) {
      baseQuery.where('bi.status', status);
    }

    if (search) {
      baseQuery.where((builder) => {
        builder
          .whereRaw(`${translateFormated('bi.file_name')} ILIKE ${translateFormated('?')}`, [
            `%${search}%`,
          ])
          .orWhereRaw(`${translateFormated('u.first_name')} ILIKE ${translateFormated('?')}`, [
            `%${search}%`,
          ])
          .orWhereRaw(`${translateFormated('u.last_name')} ILIKE ${translateFormated('?')}`, [
            `%${search}%`,
          ])
          .orWhereRaw(`${translateFormated('u.email')} ILIKE ${translateFormated('?')}`, [
            `%${search}%`,
          ]);
      });
    }

    const countResult = await baseQuery
      .clone()
      .clearSelect()
      .clearOrder()
      .countDistinct('bi.id as count')
      .first();

    const totalItems = countResult ? Number(countResult.count) : 0;
    const totalPages = Math.ceil(totalItems / limit) || 1;

    const offset = (page - 1) * limit;

    const query = baseQuery
      .clone()
      .select(
        'bi.id',
        'bi.file_name as fileName',
        'bi.file_key as fileKey',
        'bi.total_rows as totalRows',
        'bi.valid_count as validCount',
        'bi.invalid_count as invalidCount',
        'bi.success_percentage as successPercentage',
        'bi.status',
        'bi.customer_id as customerId',
        'bi.created_by as createdBy',
        'bi.imported_at as importedAt',
        'bi.created_at as createdAt',
        'bi.updated_at as updatedAt',
        'u.first_name as createdByFirstName',
        'u.last_name as createdByLastName',
        'u.email as createdByEmail'
      )
      .groupBy('bi.id', 'u.first_name', 'u.last_name', 'u.email');

    if (orderBy === 'file_name') {
      query.orderByRaw(`${translateFormated(`bi.${orderBy}`)} ${orderDirection}`);
    } else {
      query.orderBy(`bi.${orderBy}`, orderDirection);
    }

    const data = await query.limit(limit).offset(offset);

    const paginationInfo: IPaginationDTO = {
      currentPage: page,
      itemsPerPage: limit,
      totalItems,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };

    return {
      data,
      paginationInfo,
    };
  }

  async updateStatus(importId: string, status: string): Promise<void> {
    await knexInstance('bulk_imports').where('id', importId).update({ status });
  }

  async updateFileKey(importId: string, fileKey: string): Promise<void> {
    await knexInstance('bulk_imports').where('id', importId).update({ file_key: fileKey });
  }
}
