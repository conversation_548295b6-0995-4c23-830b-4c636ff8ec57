import { BaseRepository } from 'bq-knex-base-repository';
import { randomUUID } from 'crypto';

import { knexInstance } from '../../config/connectionDatabase.config';
import { BulkImportDetailInput, CountQueryResult } from '../../model/DTO/bulkImportDetails.dto';
import { IBulkImportDetail } from '../../model/IBulkImportDetails';
import { IBulkImportDetailRepository } from '../assign/bulkImportDetails.assign';

export class KnexBulkImportDetailRepository
  extends BaseRepository<IBulkImportDetail>
  implements IBulkImportDetailRepository
{
  constructor() {
    super(knexInstance, 'bulk_import_details');
  }

  async insertBatch(details: BulkImportDetailInput[]): Promise<void> {
    const detailsWithIds = details.map((detail) => ({
      ...detail,
      id: randomUUID(),
      created_at: new Date(),
      updated_at: new Date(),
    }));

    await knexInstance('bulk_import_details').insert(detailsWithIds);
  }

  async findByImportId(importId: string): Promise<IBulkImportDetail[]> {
    return await knexInstance('bulk_import_details')
      .where('bulk_import_id', importId)
      .orderBy('row_index', 'asc');
  }

  async findValidByImportId(importId: string): Promise<IBulkImportDetail[]> {
    return await knexInstance('bulk_import_details')
      .where({
        bulk_import_id: importId,
        is_valid: true,
      })
      .orderBy('row_index', 'asc');
  }

  async findInvalidByImportId(importId: string): Promise<IBulkImportDetail[]> {
    return await knexInstance('bulk_import_details')
      .where({
        bulk_import_id: importId,
        is_valid: false,
      })
      .orderBy('row_index', 'asc');
  }

  async countByImportId(importId: string): Promise<{
    totalValid: number;
    totalInvalid: number;
  }> {
    const result = await knexInstance('bulk_import_details')
      .where('bulk_import_id', importId)
      .select('is_valid')
      .count('* as count')
      .groupBy('is_valid');

    const counts = {
      totalValid: 0,
      totalInvalid: 0,
    };

    for (const row of result) {
      const item = row as unknown as CountQueryResult;

      const count = typeof item.count === 'string' ? parseInt(item.count, 10) : Number(item.count);

      if (item.is_valid) {
        counts.totalValid = count;
      } else {
        counts.totalInvalid = count;
      }
    }

    return counts;
  }

  async deleteByImportId(importId: string): Promise<void> {
    await knexInstance('bulk_import_details').where('bulk_import_id', importId).delete();
  }

  async findAllRowsByBulkImportId(importId: string): Promise<IBulkImportDetail[]> {
    return await knexInstance('bulk_import_details')
      .where('bulk_import_id', importId)
      .orderBy('row_index', 'asc');
  }

  async updateBatch(updates: Partial<IBulkImportDetail>[]): Promise<IBulkImportDetail[]> {
    if (updates.length === 0) return [];

    const updatedIds = updates.map((update) => update.id);

    await knexInstance.transaction(async (trx) => {
      for (const update of updates) {
        await trx('bulk_import_details')
          .where('id', update.id)
          .update({
            ...update,
            updated_at: new Date(),
          });
      }
    });

    return knexInstance('bulk_import_details')
      .whereIn('id', updatedIds as string[])
      .orderBy('row_index', 'asc');
  }
}
