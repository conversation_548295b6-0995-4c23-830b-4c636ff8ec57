import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { ICategoryFilterDTO } from '../../model/DTO/ICategory.dto';
import { ICategory } from '../../model/ICategory';
import { translateFormated } from '../../services/translateFormatted.service';
import { ICategoriesRepository } from '../assign/categories.assign';

export class KnexCategoriesRepository
  extends BaseRepository<ICategory>
  implements ICategoriesRepository
{
  constructor() {
    super(knexInstance, 'categories');
  }

  async findCategoryIdAndCustomerId(id: string, customerId: string): Promise<ICategory> {
    const category = await knexInstance('categories')
      .where({ id, customer_id: customerId })
      .whereNull('deleted_at')
      .first();
    return category;
  }

  async findByNameAndCustomerId(name: string, customerId: string): Promise<ICategory | undefined> {
    const category = await knexInstance('categories')
      .whereRaw(`${translateFormated('name')} = ${translateFormated('?')}`, [name])
      .andWhere('customer_id', customerId)
      .andWhere('deleted_at', null)
      .first();

    return category;
  }

  async deleteById(id: string): Promise<void> {
    await knexInstance('categories').where({ id }).update({ deleted_at: new Date() });
  }

  async findAllByCustomerId(data: ICategoryFilterDTO): Promise<ICategory[]> {
    const { customerId, orderByColumn = 'name', orderDirection = 'asc', search = null } = data;

    const query = knexInstance('categories as c')
      .leftJoin('subcategories as sc', 'sc.category_id', 'c.id')
      .select(
        'c.*',
        knexInstance.raw(
          `COALESCE(JSON_AGG(JSON_BUILD_OBJECT(
              'id', sc.id,
              'name', sc.name,
              'createdAt', sc.created_at,
              'updatedAt', sc.updated_at
            )) FILTER (WHERE sc.deleted_at IS NULL), '[]') as subcategories`
        )
      )
      .where({ 'c.customer_id': customerId })
      .whereNull('c.deleted_at')
      .whereNull('sc.deleted_at')
      .groupBy('c.id');

    if (search) {
      query.andWhere((builder) => {
        return builder
          .whereRaw(`${translateFormated('c.name')} ILIKE ${translateFormated('?')}`, [
            `%${search}%`,
          ])
          .orWhereRaw(`${translateFormated('sc.name')} ILIKE ${translateFormated('?')}`, [
            `%${search}%`,
          ]);
      });
    }

    query.orderBy(`c.${orderByColumn}`, orderDirection);

    const categories = await query;

    return categories;
  }

  async getTotalCatregories(customerId: string): Promise<number> {
    const totalCount = await knexInstance('categories')
      .count('*')
      .where({ customer_id: customerId });

    return Number(totalCount[0]?.count || 0);
  }
}
