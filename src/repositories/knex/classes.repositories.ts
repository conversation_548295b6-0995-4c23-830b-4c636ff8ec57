import { BaseRepository } from 'bq-knex-base-repository';
import { randomUUID } from 'crypto';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import {
  IClassDataForCreation,
  IClassRowDTO,
  IClassWithUsers,
  ISoftDeleteClassDTO,
  IUserRowDTO,
} from '../../model/DTO/IClasses.dto';
import { IPaginationDTO } from '../../model/DTO/IGeneric.dto';
import { IClasses } from '../../model/IClasses';
import { ListClassesQueryInput, ListClassesWithUsersInput } from '../../schema/classes.schema';
import { translateFormated } from '../../services/translateFormatted.service';
import { IClassesRepository } from '../assign/classes.assign';

export class KnexClassesRepository extends BaseRepository<IClasses> implements IClassesRepository {
  constructor() {
    super(knexInstance, 'classes');
  }

  async listByCustomerId(data: ListClassesQueryInput): Promise<IClasses[]> {
    const columnMap: Record<string, string> = {
      name: 'name',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      status: 'status',
      startDate: 'start_date',
      endDate: 'end_date',
    };

    const orderColumn = columnMap[data.orderByColumn || 'name'] || 'name';
    const orderDirection = data.orderDirection || 'asc';

    return knexInstance('classes')
      .where('customer_id', data.customerId)
      .whereNull('deleted_at')
      .modify((qb) => {
        if (data.search) {
          qb.whereRaw(`${translateFormated('name')} ILIKE ?`, [`%${data.search}%`]);
        }
      })
      .orderBy(orderColumn, orderDirection);
  }

  async softDelete(data: ISoftDeleteClassDTO): Promise<IClasses | undefined> {
    const query = await knexInstance('classes')
      .where({
        id: data.id,
        customer_id: data.customerId,
        deleted_at: null,
      })
      .update({ deleted_at: new Date(), updated_at: new Date() })
      .returning('*');

    return query[0];
  }

  async findByExternalClassId(data: {
    externalClassId: string;
    customerId: string;
  }): Promise<IClasses | undefined> {
    return knexInstance('classes')
      .where({
        external_class_id: data.externalClassId,
        customer_id: data.customerId,
      })
      .first();
  }

  async findByExternalClassIds({
    externalClassIds,
    customerId,
  }: {
    externalClassIds: string[];
    customerId: string;
  }): Promise<IClasses[]> {
    return knexInstance('classes')
      .select('*')
      .whereIn('external_class_id', externalClassIds)
      .andWhere('customer_id', customerId)
      .andWhere('deleted_at', null);
  }

  async createExternalClass(classData: IClassDataForCreation): Promise<IClasses> {
    const dataToInsert = {
      id: randomUUID(),
      created_at: new Date(),
      updated_at: new Date(),
      ...classData,
    };

    const [createdClass] = await knexInstance('classes').insert(dataToInsert).returning('*');
    return createdClass;
  }

  async findClassesWithUsersPaginated(data: ListClassesWithUsersInput): Promise<{
    classes: IClassWithUsers[];
    paginationInfo: IPaginationDTO;
  }> {
    const {
      customerId,
      search,
      orderByColumn = 'c.name',
      orderDirection = 'asc',
      page = 1,
      limit = 10,
      simulatedId,
    } = data;

    const baseQuery = knexInstance('classes as c')
      .where({ 'c.customer_id': customerId })
      .whereNull('c.deleted_at');

    if (search) {
      baseQuery.whereRaw(`${translateFormated('c.name')} ILIKE ${translateFormated('?')}`, [
        `%${search}%`,
      ]);
    }

    const [countResult] = await baseQuery.clone().count('c.id as total');
    const totalItems = parseInt(countResult.total as string, 10);
    const totalPages = Math.ceil(totalItems / limit);

    const paginationInfo: IPaginationDTO = {
      currentPage: page,
      itemsPerPage: limit,
      totalItems,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };

    if (totalItems === 0) {
      return { classes: [], paginationInfo };
    }

    let userJsonFields = `
      'id', u.id,
      'first_name', u.first_name,
      'last_name', u.last_name,
      'email', u.email,
      'role', u.role
    `;
    let joinSimulatedAccess = '';
    const params: (string | number)[] = [customerId];
    if (search) params.push(`%${search}%`);
    params.push(limit, (page - 1) * limit);

    if (simulatedId) {
      userJsonFields += ", 'marked', CASE WHEN sa.id IS NOT NULL THEN true ELSE false END";
      joinSimulatedAccess = `LEFT JOIN simulateds_access sa ON sa.user_id = u.id AND sa.simulated_id = ? AND sa.deleted_at IS NULL`;
      params.push(simulatedId);
    }

    const result = await knexInstance.raw(
      `
      WITH paginated_classes AS (
        SELECT c.*
        FROM classes c
        WHERE c.customer_id = ?
        AND c.deleted_at IS NULL
        ${search ? `AND ${translateFormated('c.name')} ILIKE ${translateFormated('?')}` : ''}
        ORDER BY ${orderByColumn} ${orderDirection}
        LIMIT ? OFFSET ?
      )
      SELECT 
        c.id, 
        c.name, 
        c.external_class_id,
        c.customer_id,
        c.created_at,
        c.updated_at,
        COALESCE(
          json_agg(
            json_build_object(
              ${userJsonFields}
            )
          ) FILTER (WHERE u.id IS NOT NULL), '[]'
        ) as users
      FROM paginated_classes c
      LEFT JOIN users_classes uc ON c.id = uc.class_id AND uc.deleted_at IS NULL
      LEFT JOIN users u ON uc.user_id = u.id AND u.deleted_at IS NULL
      ${joinSimulatedAccess}
      GROUP BY c.id, c.name, c.external_class_id, c.customer_id, c.created_at, c.updated_at
      ORDER BY ${orderByColumn} ${orderDirection}
    `,
      params
    );

    const classes = result.rows.map((row: IClassRowDTO) => {
      const users = Array.isArray(row.users) ? row.users : JSON.parse(row.users as string);
      const filteredUsers = users.filter((user: IUserRowDTO) => user && user.id);

      return {
        id: row.id,
        name: row.name,
        totalUsers: filteredUsers.length,
        users: filteredUsers,
      };
    });

    return { paginationInfo, classes };
  }

  async findByIds(ids: string[]): Promise<IClasses[]> {
    return knexInstance('classes').select('*').whereIn('id', ids).whereNull('deleted_at');
  }

  async getAllClasses(courseId: string): Promise<IClasses[]> {
    return await knexInstance('classes as c')
      .select('c.*')
      .count<{ userCount: string }[]>('uc.user_id as userCount')
      .leftJoin('users_classes as uc', function () {
        this.on('c.id', '=', 'uc.class_id').andOnNull('uc.deleted_at');
      })
      .where('c.course_id', courseId)
      .whereNull('c.deleted_at')
      .groupBy('c.id');
  }

  async createClass(data: IClassDataForCreation, trx?: Knex.Transaction): Promise<IClasses> {
    const now = new Date();
    const dataToInsert = {
      id: randomUUID(),
      created_at: now,
      updated_at: now,
      ...data,
    };

    const [createdClass] = await (trx ?? knexInstance)('classes')
      .insert(dataToInsert)
      .returning('*');
    return createdClass;
  }

  async countUsersInClass(classId: string): Promise<number> {
    const result = await knexInstance('users_classes')
      .where({ class_id: classId })
      .whereNull('deleted_at')
      .count<{ count: string }[]>('user_id as count');
    return Number(result[0]?.count ?? 0);
  }

  async deleteClass(id: string): Promise<void> {
    await knexInstance('classes').where({ id }).update({ deleted_at: new Date() });
  }

  async insert(data: Partial<IClasses>, trx?: Knex.Transaction): Promise<IClasses> {
    const now = new Date();

    const dataToInsert = {
      id: randomUUID(),
      created_at: now,
      updated_at: now,
      ...data,
    };

    const [createdClass] = await (trx ?? knexInstance)('classes')
      .insert(dataToInsert)
      .returning('*');

    return createdClass;
  }

  async updateWithTrx(item: Partial<IClasses>, trx?: Knex.Transaction): Promise<IClasses> {
    const [updatedClass] = await (trx ?? knexInstance)('classes')
      .where({ id: item.id })
      .update(item)
      .returning('*');
    return updatedClass;
  }

  async findByClassesByCoursesId(coursesId: string[]): Promise<IClasses[]> {
    return knexInstance('classes')
      .select('*')
      .whereIn('course_id', coursesId)
      .whereNull('deleted_at');
  }
}
