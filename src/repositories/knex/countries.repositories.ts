import { BaseRepository } from 'bq-knex-base-repository';
import { randomUUID } from 'crypto';

import { knexInstance } from '../../config/connectionDatabase.config';
import { ICountry } from '../../model/countries';
import { ListAllCountriesDto } from '../../schema/countries.schema';
import { translateFormated } from '../../services/translateFormatted.service';
import { ICountriesRepository } from '../assign/countries.assign';

export class KnexCountriesRepository
  extends BaseRepository<ICountry>
  implements ICountriesRepository
{
  constructor() {
    super(knexInstance, 'countries');
  }

  async insertManyCountries(countries: Partial<ICountry>[]): Promise<ICountry[]> {
    if (countries.length === 0) {
      return [];
    }

    const countriesToInsert = countries.map((country) => ({
      id: randomUUID(),
      created_at: new Date(),
      updated_at: new Date(),
      ...country,
    }));

    return await knexInstance.transaction(async (trx) => {
      const insertedCountries = await trx('countries').insert(countriesToInsert).returning('*');
      return insertedCountries;
    });
  }

  async findAllCountries(data: ListAllCountriesDto): Promise<ICountry[]> {
    const { search, orderByColumn, orderByDirection } = data;
    const query = knexInstance('countries').select('*');

    if (search) {
      query.andWhere((builder) => {
        return builder
          .whereRaw(`${translateFormated('name')} ILIKE ${translateFormated('?')}`, [`%${search}%`])

          .orWhereRaw(`${translateFormated('alpha_2')} ILIKE ${translateFormated('?')}`, [
            `%${search}%`,
          ])

          .orWhereRaw(`${translateFormated('alpha_3')} ILIKE ${translateFormated('?')}`, [
            `%${search}%`,
          ]);
      });
    }

    query.orderBy(orderByColumn || 'name', orderByDirection || 'asc');

    return query;
  }
}
