import { BaseRepository } from 'bq-knex-base-repository';
import { randomUUID } from 'crypto';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import { ILessonResponse } from '../../model/DTO/response/lesson.dto';
import { StatusLessonEnum } from '../../model/enums/status.enum';
import { ICourseLesson } from '../../model/ICourseLesson';
import { ICourseLessonRepository } from '../assign/courseLessons.assign';

export class KnexCourseLessonsRepository
  extends BaseRepository<ICourseLesson>
  implements ICourseLessonRepository
{
  constructor() {
    super(knexInstance, 'courses_lessons');
  }

  async updateLesson(data: Partial<ICourseLesson>, trx?: Knex.Transaction): Promise<ICourseLesson> {
    const [lesson] = await (trx ?? knexInstance)('courses_lessons')
      .where({ id: data.id })
      .update(data)
      .returning('*');
    return lesson;
  }

  async createLesson(data: Partial<ICourseLesson>): Promise<ICourseLesson> {
    const now = new Date();
    const payload = {
      id: randomUUID(),
      created_at: now,
      updated_at: now,
      ...data,
    };
    const [lesson] = await knexInstance('courses_lessons').insert(payload).returning('*');
    return lesson;
  }

  async updateLessonById(id: string, data: Partial<ICourseLesson>): Promise<void> {
    await knexInstance('courses_lessons')
      .where({ id })
      .update({
        ...data,
        updated_at: new Date(),
      });
  }

  async insertLesson(data: Partial<ICourseLesson>, trx?: Knex.Transaction): Promise<ICourseLesson> {
    const now = new Date();
    const payload = {
      id: randomUUID(),
      created_at: now,
      updated_at: now,
      ...data,
    };
    const [lesson] = await (trx ?? knexInstance)('courses_lessons').insert(payload).returning('*');
    return lesson;
  }

  async updateLessonsOrderBatch(
    lessons: { id: string; order_by: number }[],
    trx?: Knex.Transaction
  ): Promise<void> {
    const db = trx ?? knexInstance;
    for (const lesson of lessons) {
      await db('courses_lessons')
        .where({ id: lesson.id })
        .update({ order_by: lesson.order_by, updated_at: new Date() });
    }
  }

  async countPublishedLessonsByModuleId(moduleId: string): Promise<number> {
    const [{ count }] = await knexInstance('courses_lessons')
      .where({ module_id: moduleId, published: true, status: StatusLessonEnum.PUBLISHED })
      .whereNull('deleted_at')
      .count<{ count: string }[]>('* as count');
    return Number(count);
  }

  async findByModuleId(moduleId: string): Promise<ICourseLesson[]> {
    return knexInstance('courses_lessons')
      .where({ module_id: moduleId })
      .orderBy('order_by', 'asc');
  }

  async getLessonById(id: string): Promise<ILessonResponse | undefined> {
    const lesson = await knexInstance('courses_lessons as cl')
      .leftJoin('files as f', 'f.lesson_id', 'cl.id')
      .select(
        'cl.id',
        'cl.created_at as createdAt',
        'cl.updated_at as updatedAt',
        'cl.deleted_at as deletedAt',
        'cl.title',
        'cl.lesson_url as lessonUrl',
        'cl.duration',
        'cl.description_lessons as descriptionLessons',
        'cl.module_id as moduleId',
        'cl.question_group_id as questionGroupId',
        'cl.order_by as orderBy',
        'cl.published as published',
        'cl.status as status',
        'cl.lesson_type as type',
        knexInstance.raw(`
          COALESCE(
            json_agg(
              jsonb_build_object(
                'id', f.id,
                'url', f.url,
                'file_name', f.file_name,
                'file_type', f.file_type,
                'file_size', f.file_size,
                'created_at', f.created_at,
                'updated_at', f.updated_at
              )
            ) FILTER (WHERE f.id IS NOT NULL), '[]'
          ) AS files
        `)
      )
      .where({ 'cl.id': id })
      .whereNull('cl.deleted_at')
      .groupBy('cl.id')
      .first();

    return lesson ? { ...lesson, duration: Number(lesson.duration) ?? null } : undefined;
  }

  async softDeleteLessonsByModuleId(
    moduleId: string,
    trx?: Knex.Transaction
  ): Promise<ICourseLesson[]> {
    return (trx ?? knexInstance)('courses_lessons')
      .where({ module_id: moduleId })
      .whereNull('deleted_at')
      .update({ deleted_at: new Date(), updated_at: new Date() })
      .returning('*');
  }

  async softDeleteLesson(id: string, trx?: Knex.Transaction): Promise<ICourseLesson | undefined> {
    const [lesson] = await (trx ?? knexInstance)('courses_lessons')
      .where({ id })
      .whereNull('deleted_at')
      .update({ deleted_at: new Date(), updated_at: new Date() })
      .returning('*');
    return lesson;
  }
}
