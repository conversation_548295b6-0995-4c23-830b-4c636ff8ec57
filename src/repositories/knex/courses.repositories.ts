import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import { secondsToTime, timeToSeconds } from '../../helpers/time';
import { ICourseDetail, ICourseLastAccess } from '../../model/DTO/ICourse.dto';
import { ICourseDetailsRawData } from '../../model/DTO/request/course.request.dto';
import {
  ICourseWithoutMetadata,
  ListCoursesByUserAndClassResponseDto,
} from '../../model/DTO/response/course.dto';
import { ILastLessonAccess, ILastModuleAccess } from '../../model/DTO/response/progress.dto';
import { UserLessonsProgressStatus } from '../../model/enums/userProgressStatus.enum';
import { ICourse } from '../../model/ICourse';
import { ICourseRepository } from '../assign/courses.assign';

export class KnexCourseRepository extends BaseRepository<ICourse> implements ICourseRepository {
  constructor() {
    super(knexInstance, 'courses');
  }

  async lastAccess(userId: string): Promise<ICourseLastAccess | undefined> {
    const lastAccess = await knexInstance('users_lessons_progress as ulp')
      .select(
        'u.first_name as user_name',
        'c.id as course_id',
        'c.name as course_name',
        'c.photo_url',
        'cm.id as module_id',
        'cm.name as module_name',
        'cl.id as lesson_id',
        'cl.title as lesson_title',
        'cl.duration as lesson_duration',
        'ulp.current_position',
        'ulp.status',
        'ulp.last_accessed_at'
      )
      .leftJoin('users as u', 'ulp.user_id', 'u.id')
      .leftJoin('courses_lessons as cl', 'ulp.lesson_id', 'cl.id')
      .leftJoin('courses_modules as cm', 'cl.module_id', 'cm.id')
      .leftJoin('courses as c', 'cm.course_id', 'c.id')
      .where('ulp.user_id', userId)
      .orderBy('ulp.last_accessed_at', 'desc')
      .first();

    if (!lastAccess) {
      return undefined;
    }

    const lessonDurationSeconds = timeToSeconds(lastAccess.lesson_duration || '00:00');
    const currentPositionSeconds = parseInt(lastAccess.current_position, 10) || 0;

    const progressPercent = lessonDurationSeconds
      ? Math.round((currentPositionSeconds / lessonDurationSeconds) * 100)
      : 0;

    let remainingTime = lessonDurationSeconds - currentPositionSeconds;
    if (lastAccess.status === UserLessonsProgressStatus.COMPLETED) {
      remainingTime = 0;
    }

    return {
      userName: lastAccess.user_name,
      courseId: lastAccess.course_id,
      courseName: lastAccess.course_name,
      logoUrl: lastAccess.photo_url,
      moduleName: lastAccess.module_name,
      lessonTitle: lastAccess.lesson_title,
      lessonId: lastAccess.lesson_id,
      lessonDuration: lastAccess.lesson_duration,
      currentPosition: secondsToTime(currentPositionSeconds),
      progressPercent,
      remainingTime: secondsToTime(remainingTime),
      lastAccessedAt: lastAccess.last_accessed_at,
      status: lastAccess.status,
    };
  }

  async listAllCoursesUser(userId: string): Promise<ListCoursesByUserAndClassResponseDto[]> {
    return knexInstance('courses as c')
      .join('classes as cl', 'cl.course_id', 'c.id')
      .join('users_classes as uc', 'uc.class_id', 'cl.id')
      .select(
        'c.*',
        'uc.class_id',
        'cl.name as class_name',
        knexInstance.raw(`
          CASE 
            WHEN last_lesson_data.lesson_id IS NOT NULL THEN 
              json_build_object(
                'id', last_lesson_data.lesson_id,
                'title', last_lesson_data.lesson_title,
                'moduleId', last_lesson_data.module_id,
                'moduleName', last_lesson_data.module_name
              )
            ELSE NULL
          END as "lastLessonAccess"
        `),
        knexInstance.raw(`
          (SELECT cqg.question_group_id 
           FROM courses_questions_groups cqg 
           WHERE cqg.course_id = c.id 
           LIMIT 1) as "questionGroupId"
        `)
      )
      .leftJoin(
        knexInstance.raw(
          `
          (SELECT DISTINCT ON (course_id) 
            course_id,
            lesson_id,
            lesson_title,
            module_id,
            module_name,
            last_accessed_at
          FROM (

          SELECT 
              cm.course_id,
              ulp.lesson_id,
              cl.title as lesson_title,
              cm.id as module_id,
              cm.name as module_name,
              ulp.last_accessed_at,
              1 as priority
            FROM users_lessons_progress ulp
            JOIN courses_lessons cl ON ulp.lesson_id = cl.id
            JOIN courses_modules cm ON cl.module_id = cm.id
            WHERE ulp.user_id = ? 
              AND cl.published = true 
              AND cl.status = 'published'
              AND cm.published = true 
              AND cm.status = 'published'
            
            UNION ALL
            
            SELECT 
              cm.course_id,
              cl.id as lesson_id,
              cl.title as lesson_title,
              cm.id as module_id,
              cm.name as module_name,
              NULL as last_accessed_at,
              2 as priority
            FROM courses_modules cm
            JOIN courses_lessons cl ON cl.module_id = cm.id
            WHERE cl.order_by = 1 
              AND cm.order_by = 1 
              AND cl.published = true 
              AND cl.status = 'published'
              AND cm.published = true 
              AND cm.status = 'published'
          ) combined_data
          ORDER BY course_id, priority ASC, last_accessed_at DESC
          ) as last_lesson_data
        `,
          [userId]
        ),
        'last_lesson_data.course_id',
        'c.id'
      )
      .where('uc.user_id', '=', userId);
  }

  async listAllCoursesSimulations(customerId: string, userId: string): Promise<ICourse[]> {
    return knexInstance('courses as c')
      .select('c.*', 'cqg.question_group_id as questionBankId', 'cl.id as classId')
      .innerJoin('courses_questions_groups as cqg', 'cqg.course_id', 'c.id')
      .join('classes as cl', 'cl.course_id', 'c.id')
      .join('users_classes as uc', 'uc.class_id', 'cl.id')
      .where('c.customer_id', customerId)
      .where('uc.user_id', userId)
      .orderBy('c.name', 'asc');
  }

  async listAllCourses(customerId: string): Promise<ICourse[]> {
    return knexInstance('courses as c')
      .select(
        'c.*',
        knexInstance.raw(`(
          SELECT COUNT(DISTINCT uc.user_id)
          FROM users_classes uc
          JOIN classes cl ON uc.class_id = cl.id
          WHERE cl.course_id = c.id AND uc.deleted_at IS NULL AND cl.deleted_at IS NULL
        ) as students_count`),
        knexInstance.raw(`(
          SELECT COALESCE(json_agg(json_build_object('id', cc.courses_categories_id, 'name', cat.name)), '[]')
          FROM courses_categories_relation cc
          JOIN courses_categories cat ON cat.id = cc.courses_categories_id
          WHERE cc.course_id = c.id
        ) as categories`)
      )
      .where('c.customer_id', customerId)
      .orderBy('c.name', 'asc');
  }

  async findCourseById(courseId: string): Promise<ICourseDetail | undefined> {
    const courseData = await knexInstance('courses as c')
      .select(
        'c.id as course_id',
        'c.name as course_name',
        'c.description_course',
        'c.photo_url',
        'c.created_at as course_created_at',
        'cm.id as module_id',
        'cm.name as module_name',
        'cm.description_module',
        'cm.created_at as module_created_at',
        'cm.order_by as module_order_by',
        'cm.status',
        'cm.published',
        'cm.deleted_at as cm_deleted_at',
        'cl.id as lesson_id',
        'cl.title as lesson_title',
        'cl.lesson_url',
        'cl.duration',
        'cl.description_lessons',
        'cl.created_at as lesson_created_at',
        'cl.order_by as lesson_order_by',
        'cl.question_group_id as questionGroupId',
        'cl.deleted_at as cl_deleted_at',
        'cl.status as cl_status',
        'cl.published as cl_published',
        knexInstance.raw(`(
          SELECT COALESCE(json_agg(json_build_object('id', cc.courses_categories_id, 'name', cat.name)), '[]')
          FROM courses_categories_relation cc
          JOIN courses_categories cat ON cat.id = cc.courses_categories_id
          WHERE cc.course_id = c.id
        ) as categories`)
      )
      .leftJoin('courses_modules as cm', function () {
        this.on('c.id', '=', 'cm.course_id').onNull('cm.deleted_at');
      })
      .leftJoin('courses_lessons as cl', function () {
        this.on('cm.id', '=', 'cl.module_id').onNull('cl.deleted_at');
      })
      .where('c.id', courseId)
      .orderBy([
        { column: 'cm.order_by', order: 'asc' }, // Ordena os módulos
        { column: 'cl.order_by', order: 'asc' }, // Ordena as aulas
      ]);

    // Verifica se o curso existe
    if (!courseData.length) {
      throw new Error('Course not found');
    }

    // Organiza os dados em um objeto estruturado
    const course: ICourseDetail = {
      id: courseData[0].course_id,
      name: courseData[0].course_name,
      description_course: courseData[0].description_course,
      photo_url: courseData[0].photo_url,
      created_at: courseData[0].course_created_at,
      customer_id: courseData[0].customer_id, // e outros campos obrigatórios de ICourse
      updated_at: courseData[0].updated_at,
      deleted_at: courseData[0].deleted_at,
      modules: [],
      categories: courseData[0].categories || [],
    };

    const moduleMap = new Map();

    for (const row of courseData) {
      if (row.module_id && !moduleMap.has(row.module_id)) {
        moduleMap.set(row.module_id, {
          id: row.module_id,
          name: row.module_name,
          description_module: row.description_module,
          order_by: row.module_order_by,
          created_at: row.module_created_at,
          status: row.status, // novo campo
          published: row.published, // novo campo
          lessons: [],
          total_lessons: 0, // será atualizado depois
        });
      }

      if (row.lesson_id && row.module_id && moduleMap.has(row.module_id)) {
        moduleMap.get(row.module_id).lessons.push({
          id: row.lesson_id,
          title: row.lesson_title,
          lesson_url: row.lesson_url,
          duration: Number(row.duration),
          description_lessons: row.description_lessons,
          order_by: row.lesson_order_by,
          questionGroupId: row.questionGroupId,
          type: row.questionGroupId ? 'question' : 'video',
          created_at: row.lesson_created_at,
          status: row.cl_status, // <-- usar o status da aula
          published: row.cl_published, // <-- usar o published da aula
        });
      }
    }

    for (const module of moduleMap.values()) {
      module.total_lessons = module.lessons.length;
    }

    course.modules = Array.from(moduleMap.values());

    return course;
  }

  async getCourseQuestionBankByCourseId(courseId: string): Promise<ICourse | null> {
    const result = knexInstance('courses_questions_groups').where({ course_id: courseId }).first();
    if (!result) return null;

    return result;
  }

  async update(data: Partial<ICourse>, trx?: Knex.Transaction): Promise<ICourse> {
    if (trx) {
      const result = await trx('courses').where('id', data.id).update(data).returning('*');
      return result[0];
    }
    const result = await knexInstance('courses').where('id', data.id).update(data).returning('*');
    return result[0];
  }

  async insert(data: ICourseWithoutMetadata, trx?: Knex.Transaction): Promise<ICourse> {
    if (trx) {
      const result = await trx('courses').insert(data).returning('*');
      return result[0];
    }
    const result = await knexInstance('courses').insert(data).returning('*');
    return result[0];
  }

  async findCourseUser(data: { user_id: string }): Promise<ICourse[] | undefined> {
    const result = await knexInstance('users_classes as uc')
      .select('c.*', 'uc.user_id as user_id', 'uc.class_id as class_id', 'cl.name as class_name')
      .join('classes as cl', 'cl.id', 'uc.class_id') // ok
      .join('courses as c', 'c.id', 'cl.course_id') // corrigido aqui!
      .where('uc.user_id', data.user_id)
      .whereNull('uc.deleted_at');
    return result;
  }

  async getCourseDetails(courseId: string, userId: string): Promise<ICourseDetailsRawData[]> {
    const query = `
      SELECT 
        c.name AS course_name,
        c.id AS course_id,
        m.id AS module_id,
        m.name AS module_name,
        m.description_module AS module_description,
        l.id AS lesson_id,
        l.title AS lesson_title,
        l.description_lessons AS lesson_description,
        l.lesson_url AS lesson_url,
        l.order_by AS lesson_order_by,
        m.order_by AS module_order_by,
        l.question_group_id AS question_group_id,
        l.duration AS lesson_duration,
        COALESCE(ucp.status, 'not_started') AS course_status,
        COALESCE(ump.status, 'not_started') AS module_status,
        COALESCE(ulp.status, 'not_started') AS lesson_status,
        COALESCE(ulp.current_position, '0') AS lesson_current_position,
        (
          SELECT COALESCE(
            json_agg(
              jsonb_build_object(
                'id', f2.id,
                'url', f2.url,
                'file_name', f2.file_name,
                'file_type', f2.file_type,
                'file_size', f2.file_size,
                'created_at', f2.created_at,
                'updated_at', f2.updated_at
              )
            ), '[]'
          )
          FROM files f2
          WHERE f2.lesson_id = l.id
        ) AS lesson_files
      FROM courses AS c
      INNER JOIN courses_modules AS m ON m.course_id = c.id
      INNER JOIN courses_lessons AS l ON l.module_id = m.id
      LEFT JOIN users_courses_progress AS ucp 
        ON ucp.course_id = c.id AND ucp.user_id = ?
      LEFT JOIN users_modules_progress AS ump 
        ON ump.module_id = m.id AND ump.user_id = ?
      LEFT JOIN users_lessons_progress AS ulp 
        ON ulp.lesson_id = l.id AND ulp.user_id = ?
      WHERE c.id = ?
        AND m.published = true
        AND l.published = true
        AND l.status = 'published'
        AND m.status = 'published'
        AND m.deleted_at IS NULL
        AND l.deleted_at IS NULL
      ORDER BY m.order_by ASC, l.order_by ASC;
    `;

    const result = await knexInstance.raw(query, [userId, userId, userId, courseId]);
    return result.rows;
  }

  async getLastModuleAccess(
    userId: string,
    courseId: string
  ): Promise<ILastModuleAccess | undefined> {
    const lastModuleAccess = await knexInstance('users_modules_progress as ump')
      .leftJoin('courses_modules as cm', 'cm.id', 'ump.module_id')
      .select('ump.module_id')
      .where('ump.user_id', userId)
      .andWhere('cm.course_id', courseId)
      .orderBy('ump.last_accessed_at', 'DESC')
      .first();

    return lastModuleAccess;
  }

  async getLastLessonAccess(
    userId: string,
    courseId: string
  ): Promise<ILastLessonAccess | undefined> {
    const lastLessonAccess = await knexInstance('users_lessons_progress as ulp')
      .leftJoin('courses_lessons as cl', 'cl.id', 'ulp.lesson_id')
      .leftJoin('courses_modules as cm', 'cm.id', 'cl.module_id')
      .select('ulp.lesson_id')
      .where('ulp.user_id', userId)
      .andWhere('cm.course_id', courseId)
      .orderBy('ulp.last_accessed_at', 'DESC')
      .first();

    return lastLessonAccess;
  }
}
