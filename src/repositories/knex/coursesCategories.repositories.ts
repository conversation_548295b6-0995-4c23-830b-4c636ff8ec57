import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { ICourseCategory } from '../../model/courseCategory';
import { filterTextRegex } from '../../services/filterTextRegex.service';
import { translateFormated } from '../../services/translateFormatted.service';
import {
  ICoursesCategoriesFilters,
  ICoursesCategoriesRepository,
} from '../assign/coursesCategories.assign';

export class KnexCoursesCategoriesRepository
  extends BaseRepository<ICourseCategory>
  implements ICoursesCategoriesRepository
{
  constructor() {
    super(knexInstance, 'courses_categories');
  }

  async findAllByFilters(data: ICoursesCategoriesFilters): Promise<ICourseCategory[]> {
    const query = knexInstance.select('*').from('courses_categories');

    if (data.name) {
      const normalized = filterTextRegex(data.name).toLowerCase();

      query.whereRaw(`${translateFormated('name')} LIKE ?`, [`%${normalized}%`]);
    }

    query.orderBy('name', 'asc');

    return query;
  }
}
