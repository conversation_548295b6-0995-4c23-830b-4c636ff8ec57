import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import { ICoursesCategoriesRelation } from '../../model/coursesCategoriesRelation';
import { ICoursesCategoriesRelationRepository } from '../assign/coursesCategoriesRelation.assign';

export class KnexCoursesCategoriesRelationRepository
  extends BaseRepository<ICoursesCategoriesRelation>
  implements ICoursesCategoriesRelationRepository
{
  constructor() {
    super(knexInstance, 'courses_categories_relation');
  }

  async findAllByCourseId(data: { course_id: string }): Promise<ICoursesCategoriesRelation[]> {
    return knexInstance('courses_categories_relation')
      .select('*')
      .where('course_id', data.course_id);
  }

  async deleteByCourseIdAndCategoryIds(
    courseId: string,
    categoryIds: string[],
    trx?: Knex.Transaction
  ): Promise<void> {
    if (trx) {
      return await trx('courses_categories_relation')
        .where('course_id', courseId)
        .whereIn('courses_categories_id', categoryIds)
        .delete();
    }
    return await knexInstance('courses_categories_relation')
      .where('course_id', courseId)
      .whereIn('courses_categories_id', categoryIds)
      .delete();
  }

  async insertAll(
    data: ICoursesCategoriesRelation[],
    trx?: Knex.Transaction
  ): Promise<ICoursesCategoriesRelation[]> {
    if (trx) {
      return trx('courses_categories_relation').insert(data).returning('*');
    }
    return knexInstance('courses_categories_relation').insert(data).returning('*');
  }
}
