import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { ICourseExams } from '../../model/ICourseExams';
import { ICourseExamsRepository } from '../assign/coursesExams.assign';

export class KnexCourseExamsRepository
  extends BaseRepository<ICourseExams>
  implements ICourseExamsRepository
{
  constructor() {
    super(knexInstance, 'courses_exams');
  }

  async getExamIdsByCourseId(courseId: string): Promise<string[]> {
    const mapping = await knexInstance('courses_exams')
      .where({ course_id: courseId })
      .select('exam_id');

    return mapping?.map((item) => item.exam_id) || [];
  }

  async insertAll(courseExams: ICourseExams[]): Promise<ICourseExams[]> {
    const [inserted] = await knexInstance.insert(courseExams).into('courses_exams').returning('*');
    return inserted;
  }

  async deleteByCourseId(courseId: string): Promise<ICourseExams[]> {
    const [deleted] = await knexInstance
      .where({ course_id: courseId })
      .delete()
      .from('courses_exams')
      .returning('*');
    return deleted;
  }

  async findByCourseId(courseId: string): Promise<ICourseExams[]> {
    return knexInstance('courses_exams').where({ course_id: courseId });
  }

  async deleteByCourseIdAndExamIds(courseId: string, examIds: string[]): Promise<void> {
    await knexInstance('courses_exams')
      .where({ course_id: courseId })
      .whereIn('exam_id', examIds)
      .del();
  }
}
