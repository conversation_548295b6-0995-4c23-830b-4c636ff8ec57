import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import { ICourseQuestionsGroups } from '../../model/ICourseQuestionsGroups';
import { ICourseQuestionsGroupsRepository } from '../assign/coursesQuestionsGroups.assign';

export class KnexCourseQuestionsGroupsRepository
  extends BaseRepository<ICourseQuestionsGroups>
  implements ICourseQuestionsGroupsRepository
{
  constructor() {
    super(knexInstance, 'courses_questions_groups');
  }

  async insert(
    data: ICourseQuestionsGroups,
    trx?: Knex.Transaction
  ): Promise<ICourseQuestionsGroups> {
    if (trx) {
      const [inserted] = await trx.insert(data).into('courses_questions_groups').returning('*');
      return inserted;
    }
    const [inserted] = await knexInstance
      .insert(data)
      .into('courses_questions_groups')
      .returning('*');
    return inserted;
  }

  async update(
    data: Partial<ICourseQuestionsGroups>,
    trx?: Knex.Transaction
  ): Promise<ICourseQuestionsGroups> {
    if (trx) {
      const [updated] = await trx
        .update(data)
        .into('courses_questions_groups')
        .where('id', data.id)
        .returning('*');
      return updated;
    }
    const [updated] = await knexInstance
      .update(data)
      .where('id', data.id)
      .into('courses_questions_groups')
      .returning('*');
    return updated;
  }
}
