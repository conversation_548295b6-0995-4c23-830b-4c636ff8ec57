import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { ICustomer } from '../../model/ICustomer';
import { ICustomerRepository } from '../assign/customers.assign';

export class KnexCustomerRepository
  extends BaseRepository<ICustomer>
  implements ICustomerRepository
{
  constructor() {
    super(knexInstance, 'customers');
  }

  async findById(id: string): Promise<ICustomer | undefined> {
    return await knexInstance('customers').where({ id }).first();
  }

  async getTaxNumber(taxNumber: string): Promise<ICustomer | undefined> {
    const customer = await knexInstance('customers').where({ tax_number: taxNumber }).first();
    return customer;
  }

  async findByExternalId(externalCustomerId: string): Promise<ICustomer> {
    const query = await knexInstance('customers').where({
      external_customer_id: externalCustomerId,
    });
    return query[0];
  }

  async getDefaultCustomer(): Promise<ICustomer | undefined> {
    const [customer] = await knexInstance('customers').select('*').orderBy('created_at', 'asc');

    return customer;
  }
}
