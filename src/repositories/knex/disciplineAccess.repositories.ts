import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IDisciplineAccess } from '../../model/IDisciplineAccess';
import { IDisciplinesAccessRepository } from '../assign/disciplinesAccess.assign';

export class KnexDisciplineAccessRepository
  extends BaseRepository<IDisciplineAccess>
  implements IDisciplinesAccessRepository
{
  constructor() {
    super(knexInstance, 'disciplines_access');
  }

  async deleteByDisciplineId(disciplineId: string): Promise<void> {
    await knexInstance('disciplines_access').where({ discipline_id: disciplineId }).del();
  }
}
