import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { ICustomerIdDTO } from '../../model/DTO/ICustumers.dto';
import {
  IDisciplinesAccessHierarchyDTO,
  IDisciplineWithQuestionCount,
  ISubcategoryWithQuestionCount,
  ListDisciplinesWithQuestionCountDTO,
} from '../../model/DTO/IDiscipline.dto';
import { ICategoryAccessDTO } from '../../model/DTO/IDisciplineCategoryAccess.dto';
import { IDiscipline } from '../../model/IDiscipline';
import { OrderDirection, SortingService } from '../../services/sorting.service';
import { translateFormated } from '../../services/translateFormatted.service';
import { IDisciplinesRepository } from '../assign/disciplines.assign';

export class KnexDisciplinesRepository
  extends BaseRepository<IDiscipline>
  implements IDisciplinesRepository
{
  constructor() {
    super(knexInstance, 'disciplines');
  }

  async deleteById(id: string, customerId: string): Promise<IDiscipline> {
    const deletedDiscipline = await knexInstance('disciplines')
      .where({ id, customer_id: customerId })
      .update({ deleted_at: new Date() })
      .returning('*');

    return deletedDiscipline[0];
  }

  async findByNameAndCustomerId(
    name: string,
    customerId: string
  ): Promise<IDiscipline | undefined> {
    const query = await knexInstance('disciplines')
      .select('*')
      .whereRaw(`${translateFormated('name')} = ${translateFormated('?')}`, [name])
      .andWhere('customer_id', customerId)
      .andWhere('deleted_at', null)
      .first();

    return query;
  }

  async findById(id: string): Promise<IDiscipline | undefined> {
    return await knexInstance('disciplines').where({ id }).whereNull('deleted_at').first();
  }

  async findAllCodesByCustomerId(customerId: string): Promise<string[]> {
    const query = await knexInstance('disciplines')
      .select('code')
      .where('customer_id', customerId)
      .whereNull('deleted_at');

    return query.map((item) => item.code);
  }

  async listByCustomerId(data: {
    customerId: string;
    search?: string;
    orderByColumn?: string;
    orderDirection?: string;
  }): Promise<IDiscipline[]> {
    const { customerId, orderByColumn = 'name', orderDirection = 'asc', search } = data;

    const query = knexInstance('disciplines')
      .select('*')
      .where({ customer_id: customerId })
      .whereNull('deleted_at');

    if (search) {
      query.whereRaw(`${translateFormated('name')} ILIKE ${translateFormated('?')}`, [
        `%${search}%`,
      ]);
    }

    query.orderBy(orderByColumn, orderDirection);

    return await query;
  }

  async listDisciplinesWithAccessHierarchy({
    customerId,
  }: ICustomerIdDTO): Promise<IDisciplinesAccessHierarchyDTO[]> {
    const orderDirection: OrderDirection = OrderDirection.ASC;

    const queryResult = await knexInstance('disciplines as d')
      .innerJoin('disciplines_categories_access as dca', 'dca.discipline_id', 'd.id')
      .innerJoin('categories as c', 'c.id', 'dca.category_id')
      .leftJoin(
        knexInstance
          .select('dsa.category_id', 'dsa.discipline_id')
          .select(
            knexInstance.raw(
              `jsonb_agg(jsonb_build_object(
                'subcategoryId', s.id,
                'subcategoryName', s.name
              ) ORDER BY LOWER(s.name) ${orderDirection}) AS subcategories`
            )
          )
          .from('disciplines_subcategories_access as dsa')
          .join('subcategories as s', 's.id', 'dsa.subcategory_id')
          .groupBy(['dsa.category_id', 'dsa.discipline_id'])
          .as('subquery'),
        function () {
          this.on('subquery.category_id', '=', 'c.id').on('subquery.discipline_id', '=', 'd.id');
        }
      )
      .select(
        'd.id as disciplineId',
        'd.name as disciplineName',
        knexInstance.raw(
          `jsonb_agg(jsonb_build_object(
            'categoryId', c.id,
            'categoryName', c.name,
            'subcategories', COALESCE(subquery.subcategories, '[]'::jsonb)
          ) ORDER BY LOWER(c.name) ${orderDirection}) as category_access`
        )
      )
      .where('d.customer_id', customerId)
      .groupBy(['d.id'])
      .orderByRaw(`LOWER(d.name) ${orderDirection}`);

    let result = queryResult;
    result = SortingService.sortAlphabetically(result, 'disciplineName', orderDirection);

    result.forEach((discipline) => {
      discipline.category_access = SortingService.sortAlphabetically(
        discipline.category_access,
        'categoryName',
        orderDirection
      );

      discipline.category_access.forEach((category: ICategoryAccessDTO) => {
        category.subcategories = SortingService.sortAlphabetically(
          category.subcategories,
          'subcategoryName',
          orderDirection
        );
      });
    });

    return result;
  }

  async listDisciplinesWithAccessHierarchyForImport({
    customerId,
  }: ICustomerIdDTO): Promise<IDisciplinesAccessHierarchyDTO[]> {
    const orderDirection: OrderDirection = OrderDirection.ASC;

    const queryResult = await knexInstance('disciplines as d')
      .innerJoin('disciplines_categories_access as dca', 'dca.discipline_id', 'd.id')
      .innerJoin('categories as c', 'c.id', 'dca.category_id')
      .leftJoin(
        knexInstance
          .select('dsa.category_id', 'dsa.discipline_id')
          .select(
            knexInstance.raw(
              `jsonb_agg(jsonb_build_object(
                'subcategoryId', s.id,
                'subcategoryName', s.name
              ) ORDER BY LOWER(s.name) ${orderDirection}) AS subcategories`
            )
          )
          .from('disciplines_subcategories_access as dsa')
          .join('subcategories as s', 's.id', 'dsa.subcategory_id')
          .groupBy(['dsa.category_id', 'dsa.discipline_id'])
          .as('subquery'),
        function () {
          this.on('subquery.category_id', '=', 'c.id').on('subquery.discipline_id', '=', 'd.id');
        }
      )
      .select(
        'd.id as disciplineId',
        'd.name as disciplineName',
        knexInstance.raw(
          `jsonb_agg(jsonb_build_object(
            'categoryId', c.id,
            'categoryName', c.name,
            'subcategories', COALESCE(subquery.subcategories, '[]'::jsonb)
          ) ORDER BY LOWER(c.name) ${orderDirection}) as category_access`
        )
      )
      .where('d.customer_id', customerId)
      .groupBy(['d.id'])
      .orderByRaw(`LOWER(d.name) ${orderDirection}`);

    let result = queryResult;
    result = SortingService.sortAlphabetically(result, 'disciplineName', orderDirection);

    result.forEach((discipline) => {
      discipline.category_access = SortingService.sortAlphabetically(
        discipline.category_access,
        'categoryName',
        orderDirection
      );

      discipline.category_access.forEach((category: ICategoryAccessDTO) => {
        category.subcategories = SortingService.sortAlphabetically(
          category.subcategories,
          'subcategoryName',
          orderDirection
        );
      });
    });

    return result;
  }

  async listDisciplinesWithQuestionCount(
    data: ListDisciplinesWithQuestionCountDTO
  ): Promise<IDisciplineWithQuestionCount[]> {
    const { customerId, questionGroupId } = data;

    const { difficulty, disciplineIds, categoryIds, subcategoryIds, search } = data;

    const query = knexInstance('disciplines as d')
      .select([
        'd.id as discipline_id',
        'd.name as discipline_name',
        'c.id as category_id',
        'c.name as category_name',
        'sc.id as subcategory_id',
        'sc.name as subcategory_name',
        knexInstance.raw('COUNT(q.id) as total_questions'),
        knexInstance.raw(`COUNT(CASE WHEN q.difficulty = 'easy' THEN 1 END) as total_easy`),
        knexInstance.raw(`COUNT(CASE WHEN q.difficulty = 'medium' THEN 1 END) as total_medium`),
        knexInstance.raw(`COUNT(CASE WHEN q.difficulty = 'hard' THEN 1 END) as total_hard`),
      ])
      .innerJoin('disciplines_categories_access as dca', 'd.id', 'dca.discipline_id')
      .innerJoin('categories as c', 'c.id', 'dca.category_id')
      .innerJoin('disciplines_subcategories_access as dsa', function () {
        this.on('dsa.discipline_id', '=', 'd.id').andOn('dsa.category_id', '=', 'c.id');
      })
      .innerJoin('subcategories as sc', 'sc.id', 'dsa.subcategory_id')
      .leftJoin('questions as q', function () {
        this.on('q.discipline_id', '=', 'd.id')
          .andOn('q.category_id', '=', 'c.id')
          .andOn('q.subcategory_id', '=', 'sc.id')
          .andOn('q.status', knexInstance.raw("'published'"));
      })
      .leftJoin('questions_groups_access as qgq', 'qgq.question_id', 'q.id')
      .where('d.customer_id', customerId)
      .andWhere('qgq.question_group_id', questionGroupId)
      .whereNull('d.deleted_at')
      .whereNull('c.deleted_at')
      .whereNull('sc.deleted_at')
      .whereNull('q.deleted_at')
      .groupBy(['d.id', 'd.name', 'c.id', 'c.name', 'sc.id', 'sc.name'])
      .orderBy([
        { column: 'd.name', order: 'asc' },
        { column: 'c.name', order: 'asc' },
        { column: 'sc.name', order: 'asc' },
      ]);

    if (disciplineIds?.length) query.whereIn('d.id', disciplineIds);
    if (categoryIds?.length) query.whereIn('c.id', categoryIds);
    if (subcategoryIds?.length) query.whereIn('sc.id', subcategoryIds);
    if (difficulty?.length) query.whereIn('q.difficulty', difficulty);
    if (search) {
      query.andWhere(function () {
        this.whereILike('d.name', `%${search}%`)
          .orWhereILike('c.name', `%${search}%`)
          .orWhereILike('sc.name', `%${search}%`);
      });
    }

    const rows = await query;

    const disciplinesMap = new Map<string, IDisciplineWithQuestionCount>();

    for (const row of rows) {
      if (!disciplinesMap.has(row.discipline_id)) {
        disciplinesMap.set(row.discipline_id, {
          id: row.discipline_id,
          name: row.discipline_name,
          totalQuestions: 0,
          totalQuestionsEasy: 0,
          totalQuestionsMedium: 0,
          totalQuestionsHard: 0,
          categories: [],
        });
      }

      const discipline = disciplinesMap.get(row.discipline_id)!;
      let category = discipline.categories.find((c) => c.id === row.category_id);

      if (!category) {
        category = {
          id: row.category_id,
          name: row.category_name,
          totalQuestions: 0,
          totalQuestionsEasy: 0,
          totalQuestionsMedium: 0,
          totalQuestionsHard: 0,
          subcategories: [],
        };
        discipline.categories.push(category);
      }

      const subcategory: ISubcategoryWithQuestionCount = {
        id: row.subcategory_id,
        name: row.subcategory_name,
        totalQuestions: Number(row.total_questions),
        totalQuestionsEasy: Number(row.total_easy),
        totalQuestionsMedium: Number(row.total_medium),
        totalQuestionsHard: Number(row.total_hard),
      };

      category.subcategories.push(subcategory);

      category.totalQuestions += subcategory.totalQuestions;
      category.totalQuestionsEasy += subcategory.totalQuestionsEasy;
      category.totalQuestionsMedium += subcategory.totalQuestionsMedium;
      category.totalQuestionsHard += subcategory.totalQuestionsHard;

      discipline.totalQuestions += subcategory.totalQuestions;
      discipline.totalQuestionsEasy += subcategory.totalQuestionsEasy;
      discipline.totalQuestionsMedium += subcategory.totalQuestionsMedium;
      discipline.totalQuestionsHard += subcategory.totalQuestionsHard;
    }

    return Array.from(disciplinesMap.values());
  }
}
