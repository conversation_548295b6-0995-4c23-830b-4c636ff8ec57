import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IDisciplineCategoriesAccess } from '../../model/IDisciplineCategoryAccess';
import { IDisciplinesCategoriesAccessRepository } from '../assign/disciplinesCategoriesAccess.assign';

export class KnexDisciplinesCategoriesRepository
  extends BaseRepository<IDisciplineCategoriesAccess>
  implements IDisciplinesCategoriesAccessRepository
{
  constructor() {
    super(knexInstance, 'disciplines_categories_access');
  }

  async findByDisciplineIdAndCategoryId(data: {
    disciplineId: string;
    categoryId: string;
    customerId: string;
  }): Promise<IDisciplineCategoriesAccess | undefined> {
    const { disciplineId, categoryId, customerId } = data;

    return await knexInstance('disciplines_categories_access')
      .where({ discipline_id: disciplineId, category_id: categoryId, customer_id: customerId })
      .first();
  }

  async deleteByDisciplineIdAndCategoryId(data: {
    disciplineId: string;
    categoryId: string;
    customerId: string;
  }): Promise<void> {
    const { disciplineId, categoryId, customerId } = data;

    await knexInstance('disciplines_categories_access')
      .where({ discipline_id: disciplineId, category_id: categoryId, customer_id: customerId })
      .del();
  }

  async deleteAllByDisciplineId(disciplineId: string, customerId: string): Promise<void> {
    await knexInstance('disciplines_categories_access')
      .where({ discipline_id: disciplineId, customer_id: customerId })
      .del();
  }

  async findAllById(id: string): Promise<IDisciplineCategoriesAccess[]> {
    return await knexInstance('disciplines_categories_access').where({ category_id: id });
  }

  async deleteAll(id: string, customerId: string): Promise<object> {
    const query = await knexInstance('disciplines_categories_access')
      .where({ category_id: id, customer_id: customerId })
      .del()
      .returning('*');

    return query;
  }

  async findAllByDisciplineId(disciplineId: string): Promise<IDisciplineCategoriesAccess[]> {
    return await knexInstance('disciplines_categories_access').where({
      discipline_id: disciplineId,
    });
  }
}
