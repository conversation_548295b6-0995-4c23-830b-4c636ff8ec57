import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { ICategoryIdentifierDTO } from '../../model/DTO/ICategory.dto';
import { IDisciplineSubcategoriesAccess } from '../../model/IDisciplineSubcategoryAccess';
import { IDisciplinesSubcategoriesAccessRepository } from '../assign/disciplinesSubcategoriesAccess.assign';

export class KnexDisciplinesSubcategoriesAccessRepository
  extends BaseRepository<IDisciplineSubcategoriesAccess>
  implements IDisciplinesSubcategoriesAccessRepository
{
  constructor() {
    super(knexInstance, 'disciplines_subcategories_access');
  }

  async findByDisciplineIdAndSubcategoryId(data: {
    disciplineId: string;
    subcategoryId: string;
    customerId: string;
  }): Promise<IDisciplineSubcategoriesAccess | undefined> {
    const { disciplineId, subcategoryId, customerId } = data;
    const query = await knexInstance('disciplines_subcategories_access')
      .where({
        discipline_id: disciplineId,
        subcategory_id: subcategoryId,
        customer_id: customerId,
      })
      .first();

    return query;
  }

  async getBysubcategoryId(
    subcategoryId: string,
    customerId: string
  ): Promise<IDisciplineSubcategoriesAccess[]> {
    const query = knexInstance('disciplines_subcategories_access as ds')
      .where({
        'ds.subcategory_id': subcategoryId,
        'ds.customer_id': customerId,
      })
      .select('ds.*');

    return await query;
  }

  async deleteByDisciplineId(disciplineId: string): Promise<void> {
    await knexInstance('disciplines_subcategories').where({ discipline_id: disciplineId }).del();
  }

  async deleteBySubcategoryId(subcategoryId: string): Promise<void> {
    await knexInstance('disciplines_subcategories').where({ subcategory_id: subcategoryId }).del();
  }

  async deleteAll(subcategoryId: string, customerId: string): Promise<void> {
    await knexInstance('disciplines_subcategories_access')
      .where({ subcategory_id: subcategoryId, customer_id: customerId })
      .del();
  }

  async deleteAllByDisciplineId(disciplineId: string, customerId: string): Promise<void> {
    await knexInstance('disciplines_subcategories_access')
      .where({ discipline_id: disciplineId, customer_id: customerId })
      .del();
  }

  async findAllByDisciplineId(
    disciplineId: string,
    customerId: string
  ): Promise<IDisciplineSubcategoriesAccess[]> {
    return await knexInstance('disciplines_subcategories_access').where({
      discipline_id: disciplineId,
      customer_id: customerId,
    });
  }

  async deleteByDisciplineIdAndSubcategoryId(data: {
    disciplineId: string;
    subcategoryId: string;
    customerId: string;
  }): Promise<void> {
    const { disciplineId, subcategoryId, customerId } = data;

    await knexInstance('disciplines_subcategories_access')
      .where({
        discipline_id: disciplineId,
        subcategory_id: subcategoryId,
        customer_id: customerId,
      })
      .del();
  }

  async deleteAllByCategoryId(data: ICategoryIdentifierDTO): Promise<void> {
    await knexInstance('disciplines_subcategories_access')
      .where({ category_id: data.categoryId, customer_id: data.customerId })
      .del();
  }
}
