import { BaseRepository } from 'bq-knex-base-repository';
import { randomUUID } from 'crypto';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IDomain } from '../../model/IDomain';
import { IDomainsRepository } from '../assign/domains.assign';

export class KnexDomainsRepository extends BaseRepository<IDomain> implements IDomainsRepository {
  constructor() {
    super(knexInstance, 'domains');
  }

  async updateDomainWithTransaction(
    data: Partial<IDomain>,
    trx?: Knex.Transaction
  ): Promise<IDomain> {
    const query = trx ? trx('domains') : knexInstance('domains');
    const [domain] = await query.where({ id: data.id }).update(data).returning('*');
    return domain;
  }

  async insertDomainWithTransaction(
    data: Partial<IDomain>,
    trx?: Knex.Transaction
  ): Promise<IDomain> {
    const dataInsert = {
      ...data,
      id: randomUUID(),
      created_at: new Date(),
      updated_at: new Date(),
    };
    const query = trx ? trx('domains') : knexInstance('domains');
    const [domain] = await query.insert(dataInsert).returning('*');
    return domain;
  }

  async findByPendingValidation(): Promise<IDomain[]> {
    return knexInstance('domains').where({ status: 'pending' });
  }

  async findByValidatedNotCloudFront(): Promise<IDomain[]> {
    return knexInstance('domains')
      .where({ status: 'validated' })
      .whereNull('cloudfront_distribution_id');
  }
}
