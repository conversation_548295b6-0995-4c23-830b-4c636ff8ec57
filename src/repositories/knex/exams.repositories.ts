import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import {
  ExamsFilters,
  GetExamsFiltersParams,
  ListExamsParams,
  ListPublishedExamsPaginatedParams,
  PublishedExamsWithPaginationDTO,
} from '../../model/DTO/exams.dto';
import {
  FindByUserWithProgressParams,
  IExamWithProgressDTO,
  IGetPublishedQuestionsFromExamDTO,
  IPublishedQuestionsFromExamDTO,
} from '../../model/DTO/IExam.dto';
import { IQuestionWithAlternativesDTO, QuestionCountResult } from '../../model/DTO/IQuestion.dto';
import { ExamsStatusEnum } from '../../model/enums/examsStatus.enum';
import { QuestionStatusEnum } from '../../model/enums/questions.enum';
import { IExam } from '../../model/IExam';
import { IInstitution } from '../../model/IInstitution';
import { OrderDirection } from '../../services/sorting.service';
import { translateFormated } from '../../services/translateFormatted.service';
import { IExamsRepository } from '../assign/exams.assign';

export class KnexExamsRepository extends BaseRepository<IExam> implements IExamsRepository {
  constructor() {
    super(knexInstance, 'exams');
  }

  async listExams({
    customerId,
    search,
    orderByColumn = 'name',
    orderDirection = OrderDirection.ASC,
  }: ListExamsParams): Promise<IExam[]> {
    const baseQuery = knexInstance('exams')
      .where('customer_id', customerId)
      .whereNull('deleted_at')
      .select('*');

    if (search) {
      baseQuery.whereRaw(`${translateFormated('name')} ILIKE ${translateFormated('?')}`, [
        `%${search}%`,
      ]);
    }

    if (orderByColumn === 'name') {
      baseQuery.orderByRaw(`${translateFormated(`exams.${orderByColumn}`)} ${orderDirection}`);
    } else {
      baseQuery.orderBy(orderByColumn, orderDirection);
    }

    return await baseQuery;
  }

  async softDelete(data: { id: string; customerId: string }): Promise<IExam> {
    const { id, customerId } = data;

    const [deletedExam] = await knexInstance('exams')
      .where({ id, customer_id: customerId })
      .update({
        deleted_at: new Date(),
      })
      .returning('*');

    return deletedExam;
  }

  async listPublishedExamsPaginated({
    examIds,
    customerId,
    search,
    institution,
    year,
    page = 1,
    limit = 10,
    orderByColumn = 'year',
    orderDirection = OrderDirection.DESC,
  }: ListPublishedExamsPaginatedParams): Promise<PublishedExamsWithPaginationDTO> {
    const offset = (page - 1) * limit;

    const query = knexInstance
      .distinct('e.id')
      .select([
        'e.id',
        'e.name',
        'e.exam_time',
        'e.year',
        'e.published',
        'e.status',
        'e.created_at',
        'e.updated_at',
        'e.deleted_at',
        'i.id as institution_id',
        'i.name as institution',
        'i.acronym as acronym',
      ])
      .from('exams as e')
      .leftJoin('institutions as i', 'e.institution_id', 'i.id')
      .where('e.customer_id', customerId)
      .where('e.published', true)
      .where('e.status', ExamsStatusEnum.PUBLISHED)
      .whereNull('e.deleted_at');

    let countQuery = knexInstance('exams as e')
      .where('e.customer_id', customerId)
      .where('e.published', true)
      .where('e.status', ExamsStatusEnum.PUBLISHED)
      .whereNull('e.deleted_at');

    if (search) {
      query.where((builder) => {
        builder
          .where('e.name', 'ilike', `%${search}%`)
          .orWhere('i.name', 'ilike', `%${search}%`)
          .orWhere('i.acronym', 'ilike', `%${search}%`);
      });

      countQuery = countQuery
        .leftJoin('institutions as i', 'e.institution_id', 'i.id')
        .where((builder) => {
          builder
            .where('e.name', 'ilike', `%${search}%`)
            .orWhere('i.name', 'ilike', `%${search}%`)
            .orWhere('i.acronym', 'ilike', `%${search}%`);
        });
    }

    if (institution) {
      query.where('i.acronym', 'ilike', `%${institution}%`);

      if (!search) {
        countQuery = countQuery.leftJoin('institutions as i', 'e.institution_id', 'i.id');
      }

      countQuery.where('i.acronym', 'ilike', `%${institution}%`);
    }

    if (year) {
      query.where('e.year', year);
      countQuery.where('e.year', year);
    }

    if (examIds && examIds.length > 0) {
      query.whereIn('e.id', examIds);
      countQuery.whereIn('e.id', examIds);
    }

    if (orderByColumn === 'institution') {
      query.orderBy([
        { column: 'i.name', order: orderDirection },
        { column: 'e.id', order: 'asc' },
      ]);
    } else {
      query.orderBy([
        { column: `e.${orderByColumn}`, order: orderDirection },
        { column: 'e.id', order: 'asc' },
      ]);
    }

    query.limit(limit).offset(offset);

    const [examsResult, totalCountResult] = await Promise.all([
      query,
      countQuery.count('e.id as count').first(),
    ]);

    const totalItems = Number(totalCountResult?.count || 0);
    const totalPages = Math.max(1, Math.ceil(totalItems / limit));
    const currentPage = Math.min(Math.max(1, page), totalPages);

    const resultExamIds = examsResult.map((exam) => exam.id);

    const questionsCountMap = new Map<string, number>();

    if (resultExamIds.length > 0) {
      const questionsCountQuery = await knexInstance('questions_exams')
        .whereIn('exam_id', resultExamIds)
        .whereNull('deleted_at')
        .select('exam_id')
        .count('* as count')
        .groupBy('exam_id');

      for (const item of questionsCountQuery) {
        const typedItem = item as unknown as QuestionCountResult;
        questionsCountMap.set(typedItem.exam_id, Number(typedItem.count));
      }
    }

    return {
      exams: examsResult.map((exam) => ({
        id: exam.id,
        name: exam.name,
        institution: exam.acronym,
        institution_id: exam.institution_id,
        year: exam.year,
        published: exam.published,
        status: exam.status,
        exam_time: exam.exam_time,
        duration: exam.exam_time,
        totalQuestions: questionsCountMap.get(exam.id) || 0,
        customer_id: customerId,
        created_at: exam.created_at,
        updated_at: exam.updated_at,
        deleted_at: exam.deleted_at,
      })),
      paginationInfo: {
        currentPage,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: currentPage < totalPages,
        hasPreviousPage: currentPage > 1,
      },
    };
  }

  async getExamsFilters({ examIds, customerId }: GetExamsFiltersParams): Promise<ExamsFilters> {
    let institutionsQuery = knexInstance('institutions as i')
      .join('exams as e', 'e.institution_id', 'i.id')
      .where('e.customer_id', customerId)
      .whereNull('e.deleted_at')
      .whereNull('i.deleted_at')
      .select('i.id', 'i.name', 'i.acronym')
      .distinct();

    if (examIds.length > 0) {
      institutionsQuery = institutionsQuery.whereIn('e.id', examIds);
    }

    let yearsQuery = knexInstance('exams')
      .where('customer_id', customerId)
      .whereNull('deleted_at')
      .select('year')
      .distinct();

    if (examIds.length > 0) {
      yearsQuery = yearsQuery.whereIn('id', examIds);
    }

    const [institutions, yearsResult] = await Promise.all([institutionsQuery, yearsQuery]);

    return {
      institutions,
      years: yearsResult.map((y) => y.year as number),
    };
  }

  async findByEntities(data: Partial<IExam>): Promise<IExam[]> {
    return knexInstance('exams as e').where(data).select('*');
  }

  async findByUserWithProgress({
    userId,
    customerId,
    courseId,
  }: FindByUserWithProgressParams): Promise<IExamWithProgressDTO[]> {
    const subQuery = knexInstance
      .select('qe.*')
      .from('questions_exams as qe')
      .whereNull('qe.deleted_at')
      .distinctOn(['qe.exam_id', 'qe.question_id'])
      .orderBy([
        { column: 'qe.exam_id', order: 'asc' },
        { column: 'qe.question_id', order: 'asc' },
        { column: 'qe.created_at', order: 'desc' },
      ]);

    return knexInstance
      .with('latest_questions', subQuery)
      .select([
        'e.id',
        'e.name',
        'e.customer_id',
        'i.id as institution_id',
        'i.name as institution_name',
        'e.created_at',
        'e.updated_at',
        'ea.id as access_id',
        'ea.active',
        'ea.start_exams',
        'ea.end_exams',
        knexInstance.raw('COUNT(DISTINCT lq.question_id)::INTEGER as total_questions'),
        knexInstance.raw('COALESCE(COUNT(DISTINCT qea.id), 0)::INTEGER as answered_questions'),
        knexInstance.raw(`
          COALESCE(SUM(CASE WHEN a.correct THEN 1 ELSE 0 END), 0)::INTEGER as correct_answers
        `),
      ])
      .from('exams as e')
      .leftJoin('institutions as i', 'e.institution_id', 'i.id')
      .innerJoin('exams_access as ea', function () {
        this.on('e.id', '=', 'ea.exam_id').andOn(
          'ea.user_id',
          '=',
          knexInstance.raw('?', [userId])
        );
      })
      .innerJoin('courses_exams as ce', function () {
        this.on('e.id', '=', 'ce.exam_id').andOn(
          'ce.course_id',
          '=',
          knexInstance.raw('?', [courseId])
        );
      })
      .leftJoin('latest_questions as lq', 'e.id', 'lq.exam_id')
      .leftJoin(
        knexInstance.raw(`
          questions_exams_answered as qea
          ON lq.id = qea.question_exam_id AND qea.exam_access_id = ea.id
        `)
      )
      .leftJoin('alternatives as a', 'qea.alternative_id', 'a.id')
      .where('e.customer_id', customerId)
      .whereNull('e.deleted_at')
      .groupBy([
        'e.id',
        'e.name',
        'e.customer_id',
        'i.id',
        'i.name',
        'e.created_at',
        'e.updated_at',
        'ea.id',
        'ea.active',
        'ea.start_exams',
        'ea.end_exams',
      ]);
  }

  async findByExam(data: Partial<IExam>): Promise<IExam & IInstitution> {
    const dataFormated = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [`e.${key}`, value])
    );

    return knexInstance('exams as e')
      .leftJoin('institutions as i', 'i.id', 'e.institution_id')
      .where(dataFormated)
      .select('e.*', 'i.acronym')
      .first();
  }

  /**
   * Busca todos os exames que correspondem aos filtros fornecidos
   * @param filter - Filtros para a busca
   * @returns Lista de exames que correspondem aos filtros
   */
  async findAllBy(filter?: Partial<IExam>): Promise<IExam[]> {
    const query = knexInstance('exams');

    if (filter) {
      // Aplica os filtros à consulta
      Object.entries(filter).forEach(([key, value]) => {
        if (value !== undefined) {
          // Converte camelCase para snake_case para os nomes das colunas
          const columnName = key.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);

          if (value === null) {
            query.whereNull(columnName);
          } else {
            query.where(columnName, value);
          }
        }
      });
    }

    return await query;
  }

  async getPublishedQuestionsFromExam({
    examId,
    customerId,
  }: IGetPublishedQuestionsFromExamDTO): Promise<IPublishedQuestionsFromExamDTO> {
    const [examData] = await knexInstance('exams as e')
      .leftJoin('institutions as i', 'e.institution_id', 'i.id')
      .leftJoin('questions_exams as qe', 'e.id', 'qe.exam_id')
      .leftJoin('questions as q', 'q.id', 'qe.question_id')
      .leftJoin('disciplines as d', 'd.id', 'q.discipline_id')
      .leftJoin('categories as c', 'c.id', 'q.category_id')
      .leftJoin('subcategories as sc', 'sc.id', 'q.subcategory_id')
      .leftJoin('customers as cust', 'e.customer_id', 'cust.id')
      .select(
        'e.*',
        'i.name as institution_name',
        'i.acronym as institution_acronym',
        'cust.id as customerId',
        'cust.name as customerName',
        'cust.primary_color as customerPrimaryColor',
        'cust.secondary_color as customerSecondaryColor',
        'cust.website as customerWebsite',
        'cust.subdomain as customerSubdomain',
        knexInstance.raw(`
          COALESCE(
            json_agg(
              json_build_object(
                'id', q.id,
                'created_at', q.created_at,
                'updated_at', q.updated_at,
                'deleted_at', q.deleted_at,
                'published_at', q.published_at,
                'title', q.title,
                'description', q.description,
                'difficulty', q.difficulty,
                'image_url', q.image_url,
                'explanation_video', q.explanation_video,
                'explanation_text', q.explanation_text,
                'explanation_image', q.explanation_image,
                'published', q.published,
                'status', q.status,
                'correct_text', q.correct_text,
                'reference', q.reference,
                'institution', q.institution,
                'year', q.year,
                'question_type_id', q.question_type_id,
                'discipline_id', q.discipline_id,
                'category_id', q.category_id,
                'subcategory_id', q.subcategory_id,
                'customer_id', q.customer_id,
                'disciplineName', d.name,
                'categoryName', c.name,
                'subcategoryName', sc.name,
                'order_by', qe.order_by,
                'alternatives', (
                  SELECT json_agg(
                    jsonb_build_object(
                      'id', a.id,
                      'option', a.option,
                      'description', a.description,
                      'correct', a.correct,
                      'created_at', a.created_at,
                      'updated_at', a.updated_at
                    )
                    ORDER BY a.option ASC
                  )
                  FROM alternatives a
                  WHERE a.question_id = q.id
                )
              )
            ) FILTER (WHERE q.id IS NOT NULL),
            '[]'
          ) as questions
        `)
      )
      .where({
        'e.id': examId,
        'e.deleted_at': null,
      })
      .andWhere({
        'e.customer_id': customerId,
        'q.status': QuestionStatusEnum.Published,
        'q.deleted_at': null,
      })
      .groupBy('e.id', 'i.name', 'i.acronym', 'cust.id');

    if (!examData) {
      return {
        exam: {} as IExam,
        questions: [],
        customer: {
          id: '',
          name: '',
          primary_color: '',
          secondary_color: '',
          website: '',
          subdomain: '',
        },
      };
    }

    const { questions, ...exam } = examData;

    const {
      customerId: customerIdFromQuery,
      customerName,
      customerPrimaryColor,
      customerSecondaryColor,
      customerWebsite,
      customerSubdomain,
      ...examWithoutCustomer
    } = exam;

    const customerData = {
      id: customerIdFromQuery,
      name: customerName,
      primary_color: customerPrimaryColor,
      secondary_color: customerSecondaryColor,
      website: customerWebsite,
      subdomain: customerSubdomain,
    };

    return {
      exam: examWithoutCustomer as IExam,
      questions: questions as IQuestionWithAlternativesDTO[],
      customer: customerData,
    } as IPublishedQuestionsFromExamDTO;
  }

  async findByIds(ids: string[], customerId: string): Promise<IExam[]> {
    return knexInstance('exams')
      .select('*')
      .whereIn('id', ids)
      .where('customer_id', customerId)
      .where('deleted_at', null);
  }
}
