import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IExamAccess } from '../../model/IExamAccess';
import { IExamsAccessRepository } from '../assign/examsAccess.assign';

export class KnexExamsAccessRepository
  extends BaseRepository<IExamAccess>
  implements IExamsAccessRepository
{
  constructor() {
    super(knexInstance, 'exams_access');
  }
}
