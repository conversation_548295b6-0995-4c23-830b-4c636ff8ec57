import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IExamClass } from '../../model/IExamClass';
import { IExamsClassesRepository } from '../assign/examsClasses.assign';

export class KnexExamsClassesRepository
  extends BaseRepository<IExamClass>
  implements IExamsClassesRepository
{
  constructor() {
    super(knexInstance, 'exams_classes');
  }

  async findByClassIdAndExamIds(classId: string, examIds: string[]): Promise<IExamClass[]> {
    return knexInstance('exams_classes')
      .select('*')
      .where('class_id', classId)
      .whereIn('exam_id', examIds)
      .where('deleted_at', null);
  }

  async insertAll(data: Partial<IExamClass>[]): Promise<IExamClass[]> {
    if (data.length === 0) {
      return [];
    }

    const insertedRows = await knexInstance('exams_classes').insert(data).returning('*');

    return insertedRows;
  }

  async findByClassId(classId: string): Promise<IExamClass[]> {
    return knexInstance('exams_classes').where({ class_id: classId }).whereNull('deleted_at');
  }

  async deleteByClassIdAndExamIds(classId: string, examIds: string[]): Promise<IExamClass[]> {
    const result = await knexInstance('exams_classes')
      .where({ class_id: classId })
      .whereIn('exam_id', examIds)
      .whereNull('deleted_at')
      .del()
      .returning('*');

    return result;
  }
}
