import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IExamQuestion } from '../../model/IExamQuestion';
import { IExamsQuestionsRepository } from '../assign/examsQuestions.assign';

export class KnexExamsQuestionsRepository
  extends BaseRepository<IExamQuestion>
  implements IExamsQuestionsRepository
{
  constructor() {
    super(knexInstance, 'questions_exams');
  }

  async findByExamId(examId: string): Promise<IExamQuestion[]> {
    return knexInstance('questions_exams')
      .where({ exam_id: examId })
      .whereNull('deleted_at')
      .orderBy('order_by', 'asc');
  }

  async findByQuestionId(questionId: string): Promise<IExamQuestion[]> {
    return knexInstance('questions_exams').where({ question_id: questionId });
  }

  async insertAll(data: Partial<IExamQuestion>[]): Promise<IExamQuestion[]> {
    const result = await knexInstance('questions_exams').insert(data).returning('*');
    return result;
  }

  async deleteById(id: string): Promise<IExamQuestion> {
    const [result] = await knexInstance('questions_exams').where({ id }).del().returning('*');
    return result;
  }

  async updateBatch(updates: Partial<IExamQuestion>[]): Promise<IExamQuestion[]> {
    if (updates.length === 0) return [];

    const updatedIds = updates.map((update) => update.id);

    await knexInstance.transaction(async (trx) => {
      for (const update of updates) {
        await trx('questions_exams').where({ id: update.id }).update({
          order_by: update.order_by,
          updated_at: update.updated_at,
        });
      }
    });

    return knexInstance('questions_exams')
      .whereIn('id', updatedIds as string[])
      .orderBy('order_by', 'asc');
  }

  async softDeleteByQuestionId(questionId: string, examId?: string): Promise<string[]> {
    const params: { question_id: string; exam_id?: string } = { question_id: questionId };

    if (examId) {
      params.exam_id = examId;
    }

    const affectedRecords = await knexInstance('questions_exams')
      .where(params)
      .whereNull('deleted_at')
      .select('id', 'exam_id');

    if (affectedRecords.length === 0) return [];

    const examIds = [...new Set(affectedRecords.map((r) => r.exam_id))];

    await knexInstance('questions_exams')
      .whereIn(
        'id',
        affectedRecords.map((r) => r.id)
      )
      .update({ deleted_at: new Date() });

    return examIds;
  }

  // async reorderQuestionsForExam(examId: string): Promise<void> {
  //   const questions = await knexInstance('questions_exams')
  //     .where({ exam_id: examId })
  //     .whereNull('deleted_at')
  //     .orderBy('order_by', 'asc')
  //     .select('id');

  //   if (questions.length === 0) return;

  //   const now = new Date().toISOString();

  //   const buildCase = (field: 'order_by' | 'updated_at') =>
  //     questions
  //       .map((q, index) => {
  //         const value = field === 'order_by' ? index + 1 : `TIMESTAMPTZ '${now}'`;
  //         return `WHEN '${q.id}' THEN ${value}`;
  //       })
  //       .join('\n');

  //   const ids = questions.map((q) => `'${q.id}'`).join(',');

  //   await knexInstance.raw(`
  //   UPDATE questions_exams
  //   SET
  //     order_by = CASE id
  //       ${buildCase('order_by')}
  //     END,
  //     updated_at = CASE id
  //       ${buildCase('updated_at')}
  //     END
  //   WHERE id IN (${ids});
  // `);
  // }
}
