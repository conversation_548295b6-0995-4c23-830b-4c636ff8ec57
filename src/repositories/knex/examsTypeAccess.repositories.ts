import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IExamsTypesAccess } from '../../model/IExamsTypesAccess';
import { IExamsTypeAccessRepository } from '../assign/examsTypeAccess.assign';

export class ExamsTypeAccessRepository
  extends BaseRepository<IExamsTypesAccess>
  implements IExamsTypeAccessRepository
{
  constructor() {
    super(knexInstance, 'exams_type_access');
  }
}
