import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IExamsType } from '../../model/IExamsType';
import { IExamsTypesRepository } from '../assign/examsTypes.assign';

export class KnexExamsTypesRepository
  extends BaseRepository<IExamsType>
  implements IExamsTypesRepository
{
  constructor() {
    super(knexInstance, 'exams_types');
  }
}
