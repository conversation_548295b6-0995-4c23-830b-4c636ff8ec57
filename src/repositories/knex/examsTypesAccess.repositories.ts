import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IExamsTypesAccess } from '../../model/IExamsTypesAccess';
import { IExamsTypesAccessRepository } from '../assign/examsTypesAccess.assign';

export class KnexExamsTypesAccessRepository
  extends BaseRepository<IExamsTypesAccess>
  implements IExamsTypesAccessRepository
{
  constructor() {
    super(knexInstance, 'exams_types_access');
  }
}
