import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IFile } from '../../model/IFile';
import { IFilesRepository } from '../assign/files.assign';

export class KnexFilesRepository extends BaseRepository<IFile> implements IFilesRepository {
  constructor() {
    super(knexInstance, 'files');
  }

  async insert(data: Partial<IFile>, trx?: Knex.Transaction): Promise<IFile> {
    const query = trx ? trx('files') : knexInstance('files');
    const [file] = await query.insert(data).returning('*');
    return file;
  }

  async findByLessonId(lessonId: string): Promise<IFile[]> {
    return knexInstance('files').where({ lesson_id: lessonId });
  }

  async deleteManyByPaths(paths: string[], trx?: Knex.Transaction): Promise<void> {
    const query = trx ? trx('files') : knexInstance('files');
    await query.whereIn('url', paths).del();
  }
}
