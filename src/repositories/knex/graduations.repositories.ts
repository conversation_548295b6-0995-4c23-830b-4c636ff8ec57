import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { GraduationsWithPagination } from '../../model/DTO/request/graduations.dto';
import { IGraduation } from '../../model/IGraduation';
import { ListGraduationsParams } from '../../schema/graduations.schema';
import { translateFormated } from '../../services/translateFormatted.service';
import { IGraduationsRepository } from '../assign/graduations.assign';

export class KnexGraduationsRepository
  extends BaseRepository<IGraduation>
  implements IGraduationsRepository
{
  constructor() {
    super(knexInstance, 'graduations');
  }

  async findAllOrPaginateWithPagination(
    params: ListGraduationsParams
  ): Promise<GraduationsWithPagination> {
    const { page = 1, limit = 10, search, type, listAll } = params;

    const countQuery = knexInstance('graduations');

    if (search) {
      countQuery.whereRaw(`${translateFormated('name')} ILIKE ${translateFormated('?')}`, [
        `%${search}%`,
      ]);
    }

    if (type) {
      countQuery.where('type', type);
    }

    const [{ count }] = await countQuery.count('* as count');
    const totalItems = Number(count);

    const query = knexInstance('graduations');

    if (search) {
      query.whereRaw(`${translateFormated('name')} ILIKE ${translateFormated('?')}`, [
        `%${search}%`,
      ]);
    }

    if (type) {
      query.where('type', type);
    }

    if (!listAll) {
      const offset = (page - 1) * limit;
      query.limit(limit).offset(offset);
    }

    const graduations = await query;

    const effectiveLimit = listAll ? totalItems : limit;
    const totalPages = Math.max(1, Math.ceil(totalItems / effectiveLimit));
    const currentPage = listAll ? 1 : Math.min(Math.max(1, page), totalPages);

    return {
      graduations,
      paginationInfo: {
        currentPage,
        itemsPerPage: effectiveLimit,
        totalItems,
        totalPages,
        hasNextPage: currentPage < totalPages,
        hasPreviousPage: currentPage > 1,
      },
    };
  }
}
