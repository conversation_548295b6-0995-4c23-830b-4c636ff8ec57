import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IPaginationDTO } from '../../model/DTO/IGeneric.dto';
import { findByNameAndCustomerIdParams } from '../../model/DTO/IInstitution.dto';
import { IInstitution } from '../../model/IInstitution';
import { translateFormated } from '../../services/translateFormatted.service';
import { IInstitutionsRepository } from '../assign/institutions.assign';

export class KnexInstitutionsRepository
  extends BaseRepository<IInstitution>
  implements IInstitutionsRepository
{
  constructor() {
    super(knexInstance, 'institutions');
  }

  async findByNameAndCustomerId(
    data: findByNameAndCustomerIdParams
  ): Promise<IInstitution | undefined> {
    const { name, customerId } = data;

    const query = await knexInstance('institutions')
      .select('*')
      .whereRaw(`${translateFormated('name')} = ${translateFormated('?')}`, [name])
      .andWhere('customer_id', customerId)
      .andWhere('deleted_at', null)
      .first();

    return query;
  }

  async listAllInstitutions(customerId: string): Promise<IInstitution[]> {
    const query = await knexInstance('institutions')
      .where({ customer_id: customerId })
      .whereNull('deleted_at');

    return query;
  }

  async listInstitutionsPublicPaginated(data: {
    page: number;
    limit: number;
    search?: string;
    listAll: boolean;
    orderByColumn: string;
    orderDirection: string;
  }): Promise<{
    paginationInfo: IPaginationDTO;
    institutions: IInstitution[];
  }> {
    const { page, limit, search, listAll, orderByColumn, orderDirection } = data;

    const baseQuery = knexInstance('institutions').whereNull('customer_id').whereNull('deleted_at');

    if (search) {
      baseQuery.where((builder) => {
        builder
          .whereRaw(`${translateFormated('name')} ILIKE ${translateFormated('?')}`, [`%${search}%`])
          .orWhereRaw(`${translateFormated('acronym')} ILIKE ${translateFormated('?')}`, [
            `%${search}%`,
          ]);
      });
    }

    const countResult = await baseQuery.clone().count('id as total').first();
    const totalItems = Number((countResult as unknown as { total: string })?.total || 0);

    const totalPages = Math.ceil(totalItems / limit) || 1;

    const query = baseQuery.clone().select('*');

    if (orderByColumn === 'name') {
      query.orderByRaw(`${translateFormated(orderByColumn)} ${orderDirection}`);
    } else {
      query.orderBy(orderByColumn, orderDirection);
    }

    if (!listAll) {
      query.limit(limit).offset((page - 1) * limit);
    }

    const institutions = await query;

    const paginationInfo: IPaginationDTO = {
      currentPage: page,
      itemsPerPage: limit,
      totalItems,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };

    return {
      institutions,
      paginationInfo,
    };
  }

  async softDelete(data: { id: string; customerId: string }): Promise<IInstitution | undefined> {
    const { id, customerId } = data;

    const [institution] = await knexInstance('institutions')
      .where({ id, customer_id: customerId })
      .update({
        deleted_at: new Date(),
      })
      .returning('*');

    return institution;
  }

  async findAllBy(filter: Partial<IInstitution>): Promise<IInstitution[]> {
    const query = knexInstance('institutions');

    if (filter) {
      Object.entries(filter).forEach(([key, value]) => {
        if (value !== undefined) {
          const columnName = key.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);

          if (value === null) {
            query.whereNull(columnName);
          } else {
            query.where(columnName, value);
          }
        }
      });
    }

    if (!filter || filter.deleted_at === undefined) {
      query.whereNull('deleted_at');
    }

    return await query;
  }
}
