import { BaseRepository } from 'bq-knex-base-repository';
import { randomUUID } from 'crypto';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import { ICourseModule } from '../../model/ICourseModule';
import { ICourseModuleRepository } from '../assign/courseModules.assign';

export class KnexCourseModulesRepository
  extends BaseRepository<ICourseModule>
  implements ICourseModuleRepository
{
  constructor() {
    super(knexInstance, 'courses_modules');
  }

  async countModulesByCourseId(courseId: string): Promise<number> {
    const [count] = await knexInstance('courses_modules')
      .where({ course_id: courseId })
      .count('id as total');
    return Number(count.total);
  }

  async createModule(data: Partial<ICourseModule>): Promise<ICourseModule> {
    const now = new Date();
    const payload = {
      id: randomUUID(),
      created_at: now,
      updated_at: now,
      ...data,
    };
    const [module] = await knexInstance('courses_modules').insert(payload).returning('*');
    return module;
  }

  async updateModuleById(id: string, data: Partial<ICourseModule>): Promise<void> {
    await knexInstance('courses_modules')
      .where({ id })
      .update({
        ...data,
        updated_at: new Date(),
      });
  }

  async insertModule(data: Partial<ICourseModule>, trx?: Knex.Transaction): Promise<ICourseModule> {
    const now = new Date();
    const payload = {
      id: randomUUID(),
      created_at: now,
      updated_at: now,
      ...data,
    };
    const [module] = await (trx ?? knexInstance)('courses_modules').insert(payload).returning('*');
    return module;
  }

  async updateModule(data: Partial<ICourseModule>, trx?: Knex.Transaction): Promise<ICourseModule> {
    const [module] = await (trx ?? knexInstance)('courses_modules')
      .where({ id: data.id })
      .update(data)
      .returning('*');
    return module;
  }

  async updateModulesOrderBatch(
    modules: { id: string; order_by: number }[],
    trx?: Knex.Transaction
  ): Promise<void> {
    const db = trx ?? knexInstance;
    for (const module of modules) {
      await db('courses_modules')
        .where({ id: module.id })
        .update({ order_by: module.order_by, updated_at: new Date() });
    }
  }

  async findByCourseId(courseId: string): Promise<ICourseModule[]> {
    return knexInstance('courses_modules')
      .where({ course_id: courseId })
      .whereNull('deleted_at')
      .orderBy('order_by', 'asc');
  }

  async softDeleteModule(id: string, trx?: Knex.Transaction): Promise<ICourseModule> {
    const [module] = await (trx ?? knexInstance)('courses_modules')
      .where({ id })
      .update({ deleted_at: new Date(), updated_at: new Date() })
      .returning('*');

    return module;
  }
}
