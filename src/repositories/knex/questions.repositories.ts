import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IPaginationDTO } from '../../model/DTO/IGeneric.dto';
import {
  IFindSimilarCodesDTO,
  IlistQuestionFilterDTO,
  IListQuestionInGroupFilterDTO,
  IQuestionIdentifierDTO,
  IQuestionTitleDTO,
  IQuestionWithAlternativesDTO,
  ListQuestionsWithFiltersParams,
} from '../../model/DTO/IQuestion.dto';
import { ListQuestionsByGroupInput } from '../../model/DTO/IQuestionGroup.dto';
import { QuestionStatusEnum } from '../../model/enums/questions.enum';
import { IQuestion } from '../../model/IQuestion';
import { translateFormated } from '../../services/translateFormatted.service';
import { IQuestionsRepository } from '../assign/questions.assign';

export class KnexQuestionsRepository
  extends BaseRepository<IQuestion>
  implements IQuestionsRepository
{
  constructor() {
    super(knexInstance, 'questions');
  }

  async listQuestionsInGroup(
    data: IListQuestionInGroupFilterDTO
  ): Promise<{ items: IQuestionWithAlternativesDTO[] }> {
    const { customerId, difficulty, disciplineIds, categoryIds, subcategoryIds, questionGroupId } =
      data;

    const query = knexInstance
      .select([
        'q.*',
        'd.name AS disciplineName',
        'c.name AS categoryName',
        'sc.name AS subcategoryName',
        'qt.type AS questionTypeName',
        knexInstance.raw(`
          COALESCE((
            SELECT JSON_AGG(
              JSON_BUILD_OBJECT(
                'id', a.id,
                'correct', a.correct,
                'option', a.option,
                'description', a.description,
                'createdAt', a.created_at,
                'updatedAt', a.updated_at
              ) ORDER BY a.option ASC
            ) FROM alternatives a WHERE a.question_id = q.id
          ), '[]') AS alternatives
        `),
      ])
      .from('questions as q')
      .leftJoin('disciplines as d', 'q.discipline_id', 'd.id')
      .leftJoin('categories as c', 'q.category_id', 'c.id')
      .leftJoin('subcategories as sc', 'q.subcategory_id', 'sc.id')
      .leftJoin('questions_types as qt', 'qt.id', 'q.question_type_id')
      .leftJoin('questions_groups_access as qga', 'qga.question_id', 'q.id')
      .where('q.customer_id', customerId)
      .where({ 'qga.question_group_id': questionGroupId, 'q.status': QuestionStatusEnum.Published })
      .whereNull('q.deleted_at');

    if (difficulty && difficulty.length > 0) {
      query.whereIn('q.difficulty', difficulty);
    }

    if (disciplineIds && disciplineIds.length > 0) {
      query.whereIn('q.discipline_id', disciplineIds);
    }

    if (categoryIds && categoryIds.length > 0) {
      query.whereIn('q.category_id', categoryIds);
    }

    if (subcategoryIds && subcategoryIds.length > 0) {
      query.whereIn('q.subcategory_id', subcategoryIds);
    }

    const questions = await query;
    return { items: questions };
  }

  async findById(data: { id: string; customerId: string }): Promise<IQuestion | undefined> {
    const { id, customerId } = data;

    return await knexInstance('questions')
      .where({ id })
      .andWhere('customer_id', customerId)
      .whereNull('deleted_at')
      .first();
  }

  async deleteById(data: { id: string }): Promise<IQuestion | undefined> {
    const { id } = data;

    const deletedQuestion = await knexInstance('questions')
      .where({ id })
      .update({ deleted_at: new Date() })
      .returning('*');

    return deletedQuestion[0];
  }

  async findBySubcategoryId(params: {
    subcategoryId: string;
    customerId: string;
  }): Promise<IQuestion[] | undefined> {
    const { subcategoryId, customerId } = params;

    const questions = await knexInstance('questions as q')
      .leftJoin('subcategories as sb', 'sb.id', 'q.subcategory_id')
      .where({ 'q.subcategory_id': subcategoryId })
      .andWhere('sb.customer_id', customerId);

    return questions || [];
  }

  async findByDisciplineId(disciplineId: string, customerId: string): Promise<IQuestion[]> {
    const questions = await knexInstance('questions as q')
      .leftJoin('disciplines as d', 'd.id', 'q.discipline_id')
      .where({ 'q.discipline_id': disciplineId })
      .andWhere('d.customer_id', customerId);

    return questions || [];
  }

  async listPaginatedQuestions(data: IlistQuestionFilterDTO): Promise<{
    questionList: IQuestionWithAlternativesDTO[];
    paginationInfo: IPaginationDTO;
  }> {
    const {
      customerId,
      difficulty,
      status,
      disciplineIds,
      categoryIds,
      subcategoryIds,
      page = 1,
      limit = 10,
      search,
      orderByColumn = 'updated_at',
      orderDirection = 'desc',
      questionGroupId,
      allQuestions = 'false',
      examId,
    } = data;

    const applyFilters = (qb: Knex.QueryBuilder) => {
      if (difficulty) qb.whereIn('q.difficulty', difficulty);
      if (status) qb.whereIn('q.status', status);
      if (disciplineIds) qb.whereIn('q.discipline_id', disciplineIds);
      if (categoryIds) qb.whereIn('q.category_id', categoryIds);
      if (subcategoryIds) qb.whereIn('q.subcategory_id', subcategoryIds);

      if (questionGroupId) {
        qb.whereIn('q.id', function () {
          this.select(knexInstance.raw('DISTINCT question_id'))
            .from('questions_groups_access')
            .where({ question_group_id: questionGroupId, customer_id: customerId });
        });
      }

      if (examId) {
        qb.whereIn('q.id', function () {
          this.select(knexInstance.raw('DISTINCT question_id'))
            .from('questions_exams')
            .whereNull('deleted_at')
            .andWhere({ exam_id: examId });
        });
      }

      if (search) {
        qb.where((subQb) => {
          subQb
            .whereRaw(`${translateFormated('q.title')} ILIKE ${translateFormated('?')}`, [
              `%${search}%`,
            ])
            .orWhereRaw(`${translateFormated('q.description')} ILIKE ${translateFormated('?')}`, [
              `%${search}%`,
            ])
            .orWhereRaw(`CAST(q.year AS TEXT) ILIKE ${translateFormated('?')}`, [`%${search}%`])
            .orWhereRaw(`${translateFormated('q.institution')} ILIKE ${translateFormated('?')}`, [
              `%${search}%`,
            ]);
        });
      }
    };

    const totalQuery = knexInstance
      .countDistinct('q.id as totalQuestions')
      .from('questions as q')
      .where('q.customer_id', customerId)
      .whereNull('q.deleted_at')
      .modify(applyFilters);

    const [{ totalQuestions }] = await totalQuery;
    const totalItems = Number(totalQuestions);

    const baseQuery = knexInstance
      .select([
        'q.id',
        'q.title',
        'q.description',
        'q.year',
        'q.difficulty',
        'q.status',
        'q.discipline_id',
        'q.category_id',
        'q.subcategory_id',
        'q.institution',
        'q.question_type_id',
        'q.created_at',
        'q.updated_at',
        'd.name AS disciplineName',
        'c.name AS categoryName',
        'sc.name AS subcategoryName',
        'qt.type AS questionTypeName',
        knexInstance.raw(
          `COALESCE((
            SELECT JSON_AGG(
              JSON_BUILD_OBJECT(
                'id', a.id,
                'correct', a.correct,
                'option', a.option,
                'description', a.description,
                'createdAt', a.created_at,
                'updatedAt', a.updated_at
              ) ORDER BY a.option ASC
            ) FROM alternatives a WHERE a.question_id = q.id
          ), '[]') AS alternatives`
        ),
      ])
      .from('questions as q')
      .leftJoin('disciplines as d', 'q.discipline_id', 'd.id')
      .leftJoin('categories as c', 'q.category_id', 'c.id')
      .leftJoin('subcategories as sc', 'q.subcategory_id', 'sc.id')
      .leftJoin('questions_types as qt', 'qt.id', 'q.question_type_id')
      .where('q.customer_id', customerId)
      .whereNull('q.deleted_at')
      .modify(applyFilters);

    let query;
    if (examId) {
      const subQuery = knexInstance
        .select([
          knexInstance.raw('DISTINCT ON (q.id) q.id'),
          'q.title',
          'q.description',
          'q.year',
          'q.difficulty',
          'q.status',
          'q.discipline_id',
          'q.category_id',
          'q.subcategory_id',
          'q.institution',
          'q.question_type_id',
          'q.created_at',
          'q.updated_at',
          'q.disciplineName',
          'q.categoryName',
          'q.subcategoryName',
          'q.questionTypeName',
          'q.alternatives',
          'qe.order_by',
        ])
        .from(baseQuery.as('q'))
        .leftJoin('questions_exams as qe', function () {
          this.on('q.id', '=', 'qe.question_id')
            .andOn('qe.exam_id', '=', knexInstance.raw('?', [examId]))
            .andOnNull('qe.deleted_at')
            .andOn(
              knexInstance.raw(
                'qe.id = (SELECT id FROM questions_exams WHERE question_id = q.id AND exam_id = ? AND deleted_at IS NULL ORDER BY CAST(id AS TEXT) ASC LIMIT 1)',
                [examId]
              )
            );
        })
        .orderBy('q.id', 'asc')
        .orderBy('qe.order_by', 'asc');

      query = knexInstance
        .select([
          'sub_q.id',
          'sub_q.title',
          'sub_q.description',
          'sub_q.year',
          'sub_q.difficulty',
          'sub_q.status',
          'sub_q.discipline_id',
          'sub_q.category_id',
          'sub_q.subcategory_id',
          'sub_q.institution',
          'sub_q.question_type_id',
          'sub_q.created_at',
          'sub_q.updated_at',
          'sub_q.disciplineName',
          'sub_q.categoryName',
          'sub_q.subcategoryName',
          'sub_q.questionTypeName',
          'sub_q.alternatives',
          'sub_q.order_by',
        ])
        .from(subQuery.as('sub_q'));

      if (orderByColumn === 'difficulty') {
        query.orderByRaw(`CASE 
          WHEN sub_q.difficulty = 'easy' THEN 1
          WHEN sub_q.difficulty = 'medium' THEN 2
          WHEN sub_q.difficulty = 'hard' THEN 3
          ELSE 4
        END ${orderDirection}`);
      } else if (orderByColumn) {
        query.orderBy(`sub_q.${orderByColumn}`, orderDirection);
      }

      query.orderBy('sub_q.id', 'asc');
    } else {
      query = knexInstance
        .select([
          'q.id',
          'q.title',
          'q.description',
          'q.year',
          'q.difficulty',
          'q.status',
          'q.discipline_id',
          'q.category_id',
          'q.subcategory_id',
          'q.institution',
          'q.question_type_id',
          'q.created_at',
          'q.updated_at',
          'q.disciplineName',
          'q.categoryName',
          'q.subcategoryName',
          'q.questionTypeName',
          'q.alternatives',
        ])
        .from(baseQuery.as('q'));

      if (orderByColumn === 'difficulty') {
        query.orderByRaw(`CASE 
          WHEN q.difficulty = 'easy' THEN 1
          WHEN q.difficulty = 'medium' THEN 2
          WHEN q.difficulty = 'hard' THEN 3
          ELSE 4
        END ${orderDirection}`);
      } else if (orderByColumn) {
        query.orderBy(`q.${orderByColumn}`, orderDirection);
      }

      query.orderBy('q.id', 'asc');
    }

    if (allQuestions === 'false') {
      query.limit(limit).offset((page - 1) * limit);
    }

    const questions = await query;

    if (examId) {
      questions.sort((a, b) => a.order_by - b.order_by);
    }
    const totalPages = totalItems > 0 ? Math.ceil(totalItems / limit) : 1;

    return {
      questionList: questions,
      paginationInfo: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async findSimilarCodes(data: IFindSimilarCodesDTO): Promise<IQuestionTitleDTO[]> {
    return await knexInstance('questions')
      .select('title')
      .where('customer_id', data.customerId)
      .andWhereRaw('title LIKE ?', [`${data.baseCode}%`]);
  }

  async findQuestionFullData({
    customerId,
    questionId,
  }: IQuestionIdentifierDTO): Promise<{ question: IQuestionWithAlternativesDTO }> {
    const query = knexInstance
      .select([
        'q.*',
        'd.name AS disciplineName',
        'c.name AS categoryName',
        'sc.name AS subcategoryName',
        'qt.type AS questionTypeName',
        knexInstance.raw(`
        COALESCE((
          SELECT JSON_AGG(
            JSON_BUILD_OBJECT(
              'id', a.id,
              'correct', a.correct,
              'option', a.option,
              'description', a.description,
              'createdAt', a.created_at,
              'updatedAt', a.updated_at
            ) ORDER BY a.option ASC
          ) FROM alternatives a WHERE a.question_id = q.id
        ), '[]') AS alternatives
      `),
      ])
      .from('questions as q')
      .leftJoin('disciplines as d', 'q.discipline_id', 'd.id')
      .leftJoin('categories as c', 'q.category_id', 'c.id')
      .leftJoin('subcategories as sc', 'q.subcategory_id', 'sc.id')
      .leftJoin('questions_types as qt', 'q.question_type_id', 'qt.id')
      .where({ 'q.customer_id': customerId, 'q.id': questionId })
      .whereNull('q.deleted_at');

    const question = await query;

    return { question: question[0] };
  }

  async getQuestionsByCategoryId(data: {
    categoryId: string;
    customerId: string;
  }): Promise<IQuestion[]> {
    const { categoryId, customerId } = data;

    const query = await knexInstance('questions as q')
      .where({
        'q.category_id': categoryId,
        'q.customer_id': customerId,
      })
      .whereNull('q.deleted_at');

    return query;
  }

  async getPublishedQuestionsByGroupId(
    data: ListQuestionsByGroupInput
  ): Promise<IQuestionWithAlternativesDTO[]> {
    const { questionGroupId, customerId } = data;

    const query = knexInstance
      .select([
        'q.*',
        'd.name AS disciplineName',
        'c.name AS categoryName',
        'sc.name AS subcategoryName',
        'qt.type AS questionTypeName',
        knexInstance.raw(`
        COALESCE((
          SELECT JSON_AGG(
            JSON_BUILD_OBJECT(
              'id', a.id,
              'correct', a.correct,
              'option', a.option,
              'description', a.description,
              'createdAt', a.created_at,
              'updatedAt', a.updated_at
            ) ORDER BY a.option ASC
          ) FROM alternatives a WHERE a.question_id = q.id
        ), '[]') AS alternatives
      `),
      ])
      .from('questions as q')
      .leftJoin('disciplines as d', 'q.discipline_id', 'd.id')
      .leftJoin('categories as c', 'q.category_id', 'c.id')
      .leftJoin('subcategories as sc', 'q.subcategory_id', 'sc.id')
      .leftJoin('questions_types as qt', 'qt.id', 'q.question_type_id')
      .where('q.customer_id', customerId)
      .where('q.status', QuestionStatusEnum.Published)
      .whereNull('q.deleted_at')
      .whereIn('q.id', function () {
        this.select('question_id').from('questions_groups_access').where({
          question_group_id: questionGroupId,
          customer_id: customerId,
        });
      })
      .orderBy('q.created_at', 'asc');

    const questions = await query;

    return questions;
  }

  async listQuestionsWithFilters(filters: ListQuestionsWithFiltersParams): Promise<{
    items: IQuestionWithAlternativesDTO[];
    total: number;
  }> {
    const { customerId, difficulty, disciplineIds, categoryIds, subcategoryIds, questionGroupId } =
      filters;

    const query = knexInstance
      .select([
        'q.*',
        'd.name AS disciplineName',
        'c.name AS categoryName',
        'sc.name AS subcategoryName',
        'qt.type AS questionTypeName',
        knexInstance.raw(`
          COALESCE((
            SELECT JSON_AGG(
              JSON_BUILD_OBJECT(
                'id', a.id,
                'correct', a.correct,
                'option', a.option,
                'description', a.description,
                'createdAt', a.created_at,
                'updatedAt', a.updated_at
              ) ORDER BY a.option ASC
            ) FROM alternatives a WHERE a.question_id = q.id
          ), '[]') AS alternatives
        `),
      ])
      .from('questions as q')
      .leftJoin('disciplines as d', 'q.discipline_id', 'd.id')
      .leftJoin('categories as c', 'q.category_id', 'c.id')
      .leftJoin('subcategories as sc', 'q.subcategory_id', 'sc.id')
      .leftJoin('questions_types as qt', 'qt.id', 'q.question_type_id')
      .leftJoin('questions_groups_access as qga', 'qga.question_id', 'q.id')
      .where('q.customer_id', customerId)
      .where({ 'qga.question_group_id': questionGroupId, 'q.status': QuestionStatusEnum.Published })
      .whereNull('q.deleted_at');

    if (difficulty && difficulty.length > 0) {
      query.whereIn('q.difficulty', difficulty);
    }

    if (disciplineIds && disciplineIds.length > 0) {
      query.whereIn('q.discipline_id', disciplineIds);
    }

    if (categoryIds && categoryIds.length > 0) {
      query.whereIn('q.category_id', categoryIds);
    }

    if (subcategoryIds && subcategoryIds.length > 0) {
      query.whereIn('q.subcategory_id', subcategoryIds);
    }

    const questions = await query;
    return { items: questions, total: questions.length };
  }

  async findByIds(ids: string[], customerId: string): Promise<IQuestion[]> {
    return knexInstance('questions')
      .whereIn('id', ids)
      .andWhere('customer_id', customerId)
      .whereNull('deleted_at');
  }

  async findQuestionsWithAlternativesByIds(
    examId: string,
    userId: string,
    examAccessId: string
  ): Promise<IQuestionWithAlternativesDTO[]> {
    const query = knexInstance
      .select([
        'q.*',
        'd.name AS disciplineName',
        'c.name AS categoryName',
        'sc.name AS subcategoryName',
        'qt.type AS questionTypeName',
        'qe.order_by',
        'q.id as question_id',
        'd.id as disciplineId',
        'c.id as categoryId',
        'sc.id as subcategoryId',
        'qea.answered',
        'qea.alternative_id',
        knexInstance.raw(`
          COALESCE((
            SELECT JSON_AGG(
              JSON_BUILD_OBJECT(
                'id', a.id,
                'correct', a.correct,
                'option', a.option,
                'description', a.description,
                'createdAt', a.created_at,
                'updatedAt', a.updated_at
              ) ORDER BY a.option ASC
            ) FROM alternatives a WHERE a.question_id = q.id
          ), '[]') AS alternatives
        `),
        knexInstance.raw(`
          COALESCE(qh.saved, false) as saved
        `),
        knexInstance.raw(`
          COALESCE(qh.alternatives_line_through, '{}') as alternatives_line_through
        `),
        knexInstance.raw(`
          COALESCE(qh.highlight_description, '[]') as highlighter_description
        `),
        knexInstance.raw(`
          COALESCE(qh.highlight_explanation_text, '[]') as highlighter_explanation_text
        `),
      ])
      .from('questions as q')
      .join('questions_exams as qe', 'q.id', 'qe.question_id')
      .join('exams as e', 'e.id', 'qe.exam_id')
      .join('exams_access as ea', 'ea.exam_id', 'e.id')
      .leftJoin('questions_exams_answered as qea', function () {
        this.on('qea.exam_access_id', '=', knexInstance.raw('?', [examAccessId])).andOn(
          'qea.question_exam_id',
          '=',
          'qe.id'
        );
      })
      .leftJoin('disciplines as d', 'q.discipline_id', 'd.id')
      .leftJoin('categories as c', 'q.category_id', 'c.id')
      .leftJoin('subcategories as sc', 'q.subcategory_id', 'sc.id')
      .leftJoin('questions_types as qt', 'qt.id', 'q.question_type_id')
      .leftJoin('questions_highlights as qh', function () {
        this.on('qh.question_id', '=', 'q.id')
          .andOn('qh.user_id', '=', knexInstance.raw('?', [userId]))
          .andOn('qh.exam_id', '=', knexInstance.raw('?', [examId]))
          .andOn('qh.exam_access_id', '=', knexInstance.raw('?', [examAccessId]));
      })
      .where('qe.exam_id', examId)
      .andWhere('ea.id', examAccessId)
      .whereNull('q.deleted_at')
      .whereNull('qe.deleted_at')
      .orderBy('qe.order_by', 'asc')
      .groupBy(
        'q.id',
        'qe.id',
        'd.id',
        'd.name',
        'c.id',
        'c.name',
        'sc.id',
        'sc.name',
        'qh.saved',
        'highlighter_description',
        'highlighter_explanation_text',
        'alternatives_line_through',
        'qea.answered',
        'qea.alternative_id',
        'qt.type'
      );

    const questions = await query;

    return questions;
  }
}
