import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IQuestionExam } from '../../model/IQuestionExam';
import { IQuestionsExamsRepository } from '../assign/questionsExams.assign';

export class QuestionsExamsRepository
  extends BaseRepository<IQuestionExam>
  implements IQuestionsExamsRepository
{
  constructor() {
    super(knexInstance, 'questions_exams');
  }
}
