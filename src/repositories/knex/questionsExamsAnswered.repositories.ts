import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IQuestionExamAnswered } from '../../model/IQuestionExamAnswered';
import { IQuestionsExamsAnsweredRepository } from '../assign/questionsExamsAnswered.assign';

export class KnexQuestionsExamsAnsweredRepository
  extends BaseRepository<IQuestionExamAnswered>
  implements IQuestionsExamsAnsweredRepository
{
  constructor() {
    super(knexInstance, 'questions_exams_answered');
  }
}
