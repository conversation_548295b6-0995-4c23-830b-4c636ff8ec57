import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IQuestionGroupQueryParamsDTO } from '../../model/DTO/IQuestionGroup.dto';
import { IQuestionGroup } from '../../model/IQuestionGroup';
import { OrderDirection, SortingService, SortingType } from '../../services/sorting.service';
import { translateFormated } from '../../services/translateFormatted.service';
import { IQuestionsGroupsRepository } from '../assign/questionsGroups.assign';

export class KnexQuestionsGroupsRepository
  extends BaseRepository<IQuestionGroup>
  implements IQuestionsGroupsRepository
{
  constructor() {
    super(knexInstance, 'questions_groups');
  }

  async listSelectableQuestionGroups(
    data: IQuestionGroupQueryParamsDTO
  ): Promise<IQuestionGroup[]> {
    const query = knexInstance('questions_groups as qg')
      .leftJoin('questions_groups_access as qga', 'qg.id', 'qga.question_group_id')
      .select([
        'qg.id',
        'qg.name',
        knexInstance.raw('COALESCE(BOOL_OR(qga.question_id = ?), false) as marked', [
          data.questionId,
        ]),
      ])
      .where('qg.customer_id', data.customerId)
      .whereNull('qg.deleted_at')
      .groupBy('qg.id', 'qg.name');

    await SortingService.applySorting(
      query,
      'qg',
      'name',
      SortingType.ALPHABETICAL,
      OrderDirection.ASC
    );
    return query;
  }

  async listAllQuestionsGroups(data: {
    customerId: string;
    search?: string;
    orderByColumn?: string;
    orderDirection?: string;
  }): Promise<(IQuestionGroup & { questionsCount: number; questionPublished: number })[]> {
    const { customerId, orderByColumn = 'name', orderDirection = 'asc', search = null } = data;

    const query = knexInstance('questions_groups as qg')
      .leftJoin('questions_groups_access as qga', 'qg.id', 'qga.question_group_id')
      .select([
        'qg.id',
        'qg.name',
        knexInstance.raw('COUNT(qga.question_id) as "questionsCount"'),
        knexInstance.raw(`(
          SELECT COUNT(*)
          FROM questions q
          INNER JOIN questions_groups_access qga2 ON qga2.question_id = q.id
          WHERE qga2.question_group_id = qg.id
            AND q.status = 'published'
            AND q.deleted_at IS NULL
        ) as "questionPublished"`),
      ])
      .where('qg.customer_id', customerId)
      .whereNull('qg.deleted_at')
      .groupBy('qg.id', 'qg.name')
      .orderBy(orderByColumn, orderDirection);

    if (search) {
      query.whereRaw(`${translateFormated('qg.name')} ILIKE ${translateFormated('?')}`, [
        `%${search}%`,
      ]);
    }

    const questionsGroups = await query;

    return questionsGroups;
  }

  async deleteById(data: { id: string; customerId: string }): Promise<IQuestionGroup | undefined> {
    const { id, customerId } = data;

    const deletedQuestionGroup = await knexInstance('questions_groups')
      .where({ id, customer_id: customerId })
      .whereNull('deleted_at')
      .update({ deleted_at: new Date() })
      .returning('*');

    return deletedQuestionGroup[0];
  }

  async findById(data: { id: string; customerId: string }): Promise<IQuestionGroup> {
    const { id, customerId } = data;

    const query = await knexInstance('questions_groups')
      .where({ id, customer_id: customerId })
      .whereNull('deleted_at')
      .first();

    return query;
  }

  async findQuestionGroup(data: {
    name: string;
    customerId: string;
  }): Promise<IQuestionGroup | undefined> {
    const { name, customerId } = data;
    return await knexInstance('questions_groups')
      .whereRaw(`${translateFormated('name')} = ${translateFormated('?')}`, [name])
      .andWhere({ customer_id: customerId, deleted_at: null })
      .first();
  }
}
