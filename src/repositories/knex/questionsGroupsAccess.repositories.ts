import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IQuestionGroupAccessIdentifiersDTO } from '../../model/DTO/IQuestionGroupAccess.dto';
import { IQuestionGroup } from '../../model/IQuestionGroup';
import { IQuestionGroupAccess } from '../../model/IQuestionGroupAccess';
import { IQuestionsGroupsAccessRepository } from '../assign/questionsGroupsAccess.assign';

export class KnexQuestionsGroupsAccessRepository
  extends BaseRepository<IQuestionGroupAccess>
  implements IQuestionsGroupsAccessRepository
{
  constructor() {
    super(knexInstance, 'questions_groups_access');
  }

  async findByQuestionId(data: {
    questionId: string;
    customerId: string;
  }): Promise<IQuestionGroup[] | undefined> {
    const { questionId, customerId } = data;

    const query = await knexInstance('questions_groups_access')
      .where({
        question_id: questionId,
        customer_id: customerId,
      })
      .whereNull('deleted_at');

    return query;
  }

  async findAllAccessByGroupId(data: {
    questionGroupId: string;
    customerId: string;
  }): Promise<IQuestionGroup[]> {
    const { questionGroupId, customerId } = data;

    const query = await knexInstance('questions_groups_access').where({
      question_group_id: questionGroupId,
      customer_id: customerId,
    });

    return query;
  }

  async deleteByQuestionId(data: { questionId: string; customerId: string }): Promise<void> {
    const { questionId, customerId } = data;

    return await knexInstance('questions_groups_access')
      .where({ question_id: questionId, customer_id: customerId })
      .del();
  }

  async getQuestionGroupAccess(
    data: IQuestionGroupAccessIdentifiersDTO
  ): Promise<IQuestionGroupAccess> {
    const query = await knexInstance('questions_groups_access').where({
      question_id: data.questionId,
      customer_id: data.customerId,
      question_group_id: data.questionGroupId,
    });

    return query[0];
  }

  async removeQuestionFromGroup(data: {
    questionId: string;
    customerId: string;
    questionGroupId: string;
  }): Promise<IQuestionGroupAccess> {
    const query = await knexInstance('questions_groups_access')
      .where({
        question_id: data.questionId,
        customer_id: data.customerId,
        question_group_id: data.questionGroupId,
      })
      .del()
      .returning('*');

    return query[0];
  }
}
