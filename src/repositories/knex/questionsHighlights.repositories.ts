import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IQuestionHighlight } from '../../model/IQuestionHighlight';
import { IQuestionsHighlightsRepository } from '../assign/questionsHighlights.assign';

export class KnexQuestionsHighlightsRepository
  extends BaseRepository<IQuestionHighlight>
  implements IQuestionsHighlightsRepository
{
  constructor() {
    super(knexInstance, 'questions_highlights');
  }
}
