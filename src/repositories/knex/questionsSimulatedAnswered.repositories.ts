import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IQuestionSimulatedAnswered } from '../../model/IQuestionSimulatedAnswered';
import { IQuestionsSimulatedAnsweredRepository } from '../assign/questionsSimulatedAnswered.assign';

export class QuestionsSimulatedAnsweredRepository
  extends BaseRepository<IQuestionSimulatedAnswered>
  implements IQuestionsSimulatedAnsweredRepository
{
  constructor() {
    super(knexInstance, 'questions_simulated_answered');
  }
}
