import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import {
  IQuestionSimulatedWithAlternativesDTO,
  IQuestionSimulatedWithExtendedDataDTO,
} from '../../model/DTO/IQuestionsSimulated.dto';
import { IQuestionSimulated } from '../../model/IQuestionSimulated';
import { IQuestionsSimulatedsRepository } from '../assign/questionsSimulateds.assign';

export class KnexQuestionsSimulatedsRepository
  extends BaseRepository<IQuestionSimulated>
  implements IQuestionsSimulatedsRepository
{
  constructor() {
    super(knexInstance, 'questions_simulated');
  }

  async softDeleteByQuestionId(questionId: string): Promise<void> {
    await knexInstance('questions_simulated')
      .where({ question_id: questionId })
      .whereNull('deleted_at')
      .update({ deleted_at: new Date() });
  }

  async getAllQuestionsSimulated(
    simulatedId: string,
    userId: string
  ): Promise<{ questions: IQuestionSimulatedWithAlternativesDTO[]; totalQuestions: number }> {
    const query = knexInstance('questions as q')
      .leftJoin('questions_simulated as qs', 'qs.question_id', 'q.id')
      .leftJoin('alternatives as a', 'a.question_id', 'q.id')
      .leftJoin('simulateds_access as sa', 'sa.simulated_id', 'qs.simulated_id')
      .select(
        'q.*',
        'qs.*',
        knexInstance.raw(
          `JSON_AGG(JSON_BUILD_OBJECT('id', a.id,'option', a.option,'description', a.description,'correct', a.correct)) as alternatives`
        )
      )
      .where({
        'sa.user_id': userId,
        'sa.simulated_id': simulatedId,
      })
      .groupBy('q.id', 'qs.id');

    const result = await query;

    const questions: IQuestionSimulatedWithAlternativesDTO[] = result.map((item) => ({
      ...item,
    }));

    return { questions, totalQuestions: questions.length };
  }

  async getQuestionsWithExtendedData(
    simulatedAccessId: string,
    userId: string
  ): Promise<{ questions: IQuestionSimulatedWithExtendedDataDTO[]; totalQuestions: number }> {
    const accessRecord = await knexInstance('simulateds_access')
      .where({
        id: simulatedAccessId,
        user_id: userId,
        deleted_at: null,
      })
      .first();

    if (!accessRecord) {
      return { questions: [], totalQuestions: 0 };
    }

    const simulatedId = accessRecord.simulated_id;

    const query = knexInstance('questions as q')
      .join('questions_simulated as qs', function () {
        this.on('qs.question_id', '=', 'q.id').andOnNull('qs.deleted_at');
      })
      .leftJoin('questions_simulated_answered as qsa', function () {
        this.on('qsa.questions_simulated_id', '=', 'qs.id')
          .andOn('qsa.simulated_access', '=', knexInstance.raw('?', [simulatedAccessId]))
          .andOnNull('qs.deleted_at');
      })
      .join('simulateds as s', 's.id', 'qs.simulated_id')
      .leftJoin('simulateds_access as sa', 'sa.simulated_id', 's.id')
      .leftJoin('disciplines as d', 'd.id', 'q.discipline_id')
      .leftJoin('categories as c', 'c.id', 'q.category_id')
      .leftJoin('subcategories as sc', 'sc.id', 'q.subcategory_id')
      .leftJoin('questions_highlights as qh', function () {
        this.on('qh.question_id', '=', 'q.id')
          .andOn('qh.user_id', '=', knexInstance.raw('?', [userId]))
          .andOn('qh.simulated_id', '=', knexInstance.raw('?', [simulatedId]))
          .andOn('qh.simulated_access_id', '=', knexInstance.raw('?', [simulatedAccessId]));
      })
      .select(
        'q.*',
        'qs.*',
        'q.deleted_at',
        's.name as simulatedName',
        'd.id as disciplineId',
        'd.name as disciplineName',
        'c.id as categoryId',
        'c.name as categoryName',
        'sc.id as subcategoryId',
        'sc.name as subcategoryName',
        'qsa.answered',
        'qsa.alternative_id',

        knexInstance.raw(`(
          SELECT COALESCE(
            JSON_AGG(
              JSON_BUILD_OBJECT(
                'id', alt.id,
                'option', alt.option,
                'description', alt.description,
                'correct', alt.correct
              )
            ), '[]'::json
          )
          FROM alternatives alt
          WHERE alt.question_id = q.id
        ) as alternatives`),
        knexInstance.raw(`
          COALESCE(qh.saved, false) as saved
        `),
        knexInstance.raw(`
          COALESCE(qh.alternatives_line_through, '{}') as alternatives_line_through
        `),
        knexInstance.raw(`
          COALESCE(qh.highlight_description, '[]') as highlighter_description
        `),
        knexInstance.raw(`
          COALESCE(qh.highlight_explanation_text, '[]') as highlighter_explanation_text
        `)
      )
      .where('qs.simulated_id', simulatedId)
      .whereNull('q.deleted_at')
      .whereNull('qs.deleted_at')
      .groupBy(
        'q.id',
        'qs.id',
        's.name',
        'd.id',
        'd.name',
        'c.id',
        'c.name',
        'sc.id',
        'sc.name',
        'qh.saved',
        'highlighter_description',
        'highlighter_explanation_text',
        'alternatives_line_through',
        'qsa.answered',
        'qsa.alternative_id'
      )
      .orderBy('qs.order_by');

    const result = await query;

    const questions: IQuestionSimulatedWithExtendedDataDTO[] = result.map((item) => ({
      ...item,
    }));

    return { questions, totalQuestions: questions.length };
  }

  async findAllBy(filters: Partial<IQuestionSimulated>): Promise<IQuestionSimulated[]> {
    const query = knexInstance('questions_simulated').whereNull('deleted_at');

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        query.where(key, value);
      }
    });

    return query.select('*').orderBy('order_by');
  }

  async insertAll(
    data: Partial<IQuestionSimulated>[],
    transaction?: Knex.Transaction
  ): Promise<IQuestionSimulated[]> {
    if (data.length === 0) {
      return [];
    }

    if (transaction) {
      const insertedRows = await transaction('questions_simulated').insert(data).returning('*');
      return insertedRows;
    }

    return await knexInstance('questions_simulated').insert(data).returning('*');
  }

  async updateBatch(updates: Partial<IQuestionSimulated>[]): Promise<IQuestionSimulated[]> {
    if (updates.length > 0) {
      const ids = updates.map((u) => `'${u.id}'`).join(',');
      const orderCases = updates.map((u) => `WHEN '${u.id}' THEN ${u.order_by}`).join(' ');
      const updatedAt = new Date().toISOString();

      await knexInstance.raw(`
        UPDATE questions_simulated
        SET
          order_by = CASE id
            ${orderCases}
            ELSE order_by
          END,
          updated_at = '${updatedAt}'
        WHERE id IN (${ids});
      `);
    }

    return await knexInstance('questions_simulated')
      .whereIn('id', updates.map((u) => u.id!).filter(Boolean) as string[])
      .orderBy('order_by', 'asc');
  }

  async getAllQuestionsBySimulatedId(
    simulatedId: string
  ): Promise<IQuestionSimulatedWithAlternativesDTO[]> {
    const query = knexInstance('questions as q')
      .leftJoin('questions_simulated as qs', 'qs.question_id', 'q.id')
      .leftJoin('alternatives as a', 'a.question_id', 'q.id')
      .select(
        'q.*',
        'qs.*',
        'qs.id as question_simulated_id',
        'q.id',
        knexInstance.raw(`
          JSON_AGG(JSON_BUILD_OBJECT(
            'id', a.id,
            'option', a.option,
            'description', a.description,
            'correct', a.correct
          )) as alternatives`)
      )
      .where('qs.simulated_id', simulatedId)
      .whereNull('q.deleted_at')
      .whereNull('qs.deleted_at')
      .groupBy('q.id', 'qs.id')
      .orderBy('qs.order_by', 'asc');
    const result = await query;
    return result.map((item) => ({ ...item }));
  }
}
