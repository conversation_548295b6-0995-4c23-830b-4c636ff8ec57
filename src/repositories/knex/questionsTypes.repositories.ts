import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IQuestionType } from '../../model/IQuestionType';
import { IQuestionsTypesRepository } from '../assign/questionsTypes.assign';

export class KnexQuestionsTypesRepository
  extends BaseRepository<IQuestionType>
  implements IQuestionsTypesRepository
{
  constructor() {
    super(knexInstance, 'questions_types');
  }

  async findById(data: { questionTypeId: string }): Promise<IQuestionType | undefined> {
    const { questionTypeId } = data;

    const query = await knexInstance('questions_types')
      .where('id', questionTypeId)
      .whereNull('deleted_at')
      .first();

    return query;
  }
}
