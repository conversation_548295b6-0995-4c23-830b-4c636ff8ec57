import { BaseRepository } from 'bq-knex-base-repository';
import { randomUUID } from 'crypto';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import { TokenInfoResponseDto } from '../../model/DTO/response/user.dto';
import { IRecovery } from '../../model/IRecovery';
import { IRecoveryRepository } from '../assign/recoveries.assign';

export class KnexRecoveryRepository
  extends BaseRepository<IRecovery>
  implements IRecoveryRepository
{
  constructor() {
    super(knexInstance, 'recoveries');
  }

  async createRecovery(data: Partial<IRecovery>, trx?: Knex.Transaction): Promise<IRecovery> {
    const [recovery] = trx
      ? await trx('recoveries').insert(data).returning('*')
      : await knexInstance('recoveries').insert(data).returning('*');
    return recovery;
  }

  async createManyRecoveries(data: Partial<IRecovery>[], trx?: Knex.Transaction): Promise<void> {
    const recoveries = data.map((recovery) => ({
      ...recovery,
      id: randomUUID(),
      created_at: new Date(),
      updated_at: new Date(),
    }));

    if (!recoveries.length) return;
    if (trx) {
      await trx('recoveries').insert(recoveries);
    } else {
      await knexInstance('recoveries').insert(recoveries);
    }
  }

  async findUserByRecoveryToken(token: string): Promise<TokenInfoResponseDto | undefined> {
    const user = await knexInstance('users')
      .leftJoin('recoveries as r', 'users.id', 'r.user_id')
      .select('users.*', 'r.expires_at as expiresAt', 'r.used_at', 'r.id as recoveryId')
      .where('r.token', token)
      .first();

    return user;
  }

  async findRecoveryByUserId(userId: string): Promise<IRecovery | undefined> {
    const recovery = await knexInstance('recoveries')
      .where('user_id', userId)
      .whereNull('used_at')
      .orderBy('created_at', 'desc')
      .first();

    return recovery;
  }

  async updateRecovery(data: Partial<IRecovery>, trx?: Knex.Transaction): Promise<IRecovery> {
    const [recovery] = trx
      ? await trx('recoveries').where('id', data.id).update(data).returning('*')
      : await knexInstance('recoveries').where('id', data.id).update(data).returning('*');

    return recovery;
  }
}
