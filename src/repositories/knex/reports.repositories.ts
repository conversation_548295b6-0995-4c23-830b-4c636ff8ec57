import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IReportListDTO, IReportListParams, IReportListResult } from '../../model/DTO/IReport.dto';
import { IReport } from '../../model/IReport';
import { IReportsRepository } from '../assign/reports.assign';

export class KnexReportsRepository extends BaseRepository<IReport> implements IReportsRepository {
  constructor() {
    super(knexInstance, 'reports');
  }

  async listReports(params: IReportListParams): Promise<IReportListResult> {
    const {
      customerId,
      userId,
      questionId,
      examId,
      simulatedAccessId,
      questionGroupId,
      status,
      orderByColumn = 'created_at',
      orderDirection = 'desc',
    } = params;

    const query = knexInstance('reports as r')
      .select([
        'r.*',
        'q.title as question_title',
        'u.first_name as user_first_name',
        'u.last_name as user_last_name',
        'u.email as user_email',
      ])
      .leftJoin('questions as q', 'r.question_id', 'q.id')
      .leftJoin('users as u', 'r.user_id', 'u.id')
      .whereNull('r.deleted_at')
      .whereNull('q.deleted_at')
      .whereNull('u.deleted_at')
      .where('q.customer_id', customerId);

    if (userId) {
      query.where('r.user_id', userId);
    }

    if (questionId) {
      query.where('r.question_id', questionId);
    }

    if (examId) {
      query.whereIn('r.question_id', function () {
        this.select('question_id')
          .from('questions_exams')
          .where('exam_id', examId)
          .whereNull('deleted_at');
      });
    }

    if (simulatedAccessId) {
      query.whereIn('r.question_id', function () {
        this.select('q.id')
          .from('questions as q')
          .join('questions_simulated as qs', 'qs.question_id', 'q.id')
          .join('simulateds as s', 's.id', 'qs.simulated_id')
          .join('simulateds_access as sa', 'sa.simulated_id', 's.id')
          .where('sa.id', simulatedAccessId)
          .whereNull('qs.deleted_at')
          .whereNull('s.deleted_at')
          .whereNull('sa.deleted_at');
      });
    }

    if (questionGroupId) {
      query.whereIn('r.question_id', function () {
        this.select('question_id')
          .from('questions_groups_access')
          .where('question_group_id', questionGroupId)
          .whereNull('deleted_at');
      });
    }

    if (status && status.length > 0) {
      query.whereIn('r.status', status);
    }

    const reports = (await query.orderBy(`r.${orderByColumn}`, orderDirection)) as IReportListDTO[];

    return {
      reports,
    };
  }
}
