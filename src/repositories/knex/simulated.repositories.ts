import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import {
  IFindByUserWithProgressParams,
  ISimulatedByIdDTO,
  ISimulatedWithProgressAndStatsDTO,
  ISimulatedWithProgressDTO,
} from '../../model/DTO/ISimulated.dto';
import { SimulatedStatusEnum } from '../../model/enums/simulatedStatus.enum';
import { ISimulated } from '../../model/ISimulated';
import { IListExclusiveSimulatedsPaginatedInput } from '../../schema/simulateds.schema';
import { translateFormated } from '../../services/translateFormatted.service';
import { ISimulatedsRepository } from '../assign/simulateds.assign';

export class KnexSimulatedsRepository
  extends BaseRepository<ISimulated>
  implements ISimulatedsRepository
{
  constructor() {
    super(knexInstance, 'simulateds');
  }

  async getSimulatedById(simulatedId: string): Promise<ISimulatedByIdDTO | undefined> {
    const result = await knexInstance('simulateds as s')
      .leftJoin('simulateds_types as st', 'st.id', 's.simulated_type_id')
      .select('s.*', 'st.name as simulated_type_name')
      .where('s.id', simulatedId)
      .first();

    return result;
  }

  async findByUser(userId: string, customerId: string): Promise<ISimulatedWithProgressDTO[]> {
    const result = await knexInstance('simulateds as s')
      .join('simulateds_access as sa', 'sa.simulated_id', 's.id')
      .join('simulateds_types as st', 'st.id', 's.simulated_type_id')
      .join('users as u', 'u.id', 'sa.user_id')
      .select(
        's.id',
        's.name',
        's.active',
        's.simulated_type_id',
        'st.name as simulated_type_name',
        's.created_at',
        's.updated_at',
        'sa.start_simulated',
        'sa.end_simulated'
      )
      .where({
        'sa.user_id': userId,
        'u.customer_id': customerId,
        's.deleted_at': null,
      })
      .orderBy('s.created_at', 'desc');

    return result;
  }

  async findByUserWithProgress({
    userId,
    customerId,
    questionGroupId,
  }: IFindByUserWithProgressParams): Promise<ISimulatedWithProgressAndStatsDTO[]> {
    const query = knexInstance('simulateds_access as sa')
      .join('simulateds as s', 's.id', 'sa.simulated_id')
      .join('simulateds_types as st', 'st.id', 's.simulated_type_id')
      .join('users as u', 'u.id', 'sa.user_id')
      .select(
        's.id',
        's.name',
        'sa.active',
        's.active as active_simulated',
        's.simulated_type_id',
        'st.name as simulated_type_name',
        's.created_at',
        's.updated_at',
        'sa.id as access_id',
        'sa.start_simulated',
        'sa.end_simulated',
        knexInstance.raw(`
          (SELECT COUNT(*)::int FROM questions_simulated 
           WHERE simulated_id = s.id AND deleted_at IS NULL) as total_questions
        `),
        knexInstance.raw(`
          (SELECT COUNT(*)::int FROM questions_simulated_answered qsa
           JOIN questions_simulated qs ON qsa.questions_simulated_id = qs.id
           WHERE qs.simulated_id = s.id AND qsa.simulated_access = sa.id 
           AND qsa.answered = true) as answered_questions
        `),
        knexInstance.raw(`
          (SELECT COUNT(*)::int FROM questions_simulated_answered qsa
           JOIN questions_simulated qs ON qsa.questions_simulated_id = qs.id
           JOIN alternatives a ON qsa.alternative_id = a.id
           WHERE qs.simulated_id = s.id AND qsa.simulated_access = sa.id 
           AND qsa.answered = true AND a.correct = true) as correct_answers
        `)
      )
      .where({
        'sa.user_id': userId,
        'u.customer_id': customerId,
        'sa.deleted_at': null,
        's.deleted_at': null,
        's.question_group_id': questionGroupId,
      });

    return query.orderBy('s.created_at', 'desc');
  }

  async listExclusiveSimulatedsPaginated({
    customerId,
    search,
    // status,
    // active,
    // startDateFrom,
    // startDateTo,
    // endDateFrom,
    // endDateTo,
    // page = 1,
    // limit = 10,
  }: IListExclusiveSimulatedsPaginatedInput) {
    // const offset = (page - 1) * limit;
    const query = knexInstance('simulateds as s')
      .leftJoin('simulateds_types as st', 'st.id', 's.simulated_type_id')
      .select('s.*')
      .where('s.customer_id', customerId)
      .where('st.name', 'exclusive')
      .whereNull('s.deleted_at');

    if (search) {
      query.whereRaw(`${translateFormated('s.name')} ilike ${translateFormated('?')}`, [
        `%${search}%`,
      ]);
    }

    // const countQuery = query.clone().clearSelect().count('s.id as total');
    query.orderBy('s.created_at', 'desc');
    // .limit(limit).offset(offset);

    // const [simulateds, countResult] = await Promise.all([query, countQuery.first()]);

    // const totalItems = Number(countResult?.total || 0);
    // const totalPages = Math.max(1, Math.ceil(totalItems / limit));
    // const currentPage = Math.min(Math.max(1, page), totalPages);

    // return {
    //   simulateds,
    //   paginationInfo: {
    //     currentPage,
    //     itemsPerPage: limit,
    //     totalItems,
    //     totalPages,
    //     hasNextPage: currentPage < totalPages,
    //     hasPreviousPage: currentPage > 1,
    //   },
    // };

    const simulateds = await query;
    return { simulateds };
  }

  async findExpiredExclusiveSimulateds(): Promise<{ id: string }[]> {
    const now = new Date();

    return knexInstance('simulateds as s')
      .join('simulateds_types as st', 'st.id', 's.simulated_type_id')
      .where('st.name', 'exclusive')
      .where('s.active', true)
      .whereNotNull('s.end_date')
      .where('s.end_date', '<', now)
      .andWhere('s.status', SimulatedStatusEnum.PUBLISHED)
      .whereNull('s.deleted_at')
      .select('s.id');
  }

  async findScheduledExclusiveSimulatedsToOpen(now: Date): Promise<ISimulated[]> {
    return await knexInstance('simulateds as s')
      .join('simulateds_types as st', 'st.id', 's.simulated_type_id')
      .where('st.name', 'exclusive')
      .where('s.status', SimulatedStatusEnum.SCHEDULED)
      .where('s.active', false)
      .whereNotNull('s.start_date')
      .where('s.start_date', '<=', now)
      .whereNull('s.deleted_at')
      .select('s.*');
  }

  async updateManyStatusAndActive(
    ids: string[],
    status: string,
    active: boolean,
    transaction?: Knex.Transaction
  ): Promise<number> {
    if (!ids.length) return 0;
    const query = transaction ? transaction('simulateds') : knexInstance('simulateds');
    const result = await query.whereIn('id', ids).update({ status, active });
    return result;
  }
}
