import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IQuestionWithAlternativesDTO } from '../../model/DTO/IQuestion.dto';
import {
  IGetSimulatedTemplatesCountDTO,
  IGetSimulatedTemplatesDTO,
  IListSimulatedTemplatesDTO,
  IPublishedQuestionsFromTemplateDTO,
  ISimulatedTemplatesResult,
} from '../../model/DTO/ISimulatedTemplate.dto';
import { QuestionStatusEnum } from '../../model/enums/questions.enum';
import { ISimulatedTemplate } from '../../model/ISimulatedTemplate';
import { ISimulatedTemplateRepository } from '../assign/simulatedsTemplate.assign';

export class KnexSimulatedTemplateRepository
  extends BaseRepository<ISimulatedTemplate>
  implements ISimulatedTemplateRepository
{
  constructor() {
    super(knexInstance, 'simulated_templates');
  }

  async countByUserId({
    userId,
    customerId,
    questionGroupId,
  }: IGetSimulatedTemplatesCountDTO): Promise<number> {
    const result = await knexInstance('simulated_templates as st')
      .leftJoin('users as u', 'st.user_id', 'u.id')
      .where({
        'st.user_id': userId,
        'u.customer_id': customerId,
        'st.question_group_id': questionGroupId,
      })
      .count<{ count: string }[]>('* as count')
      .first();

    return result ? Number(result.count) : 0;
  }

  async findTemplatesWithQuestionCountByUser({
    userId,
    customerId,
    questionGroupId,
  }: IListSimulatedTemplatesDTO): Promise<ISimulatedTemplatesResult> {
    const templates = await knexInstance('simulated_templates as st')
      .join('users as u', 'st.user_id', 'u.id')
      .leftJoin('simulated_template_questions as stq', 'st.id', 'stq.simulated_template_id')
      .where({
        'st.user_id': userId,
        'u.customer_id': customerId,
        'st.question_group_id': questionGroupId,
        'st.deleted_at': null,
      })
      .groupBy('st.id')
      .orderBy('st.created_at', 'desc')
      .select('st.*', knexInstance.raw('COUNT(stq.id)::int as total_questions'));

    return {
      templates: templates as (ISimulatedTemplate & { total_questions: number })[],
    };
  }

  async deleteById(templateId: string): Promise<ISimulatedTemplate> {
    const [deletedTemplate] = await knexInstance('simulated_templates')
      .where({ id: templateId })
      .update({
        deleted_at: knexInstance.fn.now(),
        updated_at: knexInstance.fn.now(),
      })
      .returning('*');

    return deletedTemplate;
  }

  async getPublishedQuestionsFromTemplate({
    templateId,
    customerId,
  }: IGetSimulatedTemplatesDTO): Promise<IPublishedQuestionsFromTemplateDTO> {
    const [templateData] = await knexInstance('simulated_templates as st')
      .leftJoin('simulateds_types as st_type', 'st.simulated_type_id', 'st_type.id')
      .leftJoin('simulated_template_questions as stq', 'st.id', 'stq.simulated_template_id')
      .leftJoin('questions as q', 'q.id', 'stq.question_id')
      .leftJoin('disciplines as d', 'd.id', 'q.discipline_id')
      .leftJoin('categories as c', 'c.id', 'q.category_id')
      .leftJoin('subcategories as sc', 'sc.id', 'q.subcategory_id')
      .leftJoin('users as u', 'st.user_id', 'u.id')
      .leftJoin('customers as cust', 'u.customer_id', 'cust.id')
      .select(
        'st.*',
        'st_type.name as simulated_type_name',
        'cust.id as customerId',
        'cust.name as customerName',
        'cust.primary_color as customerPrimaryColor',
        'cust.secondary_color as customerSecondaryColor',
        'cust.website as customerWebsite',
        'cust.subdomain as customerSubdomain',
        knexInstance.raw(`
          COALESCE(
            json_agg(
              json_build_object(
                'id', q.id,
                'created_at', q.created_at,
                'updated_at', q.updated_at,
                'deleted_at', q.deleted_at,
                'published_at', q.published_at,
                'title', q.title,
                'description', q.description,
                'difficulty', q.difficulty,
                'image_url', q.image_url,
                'explanation_video', q.explanation_video,
                'explanation_text', q.explanation_text,
                'explanation_image', q.explanation_image,
                'published', q.published,
                'status', q.status,
                'correct_text', q.correct_text,
                'reference', q.reference,
                'institution', q.institution,
                'year', q.year,
                'question_type_id', q.question_type_id,
                'discipline_id', q.discipline_id,
                'category_id', q.category_id,
                'subcategory_id', q.subcategory_id,
                'customer_id', q.customer_id,
                'disciplineName', d.name,
                'categoryName', c.name,
                'subcategoryName', sc.name,
                'alternatives', (
                  SELECT json_agg(
                    jsonb_build_object(
                      'id', a.id,
                      'option', a.option,
                      'description', a.description,
                      'correct', a.correct,
                      'created_at', a.created_at,
                      'updated_at', a.updated_at
                    )
                    ORDER BY a.option ASC
                  )
                  FROM alternatives a
                  WHERE a.question_id = q.id
                )
              )
            ) FILTER (WHERE q.id IS NOT NULL),
            '[]'
          ) as questions
        `)
      )
      .where({
        'st.id': templateId,
        'st.deleted_at': null,
      })
      .andWhere({
        'q.customer_id': customerId,
        'q.status': QuestionStatusEnum.Published,
        'q.deleted_at': null,
      })
      .groupBy('st.id', 'st_type.name', 'cust.id');

    const { questions, ...template } = templateData;

    const {
      customerId: customerIdFromQuery,
      customerName,
      customerPrimaryColor,
      customerSecondaryColor,
      customerWebsite,
      customerSubdomain,
      ...templateWithoutCustomer
    } = template;

    const customerData = {
      id: customerIdFromQuery,
      name: customerName,
      primary_color: customerPrimaryColor,
      secondary_color: customerSecondaryColor,
      website: customerWebsite,
      subdomain: customerSubdomain,
    };

    return {
      template: templateWithoutCustomer as ISimulatedTemplate,
      questions: questions as IQuestionWithAlternativesDTO[],
      customer: customerData,
    } as IPublishedQuestionsFromTemplateDTO;
  }
}
