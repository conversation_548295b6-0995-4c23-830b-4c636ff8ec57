import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { ISimulatedTemplateQuestion } from '../../model/ISimulatedTemplateQuestion';
import { ISimulatedTemplateQuestionRepository } from '../assign/simulatedsTemplateQuestions.assign';

export class KnexSimulatedTemplateQuestionRepository
  extends BaseRepository<ISimulatedTemplateQuestion>
  implements ISimulatedTemplateQuestionRepository
{
  constructor() {
    super(knexInstance, 'simulated_template_questions');
  }

  async addQuestionsToTemplate(questions: ISimulatedTemplateQuestion[]): Promise<void> {
    await knexInstance('simulated_template_questions').insert(questions).returning('*');
  }

  async deleteByTemplateId(templateId: string): Promise<void> {
    await knexInstance('simulated_template_questions')
      .update({ deleted_at: knexInstance.fn.now() })
      .where({ simulated_template_id: templateId });
  }

  async findByTemplateId(templateId: string): Promise<ISimulatedTemplateQuestion[]> {
    return knexInstance('simulated_template_questions')
      .where({ simulated_template_id: templateId, deleted_at: null })
      .select('*');
  }
}
