import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import { ISimulatedWithProgressDTO } from '../../model/DTO/ISimulated.dto';
import { ISimulatedAccessWithSimulatedName } from '../../model/DTO/ISimulatedAccess.dto';
import { IUserSimulatedAccessDTO } from '../../model/DTO/IUserSimulatedAccess.dto';
import { ISimulatedAccess } from '../../model/ISimulatedAcces';
import { IListUserExclusiveSimulatedsQueryInput } from '../../schema/simulateds.schema';
import { ListUserExclusiveSimulatedsUserHistoryInput } from '../../use-case/simulateds/listUserExclusiveSimulateds/listUserExclusiveSimulatedsUserHistory.useCase';
import { ISimulatedsAccessRepository } from '../assign/simulatedsAccess.assign';

export class KnexSimulatedsAccessRepository
  extends BaseRepository<ISimulatedAccess>
  implements ISimulatedsAccessRepository
{
  constructor() {
    super(knexInstance, 'simulateds_access');
  }

  getSimulatedAccessWithSimulatedName(
    data: Partial<ISimulatedAccess>
  ): Promise<ISimulatedAccessWithSimulatedName | undefined> {
    const whereClause: Record<string, unknown> = {};

    Object.entries(data).forEach(([key, value]) => {
      whereClause[`sa.${key}`] = value;
    });

    return knexInstance('simulateds_access as sa')
      .leftJoin('simulateds as s', 's.id', 'sa.simulated_id')
      .leftJoin('simulateds_types as st', 'st.id', 's.simulated_type_id')
      .select(
        'sa.*',
        's.name as name',
        'st.name as type',
        's.question_group_id',
        's.time_limit as time_limit_simulated',
        's.start_date',
        's.end_date'
      )
      .where(whereClause)
      .first();
  }

  async insertAll(
    data: Partial<ISimulatedAccess>[],
    transaction?: Knex.Transaction
  ): Promise<ISimulatedAccess[]> {
    if (data.length === 0) {
      return [];
    }

    if (transaction) {
      const insertedRows = await transaction('simulateds_access').insert(data).returning('*');
      return insertedRows;
    }

    return await knexInstance('simulateds_access').insert(data).returning('*');
  }

  async deleteByUserIdsAndSimulatedId(
    userIds: string[],
    simulatedId: string,
    transaction?: Knex.Transaction
  ): Promise<number> {
    if (userIds.length === 0) {
      return 0;
    }

    if (transaction) {
      const deletedCount = await transaction('simulateds_access')
        .whereIn('user_id', userIds)
        .andWhere('simulated_id', simulatedId)
        .del();

      return deletedCount;
    }

    return await knexInstance('simulateds_access')
      .whereIn('user_id', userIds)
      .andWhere('simulated_id', simulatedId)
      .del();
  }

  async findBy(data: Partial<ISimulatedAccess>): Promise<ISimulatedAccess[]> {
    return knexInstance('simulateds_access').where(data).whereNull('deleted_at');
  }

  async deleteAllBySimulatedId(
    simulatedId: string,
    transaction?: Knex.Transaction
  ): Promise<number> {
    if (transaction) {
      return await transaction('simulateds_access').where({ simulated_id: simulatedId }).del();
    }
    return await knexInstance('simulateds_access').where({ simulated_id: simulatedId }).del();
  }

  async findUsersBySimulatedId(simulatedId: string): Promise<IUserSimulatedAccessDTO[]> {
    return knexInstance('simulateds_access as sa')
      .select('sa.id as simulated_access_id', 'sa.user_id as id', 'sa.class_id')
      .where('sa.simulated_id', simulatedId);
  }

  async findExclusiveSimulatedsByUserId({
    userId,
    customerId,
    courseId,
    isClosed,
  }: IListUserExclusiveSimulatedsQueryInput): Promise<ISimulatedWithProgressDTO[]> {
    const query = knexInstance('simulateds_access as sa')
      .join('simulateds as s', 's.id', 'sa.simulated_id')
      .join('simulateds_types as st', 'st.id', 's.simulated_type_id')
      .join('users as u', 'u.id', 'sa.user_id')
      .leftJoin('classes as c', 'sa.class_id', 'c.id')
      .select(
        's.id',
        's.name',
        's.active',
        'sa.active as access_active',
        's.simulated_type_id',
        'st.name as simulated_type_name',
        'sa.id as access_id',
        'sa.start_simulated',
        'sa.end_simulated',
        's.status',
        's.time_limit',
        's.start_date',
        's.end_date',
        's.customer_id',
        knexInstance.raw(
          '(SELECT COUNT(*) FROM questions_simulated qs WHERE qs.simulated_id = s.id AND qs.deleted_at IS NULL) as total_questions'
        )
      )
      .where({
        'sa.user_id': userId,
        'u.customer_id': customerId,
        's.deleted_at': null,
      })
      .where('st.name', 'exclusive')
      .orderBy('s.created_at', 'desc');

    if (courseId) {
      query.where('c.course_id', courseId);
    }

    if (isClosed) {
      query.whereIn('s.status', ['scheduled', 'published', 'closed']);
    } else {
      query.whereIn('s.status', ['scheduled', 'published']);
    }

    return query;
  }

  async findExclusiveSimulatedsAccessHistoryByUserId({
    userId,
    customerId,
    courseId,
  }: ListUserExclusiveSimulatedsUserHistoryInput): Promise<ISimulatedWithProgressDTO[]> {
    const query = knexInstance('simulateds_access as sa')
      .join('simulateds as s', 's.id', 'sa.simulated_id')
      .join('simulateds_types as st', 'st.id', 's.simulated_type_id')
      .join('users as u', 'u.id', 'sa.user_id')
      .leftJoin('classes as c', 'sa.class_id', 'c.id')
      .select(
        'sa.*',
        's.name',
        's.simulated_type_id',
        'st.name as simulated_type_name',
        knexInstance.raw(
          '(SELECT COUNT(*) FROM questions_simulated_answered qsa WHERE qsa.simulated_access = sa.id AND qsa.deleted_at IS NULL) as answered_questions'
        ),
        knexInstance.raw(
          '(SELECT COUNT(*) FROM questions_simulated_answered qsa JOIN alternatives a ON a.id = qsa.alternative_id WHERE qsa.simulated_access = sa.id AND a.correct = true AND qsa.deleted_at IS NULL AND a.deleted_at IS NULL) as correct_answers'
        ),
        knexInstance.raw(
          '(SELECT COUNT(*) FROM questions_simulated qs WHERE qs.simulated_id = s.id AND qs.deleted_at IS NULL) as total_questions'
        )
      )
      .where({
        'sa.user_id': userId,
        'u.customer_id': customerId,
        's.deleted_at': null,
        'sa.deleted_at': null,
      })
      .where('st.name', 'exclusive')
      .orderBy('sa.created_at', 'desc');

    if (courseId) {
      query.where('c.course_id', courseId);
    }

    return query;
  }

  async updateTimeLimitBySimulatedId(simulatedId: string, timeLimit: number): Promise<number> {
    return knexInstance('simulateds_access')
      .where({ simulated_id: simulatedId })
      .update({ time_limit: timeLimit });
  }

  async getExclusiveSimulatedStats(simulatedId: string) {
    const result = await knexInstance('simulateds_access as sa')
      .join('simulateds as s', 's.id', 'sa.simulated_id')
      .join('simulateds_types as st', 'st.id', 's.simulated_type_id')
      .where('sa.simulated_id', simulatedId)
      .whereNotNull('sa.start_simulated')
      .whereNotNull('sa.end_simulated')
      .where('st.name', 'exclusive')
      .whereNull('sa.deleted_at')
      .whereNull('s.deleted_at')
      .select(
        knexInstance.raw('COUNT(sa.id) as total_finished'),
        knexInstance.raw('AVG(sa.total_seconds) as avg_time_seconds'),
        knexInstance.raw(`AVG(
          CASE 
            WHEN qs_count.total_questions > 0 THEN (qs_count.correct_answers * 100.0 / qs_count.total_questions)
            ELSE 0
          END
        ) as avg_score_percentage`)
      )
      .leftJoin(
        knexInstance
          .select(
            'sa_inner.id as access_id',
            knexInstance.raw('COUNT(qs.id) as total_questions'),
            knexInstance.raw('SUM(CASE WHEN a.correct = true THEN 1 ELSE 0 END) as correct_answers')
          )
          .from('simulateds_access as sa_inner')
          .leftJoin('questions_simulated_answered as qsa', 'qsa.simulated_access', 'sa_inner.id')
          .leftJoin('alternatives as a', 'a.id', 'qsa.alternative_id')
          .leftJoin('questions_simulated as qs', 'qs.id', 'qsa.questions_simulated_id')
          .where('sa_inner.simulated_id', simulatedId)
          .whereNotNull('sa_inner.start_simulated')
          .whereNotNull('sa_inner.end_simulated')
          .whereNull('sa_inner.deleted_at')
          .groupBy('sa_inner.id')
          .as('qs_count'),
        'sa.id',
        'qs_count.access_id'
      )
      .first();

    return {
      totalFinished: Number(result?.total_finished ?? 0),
      avgScorePercentage: result?.avg_score_percentage ? Number(result.avg_score_percentage) : 0,
      avgTimeSeconds: result?.avg_time_seconds ? Number(result.avg_time_seconds) : 0,
    };
  }
}
