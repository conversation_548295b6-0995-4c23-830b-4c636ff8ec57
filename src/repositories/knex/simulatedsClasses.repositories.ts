import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import { ISimulatedClass } from '../../model/ISimulatedClass';
import { ISimulatedsClassesRepository } from '../assign/simulatedsClasses.assign';

export class KnexSimulatedsClassesRepository
  extends BaseRepository<ISimulatedClass>
  implements ISimulatedsClassesRepository
{
  constructor() {
    super(knexInstance, 'simulateds_classes');
  }

  async findByClassIdAndSimulatedIds(
    classId: string,
    simulatedIds: string[]
  ): Promise<ISimulatedClass[]> {
    return knexInstance('simulateds_classes')
      .select('*')
      .where('class_id', classId)
      .whereIn('simulated_id', simulatedIds)
      .where('deleted_at', null);
  }

  async insertAll(
    data: Partial<ISimulatedClass>[],
    transaction?: Knex.Transaction
  ): Promise<ISimulatedClass[]> {
    if (data.length === 0) {
      return [];
    }

    if (transaction) {
      const insertedRows = await transaction('simulateds_classes').insert(data).returning('*');
      return insertedRows;
    }

    return await knexInstance('simulateds_classes').insert(data).returning('*');
  }

  async findByClassId(classId: string): Promise<ISimulatedClass[]> {
    return knexInstance('simulateds_classes').where({ class_id: classId }).whereNull('deleted_at');
  }

  async findBy(data: Partial<ISimulatedClass>): Promise<ISimulatedClass[]> {
    return knexInstance('simulateds_classes').where(data).whereNull('deleted_at');
  }

  async deleteBySimulatedIdAndClassIds(
    simulatedId: string,
    classIds: string[],
    transaction?: Knex.Transaction
  ): Promise<ISimulatedClass[]> {
    if (classIds.length === 0) {
      return [];
    }

    if (transaction) {
      const result = await transaction('simulateds_classes')
        .where({ simulated_id: simulatedId })
        .whereIn('class_id', classIds)
        .del()
        .returning('*');

      return result;
    }

    return await knexInstance('simulateds_classes')
      .where({ simulated_id: simulatedId })
      .whereIn('class_id', classIds)
      .del()
      .returning('*');
  }

  async deleteAllBySimulatedId(
    simulatedId: string,
    transaction?: Knex.Transaction
  ): Promise<number> {
    if (transaction) {
      return await transaction('simulateds_classes').where({ simulated_id: simulatedId }).del();
    }
    return await knexInstance('simulateds_classes').where({ simulated_id: simulatedId }).del();
  }
}
