import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { ISimulatedType } from '../../model/ISimulateType';
import { ISimulatedTypesRepository } from '../assign/simulatedTypes.assign';

export class KnexSimulatedTypesRepository
  extends BaseRepository<ISimulatedType>
  implements ISimulatedTypesRepository
{
  constructor() {
    super(knexInstance, 'simulateds_types');
  }
}
