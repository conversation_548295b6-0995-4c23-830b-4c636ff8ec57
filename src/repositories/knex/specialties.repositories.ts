import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { SpecialtiesWithPagination } from '../../model/DTO/request/specialties.dto';
import { ISpecialty } from '../../model/ISpecialty';
import { ListSpecialtiesParams } from '../../schema/specialties.schema';
import { translateFormated } from '../../services/translateFormatted.service';
import { ISpecialtiesRepository } from '../assign/specialties.assign';

export class KnexSpecialtiesRepository
  extends BaseRepository<ISpecialty>
  implements ISpecialtiesRepository
{
  constructor() {
    super(knexInstance, 'specialties');
  }

  async findAllOrPaginateWithPagination(
    params: ListSpecialtiesParams
  ): Promise<SpecialtiesWithPagination> {
    const { page = 1, limit = 10, search, listAll } = params;

    const countQuery = knexInstance('specialties');

    if (search) {
      countQuery.whereRaw(`${translateFormated('name')} ILIKE ${translateFormated('?')}`, [
        `%${search}%`,
      ]);
    }

    const [{ count }] = await countQuery.count('* as count');
    const totalItems = Number(count);

    const query = knexInstance('specialties');

    if (search) {
      query.whereRaw(`${translateFormated('name')} ILIKE ${translateFormated('?')}`, [
        `%${search}%`,
      ]);
    }

    if (!listAll) {
      const offset = (page - 1) * limit;
      query.limit(limit).offset(offset);
    }

    const specialties = await query;

    const effectiveLimit = listAll ? totalItems : limit;
    const totalPages = Math.max(1, Math.ceil(totalItems / effectiveLimit));
    const currentPage = listAll ? 1 : Math.min(Math.max(1, page), totalPages);

    return {
      specialties,
      paginationInfo: {
        currentPage,
        itemsPerPage: effectiveLimit,
        totalItems,
        totalPages,
        hasNextPage: currentPage < totalPages,
        hasPreviousPage: currentPage > 1,
      },
    };
  }
}
