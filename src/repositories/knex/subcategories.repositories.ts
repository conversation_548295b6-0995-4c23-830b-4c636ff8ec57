import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { ICategoryIdentifierDTO } from '../../model/DTO/ICategory.dto';
import { ISubCategory } from '../../model/ISubcategory';
import { translateFormated } from '../../services/translateFormatted.service';
import { ISubCategoriesRepository } from '../assign/subcategories.assign';

export class KnexSubcategoriesRepository
  extends BaseRepository<ISubCategory>
  implements ISubCategoriesRepository
{
  constructor() {
    super(knexInstance, 'subcategories');
  }

  async findByName(data: { name: string; customerId: string }): Promise<ISubCategory | undefined> {
    const { name, customerId } = data;

    const subcategory = await knexInstance('subcategories')
      .whereRaw(`${translateFormated('name')} = ${translateFormated('?')}`, [name])
      .andWhere('customer_id', customerId)
      .whereNull('deleted_at')
      .first();

    return subcategory;
  }

  async findAllByCustomerId(customerId: string, categoryId: string): Promise<ISubCategory[]> {
    const query = await knexInstance('subcategories')
      .where({
        customer_id: customerId,
        category_id: categoryId,
      })
      .whereNull('deleted_at');

    return query;
  }

  async getSubcategoryCustomer(data: {
    name: string;
    categoryId: string;
    customerId: string;
  }): Promise<ISubCategory | undefined> {
    const { name, categoryId, customerId } = data;
    return knexInstance('subcategories as sc')
      .whereRaw(`${translateFormated('sc.name')} = ${translateFormated('?')}`, [name])
      .andWhere('sc.category_id', categoryId)
      .andWhere('sc.customer_id', customerId)
      .andWhere('sc.deleted_at', null)
      .select('sc.*')
      .first();
  }

  async deleteById(id: string): Promise<object> {
    const deletedSubcategory = await knexInstance('subcategories')
      .where({ id })
      .update({ deleted_at: new Date() })
      .returning('*');

    return deletedSubcategory;
  }

  async deleteAllByCategoryId(data: ICategoryIdentifierDTO): Promise<object> {
    const deletedSubcategories = await knexInstance('subcategories')
      .where({ category_id: data.categoryId, customer_id: data.customerId })
      .update({ deleted_at: new Date() })
      .returning('*');

    return deletedSubcategories;
  }
}
