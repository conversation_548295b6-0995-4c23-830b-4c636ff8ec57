import { BaseRepository } from 'bq-knex-base-repository';
import { randomUUID } from 'crypto';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import { ITerm } from '../../model/ITerm';
import { FilterListTermsInput } from '../../schema/terms.schema';
import { ITermsRepository } from '../assign/terms.assign';

export class KnexTermsRepository extends BaseRepository<ITerm> implements ITermsRepository {
  constructor() {
    super(knexInstance, 'terms');
  }

  async createTerm(data: Partial<ITerm>, trx?: Knex.Transaction): Promise<ITerm | undefined> {
    const query = trx ? trx('terms') : knexInstance('terms');

    const dataToInsert = {
      ...data,
      id: randomUUID(),
      created_at: new Date(),
      updated_at: new Date(),
    };

    const [term] = await query.insert(dataToInsert).returning('*');
    return term;
  }

  async updateTerm(data: Partial<ITerm>, trx?: Knex.Transaction): Promise<ITerm | undefined> {
    const query = trx ? trx('terms') : knexInstance('terms');
    const [term] = await query.where('id', data.id).update(data).returning('*');
    return term;
  }

  async listAllTerms(
    customerId: string,
    { active, orderByColumn = 'created_at', orderByDirection = 'desc' }: FilterListTermsInput
  ): Promise<ITerm[]> {
    const query = knexInstance('terms').select('*').where('customer_id', customerId);

    if (active) {
      query.where('active', active);
    }

    query.orderBy(orderByColumn, orderByDirection);

    return query;
  }
}
