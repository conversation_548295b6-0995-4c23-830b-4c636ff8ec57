import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import { ITermsAccepted } from '../../model/ITermsAccepted';
import { ITermsAcceptedRepository } from '../assign/termsAccepted.assign';

export class KnexTermsAcceptedRepository
  extends BaseRepository<ITermsAccepted>
  implements ITermsAcceptedRepository
{
  constructor() {
    super(knexInstance, 'terms_accepted');
  }

  async createTermsAccept(
    data: Partial<ITermsAccepted>,
    trx?: Knex.Transaction
  ): Promise<ITermsAccepted> {
    const [termsAccepted] = trx
      ? await trx('terms_accepted').insert(data).returning('*')
      : await knexInstance('terms_accepted').insert(data).returning('*');
    return termsAccepted;
  }

  async findOneBy(data: Partial<ITermsAccepted>): Promise<ITermsAccepted | undefined> {
    return knexInstance('terms_accepted').where(data).first();
  }

  async updateTermsAccept(
    data: Partial<ITermsAccepted>,
    trx?: Knex.Transaction
  ): Promise<ITermsAccepted> {
    const [termsAccepted] = trx
      ? await trx('terms_accepted').where('id', data.id).update(data).returning('*')
      : await knexInstance('terms_accepted').where('id', data.id).update(data).returning('*');
    return termsAccepted;
  }

  async findByUserIdAndTermsId(
    userId: string,
    termsId: string
  ): Promise<ITermsAccepted | undefined> {
    return knexInstance('terms_accepted')
      .where('user_id', userId)
      .where('terms_id', termsId)
      .first();
  }
}
