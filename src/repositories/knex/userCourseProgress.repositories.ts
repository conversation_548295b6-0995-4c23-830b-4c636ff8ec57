import { BaseRepository } from 'bq-knex-base-repository';
import { randomUUID } from 'crypto';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import { ProgressStatus } from '../../model/enums/progressStatus.enum';
import { IUserCourseProgress } from '../../model/IUserCourseProgress';
import { IUserCourseProgressRepository } from '../assign/userCourseProgress.assign';

export class KnexUserCourseProgressRepository
  extends BaseRepository<IUserCourseProgress>
  implements IUserCourseProgressRepository
{
  constructor() {
    super(knexInstance, 'users_courses_progress');
  }

  async insertCourseProgress(
    trx: Knex.Transaction,
    userId: string,
    courseId: string,
    status: ProgressStatus
  ): Promise<IUserCourseProgress | undefined> {
    const updateData: IUserCourseProgress = {
      id: randomUUID(),
      user_id: userId,
      course_id: courseId,
      status,
      user_course_progress_status: status,
      last_accessed_at: new Date(),
      started_at: new Date(),
      updated_at: new Date(),
      created_at: new Date(),
      deleted_at: null,
    };

    if (status === ProgressStatus.COMPLETED) {
      updateData.completed_at = new Date();
    }

    const [courseProgress] = await trx('users_courses_progress').insert(updateData).returning('*');

    return courseProgress;
  }

  async updateCourseProgress(
    trx: Knex.Transaction,
    userId: string,
    courseId: string,
    status: ProgressStatus
  ): Promise<IUserCourseProgress | undefined> {
    const updateData: Partial<IUserCourseProgress> = {
      id: randomUUID(),
      user_id: userId,
      course_id: courseId,
      status,
      user_course_progress_status: status,
      last_accessed_at: new Date(),
      updated_at: new Date(),
      created_at: new Date(),
      deleted_at: null,
    };

    if (status === ProgressStatus.COMPLETED) {
      updateData.completed_at = new Date();
    }

    const [courseProgress] = await trx('users_courses_progress')
      .where({ user_id: userId, course_id: courseId })
      .update(updateData)
      .returning('*');

    return courseProgress;
  }

  async getCourseProgress(
    userId: string,
    courseId: string
  ): Promise<IUserCourseProgress | undefined> {
    return knexInstance('users_courses_progress')
      .where({ user_id: userId, course_id: courseId })
      .first();
  }
}
