import { BaseRepository } from 'bq-knex-base-repository';
import { randomUUID } from 'crypto';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import { ILessonResponse } from '../../model/DTO/response/lesson.dto';
import { ILessonProgress, ILessonWithProgress } from '../../model/DTO/response/progress.dto';
import { ProgressStatus } from '../../model/enums/progressStatus.enum';
import { IUserLessonProgress } from '../../model/IUserLessonProgress';
import { IUserLessonsProgressRepository } from '../assign/userLessonsProgress.assign';

export class KnexUserLessonsProgressRepository
  extends BaseRepository<IUserLessonProgress>
  implements IUserLessonsProgressRepository
{
  constructor() {
    super(knexInstance, 'users_lessons_progress');
  }

  async insertLessonProgress(
    trx: Knex.Transaction,
    userId: string,
    lessonId: string,
    progress: number,
    status: ProgressStatus
  ): Promise<IUserLessonProgress | undefined> {
    const now = knexInstance.fn.now();
    const updateData: Record<string, unknown> = {
      id: randomUUID(),
      user_id: userId,
      lesson_id: lessonId,
      current_position: progress.toString(),
      status,
      user_lesson_progress_status: status,
      started_at: now,
      last_accessed_at: now,
      created_at: now,
      updated_at: now,
    };

    if (status === ProgressStatus.COMPLETED) {
      updateData.completed_at = now;
    }

    const [lessonProgress] = await trx('users_lessons_progress').insert(updateData).returning('*');

    return lessonProgress;
  }

  async updateLessonProgress(
    trx: Knex.Transaction,
    userId: string,
    lessonId: string,
    progress: number,
    status: ProgressStatus
  ): Promise<IUserLessonProgress | undefined> {
    const updateData: Record<string, unknown> = {
      current_position: progress.toString(),
      status,
      user_lesson_progress_status: status,
      last_accessed_at: knexInstance.fn.now(),
      updated_at: knexInstance.fn.now(),
    };

    if (status === ProgressStatus.COMPLETED) {
      updateData.completed_at = knexInstance.fn.now();
    }

    const [lessonProgress] = await trx('users_lessons_progress')
      .where({ user_id: userId, lesson_id: lessonId })
      .update(updateData)
      .returning('*');

    return lessonProgress;
  }

  async getLessonProgress(
    userId: string,
    lessonId: string
  ): Promise<IUserLessonProgress | undefined> {
    return knexInstance('users_lessons_progress')
      .where({ user_id: userId, lesson_id: lessonId })
      .first();
  }

  async getLessonWithProgress(userId: string, lessonId: string): Promise<ILessonWithProgress> {
    const result = await knexInstance('courses_lessons as cl')
      .leftJoin('files as f', 'f.lesson_id', 'cl.id')
      .leftJoin('users_lessons_progress as ulp', function () {
        this.on('ulp.lesson_id', '=', 'cl.id').andOn(
          'ulp.user_id',
          '=',
          knexInstance.raw('?', [userId])
        );
      })
      .select(
        'cl.id',
        'cl.created_at as createdAt',
        'cl.updated_at as updatedAt',
        'cl.deleted_at as deletedAt',
        'cl.title',
        'cl.lesson_url as lessonUrl',
        'cl.duration',
        'cl.description_lessons as descriptionLessons',
        'cl.module_id as moduleId',
        'cl.question_group_id as questionGroupId',
        'cl.order_by as orderBy',
        'cl.published as published',
        'cl.lesson_type as type',
        'ulp.current_position',
        'ulp.status',
        knexInstance.raw(`
          COALESCE(
            json_agg(
              jsonb_build_object(
                'id', f.id,
                'url', f.url,
                'file_name', f.file_name,
                'file_type', f.file_type,
                'file_size', f.file_size,
                'created_at', f.created_at,
                'updated_at', f.updated_at
              )
            ) FILTER (WHERE f.id IS NOT NULL), '[]'
          ) AS files
        `)
      )
      .where({ 'cl.id': lessonId })
      .whereNull('cl.deleted_at')
      .groupBy('cl.id', 'ulp.current_position', 'ulp.status')
      .first();

    if (!result) {
      return {
        lesson: undefined,
        currentProgress: undefined,
        progressPercentage: 0,
      };
    }

    const lesson: ILessonResponse = {
      id: result.id,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
      deletedAt: result.deletedAt,
      title: result.title,
      lessonUrl: result.lessonUrl,
      duration: Number(result.duration) ?? null,
      descriptionLessons: result.descriptionLessons,
      moduleId: result.moduleId,
      questionGroupId: result.questionGroupId,
      orderBy: result.orderBy,
      published: result.published,
      type: result.type,
    };

    const currentProgress: ILessonProgress | undefined = result.current_position
      ? {
          current_position: Number(result.current_position),
          status:
            result.status === ProgressStatus.COMPLETED
              ? ProgressStatus.COMPLETED
              : ProgressStatus.IN_PROGRESS,
        }
      : undefined;

    return {
      lesson,
      currentProgress,
      progressPercentage: 0,
    };
  }
}
