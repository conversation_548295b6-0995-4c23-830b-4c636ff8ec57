import { BaseRepository } from 'bq-knex-base-repository';
import { randomUUID } from 'crypto';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IProgressHierarchyData } from '../../model/DTO/response/progress.dto';
import { ProgressStatus } from '../../model/enums/progressStatus.enum';
import { IUserModuleProgress } from '../../model/IUserModuleProgress';
import { IUserModulesProgressRepository } from '../assign/userModulesProgress.assign';

export class KnexUserModulesProgressRepository
  extends BaseRepository<IUserModuleProgress>
  implements IUserModulesProgressRepository
{
  constructor() {
    super(knexInstance, 'users_modules_progress');
  }

  async insertModuleProgress(
    data: Partial<IUserModuleProgress>,
    trx: Knex.Transaction
  ): Promise<IUserModuleProgress | undefined> {
    data.id = randomUUID();
    data.created_at = new Date();
    data.deleted_at = null;
    data.updated_at = new Date();
    data.user_module_progress_status = data.status;
    data.started_at = new Date();
    data.last_accessed_at = new Date();

    const [moduleProgress] = await trx('users_modules_progress').insert(data).returning('*');

    return moduleProgress;
  }

  async updateModuleProgress(
    trx: Knex.Transaction,
    userId: string,
    moduleId: string,
    status: ProgressStatus
  ): Promise<IUserModuleProgress | undefined> {
    const [moduleProgress] = await trx('users_modules_progress')
      .where({ user_id: userId, module_id: moduleId })
      .update({
        id: randomUUID(),
        status,
        user_module_progress_status: status,
        last_accessed_at: new Date(),
        updated_at: new Date(),
        created_at: new Date(),
        deleted_at: null,
      })
      .returning('*');

    return moduleProgress;
  }

  async getModuleProgress(
    userId: string,
    moduleId: string
  ): Promise<IUserModuleProgress | undefined> {
    return knexInstance('users_modules_progress')
      .where({ user_id: userId, module_id: moduleId })
      .first();
  }

  async getProgressHierarchyData(
    userId: string,
    moduleId: string,
    courseId: string
  ): Promise<IProgressHierarchyData> {
    const moduleData = await knexInstance.raw(
      `
      SELECT 
        (SELECT COUNT(*) FROM courses_lessons WHERE module_id = ?) as total_lessons,
        (SELECT COUNT(*) FROM users_lessons_progress ulp 
         INNER JOIN courses_lessons cl ON cl.id = ulp.lesson_id 
         WHERE ulp.user_id = ? AND cl.module_id = ? AND ulp.status = 'completed') as completed_lessons,
        ump.status as module_status
      FROM users_modules_progress ump 
      WHERE ump.user_id = ? AND ump.module_id = ?
      LIMIT 1
    `,
      [moduleId, userId, moduleId, userId, moduleId]
    );

    const courseData = await knexInstance.raw(
      `
      SELECT 
        (SELECT COUNT(*) FROM courses_modules WHERE course_id = ?) as total_modules,
        (SELECT COUNT(*) FROM users_modules_progress ump 
         INNER JOIN courses_modules cm ON cm.id = ump.module_id 
         WHERE ump.user_id = ? AND cm.course_id = ? AND ump.status = 'completed') as completed_modules,
        ucp.status as course_status
      FROM users_courses_progress ucp 
      WHERE ucp.user_id = ? AND ucp.course_id = ?
      LIMIT 1
    `,
      [courseId, userId, courseId, userId, courseId]
    );

    const moduleCounters = await knexInstance.raw(
      `
      SELECT 
        (SELECT COUNT(*) FROM courses_lessons WHERE module_id = ?) as total_lessons,
        (SELECT COUNT(*) FROM users_lessons_progress ulp 
         INNER JOIN courses_lessons cl ON cl.id = ulp.lesson_id 
         WHERE ulp.user_id = ? AND cl.module_id = ? AND ulp.status = 'completed') as completed_lessons
    `,
      [moduleId, userId, moduleId]
    );

    const courseCounters = await knexInstance.raw(
      `
      SELECT 
        (SELECT COUNT(*) FROM courses_modules WHERE course_id = ?) as total_modules,
        (SELECT COUNT(*) FROM users_modules_progress ump 
         INNER JOIN courses_modules cm ON cm.id = ump.module_id 
         WHERE ump.user_id = ? AND cm.course_id = ? AND ump.status = 'completed') as completed_modules
    `,
      [courseId, userId, courseId]
    );

    const moduleResult = moduleData.rows[0];
    const courseResult = courseData.rows[0];
    const moduleCountersResult = moduleCounters.rows[0];
    const courseCountersResult = courseCounters.rows[0];

    return {
      moduleData: {
        totalLessons: parseInt(moduleCountersResult?.total_lessons || '0'),
        completedLessons: parseInt(moduleCountersResult?.completed_lessons || '0'),
        progressPercentage: 0,
        progress: moduleResult?.module_status ? { status: moduleResult.module_status } : undefined,
      },
      courseData: {
        totalModules: parseInt(courseCountersResult?.total_modules || '0'),
        completedModules: parseInt(courseCountersResult?.completed_modules || '0'),
        progressPercentage: 0,
        progress: courseResult?.course_status ? { status: courseResult.course_status } : undefined,
      },
    };
  }
}
