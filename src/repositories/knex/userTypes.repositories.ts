import { BaseRepository } from 'bq-knex-base-repository';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IUserType } from '../../model/IUserType';
import { IUsersTypesRepository } from '../assign/usersTypes.assign';

export class KnexUsersTypesRepository
  extends BaseRepository<IUserType>
  implements IUsersTypesRepository
{
  constructor() {
    super(knexInstance, 'users_types');
  }

  async getByName(name: string): Promise<IUserType | undefined> {
    const userType = await knexInstance('users_types').whereILike('name', `%${name}%`).first();

    return userType;
  }
}
