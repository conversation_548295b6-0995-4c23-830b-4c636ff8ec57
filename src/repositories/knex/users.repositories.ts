import { BaseRepository } from 'bq-knex-base-repository';
import { randomUUID } from 'crypto';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import {
  ICreateUserExternal,
  ICreateUserFromEmailDTO,
  ICreateUserFromGoogleDTO,
  IListStudentsParamsDTO,
  IListUsersAdmParamsDTO,
  ISoftDeleteUserDTO,
  UserPublicDTO,
} from '../../model/DTO/IUsers.dto';
import { IUserWithCoursesAndClasses } from '../../model/DTO/request/user.request.dto';
import { ListStudentsResponseDto } from '../../model/DTO/response/user.dto';
import { RolesEnum } from '../../model/enums/roles.enum';
import { IUser } from '../../model/IUser';
import { translateFormated } from '../../services/translateFormatted.service';
import {
  ListUsersWithClassesParams,
  ListUsersWithClassesResponse,
} from '../../use-case/users/listUsers/listUsersWithClasses.useCase';
import { IUsersRepository } from '../assign/users.assign';

export class KnexUserRepository extends BaseRepository<IUser> implements IUsersRepository {
  constructor() {
    super(knexInstance, 'users');
  }

  getUsersByEmail(email: string, customerId: string): Promise<IUser | undefined> {
    return knexInstance('users as u')
      .leftJoin('customers as c', 'c.id', 'u.customer_id')
      .select('u.id', 'c.id as customer_id')
      .where({ 'u.email': email, 'c.id': customerId })
      .first();
  }

  async findTotalUsers(): Promise<number> {
    const query = await knexInstance('users').count('* as total');
    return Number(query);
  }

  async findByEmailAndExternalCustomerId(data: {
    email: string;
    externalCustomerId: string;
  }): Promise<IUser | undefined> {
    return knexInstance('users as u')
      .leftJoin('customers as c', 'c.id', 'u.customer_id')
      .select('u.*')
      .where({
        'u.email': data.email,
        'c.external_customer_id': data.externalCustomerId,
      })
      .whereNull('u.deleted_at')
      .first();
  }

  async createUserFromExternalSystem(data: ICreateUserExternal): Promise<UserPublicDTO> {
    const build = {
      created_at: new Date(),
      updated_at: new Date(),
      id: randomUUID(),
      ...data,
    };
    const query = await knexInstance('users')
      .insert(build)
      .returning([
        'id',
        'first_name',
        'last_name',
        'email',
        'role',
        'active',
        'customer_id',
        'gender',
        'birth_date',
        'phone_number',
        'cpf',
        'created_at',
        'updated_at',
      ]);
    return query[0];
  }

  async softDelete(data: ISoftDeleteUserDTO): Promise<IUser> {
    const { id, customerId } = data;

    const [deletedUser] = await knexInstance('users')
      .where({
        id,
        customer_id: customerId,
        deleted_at: null,
      })
      .update({
        deleted_at: knexInstance.fn.now(),
        updated_at: knexInstance.fn.now(),
      })
      .returning('*');

    return deletedUser;
  }

  async findByRole(role: string, customerId: string): Promise<IUser[]> {
    return knexInstance('users').where({ role, customer_id: customerId, deleted_at: null });
  }

  async insertAll(users: Partial<IUser>[]): Promise<IUser[]> {
    const now = new Date();
    const usersWithTimestamps = users.map((user) => ({
      ...user,
      id: randomUUID(),
      created_at: now,
      updated_at: now,
    }));

    return knexInstance('users').insert(usersWithTimestamps).returning('*');
  }

  async findAllWithUserAndClass(
    filters: ListUsersWithClassesParams
  ): Promise<ListUsersWithClassesResponse> {
    const {
      search,
      customerId,
      page = 1,
      limit = 10,
      orderByColumn = 'u.first_name',
      orderByDirection = 'desc',
      classId,
    } = filters;

    const baseQuery = knexInstance.select('distinct_users.*').from(
      knexInstance('users as u')
        .select(
          'u.id',
          'u.first_name',
          'u.last_name',
          'u.email',
          'uc.class_id',
          'c.name as class_name',
          'u.role',
          knexInstance.raw(`${translateFormated('u.first_name')} as sorted_name`)
        )
        .join('users_classes as uc', 'u.id', 'uc.user_id')
        .join('classes as c', 'uc.class_id', 'c.id')
        .whereNull('u.deleted_at')
        .whereNull('uc.deleted_at')
        .whereNull('c.deleted_at')
        .where('u.customer_id', customerId)
        .as('distinct_users')
    );

    if (search) {
      baseQuery.andWhere(function () {
        this.whereRaw(
          `${translateFormated('distinct_users.first_name')} ILIKE ${translateFormated('?')}`,
          [`%${search}%`]
        )
          .orWhereRaw(
            `${translateFormated('distinct_users.last_name')} ILIKE ${translateFormated('?')}`,
            [`%${search}%`]
          )
          .orWhereRaw(
            `${translateFormated('distinct_users.email')} ILIKE ${translateFormated('?')}`,
            [`%${search}%`]
          );
      });
    }

    if (classId) {
      baseQuery.andWhere('distinct_users.class_id', classId);
    }

    const countQuery = await knexInstance.raw(
      `
      SELECT COUNT(DISTINCT u.id) as count
      FROM users u
      INNER JOIN users_classes uc ON u.id = uc.user_id
      INNER JOIN classes c ON uc.class_id = c.id
      WHERE u.deleted_at IS NULL
      AND uc.deleted_at IS NULL
      AND c.deleted_at IS NULL
      AND u.customer_id = ?
      ${
        search
          ? `
      AND (
        LOWER(u.first_name) LIKE LOWER(?)
        OR LOWER(u.last_name) LIKE LOWER(?)
        OR LOWER(u.email) LIKE LOWER(?)
      )`
          : ''
      }
    `,
      [customerId, ...(search ? [`%${search}%`, `%${search}%`, `%${search}%`] : [])]
    );

    const totalItems = Number(countQuery.rows[0].count);

    if (orderByColumn === 'u.first_name') {
      baseQuery.orderBy('sorted_name', 'asc');
    } else if (orderByColumn && orderByDirection) {
      baseQuery.orderBy(orderByColumn.replace('u.', 'distinct_users.'), orderByDirection);
    }

    if (page && limit) {
      baseQuery.offset((page - 1) * limit).limit(limit);
    }

    const users = await baseQuery;

    return {
      paginationInfo: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages: Math.ceil(totalItems / limit),
        hasNextPage: page < Math.ceil(totalItems / limit),
        hasPreviousPage: page > 1,
      },
      users,
    };
  }

  async hardDelete(id: string, customerId: string): Promise<void> {
    await knexInstance('users').where({ id, customer_id: customerId }).del();
  }

  async changePassword(
    userId: string,
    password: string,
    trx?: Knex.Transaction
  ): Promise<{ id: string | undefined }> {
    const query = trx
      ? trx('users').where({ id: userId })
      : knexInstance('users').where({ id: userId });

    const [user] = await query
      .update({ password, updated_at: knexInstance.fn.now() })
      .returning('*');

    return { id: user?.id };
  }

  async findUsersByCustomerAdm(
    customerId: string,
    orderByColumn: string = 'firstName',
    orderByDirection: string = 'asc',
    accessLevelIds: string[]
  ): Promise<IListUsersAdmParamsDTO[]> {
    const validColumns: Record<string, string> = {
      firstName: 'firstName',
      lastName: 'lastName',
      email: 'u.email',
      created_at: 'u.createdAt',
      description: 'al.description',
      access_level_id: 'al.id',
    };
    const column = validColumns[orderByColumn] || 'firstName';
    const direction = ['asc', 'desc'].includes((orderByDirection || 'asc').toLowerCase())
      ? orderByDirection
      : 'asc';

    return knexInstance('users as u')
      .select(
        'u.*',
        'u.*',
        'u.first_name as firstName',
        'u.last_name as lastName',
        'u.deleted_at as deletedAt',
        'u.customer_id as customerId',
        'u.access_level_id as accessLevelId',
        'al.name as accessLevelName',
        'u.last_accessed_at as lastAccessedAt',
        'al.description as description'
      )
      .leftJoin('access_levels as al', 'u.access_level_id', 'al.id')
      .where('u.customer_id', customerId)
      .whereNull('u.deleted_at')
      .whereIn('u.access_level_id', accessLevelIds)
      .orderBy(column, direction);
  }

  async getUserWithAccessLevel(userId: string): Promise<IUser | undefined> {
    return knexInstance('users as u')
      .select(
        'u.*',
        'u.first_name as firstName',
        'u.last_name as lastName',
        'u.created_at as createdAt',
        'u.updated_at as updatedAt',
        'u.deleted_at as deletedAt',
        'u.customer_id as customerId',
        'u.access_level_id as accessLevelId',
        'al.name as roleName',
        'al.role as role',
        'al.description as description'
      )
      .leftJoin('access_levels as al', 'u.access_level_id', 'al.id')
      .where({ 'u.id': userId, 'u.deleted_at': null })
      .first();
  }

  async insertWithTrx(data: IUser, trx: Knex.Transaction): Promise<IUser | undefined> {
    if (!trx) {
      const [user] = await knexInstance('users').insert(data).returning('*');
      return user;
    }
    const [user] = await trx('users').insert(data).returning('*');
    return user;
  }

  async findUsersStudents(data: IListStudentsParamsDTO): Promise<ListStudentsResponseDto[]> {
    const {
      customerId,
      orderByColumn = 'firstName',
      orderByDirection = 'asc',
      courseId,
      accessLevelId,
    } = data;

    const validColumns: Record<string, string> = {
      firstName: 'u.first_name',
      email: 'u.email',
      created_at: 'u.created_at',
    };
    const column = validColumns[orderByColumn] || 'u.first_name';
    const direction = ['asc', 'desc'].includes((orderByDirection || '').toLowerCase())
      ? orderByDirection
      : 'asc';

    let query = knexInstance('users as u')
      .select(
        'u.id',
        'u.first_name as firstName',
        'u.last_name as lastName',
        'u.email',
        'u.birth_date as birthDate',
        'u.gender',
        'u.cpf',
        'u.phone_number as phoneNumber',
        'u.image',
        'u.additional_info as additionalInfo',
        'u.created_at',
        'u.updated_at',
        'u.deleted_at',
        'u.customer_id',
        'u.access_level_id',
        'u.last_accessed_at',
        'uc.class_id as class_id',
        'cl.name as class_name'
      )
      .leftJoin('users_classes as uc', 'uc.user_id', 'u.id')
      .leftJoin('classes as cl', 'cl.id', 'uc.class_id')
      .where('u.access_level_id', accessLevelId)
      .whereNull('u.deleted_at')
      .whereNull('uc.deleted_at')
      .andWhere('u.customer_id', customerId);

    if (courseId) {
      query = query.andWhere('cl.course_id', courseId);
    }

    return await query.orderBy(column, direction);
  }

  async update(data: Partial<UserPublicDTO>, trx?: Knex.Transaction): Promise<IUser> {
    if (!trx) {
      const [user] = await knexInstance('users').where({ id: data.id }).update(data).returning('*');
      return user;
    }
    const [user] = await trx('users').where({ id: data.id }).update(data).returning('*');
    return user;
  }

  async findUserByEmailOrCpfAndCustomerId(
    email: string,
    cpf: string,
    customerId: string,
    id: string
  ): Promise<IUser | undefined> {
    return knexInstance('users')
      .where(function () {
        if (email) {
          this.orWhere('email', email.toLowerCase().trim());
        }
        if (cpf) {
          this.orWhere('cpf', cpf.trim());
        }
      })
      .andWhere('customer_id', customerId)
      .andWhere('id', '!=', id)
      .whereNull('deleted_at')
      .first();
  }

  async createUserFromGoogle(data: ICreateUserFromGoogleDTO): Promise<IUser | undefined> {
    const buildCreateUser = {
      id: randomUUID(),
      created_at: new Date(),
      updated_at: new Date(),
      ...data,
      customer_id: data.customer_id ?? undefined,
      image: data.image ?? undefined,
    };
    const [user] = await knexInstance('users').insert(buildCreateUser).returning('*');
    return user;
  }

  async createUserFromEmails(
    data: ICreateUserFromEmailDTO[],
    trx?: Knex.Transaction
  ): Promise<IUser[]> {
    const now = new Date();
    const userData = data.map((item) => ({
      id: randomUUID(),
      role: RolesEnum.STUDENT,
      created_at: now,
      updated_at: now,
      ...item,
    }));

    if (!trx) {
      const users = await knexInstance('users').insert(userData).returning('*');
      return users;
    }
    const users = await trx('users').insert(userData).returning('*');
    return users;
  }

  async findManyByEmailsAndCustomerId(emails: string[], customerId: string): Promise<IUser[]> {
    return knexInstance('users')
      .whereIn('email', emails)
      .andWhere('customer_id', customerId)
      .select('*');
  }

  async findAllByIds(ids: string[], customerId: string): Promise<IUser[]> {
    return knexInstance('users').whereIn('id', ids).andWhere('customer_id', customerId).select('*');
  }

  async findUserAndUpdateLastAccess(userId: string): Promise<IUser | undefined> {
    const [user] = await knexInstance('users')
      .where({ id: userId, deleted_at: null })
      .update({ last_accessed_at: knexInstance.fn.now() })
      .returning('*');
    return user;
  }

  async getUserWithCoursesAndClasses(
    userId: string,
    customerId: string
  ): Promise<IUserWithCoursesAndClasses[]> {
    const result = await knexInstance('users as u')
      .join('users_classes as uc', 'uc.user_id', 'u.id')
      .join('classes as c', 'c.id', 'uc.class_id')
      .join('courses as co', 'co.id', 'c.course_id')
      .select(
        'u.id as user_id',
        'u.first_name',
        'u.last_name',
        'u.email',
        'u.role',
        'u.cpf',
        'u.phone_number',
        'u.gender',
        'u.birth_date',
        'u.image as user_image',
        'co.id as course_id',
        'co.name as course_name',
        'co.description_course',
        'co.photo_url as course_photo_url',
        'uc.status',
        'uc.created_at as createdAt',
        'uc.updated_at as updatedAt',
        'uc.deleted_at as deletedAt',
        'c.id as class_id',
        'c.name as class_name'
      )
      .where('u.id', userId)
      .where('u.customer_id', customerId)
      .whereNull('u.deleted_at')
      .whereNull('co.deleted_at')
      .orderBy('uc.created_at', 'desc');

    return result;
  }

  async updateWithTrx(
    data: Partial<UserPublicDTO>,
    trx?: Knex.Transaction
  ): Promise<IUser | undefined> {
    if (!trx) {
      const [user] = await knexInstance('users').where({ id: data.id }).update(data).returning('*');
      return user;
    }
    const [user] = await trx('users').where({ id: data.id }).update(data).returning('*');
    return user;
  }
}
