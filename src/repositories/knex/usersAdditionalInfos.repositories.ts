import { BaseRepository } from 'bq-knex-base-repository';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import type { IUserAdditionalInfo } from '../../model/IUserAdditionalInfo';
import { IUsersAdditionalInfosRepository } from '../assign/usersAdditionalInfos.assign';

export class KnexUsersAdditionalInfosRepository
  extends BaseRepository<IUserAdditionalInfo>
  implements IUsersAdditionalInfosRepository
{
  constructor() {
    super(knexInstance, 'users_additional_infos');
  }

  async createUserAdditionalInfo(
    data: IUserAdditionalInfo,
    trx?: Knex.Transaction
  ): Promise<IUserAdditionalInfo | undefined> {
    if (!trx) {
      const [result] = await knexInstance('users_additional_infos').insert(data).returning('*');
      return result;
    }
    const [result] = await trx('users_additional_infos').insert(data).returning('*');
    return result;
  }

  async findOneBy(data: Partial<IUserAdditionalInfo>): Promise<IUserAdditionalInfo | undefined> {
    return knexInstance('users_additional_infos').where(data).first();
  }

  async updateUserAdditionalInfo(
    data: Partial<IUserAdditionalInfo>,
    trx?: Knex.Transaction
  ): Promise<IUserAdditionalInfo | undefined> {
    if (!trx) {
      const [result] = await knexInstance('users_additional_infos')
        .where({ id: data.id })
        .update(data)
        .returning('*');
      return result;
    }
    const [result] = await trx('users_additional_infos')
      .where({ id: data.id })
      .update(data)
      .returning('*');
    return result;
  }
}
