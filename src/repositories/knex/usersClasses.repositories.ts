import { BaseRepository } from 'bq-knex-base-repository';
import { randomUUID } from 'crypto';
import { Knex } from 'knex';

import { knexInstance } from '../../config/connectionDatabase.config';
import { IAddUserToClassDTO } from '../../model/DTO/IUserClasses.dto';
import { IUserClasses } from '../../model/IUserClasses';
import { IUserClassesRepository } from '../assign/userClasses.assign';

export class KnexUserClassesRepository
  extends BaseRepository<IUserClasses>
  implements IUserClassesRepository
{
  constructor() {
    super(knexInstance, 'users_classes');
  }

  async softDeleteByUserId(userId: string): Promise<IUserClasses> {
    const query = await knexInstance('users_classes')
      .where({ user_id: userId, deleted_at: null })
      .update({ deleted_at: new Date(), updated_at: new Date() })
      .returning('*');

    return query[0];
  }

  async softDeleteByClassId(classId: string): Promise<void> {
    await knexInstance('users_classes').where({ class_id: classId, deleted_at: null }).update({
      deleted_at: new Date(),
      updated_at: new Date(),
    });
  }

  async findByUserId(userId: string): Promise<IUserClasses[]> {
    return knexInstance('users_classes').where({ user_id: userId, deleted_at: null });
  }

  async softDeleteByUserIdAndClassIds(userId: string, classIds: string[]): Promise<void> {
    await knexInstance('users_classes')
      .where({ user_id: userId })
      .whereIn('class_id', classIds)
      .whereNull('deleted_at')
      .update({
        deleted_at: new Date(),
        updated_at: new Date(),
      });
  }

  async findByClassIds(classIds: string[]): Promise<IUserClasses[]> {
    return knexInstance('users_classes').whereIn('class_id', classIds).whereNull('deleted_at');
  }

  async insertAll(data: IAddUserToClassDTO[]): Promise<IUserClasses[]> {
    if (data.length === 0) {
      return [];
    }

    const dataToInsert = data.map((item) => ({
      ...item,
      id: randomUUID(),
      created_at: new Date(),
      updated_at: new Date(),
    }));

    return await knexInstance.transaction(async (trx) => {
      const insertedRows = await trx('users_classes').insert(dataToInsert).returning('*');
      return insertedRows;
    });
  }

  async hardDeleteByUserId(userId: string): Promise<void> {
    await knexInstance('users_classes').where({ user_id: userId }).del();
  }

  async insertWithTrx(data: IAddUserToClassDTO, trx: Knex.Transaction): Promise<IUserClasses> {
    const insertData = {
      id: randomUUID(),
      ...data,
      created_at: new Date(),
      updated_at: new Date(),
    };
    if (!trx) {
      const [user] = await knexInstance('users_classes').insert(insertData).returning('*');
      return user;
    }
    const [user] = await trx('users_classes').insert(insertData).returning('*');
    return user;
  }

  async findUserClassesInCourse(userId: string, courseId: string): Promise<IUserClasses[]> {
    return knexInstance('users_classes')
      .where({ user_id: userId })
      .whereNull('users_classes.deleted_at')
      .join('classes as c', 'c.id', 'users_classes.class_id')
      .join('courses as co', 'co.id', 'c.course_id')
      .where('co.id', courseId)
      .select(
        'c.id as classId',
        'c.name as className',
        'c.description as classDescription',
        'c.start_date as startDate',
        'c.end_date as endDate',
        'c.instructor_id as instructorId',
        'c.customer_id as customerId',
        'c.external_class_id as externalClassId',
        'c.status as classStatus',
        'c.course_id as courseId',
        'c.is_default as isDefault',
        'users_classes.status as enrollmentStatus',
        'users_classes.apply_date as applyDate'
      );
  }

  async findByUserIdAndCourseIdExcludingClass(
    userId: string,
    courseId: string,
    excludeClassId: string,
    trx?: Knex.Transaction
  ): Promise<IUserClasses | undefined> {
    const conn = trx ?? knexInstance;
    return await conn('users_classes')
      .join('classes', 'users_classes.class_id', 'classes.id')
      .where('users_classes.user_id', userId)
      .where('classes.course_id', courseId)
      .whereNot('users_classes.class_id', excludeClassId)
      .select('users_classes.*')
      .first();
  }

  async update(data: Partial<IUserClasses>, trx?: Knex.Transaction): Promise<IUserClasses> {
    if (!trx) {
      const [user] = await knexInstance('users_classes')
        .where({ id: data.id })
        .update(data)
        .returning('*');
      return user;
    }
    const [user] = await trx('users_classes').where({ id: data.id }).update(data).returning('*');
    return user;
  }

  async insertAllWithTrx(
    data: IAddUserToClassDTO[],
    trx: Knex.Transaction
  ): Promise<IUserClasses[]> {
    if (data.length === 0) {
      return [];
    }

    const dataToInsert = data.map((item) => ({
      ...item,
      id: randomUUID(),
      created_at: new Date(),
      updated_at: new Date(),
    }));

    if (!trx) {
      const [user] = await knexInstance('users_classes').insert(dataToInsert).returning('*');
      return user;
    }
    const [user] = await trx('users_classes').insert(dataToInsert).returning('*');
    return user;
  }

  async findByUserIdsAndClassId(userIds: string[], classId: string): Promise<IUserClasses[]> {
    return knexInstance('users_classes')
      .whereIn('user_id', userIds)
      .where('class_id', classId)
      .whereNull('deleted_at');
  }

  async findByUserIdsAndClassIds(userIds: string[], courseId: string): Promise<IUserClasses[]> {
    return knexInstance('users_classes as uc')
      .join('classes as cl', 'uc.class_id', 'cl.id')
      .join('courses as co', 'cl.course_id', 'co.id')
      .whereIn('uc.user_id', userIds)
      .where('co.id', courseId)
      .whereNull('uc.deleted_at')
      .whereNull('cl.deleted_at');
  }

  async softDeleteByUserIdsAndCourseId(
    userIds: string[],
    courseId: string,
    trx?: Knex.Transaction
  ): Promise<void> {
    const conn = trx ?? knexInstance;
    await conn('users_classes as uc')
      .update({
        deleted_at: new Date(),
        updated_at: new Date(),
      })
      .whereIn('uc.user_id', userIds)
      .whereNull('uc.deleted_at')
      .whereIn('uc.class_id', function () {
        this.select('cl.id')
          .from('classes as cl')
          .where('cl.course_id', courseId)
          .whereNull('cl.deleted_at');
      });
  }
}
