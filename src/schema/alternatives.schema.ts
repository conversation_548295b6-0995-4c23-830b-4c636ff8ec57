import z from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';

export const alternativeSchemaWithoutQuestionId = z.object({
  correct: z.boolean({
    required_error: errorMessages.required('correct'),
    invalid_type_error: errorMessages.invalidType('correct', 'boolean'),
  }),
  option: z
    .string({
      required_error: errorMessages.required('option'),
      invalid_type_error: errorMessages.invalidType('option', 'string'),
    })
    .trim(),
  description: z
    .string({
      required_error: errorMessages.required('description'),
      invalid_type_error: errorMessages.invalidType('description', 'string'),
    })
    .trim(),
});

export const alternativeIdSchema = z.object({
  alternativeId: z
    .string({
      required_error: errorMessages.required('alternativeId'),
      invalid_type_error: errorMessages.invalidType('alternativeId', 'string'),
    })
    .uuid({
      message: errorMessages.invalidType('alternativeId', 'uuid'),
    }),
});
