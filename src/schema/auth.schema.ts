import { z } from 'zod';

export const autologinSchema = z.object({
  token: z.string({
    required_error: 'Token é obrigatório',
    invalid_type_error: 'Token deve ser uma string',
  }),
});

export const changePasswordSchema = z.object({
  oldPassword: z.string().min(6, 'Senha atual deve ter no mínimo 6 caracteres'),
  newPassword: z.string().min(6, 'Nova senha deve ter no mínimo 6 caracteres'),
});

export type ChangePasswordDTO = z.infer<typeof changePasswordSchema>;

export const googleAuthSchema = z.object({
  googleToken: z.string().min(1, 'Token do Google é obrigatório'),
});

export const forgotPasswordSchema = z.object({
  email: z.string().email('E-mail inválido'),
  customerId: z.string({ required_error: 'CustomerId é obrigatório' }),
});

export const redefinePasswordSchema = z.object({
  password: z.string().min(6, 'Nova senha deve ter no mínimo 6 caracteres'),
  token: z.string({
    required_error: 'Token é obrigatório',
    invalid_type_error: 'Token deve ser uma string',
  }),
});

export const completeUserRegistrationSchema = z.object({
  token: z.string({
    required_error: 'Token é obrigatório',
    invalid_type_error: 'Token deve ser uma string',
  }),
  firstName: z.string().min(1, 'Nome é obrigatório').optional(),
  lastName: z.string().min(1, 'Sobrenome é obrigatório').optional(),
  birthDate: z.string().optional(),
  gender: z.string().optional(),
  cpf: z.string().max(11, 'CPF deve ter no máximo 11 caracteres').optional(),
  phoneNumber: z.string().optional(),
  password: z.string().min(6, 'Senha deve ter no mínimo 6 caracteres'),
  termsAccept: z.boolean().default(false).optional(),
  termsId: z
    .string({
      required_error: 'ID dos termos é obrigatório',
      invalid_type_error: 'ID dos termos deve ser uma string',
    })
    .optional(),
  countryId: z.string().uuid().optional(),
  ddi: z.string().optional(),
  acceptMarketing: z.boolean().default(false).optional(),
  acceptNewsletter: z.boolean().default(false).optional(),
  // Campos adicionais do aluno
  referralSource: z.string().optional(),
  formation: z.string().optional(),
  completionYear: z.number().optional(),
  hasSpecialty: z.boolean().optional(),
  currentlyAttending: z.boolean().optional(),
  attendanceLocation: z.string().optional(),
  institutionId: z.string().uuid().optional(),
  graduationId: z.string().uuid().optional(),
  primarySpecialtyId: z.string().uuid().optional(),
  secondarySpecialtyId: z.string().uuid().optional(),
});

export type CompleteUserRegistrationDTO = z.infer<typeof completeUserRegistrationSchema>;
