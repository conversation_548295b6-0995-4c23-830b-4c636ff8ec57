import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';
import { BulkImportStatus } from '../model/enums/bulkImportStatus.enum';
import { customerIdSchema } from './customers.schema';

export const listBulkImportsSchema = z.object({
  page: z.coerce.number().optional().default(1),
  limit: z.coerce.number().optional().default(10),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  search: z.string().optional(),
  status: z
    .enum([
      BulkImportStatus.PENDING,
      BulkImportStatus.PROCESSING,
      BulkImportStatus.IMPORTED,
      BulkImportStatus.CANCELLED,
      BulkImportStatus.VALIDATION_FAILED,
    ])
    .optional(),
  orderBy: z
    .enum(['created_at', 'file_name', 'status', 'success_percentage'])
    .optional()
    .default('created_at'),
  orderDirection: z.enum(['asc', 'desc']).optional().default('desc'),
});

export type ListBulkImportsParams = z.infer<typeof listBulkImportsSchema & typeof customerIdSchema>;

export const importIdSchema = z.object({
  importId: z
    .string({
      required_error: errorMessages.required('importId'),
      invalid_type_error: errorMessages.invalidType('importId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('importId', 'uuid') }),
});

export type GetBulkImportResultsInput = z.infer<typeof importIdSchema & typeof customerIdSchema>;
