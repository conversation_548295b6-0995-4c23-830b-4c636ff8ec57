import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';

export const createCategoryBodySchema = z.object({
  name: z
    .string({
      invalid_type_error: errorMessages.invalidType('name', 'string'),
    })
    .optional(),
  subcategoryName: z
    .string({
      invalid_type_error: errorMessages.invalidType('subcategoryName', 'string'),
    })
    .optional(),
  notCreateDefaults: z
    .boolean({
      invalid_type_error: errorMessages.invalidType('notCreateDefaults', 'boolean'),
    })
    .optional(),
});

export const updateCategorySchema = z.object({
  name: z.string().min(3),
});
export const paramsSchema = z.object({
  id: z.string().uuid({ message: errorMessages.invalidType('id', 'uuid') }),
});

export const categoryIdSchema = z.object({
  categoryId: z.string().uuid({ message: errorMessages.invalidType('categoryId', 'uuid') }),
});
export const updateSubcategoryBodySchema = z.object({
  name: z.string({
    required_error: errorMessages.required('name'),
    invalid_type_error: errorMessages.invalidType('name', 'string'),
  }),
  categoryId: z
    .string({
      required_error: errorMessages.required('categoryId'),
    })
    .uuid({ message: errorMessages.invalidType('categoryId', 'uuid') }),
});

export const listCategoriesQuerySchema = z.object({
  disciplineId: z
    .string()
    .uuid({ message: errorMessages.invalidType('disciplineId', 'uuid') })
    .optional(),
  search: z.string().optional(),
  orderByColumn: z
    .enum(['id', 'name', 'created_at', 'updated_at', 'deleted_at', 'customer_id'])
    .optional(),
  orderDirection: z
    .enum(['asc', 'desc'], {
      errorMap: () => ({ message: 'orderDirection deve ser asc ou desc' }),
    })
    .optional(),
});
