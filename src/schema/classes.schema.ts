import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';
import { customerIdSchema } from './customers.schema';

export const createExternalClassSchema = z.object({
  name: z.string({
    required_error: errorMessages.required('name'),
    invalid_type_error: errorMessages.invalidType('name', 'string'),
  }),
  description: z.string().optional(),
  startDate: z
    .string({
      required_error: errorMessages.required('startDate'),
      invalid_type_error: errorMessages.invalidType('startDate', 'string'),
    })
    .optional(),
  endDate: z
    .string({
      required_error: errorMessages.required('endDate'),
      invalid_type_error: errorMessages.invalidType('endDate', 'string'),
    })
    .optional(),
  instructorId: z.string().optional(),
  customerId: z.string({
    required_error: errorMessages.required('customerId'),
    invalid_type_error: errorMessages.invalidType('customerId', 'string'),
  }),
  id: z.string({
    required_error: errorMessages.required('id'),
    invalid_type_error: errorMessages.invalidType('id', 'string'),
  }),
  status: z
    .enum(['active', 'inactive'], {
      required_error: errorMessages.required('status'),
      invalid_type_error: errorMessages.invalidType('status', 'string'),
    })
    .default('active'),
});

export type ICreateExternalClassDTO = z.infer<typeof createExternalClassSchema>;

export const updateExternalClassSchema = z.object({
  name: z.string().min(1, 'O nome é obrigatório').optional(),
  description: z.string().optional(),
  status: z.enum(['active', 'inactive']).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  instructorId: z.string().optional(),
  customerId: z.string({
    required_error: errorMessages.required('customerId'),
    invalid_type_error: errorMessages.invalidType('customerId', 'string'),
  }),
});

export type IUpdateExternalClassDTO = z.infer<typeof updateExternalClassSchema>;

export const linkExamsToExternalClassSchema = z.object({
  examIds: z.array(z.string().uuid()).optional().default([]),
  classIds: z.array(z.string()).min(1, 'É necessário fornecer pelo menos um ID de turma'),
  customerId: z.string({
    required_error: errorMessages.required('customerId'),
    invalid_type_error: errorMessages.invalidType('customerId', 'string'),
  }),
});

export type ILinkExamsToExternalClassDTO = z.infer<typeof linkExamsToExternalClassSchema>;

export const listClassesSchema = z.object({
  search: z
    .string({
      invalid_type_error: errorMessages.invalidType('search', 'string'),
    })
    .trim()
    .optional(),
  orderByColumn: z
    .enum(['id', 'name', 'start_date', 'end_date', 'status', 'created_at', 'updated_at'], {
      message: errorMessages.invalidEnum('orderByColumn', [
        'id',
        'name',
        'start_date',
        'end_date',
        'status',
        'created_at',
        'updated_at',
      ]),
    })
    .optional(),
  orderDirection: z
    .enum(['asc', 'desc'], {
      message: errorMessages.invalidEnum('orderDirection', ['asc', 'desc']),
    })
    .optional(),
  page: z.coerce
    .number({
      invalid_type_error: errorMessages.invalidType('page', 'number'),
    })
    .default(1),
  limit: z.coerce
    .number({
      invalid_type_error: errorMessages.invalidType('limit', 'number'),
    })
    .default(10),
});

export const listClassesQuerySchema = z.object({
  search: z
    .string({
      invalid_type_error: errorMessages.invalidType('search', 'string'),
    })
    .optional(),
  orderByColumn: z
    .string({
      invalid_type_error: errorMessages.invalidType('orderByColumn', 'string'),
    })
    .optional(),
  orderDirection: z
    .string({
      invalid_type_error: errorMessages.invalidType('orderDirection', 'string'),
    })
    .optional(),
  page: z.coerce
    .number({
      invalid_type_error: errorMessages.invalidType('page', 'number'),
    })
    .default(1),
  limit: z.coerce
    .number({
      invalid_type_error: errorMessages.invalidType('limit', 'number'),
    })
    .default(10),
  simulatedId: z.string().optional(),
});

export type ListClassesQueryInput = z.infer<
  typeof listClassesQuerySchema & typeof customerIdSchema
>;

export type ListClassesWithUsersInput = z.infer<
  typeof listClassesQuerySchema & typeof customerIdSchema
>;

export const classIdOptionalSchema = z.object({
  classId: z
    .string({
      invalid_type_error: errorMessages.invalidType('classId', 'string'),
    })
    .uuid({
      message: errorMessages.invalidType('classId', 'uuid'),
    })
    .optional(),
});

export const createClassSchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório'),
  description: z.string().nullable().optional(),
  started_date: z.string().optional(),
  end_date: z.string().optional(),
  course_id: z.string().uuid({ message: 'course_id deve ser um UUID válido' }),
  status: z.enum(['active', 'inactive']).optional(),
  isDefault: z.boolean().optional(),
});

export type ICreateClassDTO = z.infer<typeof createClassSchema>;

export const updateClassSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().nullable().optional(),
  started_date: z.string().optional(),
  end_date: z.string().nullable().optional(),
  course_id: z.string().uuid().optional(),
  customer_id: z.string().uuid().optional(),
  status: z.enum(['active', 'inactive']).optional(),
  isDefault: z.boolean().optional(),
});

export type IUpdateClassDTO = z.infer<typeof updateClassSchema>;
