import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';
import { customerIdSchema } from './customers.schema';

export const createCourseSchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório'),
  descriptionCourse: z.string().min(1, 'Descrição é obrigatória'),
  photoUrl: z
    .string()
    .max(255, 'URL da foto do curso deve ter no máximo 255 caracteres')
    .optional(),
  categoryIds: z.array(z.string().uuid('ID da categoria inválido')).optional(),
});

export type CreateCourseRequestDTO = z.infer<typeof createCourseSchema>;

export const lessonSchema = z.object({
  id: z.number(),
  title: z.string(),
  lesson_url: z.string().nullable(),
  duration: z.string().nullable(),
  description_lessons: z.string().nullable(),
  order_position: z.number().nullable(),
  questionGroupId: z.number().nullable(),
  type: z.string(),
  created_at: z.string().nullable(),
});

export const moduleSchema = z.object({
  id: z.number(),
  name: z.string(),
  description_module: z.string().nullable(),
  order_position: z.number().nullable(),
  created_at: z.string().nullable(),
  lessons: z.array(lessonSchema),
});

export const courseDetailSchema = z.object({
  id: z.number(),
  name: z.string(),
  description_course: z.string(),
  photo_url: z.string().nullable(),
  created_at: z.string().nullable(),
  modules: z.array(moduleSchema),
});

export type CourseDetailDTO = z.infer<typeof courseDetailSchema>;

export const courseIdRequiredSchema = z.object({
  courseId: z.string().uuid(),
});

export const trackProgressSchema = z.object({
  courseId: z.string().uuid(),
  lessonId: z.string().uuid(),
  progress: z.number().min(0),
  timeWatched: z.number().min(0).optional(),
});

export const updateCourseSchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório'),
  descriptionCourse: z.string().optional(),
  photoUrl: z
    .string()
    .max(255, 'URL da foto do curso deve ter no máximo 255 caracteres')
    .optional(),
  categoryIds: z.array(z.string().uuid('ID da categoria inválido')).optional(),
});

export type UpdateCourseRequestDTO = z.infer<
  typeof updateCourseSchema & typeof courseIdRequiredSchema
>;

export const removeUsersFromCourseSchema = z.object({
  userIds: z
    .array(
      z.string({
        required_error: errorMessages.required('userId'),
        invalid_type_error: errorMessages.invalidType('userId', 'string'),
      })
    )
    .min(1, 'É necessário fornecer pelo menos um userId'),
  courseId: z.string({
    required_error: errorMessages.required('courseId'),
    invalid_type_error: errorMessages.invalidType('courseId', 'string'),
  }),
});

export type RemoveUsersFromCourseInput = z.infer<
  typeof removeUsersFromCourseSchema & typeof customerIdSchema
>;
