import { z } from 'zod';

export const createStudentSchema = z.object({
  firstName: z.string().min(2, 'Nome obrigatório'),
  lastName: z.string().min(2, 'Sobrenome obrigatório'),
  email: z.string().email('E-mail inválido'),
  birthDate: z.string().optional(),
  gender: z.string().optional(),
  cpf: z.string().min(11, 'CPF obrigatório'),
  phoneNumber: z.string().optional(),
  customerId: z.string().uuid('customerId deve ser um UUID válido'),
  classesId: z.string().uuid('classesId deve ser um UUID válido').optional(),
});

export type CreateStudentSchema = z.infer<typeof createStudentSchema>;
