import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';

export const customerIdSchema = z.object({
  customerId: z.string({
    required_error: errorMessages.required('customerId'),
  }),
});

export const loginSchema = z.object({
  email: z
    .string({
      required_error: 'Email é obrigatório',
    })
    .email('Email inválido'),
  password: z
    .string({
      required_error: 'Senha é obrigatória',
    })
    .min(6, 'Senha deve ter no mínimo 6 caracteres'),
});

export const createCustomerSchema = z.object({
  customerName: z.string({
    required_error: errorMessages.required('customerName'),
    invalid_type_error: errorMessages.invalidType('customerName', 'string'),
  }),
  email: z
    .string({
      invalid_type_error: errorMessages.invalidType('email', 'string'),
    })
    .email({ message: errorMessages.invalidType('email', 'email') })
    .optional(),
  taxNumber: z
    .string({
      required_error: errorMessages.required('tax_number'),
      invalid_type_error: errorMessages.invalidType('taxNumber', 'string'),
    })
    .optional(),
  logoUrl: z
    .string()
    .url({ message: errorMessages.invalidType('logoUrl', 'url') })
    .optional(),
  primaryColor: z
    .string()
    .regex(/^#(?:[0-9a-fA-F]{3}|[0-9a-fA-F]{6})$/, {
      message: errorMessages.invalidType('primaryColor', 'hex exemplo: #000000'),
    })
    .optional(),
  secondaryColor: z
    .string()
    .regex(/^#(?:[0-9a-fA-F]{3}|[0-9a-fA-F]{6})$/, {
      message: errorMessages.invalidType('secondaryColor', 'hex exemplo: #000000'),
    })
    .optional(),
  status: z.boolean().optional(),
  subdomain: z.string().optional(),
  websiteUrl: z
    .string()
    .url({ message: errorMessages.invalidType('websiteUrl', 'url') })
    .optional(),
  id: z.string().optional(),
  supportEmail: z
    .string({
      invalid_type_error: errorMessages.invalidType('support_email', 'string'),
    })
    .email({ message: errorMessages.invalidType('support_email', 'email') })
    .optional(),
});

export const updateCustomerSchema = z.object({
  customerName: z
    .string({
      required_error: errorMessages.required('customerName'),
      invalid_type_error: errorMessages.invalidType('customerName', 'string'),
    })
    .optional(),
  email: z
    .string({
      invalid_type_error: errorMessages.invalidType('email', 'string'),
    })
    .email({ message: errorMessages.invalidType('email', 'email') })
    .optional(),
  taxNumber: z
    .string({
      required_error: errorMessages.required('tax_number'),
      invalid_type_error: errorMessages.invalidType('taxNumber', 'string'),
    })
    .optional(),
  logoUrl: z
    .string({
      required_error: errorMessages.required('logo_url'),
      invalid_type_error: errorMessages.invalidType('logoUrl', 'string'),
    })
    .optional(),
  primaryColor: z
    .string()
    .regex(/^#(?:[0-9a-fA-F]{3}|[0-9a-fA-F]{6})$/, {
      message: errorMessages.invalidType('primaryColor', 'hex exemplo: #000000'),
    })
    .optional(),
  secondaryColor: z
    .string()
    .regex(/^#(?:[0-9a-fA-F]{3}|[0-9a-fA-F]{6})$/, {
      message: errorMessages.invalidType('secondaryColor', 'hex exemplo: #000000'),
    })
    .optional(),
  status: z.boolean().optional(),
  subdomain: z.string().optional(),
  websiteUrl: z
    .string()
    .url({ message: errorMessages.invalidType('websiteUrl', 'url') })
    .optional(),
  id: z.string().optional(),
  supportEmail: z
    .string({
      invalid_type_error: errorMessages.invalidType('support_email', 'string'),
    })
    .email({ message: errorMessages.invalidType('support_email', 'email') })
    .nullable()
    .optional(),
});
