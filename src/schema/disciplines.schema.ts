import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';
import { mergeZodSchemas } from '../helpers/zod/mergeZodSchemas.helper';
import { DifficultyEnum } from '../model/enums/questions.enum';

export const createDisciplineSchema = z.object({
  name: z
    .string({
      required_error: errorMessages.required('name'),
      invalid_type_error: errorMessages.invalidType('name', 'string'),
    })
    .min(3, errorMessages.minLength('name', 3))
    .max(100, errorMessages.maxLength('name', 100)),
});

export const updateDisciplineSchema = z.object({
  name: z
    .string({
      required_error: errorMessages.required('name'),
      invalid_type_error: errorMessages.invalidType('name', 'string'),
    })
    .min(3, errorMessages.minLength('name', 3))
    .max(100, errorMessages.maxLength('name', 100)),
});

export const disciplineIdSchema = z.object({
  disciplineId: z.string().uuid({ message: errorMessages.invalidType('disciplineId', 'uuid') }),
});

export const listDisciplinesWithQuestionCountParamsSchema = z.object({
  questionGroupId: z
    .string({
      required_error: errorMessages.required('questionGroupId'),
      invalid_type_error: errorMessages.invalidType('questionGroupId', 'string'),
    })
    .uuid(),
  customerId: z
    .string({
      required_error: errorMessages.required('customerId'),
      invalid_type_error: errorMessages.invalidType('customerId', 'string'),
    })
    .uuid(),
});

export const listDisciplinesWithQuestionCountQuerySchema = z.object({
  difficulty: z.array(z.nativeEnum(DifficultyEnum)).optional(),
  search: z.string().optional(),
  disciplineIds: z.array(z.string().uuid()).optional(),
  categoryIds: z.array(z.string().uuid()).optional(),
  subcategoryIds: z.array(z.string().uuid()).optional(),
});

export const listDisciplinesWithQuestionCountSchema = mergeZodSchemas(
  listDisciplinesWithQuestionCountParamsSchema,
  listDisciplinesWithQuestionCountQuerySchema
);
