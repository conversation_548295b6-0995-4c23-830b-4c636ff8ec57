import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';

export const manageCategoryAccessInDisciplineSchema = z.object({
  categoryId: z
    .string({
      required_error: errorMessages.required('categoryId'),
      invalid_type_error: errorMessages.invalidType('categoryId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('categoryId', 'uuid') }),
  isChecked: z.boolean({
    required_error: errorMessages.required('isChecked'),
    invalid_type_error: errorMessages.invalidType('isChecked', 'boolean'),
  }),
  subcategoriesIds: z.array(
    z.string().uuid({ message: errorMessages.invalidType('subcategoriesIds', 'uuid') })
  ),
});
