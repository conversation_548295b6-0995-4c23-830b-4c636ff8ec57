import z from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';

export const manageSubcategoryInDisciplineSchema = z.object({
  categoryId: z
    .string({
      required_error: errorMessages.required('categoryId'),
      invalid_type_error: errorMessages.invalidType('categoryId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('categoryId', 'uuid') }),
  subcategoryId: z
    .string({
      required_error: errorMessages.required('subcategoryId'),
      invalid_type_error: errorMessages.invalidType('subcategoryId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('subcategoryId', 'uuid') }),
  isChecked: z.boolean({
    required_error: errorMessages.required('isChecked'),
    invalid_type_error: errorMessages.invalidType('isChecked', 'boolean'),
  }),
});
