import { z } from 'zod';

export const createDomainSchema = z.object({
  domain: z
    .string()
    .min(1, 'Domínio é obrigatório')
    .regex(
      /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/,
      'Domínio inválido'
    ),
  customerId: z.string().uuid().optional(),
});

export const updateDomainStatusSchema = z.object({
  status: z.enum(['pending', 'validating', 'validated', 'cloudfront_created', 'error']),
  error_message: z.string().optional(),
  cloudfront_distribution_id: z.string().optional(),
  cloudfront_distribution_url: z.string().optional(),
});

export type TCreateDomainSchema = z.infer<typeof createDomainSchema>;
