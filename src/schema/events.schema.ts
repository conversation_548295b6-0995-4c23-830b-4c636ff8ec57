import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';

export const createSubdomainSchema = z.object({
  subdomain: z.string({
    required_error: errorMessages.required('subdomain'),
    invalid_type_error: errorMessages.invalidType('subdomain', 'string'),
  }),
  customerName: z.string({
    required_error: errorMessages.required('customerName'),
    invalid_type_error: errorMessages.invalidType('customerName', 'string'),
  }),
  email: z
    .string({
      required_error: errorMessages.required('email'),
      invalid_type_error: errorMessages.invalidType('email', 'string'),
    })
    .email({ message: errorMessages.invalidType('email', 'email') }),
  firstName: z.string({
    required_error: errorMessages.required('firstName'),
    invalid_type_error: errorMessages.invalidType('firstName', 'string'),
  }),
  lastName: z.string({
    required_error: errorMessages.required('lastName'),
    invalid_type_error: errorMessages.invalidType('lastName', 'string'),
  }),
  cpf: z.string({
    required_error: errorMessages.required('cpf'),
    invalid_type_error: errorMessages.invalidType('cpf', 'string'),
  }),
  birthDate: z.string({
    required_error: errorMessages.required('birthDate'),
    invalid_type_error: errorMessages.invalidType('birthDate', 'string'),
  }),
  gender: z.string({
    required_error: errorMessages.required('gender'),
    invalid_type_error: errorMessages.invalidType('gender', 'string'),
  }),
  phoneNumber: z.string({
    required_error: errorMessages.required('phoneNumber'),
    invalid_type_error: errorMessages.invalidType('phoneNumber', 'string'),
  }),
});
