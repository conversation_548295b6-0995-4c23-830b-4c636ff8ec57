import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';
import { ExamSessionActionEnum, ExamsStatusEnum } from '../model/enums/examsStatus.enum';
import { OrderDirection } from '../services/sorting.service';
import { customerIdSchema } from './customers.schema';

export const examIdSchema = z.object({
  examId: z
    .string({
      required_error: errorMessages.required('examId'),
      invalid_type_error: errorMessages.invalidType('examId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('examId', 'uuid') }),
});

export const createExamSchema = z.object({
  name: z.string({
    required_error: errorMessages.required('name'),
    invalid_type_error: errorMessages.invalidType('name', 'string'),
  }),
  published: z
    .boolean({
      message: errorMessages.invalidType('published', 'boolean'),
    })
    .optional(),
  status: z
    .nativeEnum(ExamsStatusEnum, {
      message: errorMessages.invalidEnum('status', [
        ExamsStatusEnum.DRAFT,
        ExamsStatusEnum.PUBLISHED,
        ExamsStatusEnum.INREVIEW,
      ]),
    })
    .optional(),
  examTime: z
    .number({
      message: errorMessages.invalidType('examTime', 'number'),
    })
    .int()
    .positive()
    .or(z.null())
    .optional()
    .describe('Duração do exame em minutos (se não informado, o exame terá duração ilimitada)'),
  year: z.number().int().positive().or(z.null()).optional(),
  institution: z
    .string({
      invalid_type_error: errorMessages.invalidType('institution', 'string'),
    })
    .or(z.null())
    .optional(),
});

export const listExamsSchema = z.object({
  search: z
    .string({
      invalid_type_error: errorMessages.invalidType('search', 'string'),
    })
    .optional(),
  orderByColumn: z
    .enum(['id', 'name', 'created_at', 'updated_at', 'year', 'institution', 'status'], {
      message: errorMessages.invalidEnum('orderByColumn', [
        'id',
        'name',
        'created_at',
        'updated_at',
        'year',
        'institution',
        'status',
      ]),
    })
    .optional(),
  orderDirection: z
    .enum(['asc', 'desc'], {
      message: errorMessages.invalidEnum('orderDirection', ['asc', 'desc']),
    })
    .transform((val) => (val === 'asc' ? OrderDirection.ASC : OrderDirection.DESC))
    .optional(),
});

export const updateExamSchema = z.object({
  name: z.string().optional(),
  institutionId: z
    .string({
      invalid_type_error: errorMessages.invalidType('institutionId', 'string'),
    })
    .uuid({
      message: errorMessages.invalidType('institutionId', 'uuid'),
    })
    .or(z.null())
    .optional(),
  year: z.number().or(z.null()).optional(),
  published: z.boolean().optional(),
  status: z
    .enum([ExamsStatusEnum.DRAFT, ExamsStatusEnum.PUBLISHED, ExamsStatusEnum.INREVIEW])
    .optional(),
  examTime: z.number().or(z.null()).optional(),
});

export const listPublishedExamsFiltersSchema = z.object({
  courseId: z
    .string({
      required_error: errorMessages.required('courseId'),
      invalid_type_error: errorMessages.invalidType('courseId', 'string'),
    })
    .uuid({
      message: errorMessages.invalidType('courseId', 'uuid'),
    }),
  search: z
    .string({
      invalid_type_error: errorMessages.invalidType('search', 'string'),
    })
    .optional(),
  institution: z
    .string({
      invalid_type_error: errorMessages.invalidType('institution', 'string'),
    })
    .optional(),
  year: z.coerce
    .number({
      invalid_type_error: errorMessages.invalidType('year', 'number'),
    })
    .int()
    .positive()
    .optional(),
  page: z.coerce
    .number({
      invalid_type_error: errorMessages.invalidType('page', 'number'),
    })
    .int()
    .positive()
    .optional(),
  limit: z.coerce
    .number({
      invalid_type_error: errorMessages.invalidType('limit', 'number'),
    })
    .int()
    .positive()
    .optional(),
  orderByColumn: z
    .enum(['name', 'year', 'institution', 'totalQuestions'], {
      message: errorMessages.invalidEnum('orderByColumn', [
        'name',
        'year',
        'institution',
        'totalQuestions',
      ]),
    })
    .optional(),
  orderDirection: z
    .enum(['asc', 'desc'], {
      message: errorMessages.invalidEnum('orderDirection', ['asc', 'desc']),
    })
    .transform((val) => (val === 'asc' ? OrderDirection.ASC : OrderDirection.DESC))
    .optional(),
});

export const listExamsFiltersSchema = z.object({
  courseId: z
    .string({
      required_error: errorMessages.required('courseId'),
      invalid_type_error: errorMessages.invalidType('courseId', 'string'),
    })
    .uuid({
      message: errorMessages.invalidType('courseId', 'uuid'),
    }),
});

export type CreateExamInput = z.infer<typeof createExamSchema & typeof customerIdSchema>;
export type ListExamsQueryInput = z.infer<typeof listExamsSchema>;
export type UpdateExamInput = z.infer<typeof updateExamSchema>;
export type ListPublishedExamsFiltersInput = z.infer<
  typeof listPublishedExamsFiltersSchema & typeof customerIdSchema
>;
export type ListExamsFiltersInput = z.infer<
  typeof listExamsFiltersSchema & typeof customerIdSchema
>;

export const examSessionActionParamsSchema = z.object({
  examAccessId: z
    .string({
      required_error: errorMessages.required('examAccessId'),
      invalid_type_error: errorMessages.invalidType('examAccessId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('examAccessId', 'uuid') }),
});

export const examSessionActionBodySchema = z.object({
  action: z.enum([ExamSessionActionEnum.PAUSE, ExamSessionActionEnum.RESUME], {
    required_error: errorMessages.required('action'),
    invalid_type_error: errorMessages.invalidType('action', 'string'),
  }),
});

export type ExamSessionActionPayload = z.infer<
  typeof examSessionActionBodySchema & typeof examSessionActionParamsSchema
>;

export const finishExamParamsSchema = z.object({
  examAccessId: z
    .string({
      required_error: errorMessages.required('examAccessId'),
      invalid_type_error: errorMessages.invalidType('examAccessId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('examAccessId', 'uuid') }),
});

export const externalListExamsSchema = z.object({
  search: z.string().optional(),
  orderByColumn: z.enum(['name', 'created_at', 'updated_at']).optional(),
  orderDirection: z.enum(['asc', 'desc']).optional(),
  customerId: z.string({
    required_error: errorMessages.required('customerId'),
    invalid_type_error: errorMessages.invalidType('customerId', 'string'),
  }),
});

export type ExternalListExamsInput = z.infer<typeof externalListExamsSchema>;

export const listUserExamsSchema = z.object({
  courseId: z
    .string({
      required_error: errorMessages.required('courseId'),
      invalid_type_error: errorMessages.invalidType('courseId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('courseId', 'uuid') }),
});
