import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';
import { alternativeIdSchema } from './alternatives.schema';

export const examAccessIdSchema = z.object({
  examAccessId: z
    .string({
      required_error: errorMessages.required('examAccessId'),
      invalid_type_error: errorMessages.invalidType('examAccessId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('examAccessId', 'uuid') }),
});

export const createExamAccessSchema = z.object({
  examId: z
    .string({
      required_error: errorMessages.required('examId'),
      invalid_type_error: errorMessages.invalidType('examId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('examId', 'uuid') }),
  userId: z
    .string({
      required_error: errorMessages.required('userId'),
      invalid_type_error: errorMessages.invalidType('userId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('userId', 'uuid') }),
  timeLimit: z
    .number({
      invalid_type_error: errorMessages.invalidType('timeLimit', 'number'),
    })
    .int()
    .positive()
    .optional(),
});

export type ExamAccessIdParam = z.infer<typeof examAccessIdSchema>;
export type CreateExamAccessDTO = z.infer<typeof createExamAccessSchema>;

export const answerExamQuestionParamsSchema = z.object({
  examAccessId: z
    .string({
      required_error: errorMessages.required('examAccessId'),
      invalid_type_error: errorMessages.invalidType('examAccessId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('examAccessId', 'uuid') }),
  questionId: z
    .string({
      required_error: errorMessages.required('questionId'),
      invalid_type_error: errorMessages.invalidType('questionId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('questionId', 'uuid') }),
});

export type AnswerExamQuestionInput = z.infer<
  typeof answerExamQuestionParamsSchema & typeof alternativeIdSchema
>;
