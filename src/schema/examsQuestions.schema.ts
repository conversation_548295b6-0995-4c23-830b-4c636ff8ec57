import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';

export const linkQuestionsToExamBodySchema = z.object({
  questionIds: z
    .array(z.string().uuid({ message: errorMessages.invalidType('questionIds', 'uuid') }))
    .min(1, { message: 'É necessário fornecer pelo menos uma questão para vincular' }),
});

export const reorderExamQuestionsSchema = z.object({
  questions: z
    .array(
      z.object({
        questionId: z.string().uuid(),
        order: z.number().int().positive(),
      })
    )
    .min(1),
});
