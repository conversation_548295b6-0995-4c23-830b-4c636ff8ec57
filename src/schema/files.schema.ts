import { z } from 'zod';

export const uploadFileSchema = z.object({
  version: z.string().optional(),
  type: z.string(),
  fileName: z.string(),
});

export type UploadFileDTO = z.infer<typeof uploadFileSchema>;

export const getSignedFileUrlSchema = z.object({
  fileId: z.string().uuid('ID do arquivo deve ser um UUID válido'),
});

export type TGetSignedFileUrlSchema = z.infer<typeof getSignedFileUrlSchema>;
