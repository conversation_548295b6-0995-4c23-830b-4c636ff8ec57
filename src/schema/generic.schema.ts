import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';

export const genericIdSchema = z.object({
  id: z
    .string({
      required_error: errorMessages.required('id'),
      invalid_type_error: errorMessages.invalidType('id', 'string'),
    })
    .uuid(),
});
export const providedTokenSchema = z.string({
  required_error: errorMessages.required('providedToken'),
  invalid_type_error: errorMessages.invalidType('providedToken', 'string'),
});

export const genericStringIdSchema = z.object({
  id: z.string({
    required_error: errorMessages.required('id'),
    invalid_type_error: errorMessages.invalidType('id', 'string'),
  }),
});

export const genericStringCustomerIdchema = z.object({
  customerId: z.string({
    required_error: errorMessages.required('customerId'),
    invalid_type_error: errorMessages.invalidType('customerId', 'string'),
  }),
});

export const genericSchemaCustomerIdAndId = z.object({
  customerId: z
    .string({
      required_error: errorMessages.required('customerId'),
      invalid_type_error: errorMessages.invalidType('customerId', 'string'),
    })
    .uuid(),
  id: z
    .string({
      required_error: errorMessages.required('id'),
      invalid_type_error: errorMessages.invalidType('id', 'string'),
    })
    .uuid(),
});

export type CustomerUserPayload = z.infer<typeof genericSchemaCustomerIdAndId>;

export const genericSchemaQuestionGroupId = z.object({
  questionGroupId: z
    .string({
      required_error: errorMessages.required('questionGroupId'),
      invalid_type_error: errorMessages.invalidType('questionGroupId', 'string'),
    })
    .uuid(),
});

export const genericSchemaSimulatedAccessId = z.object({
  simulatedAccessId: z
    .string({
      required_error: errorMessages.required('simulatedAccessId'),
      invalid_type_error: errorMessages.invalidType('simulatedAccessId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('simulatedAccessId', 'uuid') }),
});

export const genericSchemaToken = z.object({
  token: z.string({
    required_error: errorMessages.required('token'),
    invalid_type_error: errorMessages.invalidType('token', 'string'),
  }),
});
