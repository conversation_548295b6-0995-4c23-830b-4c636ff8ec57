import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';
import { customerIdSchema } from './customers.schema';

export const createInstitutionSchema = z.object({
  name: z
    .string({
      required_error: errorMessages.required('name'),
      invalid_type_error: errorMessages.invalidType('name', 'string'),
    })
    .trim(),
  acronym: z
    .string({
      invalid_type_error: errorMessages.invalidType('acronym', 'string'),
    })
    .trim()
    .optional(),
});

export type CreateInstitutionDTO = z.infer<
  typeof createInstitutionSchema & typeof customerIdSchema
>;

export const updateInstitutionSchema = z.object({
  name: z
    .string({
      invalid_type_error: errorMessages.invalidType('name', 'string'),
    })
    .trim()
    .optional(),
  acronym: z
    .string({
      invalid_type_error: errorMessages.invalidType('acronym', 'string'),
    })
    .trim()
    .optional(),
});

export type UpdateInstitutionDTO = z.infer<
  typeof updateInstitutionSchema & typeof customerIdSchema
>;

export const listInstitutionsPublicSchema = z.object({
  page: z.coerce.number().default(1),
  limit: z.coerce.number().default(10),
  search: z
    .string({
      invalid_type_error: errorMessages.invalidType('search', 'string'),
    })
    .trim()
    .optional(),
  listAll: z.coerce.boolean().default(false),
  orderByColumn: z
    .enum(['name', 'acronym', 'created_at', 'updated_at'], {
      message: errorMessages.invalidEnum('orderByColumn', [
        'name',
        'acronym',
        'created_at',
        'updated_at',
      ]),
    })
    .default('name'),
  orderDirection: z
    .enum(['asc', 'desc'], {
      message: errorMessages.invalidEnum('orderDirection', ['asc', 'desc']),
    })
    .default('asc'),
});

export type ListInstitutionsPublicDTO = z.infer<typeof listInstitutionsPublicSchema>;
