import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';
import { LessonTypeEnum } from '../model/enums/LessonTypeEnum.enum';
import { StatusLessonEnum } from '../model/enums/status.enum';
import { genericIdSchema } from './generic.schema';

export const createLessonSchema = z.object({
  moduleId: z
    .string({
      required_error: errorMessages.required('<PERSON>ódu<PERSON>'),
      invalid_type_error: errorMessages.invalidType('Módulo', 'string'),
    })
    .uuid(),
  title: z.string().optional(),
  descriptionLessons: z.string().optional().nullable(),
  lessonUrl: z.string().optional(),
  duration: z.coerce.number().optional(),
  orderBy: z.coerce.number().optional(),
  questionGroupId: z.string().optional().nullable(),
  published: z.coerce.boolean().optional(),
  status: z
    .enum(Object.values(StatusLessonEnum) as [string, ...string[]])
    .optional()
    .default(StatusLessonEnum.DRAFT),
  lessonType: z
    .enum(Object.values(LessonTypeEnum) as [string, ...string[]])
    .optional()
    .default(LessonTypeEnum.VIDEO),
});

export type TCreateLessonSchema = z.infer<typeof createLessonSchema>;

export const updateLessonSchema = z.object({
  moduleId: z.string().uuid().optional(),
  title: z.string().optional(),
  descriptionLessons: z.string().optional().nullable(),
  lessonUrl: z.string().optional(),
  duration: z.coerce.number().optional(),
  orderBy: z.coerce.number().optional(),
  questionGroupId: z.string().optional().nullable(),
  published: z.coerce.boolean().optional(),
  status: z.enum(Object.values(StatusLessonEnum) as [string, ...string[]]).optional(),
  lessonType: z.enum(Object.values(LessonTypeEnum) as [string, ...string[]]).optional(),
  removeFiles: z.array(z.string()).optional(),
});

export type TUpdateLessonSchema = z.infer<typeof updateLessonSchema> &
  z.infer<typeof genericIdSchema>;

export const reorderLessonsSchema = z.object({
  moduleId: z.string().uuid(),
  lessons: z
    .array(
      z.object({
        lessonId: z.string().uuid(),
        orderBy: z.number().int().min(1),
      })
    )
    .min(1),
});

export type TReorderLessonsSchema = z.infer<typeof reorderLessonsSchema>;
