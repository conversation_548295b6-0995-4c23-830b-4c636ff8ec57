import { z } from 'zod';

import { StatusModuleEnum } from '../model/enums/status.enum';
import { customerIdSchema } from './customers.schema';
import { genericIdSchema } from './generic.schema';

export const createModuleSchema = z.object({
  courseId: z.string().uuid(),
  modules: z.array(
    z.object({
      id: z.string().uuid().optional(),
      name: z.string(),
      description: z.string().nullable().optional(),
      status: z.string().optional(),
      classes: z
        .array(
          z.object({
            id: z.string().uuid().optional(),
            name: z.string(),
            description: z.string().nullable().optional(),
            video: z.string().nullable().optional(),
            duration: z.union([z.string().nullable().optional(), z.number().nullable().optional()]),
            questionGroupId: z.preprocess(
              (val) => (val === '' ? null : val),
              z.string().uuid().nullable().optional()
            ),
          })
        )
        .optional(),
    })
  ),
});

export const createSimpleModuleSchema = z.object({
  courseId: z.string(),
  name: z.string().optional(),
  descriptionModule: z.string().optional().nullable(),
  orderBy: z.number().optional(),
  published: z.boolean().optional(),
  status: z
    .enum(Object.values(StatusModuleEnum) as [string, ...string[]])
    .optional()
    .default(StatusModuleEnum.DRAFT),
});

export type TCreateSimpleModuleSchema = z.infer<typeof createSimpleModuleSchema> &
  z.infer<typeof customerIdSchema>;

export const updateModuleSchema = z.object({
  name: z.string().optional(),
  descriptionModule: z.string().optional().nullable(),
  orderBy: z.number().optional(),
  published: z.boolean().optional(),
  status: z.string().optional(),
});

export type TUpdateModuleSchema = z.infer<typeof updateModuleSchema> &
  z.infer<typeof genericIdSchema>;

export const reorderModulesSchema = z.object({
  courseId: z.string().uuid(),
  modules: z
    .array(
      z.object({
        moduleId: z.string().uuid(),
        orderBy: z.number().int().min(1),
      })
    )
    .min(1),
});

export type TReorderModulesSchema = z.infer<typeof reorderModulesSchema>;
