import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';
import {
  DifficultyEnum,
  QuestionOrderByColumnEnum,
  QuestionStatusEnum,
} from '../model/enums/questions.enum';
import { alternativeSchemaWithoutQuestionId } from './alternatives.schema';
import { customerIdSchema } from './customers.schema';
import { genericIdSchema } from './generic.schema';
import { userIdRequeridSchema } from './users.schema';

export const createQuestionBodySchema = z.object({
  title: z.string({
    required_error: errorMessages.required('title'),
    invalid_type_error: errorMessages.invalidType('title', 'string'),
  }),
  description: z
    .string({
      required_error: errorMessages.required('description'),
      invalid_type_error: errorMessages.invalidType('description', 'string'),
    })
    .optional(),
  difficulty: z
    .nativeEnum(DifficultyEnum, {
      message: errorMessages.invalidEnum('difficulty', [
        DifficultyEnum.Easy,
        DifficultyEnum.Medium,
        DifficultyEnum.Hard,
      ]),
    })
    .optional(),
  imageUrl: z
    .string({
      invalid_type_error: errorMessages.invalidType('imageUrl', 'string'),
    })
    .trim()
    .optional(),
  explanationVideo: z
    .string({
      invalid_type_error: errorMessages.invalidType('explanationVideo', 'string'),
    })
    .optional(),
  explanationText: z
    .string({
      required_error: errorMessages.required('explanationText'),
      invalid_type_error: errorMessages.invalidType('explanationText', 'string'),
    })
    .optional(),
  explanationImage: z
    .string({
      invalid_type_error: errorMessages.invalidType('explanationImage', 'string'),
    })
    .optional(),
  published: z.boolean({}).optional(),
  correctText: z.string().optional(),
  disciplineId: z
    .string({
      required_error: errorMessages.required('disciplineId'),
      invalid_type_error: errorMessages.invalidType('disciplineId', 'string'),
    })
    .uuid()
    .optional(),
  questionTypeId: z.string().uuid().optional(),
  categoryId: z.string().uuid().optional(),
  subcategoryId: z.string().uuid().optional(),
  reference: z.string().optional().optional(),
  institution: z.string().optional().optional(),
  year: z
    .union([
      z
        .string()
        .refine((val) => val.trim() === '', {
          message: 'O campo ano da questão deve ser um número válido.',
        })
        .transform(() => null),
      z.coerce.number().int().nonnegative().optional(),
    ])
    .optional(),
  alternatives: z.array(alternativeSchemaWithoutQuestionId).optional(),
  status: z
    .enum([QuestionStatusEnum.Draft, QuestionStatusEnum.InReview, QuestionStatusEnum.Published])
    .optional(),
});

export const listPaginatedQuestionsSchema = z.object({
  questionGroupId: z.string().uuid().optional(),
  search: z
    .string({
      invalid_type_error: errorMessages.invalidType('search', 'string'),
    })
    .trim()
    .optional(),
  orderByColumn: z
    .enum(
      [
        QuestionOrderByColumnEnum.Id,
        QuestionOrderByColumnEnum.Title,
        QuestionOrderByColumnEnum.Description,
        QuestionOrderByColumnEnum.CreatedAt,
        QuestionOrderByColumnEnum.UpdatedAt,
        QuestionOrderByColumnEnum.DeletedAt,
        QuestionOrderByColumnEnum.Difficulty,
        QuestionOrderByColumnEnum.Status,
      ],
      {
        message: errorMessages.invalidEnum('orderByColumn', [
          QuestionOrderByColumnEnum.Id,
          QuestionOrderByColumnEnum.Title,
          QuestionOrderByColumnEnum.Description,
          QuestionOrderByColumnEnum.CreatedAt,
          QuestionOrderByColumnEnum.UpdatedAt,
          QuestionOrderByColumnEnum.DeletedAt,
          QuestionOrderByColumnEnum.Difficulty,
          QuestionOrderByColumnEnum.Status,
        ]),
      }
    )
    .optional(),
  orderDirection: z
    .enum(['asc', 'desc'], {
      message: errorMessages.invalidEnum('orderDirection', ['asc', 'desc']),
    })
    .optional(),
  page: z.coerce.number().default(1),
  limit: z.coerce.number().default(10),
  difficulty: z
    .array(z.enum([DifficultyEnum.Easy, DifficultyEnum.Medium, DifficultyEnum.Hard]), {
      message: errorMessages.invalidEnum('difficulty', [
        DifficultyEnum.Easy,
        DifficultyEnum.Medium,
        DifficultyEnum.Hard,
      ]),
    })
    .optional(),
  status: z
    .array(
      z.enum([QuestionStatusEnum.Draft, QuestionStatusEnum.InReview, QuestionStatusEnum.Published]),
      {
        message: errorMessages.invalidEnum('status', [
          QuestionStatusEnum.Draft,
          QuestionStatusEnum.InReview,
          QuestionStatusEnum.Published,
        ]),
      }
    )
    .optional(),
  disciplineIds: z.array(z.string().uuid()).optional(),
  categoryIds: z.array(z.string().uuid()).optional(),
  subcategoryIds: z.array(z.string().uuid()).optional(),
  examId: z.string().uuid().optional(),
  allQuestions: z.string(z.boolean().default(false)).optional(),
});

export const updateQuestionBodySchema = z.object({
  title: z.string(),
  description: z.string().optional(),
  difficulty: z
    .nativeEnum(DifficultyEnum, {
      message: errorMessages.invalidEnum('difficulty', [
        DifficultyEnum.Easy,
        DifficultyEnum.Medium,
        DifficultyEnum.Hard,
      ]),
    })
    .optional(),
  imageUrl: z.string().optional(),
  explanationVideo: z.string().optional(),
  explanationText: z.string().optional(),
  explanationImage: z.string().optional(),
  published: z.boolean(),
  status: z
    .enum([QuestionStatusEnum.Draft, QuestionStatusEnum.InReview, QuestionStatusEnum.Published], {
      message: errorMessages.invalidEnum('status', [
        QuestionStatusEnum.Draft,
        QuestionStatusEnum.InReview,
        QuestionStatusEnum.Published,
      ]),
    })
    .optional(),
  correctText: z.string().optional(),
  questionTypeId: z.string().uuid().optional(),
  disciplineId: z.string().uuid().optional(),
  categoryId: z.string().uuid().optional(),
  subcategoryId: z.string().uuid().optional(),
  reference: z.string().optional(),
  institution: z.string().optional(),
  year: z
    .union([
      z
        .string()
        .refine((val) => val.trim() === '', {
          message: 'O campo ano da questão deve ser um número válido.',
        })
        .transform(() => null),
      z.coerce.number().int().nonnegative().optional(),
    ])
    .optional(),
  alternatives: z
    .array(
      z.object({
        id: z
          .string()
          .optional()
          .transform((id) => id ?? ''),
        correct: z.boolean(),
        option: z.string(),
        description: z.string(),
      })
    )
    .optional(),
});

export const fileKeySchema = z.object({
  fileKey: z.string({
    required_error: errorMessages.required('fileKey'),
    invalid_type_error: errorMessages.invalidType('fileKey', 'string'),
  }),
});

export const listQuestionInGroupQuerySchema = z.object({
  difficulty: z
    .array(
      z.enum([DifficultyEnum.Easy, DifficultyEnum.Medium, DifficultyEnum.Hard], {
        errorMap: () => ({
          message: errorMessages.invalidEnum('difficulty', [
            DifficultyEnum.Easy,
            DifficultyEnum.Medium,
            DifficultyEnum.Hard,
          ]),
        }),
      })
    )
    .optional()
    .transform((val) => {
      if (!val) return [];
      return val.map((d) => d.toLowerCase() as DifficultyEnum);
    }),
  disciplineIds: z
    .array(
      z.string().uuid({
        message: errorMessages.invalidType('disciplineIds', 'uuid'),
      })
    )
    .optional()
    .transform((val) => val || []),
  categoryIds: z
    .array(
      z.string().uuid({
        message: errorMessages.invalidType('categoryIds', 'uuid'),
      })
    )
    .optional()
    .transform((val) => val || []),
  subcategoryIds: z
    .array(
      z.string().uuid({
        message: errorMessages.invalidType('subcategoryIds', 'uuid'),
      })
    )
    .optional()
    .transform((val) => val || []),
});

export type ListQuestionInQuestionGroupQueryDTO = z.infer<typeof listQuestionInGroupQuerySchema>;

export type ListQuestionInQuestionGroupParamsDTO = z.infer<typeof genericIdSchema>;

export const questionHighlightsSchema = z.object({
  saved: z.boolean().optional(),
  highlightDescription: z.array(z.unknown()).optional(),
  highlightExplanationText: z.array(z.unknown()).optional(),
  alternativesLineThrough: z.array(z.string().uuid()).optional(),
  simulatedId: z.string().uuid().nullable().optional(),
  simulatedAccessId: z.string().uuid().nullable().optional(),
  examId: z.string().uuid().nullable().optional(),
  examAccessId: z.string().uuid().nullable().optional(),
});

export const questionIdSchema = z.object({
  questionId: z.string().uuid(),
});

export type QuestionHighlightsRequestDTO = z.infer<
  typeof questionHighlightsSchema &
    typeof questionIdSchema &
    typeof userIdRequeridSchema &
    typeof customerIdSchema
>;
