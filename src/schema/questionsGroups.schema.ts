import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';

export const createQuestionGroupSchema = z.object({
  name: z
    .string({
      required_error: errorMessages.required('name'),
      invalid_type_error: errorMessages.invalidType('name', 'string'),
    })
    .trim(),
});

export const updateQuestionGroupSchema = z.object({
  name: z
    .string({
      required_error: errorMessages.required('name'),
      invalid_type_error: errorMessages.invalidType('name', 'string'),
    })
    .trim(),
});

export const listQuestionsGroupsSchema = z.object({
  search: z
    .string({
      invalid_type_error: errorMessages.invalidType('search', 'string'),
    })
    .trim()
    .optional(),
  orderByColumn: z
    .enum(['id', 'name', 'created_at', 'updated_at', 'deleted_at', 'customer_id'], {
      message: errorMessages.invalidEnum('orderByColumn', [
        'id',
        'name',
        'created_at',
        'updated_at',
        'deleted_at',
        'customer_id',
      ]),
    })
    .optional(),
  orderDirection: z
    .enum(['asc', 'desc'], {
      message: errorMessages.invalidEnum('orderDirection', ['asc', 'desc']),
    })
    .optional(),
});

export const ExternalListQuestionsGroupsSchema = z.object({
  customerId: z.string({
    required_error: 'customerId é obrigatório',
    invalid_type_error: 'customerId deve ser uma string',
  }),
  ...listQuestionsGroupsSchema.shape,
});

export const questionIdSchema = z.object({
  questionId: z
    .string({
      required_error: errorMessages.required('questionId'),
      invalid_type_error: errorMessages.invalidType('questionId', 'string'),
    })
    .uuid(),
});

export const questionGroupIdSchema = z.object({
  questionGroupId: z
    .string({
      required_error: errorMessages.required('questionGroupId'),
      invalid_type_error: errorMessages.invalidType('questionGroupId', 'string'),
    })
    .uuid(),
});
