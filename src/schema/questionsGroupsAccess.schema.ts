import z from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';

export const questionGroupAccessIdentifierSchema = z.object({
  questionGroupId: z
    .string({
      required_error: errorMessages.required('questionGroupId'),
      invalid_type_error: errorMessages.invalidType('questionGroupId', 'string'),
    })
    .uuid(),
  questionId: z
    .string({
      required_error: errorMessages.required('questionId'),
      invalid_type_error: errorMessages.invalidType('questionId', 'string'),
    })
    .uuid(),
});
