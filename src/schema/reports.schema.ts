import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';
import { ReportStatus } from '../model/enums/reportStatus.enum';

export const createReportSchema = z.object({
  description: z.string().optional(),
  category: z.string({
    required_error: errorMessages.required('category'),
    invalid_type_error: errorMessages.invalidType('category', 'string'),
  }),
  questionId: z
    .string({
      required_error: errorMessages.required('questionId'),
      invalid_type_error: errorMessages.invalidType('questionId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('questionId', 'uuid') }),
  studyMode: z.string({
    required_error: errorMessages.required('studyMode'),
    invalid_type_error: errorMessages.invalidType('studyMode', 'string'),
  }),
});

export const listReportsSchema = z.object({
  userId: z.string().uuid().optional(),
  questionId: z.string().uuid().optional(),
  examId: z.string().uuid().optional(),
  simulatedAccessId: z.string().uuid().optional(),
  questionGroupId: z.string().uuid().optional(),
  status: z.array(z.nativeEnum(ReportStatus)).optional(),
  orderByColumn: z.string().optional().default('created_at'),
  orderDirection: z.enum(['asc', 'desc']).optional().default('desc'),
});

export type CreateReportInput = z.infer<typeof createReportSchema>;
