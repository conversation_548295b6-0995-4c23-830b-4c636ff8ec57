import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';

export const finishSimulatedParamsSchema = z.object({
  simulatedAccessId: z
    .string({
      required_error: errorMessages.required('simulatedAccessId'),
      invalid_type_error: errorMessages.invalidType('simulatedAccessId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('simulatedAccessId', 'uuid') }),
});
