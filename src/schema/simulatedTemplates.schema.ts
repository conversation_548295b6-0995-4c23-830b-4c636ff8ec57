import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';
import { DifficultyEnum } from '../model/enums/questions.enum';

export const createSimulatedTemplateSchema = z.object({
  difficulty: z
    .array(
      z.enum([DifficultyEnum.Easy, DifficultyEnum.Medium, DifficultyEnum.Hard], {
        errorMap: () => ({
          message: errorMessages.invalidEnum('difficulty', [
            DifficultyEnum.Easy,
            DifficultyEnum.Medium,
            DifficultyEnum.Hard,
          ]),
        }),
      })
    )
    .optional()
    .transform((val) => {
      if (!val) return [];
      return val.map((d) => d.toLowerCase() as DifficultyEnum);
    }),
  disciplineIds: z
    .array(
      z.string().uuid({
        message: errorMessages.invalidType('disciplineIds', 'uuid'),
      })
    )
    .optional()
    .transform((val) => val || []),
  categoryIds: z
    .array(
      z.string().uuid({
        message: errorMessages.invalidType('categoryIds', 'uuid'),
      })
    )
    .optional()
    .transform((val) => val || []),
  subcategoryIds: z
    .array(
      z.string().uuid({
        message: errorMessages.invalidType('subcategoryIds', 'uuid'),
      })
    )
    .optional()
    .transform((val) => val || []),
  quantity: z
    .number({
      message: errorMessages.invalidType('quantity', 'number'),
    })
    .int()
    .positive()
    .optional(),
  questionGroupId: z
    .string({
      message: errorMessages.invalidType('questionGroupId', 'uuid'),
      required_error: errorMessages.required('questionGroupId'),
    })
    .uuid(),
  name: z.string({
    required_error: errorMessages.required('name'),
    invalid_type_error: errorMessages.invalidType('name', 'string'),
  }),
  isFreeStudy: z.boolean().optional().default(false),
  timeLimit: z
    .number()
    .int()
    .positive({ message: 'O tempo limite deve ser um número positivo' })
    .min(60, { message: 'O tempo mínimo para o simulado é de 1 minuto' })
    .max(60 * 60 * 24, { message: 'O tempo máximo para o simulado é de 24 horas ' })
    .optional()
    .describe('Tempo limite em segundos para completar o simulado'),
});

export type CreateSimulatedTemplatePayload = z.infer<typeof createSimulatedTemplateSchema>;
export interface QuestionFiltersPayload extends Omit<CreateSimulatedTemplatePayload, 'name'> {
  customerId: string;
}
