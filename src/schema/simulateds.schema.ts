import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';
import { SimulatedStatusEnum } from '../model/enums/simulatedStatus.enum';
import { StatusSimulatedEnum } from '../model/enums/statusSimulated.enum';
import { customerIdSchema } from './customers.schema';
import { userIdRequeridSchema } from './users.schema';

export const finishSimulatedBodySchema = z.object({
  simulatedId: z
    .string({
      required_error: errorMessages.required('simulatedId'),
      invalid_type_error: errorMessages.invalidType('simulatedId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('simulatedId', 'uuid') }),
});

export const answerQuestionSimulatedSchema = z.object({
  alternativeId: z.string().uuid(),
});

export const pauseResumeSimulatedParamsSchema = z.object({
  simulatedAccessId: z
    .string({
      required_error: errorMessages.required('simulatedAccessId'),
      invalid_type_error: errorMessages.invalidType('simulatedAccessId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('simulatedAccessId', 'uuid') }),
});

export const pauseResumeSimulatedBodySchema = z.object({
  action: z.enum([StatusSimulatedEnum.PAUSE, StatusSimulatedEnum.START], {
    required_error: errorMessages.required('action'),
    invalid_type_error: errorMessages.invalidType('action', 'string'),
  }),
});

export type PauseResumeSimulatedPayload = z.infer<
  typeof pauseResumeSimulatedBodySchema &
    typeof pauseResumeSimulatedParamsSchema &
    typeof userIdRequeridSchema
>;

export const createExclusiveSimulatedSchema = z
  .object({
    name: z.string(),
    active: z.boolean().optional(),
    timeLimit: z.number().optional(),
    status: z.nativeEnum(SimulatedStatusEnum).default(SimulatedStatusEnum.DRAFT),
    startDate: z
      .string()
      .datetime()
      .transform((val) => new Date(val))
      .optional(),
    endDate: z
      .string()
      .datetime()
      .transform((val) => new Date(val))
      .optional(),
  })
  .refine(
    (data) => {
      if (!data.startDate || !data.endDate) return true;
      return data.startDate < data.endDate;
    },
    {
      message: 'A data de início deve ser anterior à data de término',
      path: ['endDate'],
    }
  );

export const updateExclusiveSimulatedSchema = z
  .object({
    name: z.string().optional(),
    active: z.boolean().optional(),
    simulatedTypeId: z.string().uuid().optional(),
    timeLimit: z.number().optional(),
    status: z.nativeEnum(SimulatedStatusEnum).optional(),
    startDate: z
      .string()
      .datetime()
      .transform((val) => new Date(val))
      .optional(),
    endDate: z
      .string()
      .datetime()
      .transform((val) => new Date(val))
      .optional(),
  })
  .refine(
    (data) => {
      if (!data.startDate || !data.endDate) return true;
      return data.startDate < data.endDate;
    },
    {
      message: 'A data de início deve ser anterior à data de término',
      path: ['endDate'],
    }
  );

export const listExclusiveSimulatedsPaginatedSchema = z.object({
  search: z.string().optional(),
  page: z.coerce.number().int().min(1).optional(),
  limit: z.coerce.number().int().min(1).max(100).optional(),
});

export type IListExclusiveSimulatedsPaginatedInput = z.infer<
  typeof listExclusiveSimulatedsPaginatedSchema & typeof customerIdSchema
>;

export const simulatedClassesParamsSchema = z.object({
  simulatedId: z
    .string({
      required_error: errorMessages.required('simulatedId'),
      invalid_type_error: errorMessages.invalidType('simulatedId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('simulatedId', 'uuid') }),
});

export const simulatedClassesBodySchema = z.object({
  classIds: z
    .array(
      z
        .string({
          required_error: errorMessages.required('classIds'),
          invalid_type_error: errorMessages.invalidType('classIds', 'string'),
        })
        .uuid({ message: errorMessages.invalidType('classIds', 'uuid') })
    )
    .optional()
    .default([]),
});

export type ISimulatedClassesInput = z.infer<
  typeof simulatedClassesParamsSchema & typeof simulatedClassesBodySchema
>;

export const linkQuestionsToSimulatedBodySchema = z.object({
  questionIds: z
    .array(z.string().uuid({ message: errorMessages.invalidType('questionIds', 'uuid') }))
    .min(1, { message: 'É necessário fornecer pelo menos uma questão para vincular' }),
});

export const linkQuestionsToSimulatedParamsSchema = z.object({
  simulatedId: z
    .string({
      required_error: errorMessages.required('simulatedId'),
      invalid_type_error: errorMessages.invalidType('simulatedId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('simulatedId', 'uuid') }),
});

export type ILinkQuestionsToSimulatedInput = z.infer<
  typeof linkQuestionsToSimulatedParamsSchema & typeof linkQuestionsToSimulatedBodySchema
>;

export const manageUserToExclusiveSimulatedBodySchema = z.object({
  data: z.array(
    z.object({
      classId: z.string().uuid({ message: errorMessages.invalidType('classId', 'uuid') }),
      users: z.array(z.string().uuid({ message: errorMessages.invalidType('userId', 'uuid') })),
    })
  ),
});

export const manageUserToExclusiveSimulatedParamsSchema = z.object({
  simulatedId: z.string().uuid({ message: errorMessages.invalidType('simulatedId', 'uuid') }),
});

export type IManageUserToExclusiveSimulatedInput = z.infer<
  typeof manageUserToExclusiveSimulatedParamsSchema &
    typeof manageUserToExclusiveSimulatedBodySchema
>;

export const reorderSimulatedQuestionsSchema = z.object({
  questions: z
    .array(
      z.object({
        questionId: z.string().uuid({ message: errorMessages.invalidType('questionId', 'uuid') }),
        order: z.number().int().min(1),
      })
    )
    .min(1, { message: 'É necessário fornecer pelo menos uma questão para reordenar' }),
});

export const simulatedIdSchema = z.object({
  simulatedId: z.string().uuid({ message: errorMessages.invalidType('simulatedId', 'uuid') }),
});

export const listUserExclusiveSimulatedsQuerySchema = z.object({
  courseId: z
    .string({
      invalid_type_error: errorMessages.invalidType('courseId', 'string'),
    })
    .uuid({
      message: errorMessages.invalidType('courseId', 'uuid'),
    })
    .optional(),
  isClosed: z.preprocess(
    (val) => (val === 'true' ? true : val === 'false' ? false : val),
    z.boolean().optional()
  ),
});

export type IListUserExclusiveSimulatedsQueryInput = z.infer<
  typeof listUserExclusiveSimulatedsQuerySchema &
    typeof customerIdSchema &
    typeof userIdRequeridSchema
>;

export const simulatedAccessIdSchema = z.object({
  simulatedAccessId: z
    .string()
    .uuid({ message: errorMessages.invalidType('simulatedAccessId', 'uuid') }),
});
