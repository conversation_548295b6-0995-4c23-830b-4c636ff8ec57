import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';

export const listStudentsParamsSchema = z.object({
  customerId: z.string().uuid({ message: 'customerId inválido' }),
});

export const listStudentsQuerySchema = z.object({
  orderByColumn: z.string().optional(),
  orderByDirection: z.enum(['asc', 'desc']).optional(),
  courseId: z.string().uuid().optional(),
});

export const addStudentsByEmailSchema = z.object({
  courseId: z
    .string({
      required_error: errorMessages.required('courseId'),
      invalid_type_error: errorMessages.invalidType('courseId', 'string'),
    })
    .uuid(),
  emails: z
    .array(
      z
        .string({
          required_error: errorMessages.required('email'),
          invalid_type_error: errorMessages.invalidType('email', 'string'),
        })
        .email(errorMessages.invalidType('email', 'email'))
    )
    .min(1, 'É necessário fornecer pelo menos um e-mail'),
  classId: z.string().uuid(),
});

export type AddStudentsByEmailInput = z.infer<typeof addStudentsByEmailSchema>;
