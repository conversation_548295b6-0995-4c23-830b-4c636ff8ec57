import { z } from 'zod';

export const updateTermSchema = z.object({
  active: z.coerce.boolean(),
});

export type UpdateTermInput = z.infer<typeof updateTermSchema>;

export const filterListTermsSchema = z.object({
  active: z.coerce.boolean().optional(),
  orderByColumn: z.enum(['name', 'created_at', 'updated_at', 'active']).optional(),
  orderByDirection: z.enum(['asc', 'desc']).optional(),
});

export type FilterListTermsInput = z.infer<typeof filterListTermsSchema>;
