import z from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';

export const uploadQuestionImageSchema = z.object({
  imageType: z
    .enum(['enunciado', 'explicacao'], {
      required_error: errorMessages.required('imageCategory'),
      invalid_type_error: errorMessages.invalidType('imageCategory', 'string'),
    })
    .optional()
    .describe(
      'Tipo de imagem: "enunciado" para descrição da questão ou "explicacao" para explicação.'
    ),

  previousImageKey: z
    .string({
      invalid_type_error: errorMessages.invalidType('previousImageKey', 'string'),
    })
    .optional()
    .describe('Chave da imagem anterior, caso a imagem seja substituída.'),

  codeQuestion: z
    .string({
      required_error: errorMessages.required('codeQuestion'),
    })
    .describe('Código único da questão associada à imagem.'),
});
