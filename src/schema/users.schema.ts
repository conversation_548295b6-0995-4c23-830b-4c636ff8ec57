import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';
import { RolesEnum } from '../model/enums/roles.enum';
import { customerIdSchema } from './customers.schema';

export const createExternalUserSchema = z.object({
  firstName: z.string({
    required_error: errorMessages.required('firstName'),
    invalid_type_error: errorMessages.invalidType('firstName', 'string'),
  }),
  lastName: z.string({
    required_error: errorMessages.required('lastName'),
    invalid_type_error: errorMessages.invalidType('lastName', 'string'),
  }),
  email: z
    .string({
      required_error: errorMessages.required('email'),
      invalid_type_error: errorMessages.invalidType('email', 'string'),
    })
    .email(),
  id: z.string({
    required_error: errorMessages.required('id'),
    invalid_type_error: errorMessages.invalidType('id', 'string'),
  }),
  customerId: z.string({
    required_error: errorMessages.required('customerId'),
    invalid_type_error: errorMessages.invalidType('customerId', 'string'),
  }),
  classIds: z.array(z.string()).optional(),
  role: z.nativeEnum(RolesEnum),
  status: z.string().optional(),
  createdBy: z.string().optional(),
});

export type CreateExternalUserDTO = z.infer<typeof createExternalUserSchema>;

export const updateExternalUserSchema = z.object({
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  email: z.string().email().optional(),
  phoneNumber: z.string().optional(),
  gender: z.string().optional(),
  birthDate: z.string().optional(),
  cpf: z.string().optional(),
  role: z.nativeEnum(RolesEnum).optional(),
  password: z.string().optional(),
  customerId: z.string(),
  classIds: z.array(z.string()).optional(), // Adicionado array de classIds
  status: z.string().optional(), // Status para as associações de turma
});

export type UpdateExternalUserDTO = z.infer<typeof updateExternalUserSchema>;

export const updateUserSchema = z.object({
  firstName: z
    .string({
      invalid_type_error: errorMessages.invalidType('firstName', 'string'),
    })
    .optional(),
  lastName: z.string().optional(),
  birthDate: z
    .string({
      invalid_type_error: errorMessages.invalidType('birthDate', 'string'),
    })
    .optional(),
  gender: z.string().optional(),
  phoneNumber: z.string().optional(),
  countryId: z.string().uuid().optional(),
});

export type UpdateUserDTO = z.infer<typeof updateUserSchema> & {
  userId: string;
};

export const userIdRequeridSchema = z.object({
  userId: z
    .string({
      required_error: errorMessages.required('userId'),
      invalid_type_error: errorMessages.invalidType('userId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('userId', 'uuid') }),
});

export const listUsersWithClassesSchema = z.object({
  search: z.string().optional(),
  page: z.coerce.number().int().min(1).optional().default(1),
  limit: z.coerce.number().int().min(1).max(100).optional().default(10),
  orderByColumn: z.string().optional(),
  orderByDirection: z.string().optional(),
  classId: z.string().optional(),
});

export type ListUsersWithClassesSchema = z.infer<
  typeof listUsersWithClassesSchema & typeof customerIdSchema
>;

export const createUserAdminSchema = z.object({
  email: z.string({
    required_error: errorMessages.required('email'),
    invalid_type_error: errorMessages.invalidType('email', 'string'),
  }),
  fullName: z.string({
    required_error: errorMessages.required('fullName'),
    invalid_type_error: errorMessages.invalidType('fullName', 'string'),
  }),
  accessLevelId: z.string({
    required_error: errorMessages.required('accessLevelId'),
    invalid_type_error: errorMessages.invalidType('accessLevelId', 'string'),
  }),
});

export type CreateUserAdminRequestDto = z.infer<typeof createUserAdminSchema>;

export const updateUserAdminSchema = z.object({
  userId: z.string({
    required_error: errorMessages.required('userId'),
    invalid_type_error: errorMessages.invalidType('userId', 'string'),
  }),
  email: z.string({
    required_error: errorMessages.required('email'),
    invalid_type_error: errorMessages.invalidType('email', 'string'),
  }),
  fullName: z.string({
    required_error: errorMessages.required('fullName'),
    invalid_type_error: errorMessages.invalidType('fullName', 'string'),
  }),
  accessLevelId: z.string({
    required_error: errorMessages.required('accessLevelId'),
    invalid_type_error: errorMessages.invalidType('accessLevelId', 'string'),
  }),
});

export type UpdateUserAdminRequestDto = z.infer<typeof updateUserAdminSchema>;

export const courseIdRequiredSchema = z.object({
  courseId: z
    .string({
      required_error: errorMessages.required('courseId'),
      invalid_type_error: errorMessages.invalidType('courseId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('courseId', 'uuid') }),
});

export const updateStudentParamsSchema = z.object({
  userId: z
    .string({
      required_error: errorMessages.required('userId'),
      invalid_type_error: errorMessages.invalidType('userId', 'string'),
    })
    .uuid({ message: errorMessages.invalidType('userId', 'uuid') }),
});

export const updateStudentBodySchema = z.object({
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  email: z.string().email().optional(),
  birthDate: z.string().optional(),
  gender: z.string().nullable().optional(),
  phoneNumber: z.string().optional(),
  cpf: z.string().optional(),
  classId: z.string().uuid().optional(),
  countryId: z.string().uuid().optional(),
});

export type UpdateStudentBodySchema = z.infer<typeof updateStudentBodySchema>;

export const usersListAdminsSchema = z.object({
  orderByColumn: z.string().optional(),
  orderByDirection: z.enum(['asc', 'desc']).optional(),
});

export type UsersListAdminsDto = z.infer<typeof usersListAdminsSchema>;
