import { z } from 'zod';

import { errorMessages } from '../helpers/errorMessagesZod';

export const createUserSchema = z.object({
  first_name: z
    .string({
      required_error: errorMessages.required('first_name'),
      invalid_type_error: errorMessages.invalidType('first_name', 'string'),
    })
    .min(1, { message: errorMessages.minLength('first_name', 1) })
    .trim(),
  last_name: z
    .string({
      invalid_type_error: errorMessages.invalidType('last_name', 'string'),
      required_error: errorMessages.required('last_name'),
    })
    .min(1, { message: errorMessages.minLength('last_name', 1) }),
  cpf: z
    .string({
      required_error: errorMessages.required('cpf'),
      invalid_type_error: errorMessages.invalidType('cpf', 'string'),
    })
    .min(11, { message: errorMessages.minLength('cpf', 11) }),
  email: z
    .string({
      required_error: errorMessages.required('email'),
      invalid_type_error: errorMessages.invalidType('email', 'string'),
    })
    .email({ message: errorMessages.email('email') }),
  password: z
    .string({
      required_error: errorMessages.required('password'),
      invalid_type_error: errorMessages.invalidType('password', 'string'),
    })
    .min(6, { message: errorMessages.minLength('password', 6) }),
});
