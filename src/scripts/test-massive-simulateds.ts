import { v4 as uuidv4 } from 'uuid';

import { knexInstance } from '../config/connectionDatabase.config';
import { SimulatedStatusEnum } from '../model/enums/simulatedStatus.enum';
import { KnexAnswerSessionsRepository } from '../repositories/knex/answerSessions.repositories';
import { KnexSimulatedsRepository } from '../repositories/knex/simulated.repositories';
import { KnexSimulatedsAccessRepository } from '../repositories/knex/simulatedsAccess.repositories';
import { KnexSimulatedTypesRepository } from '../repositories/knex/simulatedsTypes.repositories';
import { KnexUserRepository } from '../repositories/knex/users.repositories';

async function createMassiveSimulateds() {
  const simulatedsRepo = new KnexSimulatedsRepository();
  const simulatedTypesRepo = new KnexSimulatedTypesRepository();
  const simulatedsAccessRepo = new KnexSimulatedsAccessRepository();
  const answerSessionsRepo = new KnexAnswerSessionsRepository();
  const usersRepo = new KnexUserRepository();

  // Buscar o primeiro customer existente
  const customer = await knexInstance('customers').first();

  if (!customer) {
    throw new Error('Nenhum customer encontrado na tabela customers.');
  }

  const customerId = customer.id;

  // Buscar tipo de simulado exclusivo
  const exclusiveType = await simulatedTypesRepo.findOneBy({ name: 'exclusive' });
  if (!exclusiveType) {
    console.error('Tipo de simulado exclusivo não encontrado!');
    return;
  }

  const NUM_SCHEDULED = 1000;
  const NUM_EXPIRED = 1000;
  const NUM_ACESSOS_POR_SIMULADO = 3;
  const now = new Date();
  const createdScheduled: string[] = [];
  const createdExpired: string[] = [];
  const createdAccesses: string[] = [];
  const createdSessions: string[] = [];

  console.log(
    `🚀 Criando ${NUM_SCHEDULED} simulados agendados (scheduled) e ${NUM_EXPIRED} expirados (expired)...`
  );
  try {
    // Scheduled
    for (let i = 0; i < NUM_SCHEDULED; i++) {
      const simuladoId = uuidv4();
      const startDate = new Date(now.getTime() - (i + 1) * 60000); // cada um 1 min mais antigo
      await simulatedsRepo.insert({
        id: simuladoId,
        name: `Simulado Scheduled Stress ${i + 1}`,
        active: false,
        simulated_type_id: exclusiveType.id,
        status: SimulatedStatusEnum.SCHEDULED,
        start_date: startDate,
        end_date: null,
        customer_id: customerId,
      });
      createdScheduled.push(simuladoId);
      // Criar acessos e sessões
      for (let j = 0; j < NUM_ACESSOS_POR_SIMULADO; j++) {
        const userId = uuidv4();
        await usersRepo.insert({
          id: userId,
          first_name: `Usuário Stress ${userId}`,
          last_name: 'Stress',
          email: `stress+${userId}@test.com`,
          role: 'student',
          customer_id: customerId,
          active: true,
          terms_accept: true,
        });
        const accessId = uuidv4();
        await simulatedsAccessRepo.insert({
          id: accessId,
          user_id: userId,
          simulated_id: simuladoId,
          active: true,
          start_simulated: startDate,
          end_simulated: null,
          total_seconds: null,
          time_limit: 7200,
        });
        createdAccesses.push(accessId);
        // Criar sessão de resposta (não finalizada)
        const sessionId = uuidv4();
        await answerSessionsRepo.insert({
          id: sessionId,
          simulated_access_id: accessId,
          started_at: startDate,
          paused_at: null,
          duration_seconds: null,
        });
        createdSessions.push(sessionId);
      }
      if ((i + 1) % 100 === 0) {
        console.log(`✅ Criados ${i + 1} simulados agendados`);
      }
    }
    // Expired
    for (let i = 0; i < NUM_EXPIRED; i++) {
      const simuladoId = uuidv4();
      const endDate = new Date(now.getTime() - (i + 1) * 60000); // cada um 1 min mais antigo
      await simulatedsRepo.insert({
        id: simuladoId,
        name: `Simulado Expired Stress ${i + 1}`,
        active: true,
        simulated_type_id: exclusiveType.id,
        status: SimulatedStatusEnum.PUBLISHED,
        start_date: null,
        end_date: endDate,
        customer_id: customerId,
      });
      createdExpired.push(simuladoId);
      // Criar acessos e sessões
      for (let j = 0; j < NUM_ACESSOS_POR_SIMULADO; j++) {
        const userId = uuidv4();
        await usersRepo.insert({
          id: userId,
          first_name: `Usuário Stress ${userId}`,
          last_name: 'Stress',
          email: `stress+${userId}@test.com`,
          role: 'student',
          customer_id: customerId,
          active: true,
          terms_accept: true,
        });
        const accessId = uuidv4();
        await simulatedsAccessRepo.insert({
          id: accessId,
          user_id: userId,
          simulated_id: simuladoId,
          active: true,
          start_simulated: endDate,
          end_simulated: null,
          total_seconds: null,
          time_limit: 7200,
        });
        createdAccesses.push(accessId);
        // Criar sessão de resposta (finalizada)
        const sessionId = uuidv4();
        await answerSessionsRepo.insert({
          id: sessionId,
          simulated_access_id: accessId,
          started_at: endDate,
          paused_at: new Date(endDate.getTime() + 1800000), // 30 min depois
          duration_seconds: 1800,
        });
        createdSessions.push(sessionId);
      }
      if ((i + 1) % 100 === 0) {
        console.log(`✅ Criados ${i + 1} simulados expirados`);
      }
    }
    console.log(`\n🎉 CRIAÇÃO CONCLUÍDA!`);
    console.log(`Simulados agendados criados: ${createdScheduled.length}`);
    console.log(`Simulados expirados criados: ${createdExpired.length}`);
    console.log(`Acessos criados: ${createdAccesses.length}`);
    console.log(`Sessões criadas: ${createdSessions.length}`);
    console.log(`IDs dos primeiros 3 agendados:`);
    createdScheduled.slice(0, 3).forEach((id, idx) => console.log(`  ${idx + 1}. ${id}`));
    console.log(`IDs dos primeiros 3 expirados:`);
    createdExpired.slice(0, 3).forEach((id, idx) => console.log(`  ${idx + 1}. ${id}`));
    console.log(`\n⚡ Execute a Lambda para testar a performance!`);
  } catch (error) {
    console.error('❌ Erro ao criar simulados:', error);
  }
}

createMassiveSimulateds()
  .then(() => {
    console.log('\n✅ Script executado com sucesso!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Erro no script:', error);
    process.exit(1);
  });
