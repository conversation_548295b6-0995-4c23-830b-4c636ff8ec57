import {
  DeleteObjectCommand,
  GetObjectCommand,
  GetObjectCommandInput,
  PutObjectCommand,
  PutObjectCommandInput,
  S3Client,
  S3ClientConfig,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Readable } from 'stream';

class S3Service {
  private s3Client: S3Client;

  constructor(config?: S3ClientConfig) {
    const s3Config: S3ClientConfig = {
      region: 'us-east-1',
      // REMOVER
      // credentials: {
      //   accessKeyId: process.env.AWS_ACCESS_KEY_ID as string,
      //   secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY as string,
      // },
      ...config, // Permite a passagem de configurações adicionais
    };

    this.s3Client = new S3Client(s3Config);
  }

  getClient(): S3Client {
    return this.s3Client;
  }

  public async uploadFile(
    bucketName: string,
    key: string,
    body: Buffer | Uint8Array | Blob | string | Readable,
    contentType: string
  ): Promise<void> {
    const uploadParams: PutObjectCommandInput = {
      Bucket: bucketName,
      Key: key,
      Body: body,
      ContentType: contentType,
    };

    try {
      const command = new PutObjectCommand(uploadParams);
      const response = await this.s3Client.send(command);
      console.log(response);
      console.log(`File uploaded successfully to ${bucketName}/${key}`);
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
  }

  public async getSignedUrl(
    bucketName: string,
    key: string,
    expiresIn: number = 3600
  ): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: bucketName,
      Key: key,
    });

    try {
      const url = await getSignedUrl(this.s3Client, command, { expiresIn });
      return url;
    } catch (error) {
      console.error('Error generating signed URL:', error);
      throw error;
    }
  }

  public async downloadFile(bucketName: string, key: string): Promise<Readable> {
    const downloadParams: GetObjectCommandInput = {
      Bucket: bucketName,
      Key: key,
    };

    try {
      const command = new GetObjectCommand(downloadParams);
      const response = await this.s3Client.send(command);
      return response.Body as Readable;
    } catch (error) {
      console.error('Error downloading file:', error);
      throw error;
    }
  }

  public async deleteFile(bucketName: string, key: string): Promise<void> {
    const command = new DeleteObjectCommand({ Bucket: bucketName, Key: key });
    await this.s3Client.send(command);
  }
}

export default S3Service;
