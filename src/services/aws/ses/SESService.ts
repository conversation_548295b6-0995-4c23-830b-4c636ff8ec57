import { SendEmailCommand } from '@aws-sdk/client-ses';

import { sesClient } from './config';

interface EmailParams {
  to: string | string[];
  subject: string;
  body: string;
  isHtml?: boolean;
  from?: string;
}

export class SESService {
  private defaultSender = process.env.MAIL_FROM || '<EMAIL>';

  /**
   * Envia um e-mail utilizando o Amazon SES.
   * @param {EmailParams} params - Parâmetros para envio de e-mail.
   */
  async sendEmail(params: EmailParams): Promise<void> {
    const { to, subject, body, from, isHtml = false } = params;

    const recipients = Array.isArray(to) ? to : [to];

    const emailCommand = new SendEmailCommand({
      Destination: {
        ToAddresses: recipients,
      },
      Message: {
        Body: isHtml
          ? { Html: { Data: body } } // Envio em HTML
          : { Text: { Data: body } }, // Envio em texto simples
        Subject: {
          Data: subject,
        },
      },
      Source: from || this.defaultSender,
    });

    try {
      const response = await sesClient.send(emailCommand);
      console.log('📧 Email enviado com sucesso:', response.MessageId);
    } catch (error) {
      console.error('❌ Erro ao enviar o e-mail:', error);
      throw new Error('Erro ao enviar o e-mail.');
    }
  }
}
