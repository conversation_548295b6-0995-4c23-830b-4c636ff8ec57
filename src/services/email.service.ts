import { SESClient, SendEmailCommand, SendEmailCommandInput } from '@aws-sdk/client-ses';
import fs from 'fs';
import path from 'path';

import { env } from '../env';

interface SendEmailParams {
  to: string | string[];
  subject: string;
  html: string;
  from?: string;
  ReplyToAddresses?: string;
}

interface EmailDebugInfo {
  from: string;
  toAddresses: string[];
  subject: string;
  html: string;
  ReplyToAddresses?: string;
}

export class EmailService {
  private readonly sesClient: SESClient;
  private readonly defaultFromEmail: string;
  private readonly debugMode: boolean;
  private readonly verifiedEmails: string[];
  private readonly isProduction: boolean;

  constructor() {
    this.sesClient = new SESClient({ region: env.CUSTOM_AWS_REGION });
    this.defaultFromEmail = env.EMAIL_FROM;
    this.debugMode = env.EMAIL_DEBUG_MODE === 'true';
    this.isProduction = env.ENVIRONMENT === 'prod';
    this.verifiedEmails = [...env.EMAIL_REPORT_DEV.split(',')];

    if (!this.isProduction && !this.verifiedEmails.includes(this.defaultFromEmail)) {
      this.verifiedEmails.push(this.defaultFromEmail);
    }
  }

  async sendEmail({ to, subject, html, from, ReplyToAddresses }: SendEmailParams): Promise<void> {
    const toAddresses = Array.isArray(to) ? to : [to];
    const senderEmail = from || this.defaultFromEmail;

    if (this.debugMode || (!this.isProduction && env.EMAIL_DEBUG_MODE === 'true')) {
      this.logEmailDebugInfo({
        from: senderEmail,
        toAddresses,
        subject,
        html,
        ReplyToAddresses,
      });

      if (this.debugMode) {
        console.log('Email em modo de debug - não enviado');
        return;
      }
    }

    if (this.isProduction) {
      await this.sendProductionEmail(toAddresses, subject, html, senderEmail, ReplyToAddresses);
    } else {
      await this.sendDevelopmentEmail(toAddresses, subject, html, senderEmail, ReplyToAddresses);
    }
  }

  private async sendProductionEmail(
    toAddresses: string[],
    subject: string,
    html: string,
    senderEmail: string,
    replyTo?: string
  ): Promise<void> {
    const params = this.buildParams(toAddresses, subject, html, senderEmail, replyTo);
    const command = new SendEmailCommand(params);
    await this.sesClient.send(command);
  }

  private async sendDevelopmentEmail(
    toAddresses: string[],
    subject: string,
    html: string,
    senderEmail: string,
    replyTo?: string
  ): Promise<void> {
    try {
      const allVerified = toAddresses.every((email) => this.verifiedEmails.includes(email));

      if (allVerified) {
        const command = new SendEmailCommand(
          this.buildParams(toAddresses, subject, html, senderEmail, replyTo)
        );
        await this.sesClient.send(command);
      } else {
        const redirectSubject = `[REDIRECIONADO] ${subject}`;
        const redirectedAddresses = [...this.verifiedEmails];

        const redirectInfo = `
          <div style="background-color: #f8f9fa; padding: 10px; margin-bottom: 20px; border-left: 4px solid #007bff;">
            <p><strong>Email redirecionado:</strong> Este email foi originalmente destinado a: ${toAddresses.join(', ')}</p>
            <p>Em ambiente de desenvolvimento, emails só podem ser enviados para endereços verificados no AWS SES.</p>
          </div>
        `;
        const redirectedHtml = redirectInfo + html;

        const params = this.buildParams(
          redirectedAddresses,
          redirectSubject,
          redirectedHtml,
          senderEmail,
          replyTo
        );

        const command = new SendEmailCommand(params);
        await this.sesClient.send(command);
      }
    } catch (error) {
      console.error('Erro ao enviar email:', error);
      throw error;
    }
  }

  private logEmailDebugInfo(emailInfo: EmailDebugInfo): void {
    if (!this.isProduction) {
      this.saveEmailToFile(emailInfo);
    } else {
      console.log(`[PROD] Email enviado para: ${emailInfo.toAddresses.length} destinatário(s)`);
    }
  }

  private async saveEmailToFile(emailInfo: EmailDebugInfo): Promise<void> {
    try {
      if (this.isProduction) {
        return;
      }

      const emailsDir = path.resolve(__dirname, '../../debug-emails');

      if (!fs.existsSync(emailsDir)) {
        fs.mkdirSync(emailsDir, { recursive: true });
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = path.join(emailsDir, `email-${timestamp}.html`);

      const emailContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>${emailInfo.subject}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .email-info { background: #f5f5f5; padding: 15px; margin-bottom: 20px; }
            .email-content { border: 1px solid #ddd; padding: 15px; }
          </style>
        </head>
        <body>
          <div class="email-info">
            <p><strong>De:</strong> ${emailInfo.from}</p>
            <p><strong>Para:</strong> ${emailInfo.toAddresses.join(', ')}</p>
            <p><strong>Assunto:</strong> ${emailInfo.subject}</p>
            ${emailInfo.ReplyToAddresses ? `<p><strong>Responder Para:</strong> ${emailInfo.ReplyToAddresses}</p>` : ''}
            <p><strong>Data:</strong> ${new Date().toLocaleString()}</p>
          </div>
          <div class="email-content">
            ${emailInfo.html}
          </div>
        </body>
        </html>
      `;

      await fs.promises.writeFile(filename, emailContent);
    } catch (error) {
      console.error('Erro ao salvar email em arquivo:', error);
    }
  }

  private buildParams(
    to: string[],
    subject: string,
    html: string,
    from: string,
    replyTo?: string
  ): SendEmailCommandInput {
    const fallbackText =
      'Este email contém conteúdo HTML. Por favor, use um cliente de email que suporte HTML para visualizá-lo corretamente.';

    const params: SendEmailCommandInput = {
      Source: from,
      Destination: {
        ToAddresses: to,
      },
      Message: {
        Subject: {
          Data: subject,
          Charset: 'UTF-8',
        },
        Body: {
          Html: {
            Data: html,
            Charset: 'UTF-8',
          },
          Text: {
            Data: fallbackText,
            Charset: 'UTF-8',
          },
        },
      },
    };

    if (replyTo) {
      params.ReplyToAddresses = [replyTo];
    }

    return params;
  }
}
