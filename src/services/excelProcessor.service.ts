import * as xlsx from 'xlsx';
import * as xlsxStyle from 'xlsx-js-style';

export interface ExcelRow {
  [key: string]: string | number | boolean | Date | null | undefined;
  disciplina?: string;
  categoria?: string;
  subcategoria?: string;
  tipo_questao?: string;
  enunciado?: string;
  alternativa_a?: string;
  alternativa_b?: string;
  alternativa_c?: string;
  alternativa_d?: string;
  alternativa_e?: string;
  correta?: string;
  dificuldade?: string;
  explicacao?: string | null;
}

export interface ExcelRowWithErrors {
  row: ExcelRow;
  errors: string[];
}

export interface ExcelRowWithStatus {
  row: ExcelRow;
  isValid: boolean;
  errors: string[];
}

export interface ExcelProcessingResult {
  validRows: ExcelRow[];
  invalidRows: ExcelRowWithErrors[];
  stats: {
    totalRows: number;
    validCount: number;
    invalidCount: number;
    successPercentage: number;
  };
}

export class ExcelProcessor {
  /**
   * Processa um arquivo Excel e valida suas linhas
   * @param file Arquivo Excel a ser processado
   * @returns Resultado do processamento com linhas válidas e inválidas
   */
  async processExcel(file: Express.Multer.File): Promise<{
    validRows: ExcelRow[];
    invalidRows: ExcelRowWithErrors[];
    stats: {
      totalRows: number;
      validCount: number;
      invalidCount: number;
      successPercentage: number;
    };
  }> {
    const workbook = xlsx.read(file.buffer, { type: 'buffer' });

    if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
      throw new Error('Arquivo Excel não contém planilhas');
    }

    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];

    const rows = xlsx.utils.sheet_to_json<ExcelRow>(worksheet, { defval: '' });

    const validRows: ExcelRow[] = [];
    const invalidRows: ExcelRowWithErrors[] = [];

    for (let i = 0; i < rows.length; i++) {
      const row = rows[i];
      const rowIndex = i + 2; // Ajustando para considerar o cabeçalho e índice 1-based

      const errors: string[] = [];

      if (!this.hasMinimumRequiredFields(row)) {
        errors.push(`Linha ${rowIndex}: Não possui os campos mínimos necessários`);
      }

      if (!row.enunciado || String(row.enunciado).trim() === '') {
        errors.push(`Linha ${rowIndex}: Enunciado é obrigatório`);
      }

      if (!row.disciplina || String(row.disciplina).trim() === '') {
        errors.push(`Linha ${rowIndex}: Disciplina é obrigatória`);
      }

      if (!row.categoria || String(row.categoria).trim() === '') {
        errors.push(`Linha ${rowIndex}: Categoria é obrigatória`);
      }

      if (!row.subcategoria || String(row.subcategoria).trim() === '') {
        errors.push(`Linha ${rowIndex}: Subcategoria é obrigatória`);
      }

      if (!row.correta || String(row.correta).trim() === '') {
        errors.push(`Linha ${rowIndex}: Alternativa correta é obrigatória`);
      }

      if (errors.length === 0) {
        validRows.push(row);
      } else {
        invalidRows.push({ row, errors });
      }
    }

    const totalRows = rows.length;
    const validCount = validRows.length;
    const invalidCount = invalidRows.length;
    const successPercentage = totalRows > 0 ? Math.round((validCount / totalRows) * 100) : 0;

    return {
      validRows,
      invalidRows,
      stats: {
        totalRows,
        validCount,
        invalidCount,
        successPercentage,
      },
    };
  }

  /**
   * Gera um arquivo Excel contendo as linhas com erro
   * @param rows Array de objetos contendo a linha e os erros
   * @returns Buffer contendo o arquivo Excel
   */
  async generateErrorsExcel(rows: ExcelRowWithErrors[]): Promise<Buffer> {
    const workbook = xlsx.utils.book_new();

    const processedRows = rows.map((item) => {
      const rowWithErrors = { ...item.row };

      rowWithErrors.ERROS = item.errors.join('; ');

      return rowWithErrors;
    });

    const worksheet = xlsx.utils.json_to_sheet(processedRows);

    xlsx.utils.book_append_sheet(workbook, worksheet, 'Linhas com Erro');

    const excelBuffer = xlsx.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    return excelBuffer;
  }

  /**
   * Gera um arquivo Excel contendo todas as linhas, destacando as que têm erro
   * @param rows Array de objetos contendo a linha, se é válida e os erros
   * @returns Buffer contendo o arquivo Excel
   */
  async generateFullExcelWithErrors(rows: ExcelRowWithStatus[]): Promise<Buffer> {
    const columnOrder = [
      'disciplina',
      'categoria',
      'subcategoria',
      'tipo_questao',
      'enunciado',
      'alternativa_a',
      'alternativa_b',
      'alternativa_c',
      'alternativa_d',
      'alternativa_e',
      'correta',
      'dificuldade',
      'explicacao',
      'STATUS',
      'ERROS',
    ];

    const processedRows = rows.map((item) => {
      const processedRow = { ...item.row };

      processedRow.STATUS = item.isValid ? 'VÁLIDO' : 'INVÁLIDO';
      processedRow.ERROS = item.isValid ? '' : item.errors.join('; ');

      return processedRow;
    });

    const orderedRows = processedRows.map((row) => {
      const orderedRow: Record<string, string | number | boolean | Date | null | undefined> = {};

      columnOrder.forEach((col) => {
        if (col in row || col === 'STATUS' || col === 'ERROS') {
          orderedRow[col] = row[col as keyof typeof row];
        }
      });

      Object.keys(row).forEach((key) => {
        if (!columnOrder.includes(key)) {
          orderedRow[key] = row[key as keyof typeof row];
        }
      });

      return orderedRow;
    });

    const worksheet = xlsx.utils.json_to_sheet(orderedRows);

    const defaultColWidth = 20;
    const cols: Array<{ width: number }> = [];

    columnOrder.forEach((col, index) => {
      cols[index] = {
        width:
          col === 'ERROS'
            ? 50
            : col === 'STATUS'
              ? 15
              : col === 'enunciado'
                ? 40
                : col === 'explicacao'
                  ? 40
                  : defaultColWidth,
      };
    });

    worksheet['!cols'] = cols;

    const statusColIndex = columnOrder.indexOf('STATUS');

    if (statusColIndex !== -1) {
      const range = xlsx.utils.decode_range(worksheet['!ref'] || 'A1');

      for (let rowIndex = range.s.r + 1; rowIndex <= range.e.r; rowIndex++) {
        const cellRef = xlsx.utils.encode_cell({ r: rowIndex, c: statusColIndex });
        const cell = worksheet[cellRef];

        if (cell) {
          cell.s = {
            fill: {
              patternType: 'solid',
              fgColor: { rgb: cell.v === 'VÁLIDO' ? '007D23' : 'E90000' },
            },
            font: {
              color: { rgb: 'FFFFFF' },
              bold: true,
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center',
            },
          };
        }
      }
    }

    const workbook = xlsxStyle.utils.book_new();
    xlsxStyle.utils.book_append_sheet(workbook, worksheet, 'Todas as Linhas');

    const excelBuffer = xlsxStyle.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    return excelBuffer;
  }

  private containsExcelFormula(text: string): boolean {
    if (!text) return false;

    // Padrões comuns de fórmulas do Excel
    const formulaPatterns = [
      /^=/, // Começa com =
      /^@/, // Começa com @
      /^[-+]=/, // Começa com += ou -=
      /^[-+]@/, // Começa com +@ ou -@
      /\b(SUM|AVERAGE|COUNT|IF|VLOOKUP|HLOOKUP|INDEX|MATCH|CONCATENATE|LEN)\s*\(/i,
    ];

    return formulaPatterns.some((pattern) => pattern.test(text));
  }

  private hasMinimumRequiredFields(row: ExcelRow): boolean {
    const requiredFields = ['disciplina', 'categoria', 'subcategoria', 'enunciado', 'correta'];

    for (const field of requiredFields) {
      if (!(field in row)) {
        return false;
      }
    }

    return true;
  }
}
