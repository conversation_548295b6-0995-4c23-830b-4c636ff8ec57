export function generateCode(
  name: string,
  minCodeLength: number,
  existingGroup: string[] = []
): string {
  name = name
    .trim()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '');

  let code = '';

  if (name.indexOf(' ') === -1) {
    code = name.slice(0, minCodeLength).toUpperCase();
  } else {
    const words = name.split(' ');

    const firstThreeLetters = words.map((word) => {
      if (['de', 'da', 'do', 'dos'].includes(word.toLowerCase())) {
        return '';
      } else {
        return word.slice(0, minCodeLength - 2).toUpperCase();
      }
    });

    code = firstThreeLetters.join('');
  }

  let length = minCodeLength;

  if (existingGroup.includes(code)) {
    length++;

    const newCode = generateCode(name, length, existingGroup);

    return newCode;
  }

  return code;
}
