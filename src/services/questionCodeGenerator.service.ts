import { IQuestionsRepository } from '../repositories/assign/questions.assign';
import { ServiceUnavailableError } from '../use-case/errors/ServiceUnavailableError';

export class QuestionCodeGeneratorService {
  private readonly codePrefixLength = 5;
  private readonly codeSuffixLength = 5;
  private readonly maxCodeGenerationAttempts = 5;
  private readonly maxCodeSuffixTries = 100;

  constructor(private questionsRepository: IQuestionsRepository) {}

  async execute({ customerId }: { customerId: string }): Promise<string> {
    let generationAttempts = 0;

    while (generationAttempts < this.maxCodeGenerationAttempts) {
      const codePrefix = Math.floor(
        10 ** (this.codePrefixLength - 1) + Math.random() * 9 * 10 ** (this.codePrefixLength - 1)
      ).toString();

      const existingQuestionCodes = new Set(
        (
          await this.questionsRepository.findSimilarCodes({
            baseCode: `${codePrefix}%`,
            customerId,
          })
        ).map((q) => q.title)
      );

      for (let i = 0; i < this.maxCodeSuffixTries; i++) {
        const codeSuffix = (
          Math.floor(Math.random() * 10 ** this.codeSuffixLength) +
          10 ** (this.codeSuffixLength - 1)
        ).toString();
        const questionCode = `${codePrefix}${codeSuffix}`;

        if (!existingQuestionCodes.has(questionCode)) {
          return questionCode;
        }
      }

      generationAttempts++;
    }

    throw new ServiceUnavailableError(
      'Todos os códigos disponíveis foram usados. Gerando novos códigos, tente novamente.'
    );
  }
}
