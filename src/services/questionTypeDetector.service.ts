import { IQuestionType } from '../model/IQuestionType';
import { ExcelRowData } from '../use-case/bulkImportQuestions/uploadQuestionsExcel.useCase';

export interface QuestionType {
  id: string;
  type: string;
  name?: string;
}

export class QuestionTypeDetectorService {
  assignQuestionType(
    rowData: ExcelRowData,
    alternativesType: IQuestionType,
    trueOrFalseType: IQuestionType
  ): void {
    const tipoQuestaoLower = ['v', 'f'].includes(String(rowData.correta).toLowerCase().trim());

    if (tipoQuestaoLower) {
      rowData.question_type_id = trueOrFalseType.id;
    } else {
      rowData.question_type_id = alternativesType.id;
    }

    if (!tipoQuestaoLower) {
      const altA = String(rowData.alternativa_a || '')
        .trim()
        .toLowerCase();
      const altB = String(rowData.alternativa_b || '')
        .trim()
        .toLowerCase();

      const isAVerdadeiro = altA === 'verdadeiro' || altA === 'verdadeiro.';
      const isBFalso = altB === 'falso' || altB === 'falso.';

      if (isAVerdadeiro && isBFalso) {
        rowData.question_type_id = trueOrFalseType.id;
      } else {
        const hasMoreAlternatives = Boolean(
          rowData.alternativa_c?.trim() ||
            rowData.alternativa_d?.trim() ||
            rowData.alternativa_e?.trim()
        );

        if (hasMoreAlternatives) {
          rowData.question_type_id = alternativesType.id;
        } else {
          const hasAB = Boolean(altA || altB);

          if (hasAB) {
            rowData.question_type_id = alternativesType.id;
          }
        }
      }
    }
  }
}
