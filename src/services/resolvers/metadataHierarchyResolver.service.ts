// services/metadataResolver.service.ts

import { IDisciplinesRepository } from '../../repositories/assign/disciplines.assign';
import { filterTextRegex } from '../filterTextRegex.service';

export interface MetadataMaps {
  disciplines: Map<string, { id: string; name: string }>;
  categories: Map<string, { id: string; name: string }>;
  subcategories: Map<string, { id: string; name: string; categoryId: string }>;
  disciplineCategoryAccess: Map<string, Set<string>>;
  categorySubcategoryAccess: Map<string, Set<{ [key: string]: string }>>;
  disciplineSubcategoryAccess: Map<string, Set<string>>;
}

interface RawHierarchyItem {
  disciplineId: string;
  disciplineName: string;
  category_access: Array<{
    categoryId: string;
    categoryName: string;
    subcategories: Array<{
      subcategoryId: string;
      subcategoryName: string;
    }>;
  }>;
}

export class MetadataResolverService {
  constructor(private readonly disciplinesRepository: IDisciplinesRepository) {}

  async resolve(customerId: string): Promise<MetadataMaps> {
    const hierarchyData =
      await this.disciplinesRepository.listDisciplinesWithAccessHierarchyForImport({
        customerId,
      });

    if (!hierarchyData || !Array.isArray(hierarchyData)) {
      return {
        disciplines: new Map(),
        categories: new Map(),
        subcategories: new Map(),
        disciplineCategoryAccess: new Map(),
        disciplineSubcategoryAccess: new Map(),
        categorySubcategoryAccess: new Map(),
      };
    }

    const hierarchy = hierarchyData as unknown as RawHierarchyItem[];

    const disciplineMap = new Map<string, { id: string; name: string }>();
    const categoryMap = new Map<string, { id: string; name: string }>();
    const subcategoryMap = new Map<string, { id: string; name: string; categoryId: string }>();
    const disciplineCategoryAccessMap = new Map<string, Set<string>>();
    const disciplineSubcategoryAccessMap = new Map<string, Set<string>>();
    const categorySubcategoryAccessMap = new Map<string, Set<{ [key: string]: string }>>();

    for (let i = 0; i < hierarchy.length; i++) {
      const item = hierarchy[i];

      try {
        if (!item || !item.disciplineId || !item.disciplineName) {
          continue;
        }

        const disciplineName = filterTextRegex(item.disciplineName.toLowerCase());
        const disciplineId = item.disciplineId;

        disciplineMap.set(disciplineName, {
          id: disciplineId,
          name: filterTextRegex(item.disciplineName),
        });

        if (!disciplineCategoryAccessMap.has(disciplineId)) {
          disciplineCategoryAccessMap.set(disciplineId, new Set());
        }

        if (!disciplineSubcategoryAccessMap.has(disciplineId)) {
          disciplineSubcategoryAccessMap.set(disciplineId, new Set());
        }

        if (!item.category_access || !Array.isArray(item.category_access)) {
          continue;
        }

        for (const c of item.category_access) {
          if (!c || !c.categoryId || !c.categoryName) {
            continue;
          }

          if (!categorySubcategoryAccessMap.has(c.categoryId)) {
            categorySubcategoryAccessMap.set(c.categoryId, new Set());
          }

          categoryMap.set(filterTextRegex(c.categoryName.toLowerCase()), {
            id: c.categoryId,
            name: filterTextRegex(c.categoryName),
          });

          disciplineCategoryAccessMap.get(disciplineId)?.add(c.categoryId);

          if (!c.subcategories || !Array.isArray(c.subcategories)) {
            continue;
          }

          for (const s of c.subcategories) {
            if (!s || !s.subcategoryId || !s.subcategoryName) {
              continue;
            }

            subcategoryMap.set(filterTextRegex(s.subcategoryName.toLowerCase()), {
              id: s.subcategoryId,
              name: filterTextRegex(s.subcategoryName),
              categoryId: c.categoryId,
            });

            disciplineSubcategoryAccessMap.get(disciplineId)?.add(s.subcategoryId);
            categorySubcategoryAccessMap.get(c.categoryId)?.add({
              [filterTextRegex(s.subcategoryName.toLowerCase())]: s.subcategoryId,
            });
          }
        }
      } catch (error) {
        console.error(`Erro ao processar item ${i}:`, error);
      }
    }

    return {
      disciplines: disciplineMap,
      categories: categoryMap,
      subcategories: subcategoryMap,
      disciplineCategoryAccess: disciplineCategoryAccessMap,
      disciplineSubcategoryAccess: disciplineSubcategoryAccessMap,
      categorySubcategoryAccess: categorySubcategoryAccessMap,
    };
  }
}
