import { Knex } from 'knex';

export enum SortingType {
  DEFAULT = 'default',
  ALPHABETICAL = 'alphabetical',
  RANDOM = 'random',
  DIFFICULTY = 'difficulty',
}

export enum OrderDirection {
  ASC = 'asc',
  DESC = 'desc',
}

export class SortingService {
  private static supportsUnaccent: boolean | null = null;

  /**
   * Verifica se o banco de dados suporta a função UNACCENT().
   * @param knex - Instância do Knex.
   */
  static async checkUnaccentSupport(knex: Knex): Promise<void> {
    if (this.supportsUnaccent !== null) return;

    try {
      await knex.raw(`SELECT UNACCENT('teste')`);
      this.supportsUnaccent = true;
      console.log('✅ Banco de dados suporta UNACCENT().');
    } catch (error) {
      this.supportsUnaccent = false;
      console.warn('⚠️ UNACCENT() não disponível. Usando ordenação no backend.');
    }
  }

  /**
   * Aplica a ordenação conforme o tipo especificado.
   * @param query - Instância da QueryBuilder do Knex.
   * @param tableName - Nome da tabela que contém a coluna a ser ordenada.
   * @param orderByColumn - Nome da coluna usada para ordenação.
   * @param sortingType - Tipo de ordenação a ser aplicado.
   * @param orderDirection - Direção da ordenação (ASC ou DESC).
   * @returns QueryBuilder modificado com a ordenação aplicada.
   */
  static applySorting(
    query: Knex.QueryBuilder,
    tableName: string,
    orderByColumn: string,
    sortingType: SortingType = SortingType.DEFAULT,
    orderDirection: string
  ): Knex.QueryBuilder {
    switch (sortingType) {
      case SortingType.ALPHABETICAL:
        if (this.supportsUnaccent) {
          return query.orderByRaw(
            `LOWER(UNACCENT(${tableName}.${orderByColumn})) ${orderDirection}`
          );
        } else {
          console.warn(`⚠️ UNACCENT() não disponível. Ordenando pelo backend.`);
          return query.orderByRaw(`LOWER(${tableName}.${orderByColumn}) ${orderDirection}`);
        }

      case SortingType.DIFFICULTY:
        return query.orderByRaw(
          `CASE ${tableName}.difficulty 
            WHEN 'easy' THEN 1
            WHEN 'medium' THEN 2
            WHEN 'hard' THEN 3
          END ${orderDirection}`
        );

      case SortingType.RANDOM:
        return query.orderByRaw('RANDOM()');

      default:
        return query.orderBy(`${tableName}.${orderByColumn}`, orderDirection);
    }
  }

  /**
   * Ordena uma lista de objetos alfabeticamente no backend.
   * @param items - Lista de objetos a serem ordenados.
   * @param key - Propriedade do objeto usada na ordenação.
   * @param orderDirection - Direção da ordenação (ASC ou DESC).
   * @returns Lista ordenada alfabeticamente.
   */
  static sortAlphabetically<T>(items: T[], key: keyof T, orderDirection: string): T[] {
    const validOrderDirection =
      orderDirection.toLowerCase() === OrderDirection.DESC
        ? OrderDirection.DESC
        : OrderDirection.ASC;

    return items.sort((a, b) => {
      const valueA = (a[key] as string)
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .toLowerCase();
      const valueB = (b[key] as string)
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .toLowerCase();

      return validOrderDirection === OrderDirection.ASC
        ? valueA.localeCompare(valueB, 'pt-BR', { sensitivity: 'base' })
        : valueB.localeCompare(valueA, 'pt-BR', { sensitivity: 'base' });
    });
  }
}
