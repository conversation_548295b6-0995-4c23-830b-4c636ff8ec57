import {
  DeleteObjectCommand,
  GetObjectCommand,
  HeadObjectCommand,
  PutObjectCommand,
  S3Client,
  S3ServiceException,
} from '@aws-sdk/client-s3';
import dotenv from 'dotenv';
import { Readable } from 'stream';

import { env } from '../env';
import { GenericError } from '../use-case/errors/GenericError';
import { ResourceNotFoundError } from '../use-case/errors/ResourceNotFound';
dotenv.config();

const s3 = new S3Client({ region: env.CUSTOM_AWS_REGION || 'us-east-1' });

export class UploadService {
  /**
   * Adiciona um arquivo ao S3.
   * @param file Arquivo a ser enviado
   * @param storagePath Caminho da pasta dentro do S3
   * @param bucket Nome do bucket
   * @returns fileKey (chave do arquivo dentro do S3)
   */
  async uploadFile({
    file,
    storagePath,
    bucket,
  }: {
    file: Express.Multer.File;
    storagePath: string;
    bucket: string;
  }): Promise<string> {
    if (!bucket) throw new GenericError('Bucket não configurado');

    const response = await s3.send(
      new PutObjectCommand({
        Bucket: bucket,
        Key: storagePath,
        Body: file.buffer,
        ContentType: file.mimetype,
      })
    );

    if (!response) throw new GenericError('Erro ao enviar arquivo.');

    return storagePath;
  }

  /**
   * Exclui um arquivo do S3.
   * @param fileKey Chave do arquivo no S3
   */
  async deleteFile(fileKey: string, bucket: string): Promise<void> {
    if (!bucket) throw new GenericError('Bucket não configurado');

    const response = await s3.send(new DeleteObjectCommand({ Bucket: bucket, Key: fileKey }));

    if (!response) throw new GenericError('Erro ao deletar arquivo.');
  }

  /**
   * Obtém um arquivo do S3.
   * @param fileKey Chave do arquivo no S3
   * @returns Stream do arquivo
   */
  async getFile(fileKey: string, bucket: string): Promise<Readable> {
    if (!bucket) throw new GenericError('Bucket não configurado');

    const response = await s3.send(
      new GetObjectCommand({
        Bucket: bucket,
        Key: fileKey,
      })
    );

    if (!response.Body) {
      throw new ResourceNotFoundError('Arquivo não encontrado.');
    }

    return response.Body as Readable;
  }

  /**
   *
   * @param fileKey Chave do arquivo no S3
   * @param bucket Nome do bucket
   */
  async checkFileExists(fileKey: string, bucket: string): Promise<void> {
    if (!bucket) throw new GenericError('Bucket não configurado.');

    try {
      await s3.send(new HeadObjectCommand({ Bucket: bucket, Key: fileKey }));
    } catch (error) {
      if (error instanceof S3ServiceException) {
        if (error.name === 'NotFound' || error.$metadata?.httpStatusCode === 404) {
          throw new ResourceNotFoundError(`Arquivo não encontrado.`);
        }
      } else if (error instanceof Error) {
        console.error('Erro ao verificar existência do arquivo no S3:', error.message);
        throw new GenericError('Erro ao verificar o arquivo no servidor.');
      }
      throw new GenericError('Erro inesperado ao verificar o arquivo no S3.');
    }
  }
}
