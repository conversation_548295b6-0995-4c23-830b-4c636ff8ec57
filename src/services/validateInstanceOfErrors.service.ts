import { Zod<PERSON>rror, ZodIssue } from 'zod';

import { BadRequestError } from '../use-case/errors/BadRequestError';
import { ForbiddenError } from '../use-case/errors/ForbiddenError';
import { GenericError } from '../use-case/errors/GenericError';
import { ResourceAlreadyExistsError } from '../use-case/errors/ResourceAlreadyExistsError';
import { ResourceNotFoundError } from '../use-case/errors/ResourceNotFound';
import { ServiceUnavailableError } from '../use-case/errors/ServiceUnavailableError';
import { UnauthorizedError } from '../use-case/errors/UnauthorizedError.ts';

interface IValidateInstanceOfErrors {
  statusCode: number;
  message: string;
  error: string;
  issue?: ZodIssue[];
}
export function validateInstanceOfErrors(error: unknown): IValidateInstanceOfErrors {
  if (error instanceof ResourceNotFoundError) {
    return {
      statusCode: error.statusCode,
      message: error.message,
      error: error.name,
    };
  }
  if (error instanceof ZodError) {
    return {
      statusCode: 400,
      message: 'Erro na requisição',
      error: error.name,
      issue: error.issues,
    };
  }
  if (error instanceof BadRequestError) {
    return {
      statusCode: error.statusCode,
      message: error.message,
      error: error.name,
    };
  }
  if (error instanceof GenericError) {
    return {
      statusCode: error.statusCode,
      message: error.message,
      error: error.name,
    };
  }
  if (error instanceof ResourceAlreadyExistsError) {
    return {
      statusCode: error.statusCode,
      message: error.message,
      error: error.name,
    };
  }
  if (error instanceof ServiceUnavailableError) {
    return {
      statusCode: error.statusCode,
      message: error.message,
      error: error.name,
    };
  }
  if (error instanceof UnauthorizedError) {
    return {
      statusCode: error.statusCode,
      message: error.message,
      error: error.name,
    };
  }
  if (error instanceof ForbiddenError) {
    return {
      statusCode: error.statusCode,
      message: error.message,
      error: error.name,
    };
  }
  return {
    statusCode: 500,
    message: 'Erro interno do servidor',
    error: 'Internal Error',
  };
}
