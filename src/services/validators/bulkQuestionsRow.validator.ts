import { IQuestionsRepository } from '../../repositories/assign/questions.assign';
import { ExcelRowData } from '../../use-case/bulkImportQuestions/uploadQuestionsExcel.useCase';

export interface ValidationContext {
  row: ExcelRowData;
  customerId: string;
  rowIndex: number;
  existingQuestionsRepo: IQuestionsRepository;
  maps: {
    disciplines: Map<string, { id: string; name: string }>;
    categories: Map<string, { id: string; name: string }>;
    subcategories: Map<string, { id: string; name: string; categoryId: string }>;
    disciplineCategoryAccess: Map<string, Set<string>>;
    disciplineSubcategoryAccess: Map<string, Set<string>>;
    categorySubcategoryAccess: Map<string, Set<{ [key: string]: string }>>;
  };
  questionTypes: {
    trueOrFalseTypeId: string;
    alternativesTypeId: string;
  };
}

const MAX_TEXT_LENGTH = 2000;
export class QuestionValidationService {
  async validate(ctx: ValidationContext): Promise<string[]> {
    const { row, customerId, rowIndex, existingQuestionsRepo } = ctx;

    const errors: string[] = [];

    const isTrueOrFalse = ['v', 'f'].includes(String(row.correta).toLowerCase().trim());

    const required = ['disciplina', 'categoria', 'subcategoria', 'enunciado'];
    for (const field of required) {
      if (!row[field] || String(row[field]).trim() === '') {
        errors.push(`Campo "${field}" é obrigatório.`);
      }
    }

    if (row.dificuldade) {
      const dificuldadeValida = [
        'fácil',
        'média',
        'difícil',
        'facil',
        'media',
        'dificil',
        'easy',
        'medium',
        'hard',
        'f',
        'm',
        'd',
        'F',
        'M',
        'D',
        'Medio',
      ];
      if (!dificuldadeValida.includes(String(row.dificuldade).toLowerCase().trim())) {
        errors.push(
          `Dificuldade "${row.dificuldade}" inválida. Use "Fácil", "Média" ou "Difícil".`
        );
      }
    }

    if (!row.correta || String(row.correta).trim() === '') {
      errors.push(`Alternativa correta é obrigatória.`);
    } else {
      const corretaLower = String(row.correta).toLowerCase().trim();

      if (isTrueOrFalse) {
        if (!['v', 'f'].includes(corretaLower)) {
          errors.push(
            `Para questões de Verdadeiro ou Falso, a alternativa correta deve ser V ou F.`
          );
        }
        const altA = String(row.alternativa_a || '')
          .trim()
          .toLowerCase();
        const altB = String(row.alternativa_b || '')
          .trim()
          .toLowerCase();
        const altC = String(row.alternativa_c || '')
          .trim()
          .toLowerCase();
        const altD = String(row.alternativa_d || '')
          .trim()
          .toLowerCase();
        const altE = String(row.alternativa_e || '')
          .trim()
          .toLowerCase();

        const isAVerdadeiro = altA === 'verdadeiro' || altA === 'verdadeiro.';
        const isBFalso = altB === 'falso' || altB === 'falso.';

        if (!isAVerdadeiro && altA.length) {
          errors.push(
            `Alternativa A não precisa ser preenchida com para uma questão verdadeira ou falso`
          );
        } else {
          row.alternativa_a = 'Verdadeiro';
        }
        if (!isBFalso && altB.length) {
          errors.push(
            `Alternativa B não precisa ser preenchida com para uma questão verdadeira ou falso`
          );
        } else {
          row.alternativa_b = 'Falso';
        }
        if (altC || altD || altE) {
          errors.push(
            `Alternativas C, D e E não precisa devem ser preenchidas para uma questão verdadeira ou falso`
          );
        }
      } else {
        if (!['a', 'b', 'c', 'd', 'e'].includes(corretaLower)) {
          errors.push(`Alternativa correta deve ser A, B, C, D ou E.`);
        } else {
          const altCorretaKey = `alternativa_${corretaLower}` as keyof typeof row;
          if (!row[altCorretaKey] || String(row[altCorretaKey]).trim() === '') {
            errors.push(
              `A alternativa ${corretaLower.toUpperCase()} marcada como correta não possui conteúdo.`
            );
          }

          const alternativasRequeridas = [];

          if (corretaLower === 'e') alternativasRequeridas.push('d', 'c', 'b', 'a');
          else if (corretaLower === 'd') alternativasRequeridas.push('c', 'b', 'a');
          else if (corretaLower === 'c') alternativasRequeridas.push('b', 'a');
          else if (corretaLower === 'b') alternativasRequeridas.push('a');

          for (const alt of alternativasRequeridas) {
            const altKey = `alternativa_${alt}` as keyof typeof row;
            if (!row[altKey] || String(row[altKey]).trim() === '') {
              errors.push(
                `A alternativa ${alt.toUpperCase()} é obrigatória quando a alternativa correta é ${corretaLower.toUpperCase()}.`
              );
            }
          }
        }
      }
    }

    if (!isTrueOrFalse) {
      if (!row.alternativa_a || String(row.alternativa_a).trim() === '') {
        errors.push(`Alternativa A é obrigatória`);
      }

      if (!row.alternativa_b || String(row.alternativa_b).trim() === '') {
        errors.push(`Alternativa B é obrigatória`);
      }

      if (row.correta) {
        const altKey = `alternativa_${row.correta.toLowerCase()}` as keyof typeof row;
        if (!row[altKey] || String(row[altKey]).trim() === '') {
          errors.push(
            `A alternativa marcada como correta (${row.correta.toUpperCase()}) não possui conteúdo`
          );
        }
      }
    }

    const enunciado = row.enunciado?.trim();
    if (enunciado) {
      try {
        const exists = await existingQuestionsRepo.findOneBy({
          description: enunciado,
          customer_id: customerId,
          deleted_at: null,
        });

        if (exists) {
          errors.push(`Enunciado já existe no banco deste cliente.`);
        }
      } catch (error) {
        console.error(`Erro ao verificar duplicidade do enunciado:`, error);
      }
    }

    if (!row.discipline_id) {
      errors.push(`Disciplina "${row.disciplina}" não foi encontrada ou não possui acessos.`);
    }

    if (!row.category_id) {
      errors.push(`Categoria "${row.categoria}" não encontrada ou não possui acessos.`);
    }

    if (!row.subcategory_id) {
      errors.push(`Subcategoria "${row.subcategoria}" não encontrada ou não possui acesso.`);
    }

    this.validateTextLength(enunciado, 'enunciado', rowIndex, errors);

    if (!isTrueOrFalse) {
      for (const alt of ['a', 'b', 'c', 'd', 'e']) {
        const altKey = `alternativa_${alt}` as keyof typeof row;
        this.validateTextLength(
          row[altKey] as string | number | undefined,
          `alternativa_${alt}`,
          rowIndex,
          errors
        );
      }
    }

    return errors;
  }

  private validateTextLength(
    text: string | number | undefined,
    fieldName: string,
    rowIndex: number,
    errors: string[]
  ) {
    if (text && String(text).trim().length > MAX_TEXT_LENGTH) {
      errors.push(
        `Linha ${rowIndex}: Campo "${fieldName}" excede o tamanho máximo de ${MAX_TEXT_LENGTH} caracteres.`
      );
    }
  }
}
