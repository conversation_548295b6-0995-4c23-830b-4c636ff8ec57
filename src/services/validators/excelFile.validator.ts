import * as mime from 'mime-types';
import * as xlsx from 'xlsx';

import { env } from '../../env';
import { BadRequestError } from '../../use-case/errors/BadRequestError';

export class ExcelFileValidator {
  /**
   * Valida se um arquivo é um Excel válido e seguro
   * @param file Arquivo a ser validado
   * @param options Opções de validação
   * @throws BadRequestError se o arquivo for inválido
   */
  async validate(
    file: Express.Multer.File,
    options: {
      maxSizeMB?: number;
      allowedExtensions?: string[];
    } = {}
  ): Promise<void> {
    const {
      maxSizeMB = parseInt(env.MAX_EXCEL_FILE_SIZE_MB || '10', 10),
      allowedExtensions = ['.xlsx', '.xls'],
    } = options;

    this.validateSize(file, maxSizeMB);
    this.validateExtension(file, allowedExtensions);
    this.validateSignature(file);
    this.validateMimeType(file);
    await this.validateExcelContent(file);
  }

  private validateSize(file: Express.Multer.File, maxSizeMB: number) {
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      throw new BadRequestError(`Arquivo excede o tamanho máximo permitido de ${maxSizeMB}MB.`);
    }
  }

  private validateExtension(file: Express.Multer.File, allowedExtensions: string[]) {
    const fileExtension = file.originalname
      .toLowerCase()
      .substring(file.originalname.lastIndexOf('.'));
    if (!allowedExtensions.includes(fileExtension)) {
      throw new BadRequestError(
        `Extensão de arquivo inválida. Apenas ${allowedExtensions.join(', ')} são permitidas.`
      );
    }
  }

  private validateSignature(file: Express.Multer.File) {
    const isValidSignature = this.isValidExcelSignature(file.buffer);
    if (!isValidSignature) {
      const firstBytesString = Buffer.from(file.buffer.slice(0, 20)).toString();
      if (
        firstBytesString.includes('#!/bin/') ||
        firstBytesString.includes('<?php') ||
        firstBytesString.includes('<script')
      ) {
        throw new BadRequestError(
          'Algo deu errado com o arquivo. Verifique se é um Excel válido (.xlsx ou .xls) e tente novamente. Se o problema continuar, entre em contato com o suporte.'
        );
      }

      throw new BadRequestError(
        'O arquivo enviado não possui o formato Excel esperado. Envie um arquivo válido (.xlsx ou .xls).'
      );
    }
  }

  private validateMimeType(file: Express.Multer.File) {
    const validMimeTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'application/octet-stream',
    ];

    const detectedMimeType = mime.lookup(file.originalname);
    if (detectedMimeType && !validMimeTypes.includes(detectedMimeType)) {
      throw new BadRequestError(
        'O tipo de arquivo não corresponde a um arquivo Excel válido (.xlsx ou .xls).'
      );
    }
  }

  private async validateExcelContent(file: Express.Multer.File) {
    try {
      const workbook = xlsx.read(file.buffer, {
        type: 'buffer',
        cellFormula: false,
        cellNF: false,
      });

      if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
        throw new BadRequestError('O arquivo não contém planilhas Excel válidas.');
      }

      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];

      if (!worksheet) {
        throw new BadRequestError(
          'A planilha não pôde ser lida. Verifique se o arquivo não está corrompido.'
        );
      }

      const data = xlsx.utils.sheet_to_json(worksheet);
      if (!Array.isArray(data) || data.length === 0) {
        throw new BadRequestError('O arquivo Excel não contém dados válidos.');
      }
    } catch (error) {
      if (error instanceof BadRequestError) throw error;

      console.error('Erro ao processar o Excel:', error);
      throw new BadRequestError(
        'Não foi possível processar o arquivo como Excel. Verifique o formato do arquivo.'
      );
    }
  }

  private isValidExcelSignature(buffer: Buffer): boolean {
    if (buffer.length < 4) return false;

    const isXLSX =
      buffer[0] === 0x50 && buffer[1] === 0x4b && [0x03, 0x05, 0x07].includes(buffer[2]);

    const isXLS =
      buffer[0] === 0xd0 && buffer[1] === 0xcf && buffer[2] === 0x11 && buffer[3] === 0xe0;

    return isXLSX || isXLS;
  }
}
