import { QuestionValidationDTO } from '../../model/DTO/IQuestion.dto';
import { QuestionStatusEnum } from '../../model/enums/questions.enum';
import { BadRequestError } from '../../use-case/errors/BadRequestError';

export class QuestionValidator {
  private static fieldNamesMap: Partial<Record<keyof QuestionValidationDTO, string>> = {
    title: 'Títu<PERSON>',
    customerId: 'ID do Cliente',
    status: 'Status',
    description: 'Enunciado',
    difficulty: 'Dificuldade',
    disciplineId: 'Disciplina',
    categoryId: 'Categoria',
    subcategoryId: 'Subcategoria',
    questionTypeId: 'Tipo de Questão',
    explanationText: 'Explicação da questão',
    alternatives: 'Alternativas',
  };

  /**
   * Valida os campos obrigatórios de uma questão, dependendo do status e se é uma criação ou edição.
   * @param {QuestionValidationDTO} inputData - Dados da questão a ser validada.
   * @param {boolean} [isEdit=false] - Indica se a validação é para edição (true) ou criação (false).
   * @throws {BadRequestError} Se algum campo obrigatório estiver ausente ou incorreto.
   */

  static validate(inputData: QuestionValidationDTO, isEdit = false) {
    this.validateStatus(inputData);
    this.validateRequiredFields(inputData);
    if (inputData.status !== QuestionStatusEnum.Draft) {
      this.validateAlternatives(inputData);
    }
    this.preventDirectPublication(inputData, isEdit);
  }

  private static validateStatus(inputData: QuestionValidationDTO) {
    if (!inputData.status || inputData.status.trim() === '') {
      throw new BadRequestError(
        `O campo "${this.getTranslatedFieldName('status')}" é obrigatório e não pode estar vazio.`
      );
    }
  }

  private static validateRequiredFields(inputData: QuestionValidationDTO) {
    const requiredForDraft: (keyof QuestionValidationDTO)[] = ['title', 'customerId', 'status'];
    const requiredForReview: (keyof QuestionValidationDTO)[] = [
      'description',
      'difficulty',
      'explanationText',
      'questionTypeId',
      'disciplineId',
      'categoryId',
      'subcategoryId',
      'alternatives',
    ];

    const isEmpty = (value: unknown) =>
      value === undefined || value === null || (typeof value === 'string' && value.trim() === '');

    const missingFields = (fields: (keyof QuestionValidationDTO)[]) =>
      fields.filter((field) => isEmpty(inputData[field]));

    if (inputData.status === 'draft') {
      const missingDraftFields = missingFields(requiredForDraft);
      if (missingDraftFields.length > 0) {
        throw new BadRequestError(
          `Os seguintes campos são obrigatórios para rascunho e não podem estar vazios: ${this.formatMissingFields(
            missingDraftFields
          )}`
        );
      }
    }

    if (inputData.status === 'inReview' || inputData.status === 'published') {
      const missingReviewFields = missingFields(requiredForReview);
      if (missingReviewFields.length > 0) {
        throw new BadRequestError(
          `Os seguintes campos são obrigatórios para ${inputData.status === 'published' ? 'publicação' : 'revisão'} e não podem estar vazios: ${this.formatMissingFields(
            missingReviewFields
          )}`
        );
      }
    }
  }

  private static validateAlternatives(inputData: QuestionValidationDTO) {
    if (!inputData.alternatives || inputData.alternatives.length < 2) {
      throw new BadRequestError(
        `A questão deve ter no mínimo duas ${this.getTranslatedFieldName('alternatives')}.`
      );
    }

    const alternativeMap = new Map(
      inputData.alternatives.map((alt) => [alt.option.trim().toUpperCase(), alt])
    );

    if (
      inputData.status === QuestionStatusEnum.Published ||
      inputData.status === QuestionStatusEnum.InReview
    ) {
      const requiredOptions = ['A', 'B'];
      const missingOptions = requiredOptions.filter((option) => {
        const alt = alternativeMap.get(option);
        return !alt || !alt.description || alt.description.trim().length === 0;
      });

      if (missingOptions.length > 0) {
        throw new BadRequestError(
          `Questões em revisão/publicação devem conter as alternativas obrigatórias: ${missingOptions
            .map((opt) => `Alternativa ${opt}`)
            .join(', ')}`
        );
      }
    }

    const requiredSequence = ['A', 'B', 'C', 'D', 'E'];
    for (let i = requiredSequence.length - 1; i > 0; i--) {
      const dependentAlt = alternativeMap.get(requiredSequence[i]);
      const requiredAlt = alternativeMap.get(requiredSequence[i - 1]);

      if (dependentAlt?.description?.trim() && (!requiredAlt || !requiredAlt.description?.trim())) {
        throw new BadRequestError(
          `Se a alternativa '${requiredSequence[i]}' for enviada com descrição, a alternativa '${requiredSequence[i - 1]}' deve estar presente com uma descrição válida.`
        );
      }
    }
  }

  private static preventDirectPublication(inputData: QuestionValidationDTO, isEdit: boolean) {
    if (inputData.status === 'published') {
      this.validateRequiredFields(inputData);

      if (!isEdit) {
        throw new BadRequestError(
          'Não é possível publicar diretamente. A questão deve ser revisada primeiro.'
        );
      }
    }
  }

  private static getTranslatedFieldName(field: keyof QuestionValidationDTO): string {
    return this.fieldNamesMap[field] || field;
  }

  private static formatMissingFields(fields: (keyof QuestionValidationDTO)[]): string {
    return fields.map((field) => this.getTranslatedFieldName(field)).join(', ');
  }
}
