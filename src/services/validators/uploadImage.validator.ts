export const maxFileSizeMb = 1;
const allowedMimeTypes = ['image/png', 'image/jpeg'];

interface FileTypeResult {
  ext: string;
  mime: string;
}

export class QuestionImageValidator {
  /**
   * Verifica se um arquivo é uma imagem válida
   * @param file Arquivo a ser verificado
   * @returns boolean (se o arquivo é válido ou não)
   */
  async isValidImage(file: Express.Multer.File): Promise<boolean> {
    console.log('file', file);

    console.log('Arquivo recebido:', {
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      bufferLength: file.buffer?.length,
    });
    if (file.size / (1024 * 1024) > maxFileSizeMb) {
      return false;
    }

    const detectedType = await this.detectFileType(file.buffer);
    return !!detectedType?.mime && allowedMimeTypes.includes(detectedType.mime);
  }

  private async detectFileType(buffer: Buffer): Promise<FileTypeResult | undefined> {
    const fileTypeModule = await import('file-type');
    const { fileTypeFromBuffer } = fileTypeModule;
    return fileTypeFromBuffer(buffer);
  }
}
