import { KnexCustomerRepository } from '../repositories/knex/customers.repositories';
import { ResourceNotFoundError } from '../use-case/errors/ResourceNotFound';

export const verifyCustomer = async (customerId: string) => {
  const customersRepository = new KnexCustomerRepository();

  const customer = await customersRepository.findById(customerId);

  if (!customer) {
    throw new ResourceNotFoundError('Cliente não encontrado!');
  }

  return customer;
};
