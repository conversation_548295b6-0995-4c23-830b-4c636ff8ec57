import { ICustomer } from '../model/ICustomer';
import { ICustomerRepository } from '../repositories/assign/customers.assign';
import { ResourceNotFoundError } from '../use-case/errors/ResourceNotFound';

export const verifyExternalCustomer = async (
  customerRepository: ICustomerRepository,
  externalCustomerId: string
): Promise<ICustomer> => {
  const cliente = await customerRepository.findOneBy({
    external_customer_id: externalCustomerId,
    deleted_at: null,
  });

  if (!cliente) {
    throw new ResourceNotFoundError('Cliente não encontrado');
  }

  return cliente;
};
