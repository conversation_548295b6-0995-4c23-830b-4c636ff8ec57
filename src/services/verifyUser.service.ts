import { IUser } from '../model/IUser';
import { IUsersRepository } from '../repositories/assign/users.assign';
import { ResourceNotFoundError } from '../use-case/errors/ResourceNotFound';

export const verifyUser = async (
  userRepository: IUsersRepository,
  customerId: string,
  userId: string
): Promise<IUser> => {
  const user = await userRepository.findUserAndUpdateLastAccess(userId);

  if (!user) {
    throw new ResourceNotFoundError('Usu<PERSON>rio não encontrado');
  }

  return user;
};
