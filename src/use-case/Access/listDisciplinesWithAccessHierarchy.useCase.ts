import { ICustomerIdDTO } from '../../model/DTO/ICustumers.dto';
import { IDisciplinesAccessHierarchyDTO } from '../../model/DTO/IDiscipline.dto';
import { IDisciplinesRepository } from '../../repositories/assign/disciplines.assign';

export class ListDisciplinesWithAccessHierarchyUseCase {
  constructor(private disciplinesRepository: IDisciplinesRepository) {}

  async execute({ customerId }: ICustomerIdDTO): Promise<IDisciplinesAccessHierarchyDTO[]> {
    return await this.disciplinesRepository.listDisciplinesWithAccessHierarchy({
      customerId,
    });
  }
}
