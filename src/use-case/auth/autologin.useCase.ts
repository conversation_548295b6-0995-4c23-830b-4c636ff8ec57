import { JwtPayload } from 'jsonwebtoken';

import { env } from '../../env';
import { ICustomer } from '../../model/ICustomer';
import { IUser } from '../../model/IUser';
import { ICustomerRepository } from '../../repositories/assign/customers.assign';
import { IUsersRepository } from '../../repositories/assign/users.assign';
import { generateToken } from '../../services/jwt/generateToken.jwt';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

interface AutologinTokenPayload extends JwtPayload {
  id: string;
  customerId: string;
}

interface AutologinUseCaseResponse {
  customer: ICustomer;
  token: string;
}

export class AutologinUseCase {
  constructor(
    private readonly customerRepository: ICustomerRepository,
    private readonly userRepository: IUsersRepository
  ) {}

  async execute(authPayload: AutologinTokenPayload): Promise<AutologinUseCaseResponse> {
    const { customerId, id } = authPayload;

    const customer = await this.verifyCustomerByExternalId(customerId);

    const user = await this.verifyUserByExternalId(id, customer);

    const newToken = generateToken(
      {
        id: user.id,
        customerId: customer.id,
        externalCustomerId: customer.external_customer_id,
        externalUserId: id,
        role: user.role,
      },
      env.JWT_SECRET
    );

    return { customer, token: newToken };
  }

  private async verifyCustomerByExternalId(externalCustomerId: string): Promise<ICustomer> {
    const customer = await this.customerRepository.findByExternalId(externalCustomerId);

    if (!customer) {
      throw new ResourceNotFoundError('Cliente não encontrado!');
    }

    return customer;
  }

  private async verifyUserByExternalId(
    externalUserId: string,
    customer: Pick<ICustomer | ICustomer, 'id'>
  ): Promise<IUser> {
    const user = await this.userRepository.findOneBy({
      external_user_id: externalUserId,
      customer_id: customer.id,
      deleted_at: null,
    });

    if (!user) {
      throw new ResourceNotFoundError('Usuário não encontrado!');
    }

    return user;
  }
}
