import bcrypt from 'bcryptjs';

import { IUsersRepository } from '../../repositories/assign/users.assign';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

interface ChangePasswordInput {
  oldPassword: string;
  newPassword: string;
  userId: string;
}

export class ChangePasswordUseCase {
  constructor(private userRepository: IUsersRepository) {}

  public async execute({ oldPassword, newPassword, userId }: ChangePasswordInput): Promise<void> {
    const user = await this.userRepository.findOneBy({ id: userId });

    if (!user) {
      throw new ResourceNotFoundError('Usuário não encontrado');
    }

    const passwordCript = await bcrypt.hash(newPassword, 10);

    const compare = await bcrypt.compare(oldPassword, user.password!);

    if (!compare) {
      throw new ResourceNotFoundError('Senha atual inválida.');
    }
    await this.userRepository.changePassword(userId, passwordCript);
  }
}
