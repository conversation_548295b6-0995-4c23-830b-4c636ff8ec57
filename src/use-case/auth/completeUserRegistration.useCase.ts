import bcrypt from 'bcryptjs';
import { randomUUID } from 'crypto';
import { Knex } from 'knex';

import { generateTransaction } from '../../helpers/transaction.helper';
import { TokenInfoResponseDto } from '../../model/DTO/response/user.dto';
import { RolesEnum } from '../../model/enums/roles.enum';
import { ICountriesRepository } from '../../repositories/assign/countries.assign';
import { IRecoveryRepository } from '../../repositories/assign/recoveries.assign';
import { ITermsRepository } from '../../repositories/assign/terms.assign';
import { ITermsAcceptedRepository } from '../../repositories/assign/termsAccepted.assign';
import { IUsersRepository } from '../../repositories/assign/users.assign';
import { IUsersAdditionalInfosRepository } from '../../repositories/assign/usersAdditionalInfos.assign';
import { CompleteUserRegistrationDTO } from '../../schema/auth.schema';
import { BadRequestError } from '../errors/BadRequestError';
import { GenericError } from '../errors/GenericError';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

export class CompleteUserRegistrationUseCase {
  constructor(
    private readonly recoveryRepository: IRecoveryRepository,
    private readonly usersRepository: IUsersRepository,
    private readonly termsRepository: ITermsRepository,
    private readonly termsAcceptRepository: ITermsAcceptedRepository,
    private readonly countriesRepository: ICountriesRepository,
    private readonly usersAdditionalInfosRepository: IUsersAdditionalInfosRepository
  ) {}

  async execute(data: CompleteUserRegistrationDTO): Promise<void> {
    const trx = await generateTransaction();

    try {
      const recovery = await this.recoveryRepository.findUserByRecoveryToken(data.token);
      await this.validateRecoveryToken(recovery as TokenInfoResponseDto);

      const user = await this.usersRepository.findOneBy({ id: recovery?.id });

      if (!user) {
        throw new ResourceNotFoundError('Usuário não encontrado');
      }

      const isStudent = user.role === RolesEnum.STUDENT;

      if (isStudent) {
        if (data.countryId) {
          await this.validateCountryId(data.countryId);
        }

        this.validateIncompleteUser(user);

        const terms = await this.termsRepository.findOneBy({ id: data.termsId });
        if (!terms) {
          throw new ResourceNotFoundError('Termos não encontrados');
        }

        const passwordHash = await this.generatePasswordHash(data.password);

        const updatedUser = await this.usersRepository.updateWithTrx(
          {
            id: user.id,
            first_name: data.firstName,
            last_name: data.lastName,
            birth_date: data.birthDate,
            gender: data.gender,
            cpf: data.cpf,
            password: passwordHash,
            phone_number: data.phoneNumber,
            country_id: data.countryId,
            ddi: data.ddi,
            accept_marketing: data.acceptMarketing,
            accept_newsletter: data.acceptNewsletter,
            updated_at: new Date(),
          },
          trx
        );

        if (!updatedUser) {
          await trx.rollback();
          throw new GenericError('Erro ao atualizar dados do usuário');
        }

        await this.saveUserAdditionalInfo(user.id, data, trx);

        await this.acceptTerms(user.id, data, trx);
      } else {
        const passwordHash = await this.generatePasswordHash(data.password);

        const updatedUser = await this.usersRepository.updateWithTrx(
          {
            id: user.id,
            password: passwordHash,
            updated_at: new Date(),
          },
          trx
        );

        if (!updatedUser) {
          await trx.rollback();
          throw new GenericError('Erro ao atualizar senha do usuário');
        }
      }

      await this.markTokenAsUsed(recovery?.recoveryId, trx);

      await trx.commit();
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  private async generatePasswordHash(password: string): Promise<string> {
    return bcrypt.hash(password, 10);
  }

  private async saveUserAdditionalInfo(
    userId: string,
    data: CompleteUserRegistrationDTO,
    trx: Knex.Transaction
  ): Promise<void> {
    const existingInfo = await this.usersAdditionalInfosRepository.findOneBy({ user_id: userId });

    const additionalInfoData = {
      user_id: userId,
      referral_source: data.referralSource,
      formation: data.formation,
      completion_year: data.completionYear,
      has_specialty: data.hasSpecialty,
      currently_attending: data.currentlyAttending,
      attendance_location: data.attendanceLocation,
      institution_id: data.institutionId,
      graduation_id: data.graduationId,
      primary_specialty_id: data.primarySpecialtyId,
      secondary_specialty_id: data.secondarySpecialtyId,
      created_at: new Date(),
      updated_at: new Date(),
      deleted_at: null,
    };

    if (existingInfo) {
      const updatedInfo = await this.usersAdditionalInfosRepository.updateUserAdditionalInfo(
        {
          id: existingInfo.id,
          ...additionalInfoData,
        },
        trx
      );

      if (!updatedInfo) {
        await trx.rollback();
        throw new GenericError('Erro ao atualizar informações adicionais do usuário');
      }
    } else {
      const createdInfo = await this.usersAdditionalInfosRepository.createUserAdditionalInfo(
        {
          id: randomUUID(),
          ...additionalInfoData,
        },
        trx
      );

      if (!createdInfo) {
        await trx.rollback();
        throw new GenericError('Erro ao criar informações adicionais do usuário');
      }
    }
  }

  private async acceptTerms(
    userId: string,
    data: CompleteUserRegistrationDTO,
    trx: Knex.Transaction
  ): Promise<void> {
    await this.termsAcceptRepository.createTermsAccept(
      {
        id: randomUUID(),
        user_id: userId,
        terms_id: data.termsId,
        accepted_terms: data.termsAccept,
        date_accepted_terms: data.termsAccept ? new Date() : undefined,
        created_at: new Date(),
        updated_at: new Date(),
      },
      trx
    );
  }

  private async markTokenAsUsed(
    recoveryId: string | undefined,
    trx: Knex.Transaction
  ): Promise<void> {
    const updatedRecovery = await this.recoveryRepository.updateRecovery(
      {
        id: recoveryId,
        used_at: new Date(),
      },
      trx
    );

    if (!updatedRecovery) {
      await trx.rollback();
      throw new GenericError('Erro ao atualizar recovery');
    }
  }

  private async validateRecoveryToken(recovery: TokenInfoResponseDto): Promise<void> {
    if (!recovery) {
      throw new ResourceNotFoundError('Token inválido');
    }

    if (recovery.used_at) {
      throw new BadRequestError('Token já foi utilizado');
    }

    if (recovery.expiresAt && new Date(recovery.expiresAt) < new Date()) {
      throw new BadRequestError('Token expirado');
    }
  }

  private async validateCountryId(countryId: string): Promise<void> {
    const country = await this.countriesRepository.findOneBy({ id: countryId });

    if (!country) {
      throw new BadRequestError('País não encontrado');
    }
  }

  private validateIncompleteUser(user: {
    first_name?: string;
    last_name?: string;
    password?: string;
  }): void {
    if (user.first_name && user.last_name && user.password) {
      throw new BadRequestError('Usuário já possui registro completo');
    }
  }
}
