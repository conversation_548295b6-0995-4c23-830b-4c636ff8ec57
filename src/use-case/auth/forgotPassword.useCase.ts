import { randomUUID } from 'crypto';
import fs from 'fs';
import path from 'path';

import { env } from '../../env';
import { ICustomerRepository } from '../../repositories/assign/customers.assign';
import { IFilesRepository } from '../../repositories/assign/files.assign';
import { IRecoveryRepository } from '../../repositories/assign/recoveries.assign';
import { ITermsRepository } from '../../repositories/assign/terms.assign';
import { IUsersRepository } from '../../repositories/assign/users.assign';
import S3Service from '../../services/aws/s3/S3Service';
import { SESService } from '../../services/aws/ses/SESService';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

interface IForgotPasswordInput {
  email: string;
  customerId: string;
}

export class ForgotPasswordUseCase {
  constructor(
    private readonly customerRepository: ICustomerRepository,
    private readonly userRepository: IUsersRepository,
    private readonly recoveryRepository: IRecoveryRepository,
    private readonly termsRepository: ITermsRepository,
    private readonly filesRepository: IFilesRepository,
    private readonly sesService: SESService,
    private readonly s3Service: S3Service
  ) {}

  async execute({ email, customerId }: IForgotPasswordInput): Promise<void> {
    const user = await this.userRepository.findOneBy({
      email,
      customer_id: customerId,
      deleted_at: null,
    });

    if (!user) {
      throw new ResourceNotFoundError('Usuário não encontrado');
    }

    const customer = await this.customerRepository.findById(customerId);

    if (!customer || !customer?.subdomain) {
      throw new ResourceNotFoundError('Customer não informado ou sem subdomain');
    }

    const resetToken = randomUUID();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000);

    await this.recoveryRepository.createRecovery({
      id: randomUUID(),
      user_id: user.id,
      token: resetToken,
      expires_at: expiresAt,
    });

    const url = customer?.subdomain;
    const urlForgotPassword = `https://${url}/welcome/${resetToken}`;

    let termosUrl = '#';
    try {
      const terms = await this.termsRepository.findOneBy({ customer_id: customerId, active: true });
      if (terms && terms.file_id) {
        const file = await this.filesRepository.findOneBy({ id: terms.file_id });
        if (file) {
          const bucket = env.BUCKET_FILES;
          termosUrl = await this.s3Service.getSignedUrl(bucket, file.url, 3600);
        }
      }
    } catch (error) {
      console.error('Erro ao buscar termos de uso:', error);
    }

    let termsLinks = '';
    if (termosUrl && termosUrl !== '#') {
      termsLinks = `<a href="${termosUrl}" style="color:#2F80ED; text-decoration:underline; margin:0 8px; font-size:11px; font-family:Inter,sans-serif; font-style:normal; font-weight:500; line-height:155%; letter-spacing:0.22px;">Termos de uso</a>`;
    }

    const currentYear = new Date().getFullYear();
    const supportEmail = customer.support_email || customer.email!;
    let supportLink = '';
    if (supportEmail) {
      supportLink = `<a href="mailto:${supportEmail}" style="color:#2F80ED; text-decoration:underline; margin:0 8px; font-size:11px; font-family:Inter,sans-serif; font-style:normal; font-weight:500; line-height:155%; letter-spacing:0.22px;">Suporte</a>`;
    }

    const templatePath = path.join(__dirname, '../../templates/email/forgotPassword.template.html');
    let emailTemplate = fs.readFileSync(templatePath, 'utf8');

    emailTemplate = emailTemplate
      .replace(/{{firstName}}/g, user.first_name || user.email)
      .replace(/{{link}}/g, urlForgotPassword)
      .replace(/{{empresa}}/g, customer.name)
      .replace(/{{ano}}/g, currentYear.toString())
      .replaceAll('#C1086C', customer.primary_color || customer.secondary_color || '#C1086C')
      .replace(/{{termsLinks}}/g, termsLinks)
      .replace(/{{supportLink}}/g, supportLink);

    await this.sesService.sendEmail({
      to: email,
      subject: 'Recuperação de senha',
      body: emailTemplate,
      isHtml: true,
    });
  }
}
