import { OAuth2Client } from 'google-auth-library';

import { env } from '../../env';
import { RolesEnum } from '../../model/enums/roles.enum';
import { IUsersRepository } from '../../repositories/assign/users.assign';
import { generateToken } from '../../services/jwt/generateToken.jwt';
import { BadRequestError } from '../errors/BadRequestError';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

export class GoogleAuthUseCase {
  constructor(private userRepository: IUsersRepository) {}

  async execute({ googleToken }: { googleToken: string }) {
    const client = new OAuth2Client(env.GOOGLE_CLIENT_ID);

    const ticket = await client.verifyIdToken({
      idToken: googleToken,
      audience: env.GOOGLE_CLIENT_ID,
    });

    const payload = ticket.getPayload();

    if (!payload || !payload.email) {
      throw new BadRequestError('Google token inválido');
    }

    let userExists = await this.userRepository.findOneBy({ email: payload.email });

    if (!userExists) {
      userExists = await this.userRepository.createUserFromGoogle({
        email: payload.email,
        first_name: payload.given_name || '',
        last_name: payload.family_name || '',
        active: true,
        role: RolesEnum.STUDENT,
        image: payload.picture || null,
        customer_id: null,
        created_by: 'google',
        terms_accept: false,
      });
    }

    if (!userExists) {
      throw new ResourceNotFoundError('Usuário não encontrado');
    }

    const token = generateToken({ id: userExists.id, email: userExists.email }, env.JWT_SECRET);
    return { token };
  }
}
