import bcrypt from 'bcryptjs';

import { generateTransaction } from '../../helpers/transaction.helper';
import { IRecoveryRepository } from '../../repositories/assign/recoveries.assign';
import { IUsersRepository } from '../../repositories/assign/users.assign';
import { BadRequestError } from '../errors/BadRequestError';
import { GenericError } from '../errors/GenericError';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

export class RedefinePasswordUseCase {
  constructor(
    private readonly recoveryRepository: IRecoveryRepository,
    private readonly usersRepository: IUsersRepository
  ) {}

  async execute(token: string, newPassword: string): Promise<void> {
    const trx = await generateTransaction();
    const recovery = await this.recoveryRepository.findUserByRecoveryToken(token);

    if (!recovery) {
      throw new ResourceNotFoundError('Token inválido');
    }

    if (recovery.used_at) {
      throw new BadRequestError('Token já foi utilizado');
    }

    if (recovery.expiresAt && new Date(recovery.expiresAt) < new Date()) {
      throw new BadRequestError('Token expirado');
    }

    const passwordHash = await bcrypt.hash(newPassword, 10);
    const userUpdated = await this.usersRepository.changePassword(recovery.id, passwordHash, trx);

    if (!userUpdated) {
      await trx.rollback();
      throw new GenericError('Erro ao atualizar senha');
    }

    const updatedRecovery = await this.recoveryRepository.updateRecovery(
      {
        id: recovery.recoveryId,
        used_at: new Date(),
      },
      trx
    );

    if (!updatedRecovery) {
      await trx.rollback();
      throw new GenericError('Erro ao atualizar recovery');
    }

    await trx.commit();
  }
}
