import { IRecoveryRepository } from '../../repositories/assign/recoveries.assign';
import { BadRequestError } from '../errors/BadRequestError';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

export class TokenInfoUseCase {
  constructor(private readonly recoveryRepository: IRecoveryRepository) {}
  async execute(token: string): Promise<{ email: string }> {
    const recovery = await this.recoveryRepository.findUserByRecoveryToken(token);

    if (!recovery) {
      throw new ResourceNotFoundError('Token inválido');
    }

    if (recovery.expiresAt && new Date(recovery.expiresAt) < new Date()) {
      throw new BadRequestError('Token expirado');
    }

    return { email: recovery.email };
  }
}
