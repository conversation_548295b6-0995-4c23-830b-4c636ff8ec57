import { env } from '../../env';
import { BulkImportStatus } from '../../model/enums/bulkImportStatus.enum';
import { IBulkImportRepository } from '../../repositories/assign/bulkImport.assign';
import { IBulkImportDetailRepository } from '../../repositories/assign/bulkImportDetails.assign';
import { IDisciplinesRepository } from '../../repositories/assign/disciplines.assign';
import { IQuestionsRepository } from '../../repositories/assign/questions.assign';
import { ExcelProcessor, ExcelRow } from '../../services/excelProcessor.service';
import { filterTextRegex } from '../../services/filterTextRegex.service';
import {
  MetadataMaps,
  MetadataResolverService,
} from '../../services/resolvers/metadataHierarchyResolver.service';
import { UploadService } from '../../services/uploadFile.service';
import { QuestionValidationService } from '../../services/validators/bulkQuestionsRow.validator';
import { ExcelFileValidator } from '../../services/validators/excelFile.validator';
import { BadRequestError } from '../errors/BadRequestError';
import { ExcelRowData } from './uploadQuestionsExcel.useCase';

export interface ExcelRowWithErrors {
  row: ExcelRow;
  errors: string[];
}

/**
 * Classe base abstrata para casos de uso de importação em massa
 */
export abstract class AbstractBulkImportUseCase {
  protected metadataResolver: MetadataResolverService;
  protected questionValidationService: QuestionValidationService;

  constructor(
    protected readonly bulkImportRepository: IBulkImportRepository,
    protected readonly bulkImportDetailRepository: IBulkImportDetailRepository,
    protected readonly questionsRepository: IQuestionsRepository,
    protected readonly disciplinesRepository: IDisciplinesRepository,
    protected readonly excelProcessor: ExcelProcessor,
    protected readonly uploadService: UploadService
  ) {
    this.metadataResolver = new MetadataResolverService(disciplinesRepository);
    this.questionValidationService = new QuestionValidationService();
  }

  protected async validateExcelFile(file: Express.Multer.File): Promise<void> {
    try {
      const excelValidator = new ExcelFileValidator();

      await excelValidator.validate(file, {
        maxSizeMB: parseInt(env.MAX_EXCEL_FILE_SIZE_MB || '10', 10),
        allowedExtensions: ['.xlsx', '.xls'],
      });
    } catch (error) {
      if (error instanceof BadRequestError) {
        throw error;
      } else {
        console.error('Erro na validação do arquivo Excel:', error);
        throw new BadRequestError(
          'Não foi possível validar o arquivo. Verifique se é um arquivo Excel válido.'
        );
      }
    }
  }

  protected validateRowCount(totalRows: number): void {
    const maxRows = this.getMaxRowsLimit();
    if (totalRows > maxRows) {
      throw new BadRequestError(
        `O arquivo excede o limite máximo de ${maxRows} linhas. Por favor, divida o arquivo em partes menores.`
      );
    }
  }

  protected getMaxRowsLimit(): number {
    return parseInt(env.MAX_EXCEL_ROWS || '1000', 10);
  }

  protected convertToExcelRowData(row: ExcelRow): ExcelRowData {
    return {
      disciplina: String(row.disciplina || '').trim(),
      categoria: String(row.categoria || '').trim(),
      subcategoria: String(row.subcategoria || '').trim(),
      enunciado: String(row.enunciado || '').trim(),
      alternativa_a: String(row.alternativa_a || '').trim(),
      alternativa_b: String(row.alternativa_b || '').trim(),
      alternativa_c: String(row.alternativa_c || '').trim(),
      alternativa_d: String(row.alternativa_d || '').trim(),
      alternativa_e: String(row.alternativa_e || '').trim(),
      correta: String(row.correta || '')
        .trim()
        .toLowerCase(),
      explicacao: String(row.explicacao || '').trim(),
      dificuldade: String(row.dificuldade || '').trim(),
      tipo_questao: String(row.tipo_questao || '').trim(),
      discipline_id: '',
      category_id: '',
      subcategory_id: '',
      question_type_id: '',
    };
  }

  protected checkDuplicateStatements(rows: ExcelRow[]): {
    enunciadosMap: Map<string, number>;
    duplicates: Map<string, number>;
  } {
    const enunciadosMap = new Map<string, number>();
    const duplicates = new Map<string, number>();

    rows.forEach((row, index) => {
      const enunciado = row.enunciado?.trim();
      if (enunciado) {
        const rowIndex = index + 2; // Ajustando para considerar o cabeçalho e índice 1-based
        if (enunciadosMap.has(enunciado)) {
          duplicates.set(enunciado, enunciadosMap.get(enunciado)!);
        } else {
          enunciadosMap.set(enunciado, rowIndex);
        }
      }
    });

    return { enunciadosMap, duplicates };
  }

  protected addMetadataIds(rowData: ExcelRowData, metadataMaps: MetadataMaps): void {
    const disciplinaLower = filterTextRegex(rowData.disciplina.toLowerCase());
    const categoriaLower = filterTextRegex(rowData.categoria.toLowerCase());
    const subcategoriaLower = filterTextRegex(rowData.subcategoria.toLowerCase());

    const categoryId = metadataMaps.categories.get(categoriaLower)?.id;

    const categorySubcategoryAccess = metadataMaps.categorySubcategoryAccess.get(categoryId || '');

    const subcategoryExists = categorySubcategoryAccess
      ? Array.from(categorySubcategoryAccess).find((item) => {
          return Object.keys(item).some((value) => {
            return filterTextRegex(value.toLowerCase()) === subcategoriaLower;
          });
        })
      : undefined;

    const subcategoryId = subcategoryExists
      ? subcategoryExists[subcategoriaLower]
      : metadataMaps.subcategories.get(subcategoriaLower)?.id;

    if (metadataMaps.disciplines.has(disciplinaLower)) {
      rowData.discipline_id = metadataMaps.disciplines.get(disciplinaLower)?.id;
    }

    if (metadataMaps.categories.has(categoriaLower)) {
      rowData.category_id = metadataMaps.categories.get(categoriaLower)?.id;
    }

    if (subcategoryId) {
      rowData.subcategory_id = subcategoryId;
    }
  }

  protected async insertImportDetails(
    importId: string,
    updatedValidRows: ExcelRowData[],
    updatedInvalidRows: { row: ExcelRowData; errors: string[] }[]
  ): Promise<void> {
    const allDetailsToInsert = [
      ...updatedValidRows.map((row, index) => ({
        bulk_import_id: importId,
        row_data: row,
        is_valid: true,
        row_index: index + 1,
      })),
      ...updatedInvalidRows.map((item, index) => ({
        bulk_import_id: importId,
        row_data: item.row,
        is_valid: false,
        errors: item.errors,
        row_index: updatedValidRows.length + index + 1,
      })),
    ];

    const DETAILS_BATCH_SIZE = 100;
    for (let i = 0; i < allDetailsToInsert.length; i += DETAILS_BATCH_SIZE) {
      const detailsBatch = allDetailsToInsert.slice(i, i + DETAILS_BATCH_SIZE);
      try {
        await this.bulkImportDetailRepository.insertBatch(detailsBatch);
      } catch (error) {
        console.error('Erro ao inserir lote de detalhes:', error);
        throw error;
      }
    }
  }

  protected async updateImportStats(
    importId: string,
    updatedValidRows: ExcelRowData[],
    updatedInvalidRows: { row: ExcelRowData; errors: string[] }[],
    totalRows: number
  ): Promise<void> {
    const validCount = updatedValidRows.length;
    const invalidCount = updatedInvalidRows.length;
    const successPercentage = totalRows > 0 ? Math.round((validCount / totalRows) * 100) : 0;

    await this.bulkImportRepository.update({
      id: importId,
      valid_count: validCount,
      invalid_count: invalidCount,
      success_percentage: successPercentage,
    });
  }

  protected async updateImportStatus(importId: string, status: BulkImportStatus): Promise<void> {
    await this.bulkImportRepository.update({
      id: importId,
      status,
    });
  }

  protected async uploadFileToStorage(
    file: Express.Multer.File,
    customerId: string,
    previousFileKey?: string | null
  ): Promise<string> {
    const bucketName = env.AWS_S3_PUBLIC_BUCKET;

    if (previousFileKey) {
      try {
        await this.uploadService.deleteFile(previousFileKey, bucketName);
      } catch (error) {
        console.error('Erro ao excluir arquivo antigo:', error);
      }
    }

    const timestamp = Date.now();
    const sanitizedFileName = file.originalname.replace(/\s+/g, '-');
    const storagePath = `${customerId}/bulk-imports/questions/${timestamp}-${sanitizedFileName}`;

    return this.uploadService.uploadFile({
      file,
      storagePath,
      bucket: bucketName,
    });
  }

  protected async updateImportWithFileKey(importId: string, fileKey: string): Promise<void> {
    await this.bulkImportRepository.update({
      id: importId,
      file_key: fileKey,
    });
  }

  protected async handleBackgroundProcessingError(
    importId: string,
    error: Error | unknown
  ): Promise<void> {
    console.error(`Falha no processamento em background para importação ${importId}:`, error);

    try {
      await this.bulkImportRepository.update({
        id: importId,
        status: BulkImportStatus.VALIDATION_FAILED,
        updated_at: new Date(),
      });

      await this.bulkImportDetailRepository.insert({
        bulk_import_id: importId,
        row_data: { error: 'Erro de processamento' } as unknown as ExcelRowData,
        is_valid: false,
        errors: [
          'Ocorreu um erro durante o processamento do arquivo. Por favor, tente novamente.',
          error instanceof Error ? error.message : String(error),
        ],
        row_index: 0,
      });
    } catch (updateError) {
      console.error(
        `Não foi possível atualizar o status da importação ${importId} após falha:`,
        updateError
      );
    }
  }

  protected abstract processRows(
    validRows: ExcelRow[],
    invalidRows: ExcelRowWithErrors[],
    customerId: string
  ): Promise<{
    updatedValidRows: ExcelRowData[];
    updatedInvalidRows: { row: ExcelRowData; errors: string[] }[];
  }>;

  protected abstract completeProcessingInBackground(
    importId: string,
    file: Express.Multer.File,
    customerId: string,
    userId: string,
    validRows: ExcelRow[],
    invalidRows: ExcelRowWithErrors[],
    stats: {
      totalRows: number;
      validCount: number;
      invalidCount: number;
      successPercentage: number;
    },
    previousFileKey?: string | null
  ): Promise<void>;
}
