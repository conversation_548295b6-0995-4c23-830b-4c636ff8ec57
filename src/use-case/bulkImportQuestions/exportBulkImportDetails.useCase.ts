import { IBulkImportRepository } from '../../repositories/assign/bulkImport.assign';
import { IBulkImportDetailRepository } from '../../repositories/assign/bulkImportDetails.assign';
import { ExcelProcessor, ExcelRow } from '../../services/excelProcessor.service';
import { BadRequestError } from '../errors/BadRequestError';

interface ExportAllRowsRequest {
  importId: string;
  customerId: string;
}

interface ExportAllRowsResponse {
  fileName: string;
  buffer: Buffer;
}

export class ExportBulkImportDetailsUseCase {
  private readonly excelProcessor = new ExcelProcessor();

  constructor(
    private readonly bulkImportRepository: IBulkImportRepository,
    private readonly bulkImportDetailRepository: IBulkImportDetailRepository
  ) {}

  async execute({ importId, customerId }: ExportAllRowsRequest): Promise<ExportAllRowsResponse> {
    const bulkImport = await this.bulkImportRepository.findOneBy({
      id: importId,
      customer_id: customerId,
    });

    if (!bulkImport) {
      throw new BadRequestError('Importação não encontrada');
    }

    const allRows = await this.bulkImportDetailRepository.findByImportId(importId);

    if (allRows.length === 0) {
      throw new BadRequestError('Não há linhas para exportar');
    }

    const columnOrder = [
      'disciplina',
      'categoria',
      'subcategoria',
      'tipo_questao',
      'enunciado',
      'alternativa_a',
      'alternativa_b',
      'alternativa_c',
      'alternativa_d',
      'alternativa_e',
      'correta',
      'dificuldade',
      'explicacao',
    ];

    const formattedRows = allRows.map((row) => {
      const rowData = { ...row.row_data } as ExcelRow;

      const idFields = ['discipline_id', 'category_id', 'subcategory_id', 'question_type_id'];

      idFields.forEach((field) => {
        if (rowData && field in rowData) {
          delete rowData[field];
        }
      });

      const orderedRowData: Record<string, unknown> = {};
      columnOrder.forEach((field) => {
        if (field in rowData) {
          orderedRowData[field] = rowData[field];
        } else {
          orderedRowData[field] = '';
        }
      });

      return {
        row: orderedRowData as ExcelRow,
        isValid: row.is_valid,
        errors: row.errors || [],
      };
    });

    const buffer = await this.excelProcessor.generateFullExcelWithErrors(formattedRows);

    const originalFileName = bulkImport.file_name;
    const fileNameWithoutExt = originalFileName.replace(/\.[^/.]+$/, '');

    const today = new Date();
    const dateStr = today.toISOString().split('T')[0];

    const fileName = `${fileNameWithoutExt}_${dateStr}.xlsx`;

    return {
      fileName,
      buffer,
    };
  }
}
