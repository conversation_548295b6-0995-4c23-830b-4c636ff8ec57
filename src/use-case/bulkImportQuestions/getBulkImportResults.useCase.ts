import { IBulkImport } from '../../model/IBulkImport';
import { IBulkImportDetail } from '../../model/IBulkImportDetails';
import { IBulkImportRepository } from '../../repositories/assign/bulkImport.assign';
import { IBulkImportDetailRepository } from '../../repositories/assign/bulkImportDetails.assign';
import { GetBulkImportResultsInput } from '../../schema/bulkImport.schema';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

interface GetBulkImportResultsRequest extends GetBulkImportResultsInput {}

interface GetBulkImportResultsResponse {
  import: IBulkImport;
  stats: {
    totalRows: number;
    validCount: number;
    invalidCount: number;
    successPercentage: number;
  };
  validRows?: IBulkImportDetail[];
  invalidRows?: IBulkImportDetail[];
}

export class GetBulkImportResultsUseCase {
  constructor(
    private bulkImportRepository: IBulkImportRepository,
    private bulkImportDetailRepository: IBulkImportDetailRepository
  ) {}

  async execute(request: GetBulkImportResultsRequest): Promise<GetBulkImportResultsResponse> {
    const { importId, customerId } = request;

    const importRecord = await this.bulkImportRepository.findOneBy({
      id: importId,
      customer_id: customerId,
    });

    if (!importRecord) {
      throw new ResourceNotFoundError('Importação não encontrada');
    }

    const counts = await this.bulkImportDetailRepository.countByImportId(importId);

    const totalRows = counts.totalValid + counts.totalInvalid;
    const successPercentage = totalRows > 0 ? (counts.totalValid / totalRows) * 100 : 0;

    // const [validRows, invalidRows] = await Promise.all([
    //   this.bulkImportDetailRepository.findValidByImportId(importId),
    //   this.bulkImportDetailRepository.findInvalidByImportId(importId),
    // ]);

    return {
      import: importRecord,
      stats: {
        totalRows,
        validCount: counts.totalValid,
        invalidCount: counts.totalInvalid,
        successPercentage: Math.round(successPercentage),
      },
      // validRows,
      // invalidRows,
    };
  }
}
