import { IBaseAlternativeDTO } from '../../model/DTO/IAlternative.dto';
import { BulkImportStatus } from '../../model/enums/bulkImportStatus.enum';
import { DifficultyEnum, QuestionStatusEnum } from '../../model/enums/questions.enum';
import { IBulkImportDetail } from '../../model/IBulkImportDetails';
import { IAlternativesRepository } from '../../repositories/assign/alternatives.assign';
import { IBulkImportRepository } from '../../repositories/assign/bulkImport.assign';
import { IBulkImportDetailRepository } from '../../repositories/assign/bulkImportDetails.assign';
import { IDisciplinesRepository } from '../../repositories/assign/disciplines.assign';
import { IQuestionsRepository } from '../../repositories/assign/questions.assign';
import { QuestionCodeGeneratorService } from '../../services/questionCodeGenerator.service';
import { BadRequestError } from '../errors/BadRequestError';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';
import { ExcelRowData } from './uploadQuestionsExcel.useCase';

interface ImportValidatedQuestionsRequest {
  importId: string;
  customerId: string;
  userId: string;
}

interface ImportValidatedQuestionsResponse {
  importId: string;
  totalImported: number;
  status: BulkImportStatus;
}

export class ImportValidatedQuestionsUseCase {
  constructor(
    private bulkImportRepository: IBulkImportRepository,
    private bulkImportDetailRepository: IBulkImportDetailRepository,
    private questionsRepository: IQuestionsRepository,
    private disciplinesRepository: IDisciplinesRepository,
    private alternativesRepository: IAlternativesRepository,
    private questionCodeGenerator: QuestionCodeGeneratorService
  ) {}

  async execute({
    importId,
    customerId,
    userId,
  }: ImportValidatedQuestionsRequest): Promise<ImportValidatedQuestionsResponse> {
    const bulkImport = await this.bulkImportRepository.findOneBy({
      id: importId,
      customer_id: customerId,
    });

    if (!bulkImport) {
      throw new ResourceNotFoundError('Importação não encontrada');
    }

    if (bulkImport.status === BulkImportStatus.IMPORTED) {
      throw new BadRequestError('Esta importação já foi concluída');
    }

    if (bulkImport.status !== BulkImportStatus.PENDING) {
      throw new BadRequestError(
        'Esta importação não está pronta para ser concluída. Verifique se há erros de validação.'
      );
    }

    const validRows = await this.bulkImportDetailRepository.findValidByImportId(importId);

    if (validRows.length === 0) {
      throw new BadRequestError('Não há questões válidas para importar');
    }

    await this.bulkImportRepository.update({
      id: importId,
      status: BulkImportStatus.IMPORTED,
      updated_by: userId,
      updated_at: new Date(),
      imported_at: new Date(),
    });

    try {
      const importedQuestions = await this.importQuestions(validRows, customerId);

      await this.bulkImportRepository.update({
        id: importId,
        status: BulkImportStatus.IMPORTED,
        updated_by: userId,
        updated_at: new Date(),
      });

      return {
        importId,
        totalImported: importedQuestions.length,
        status: BulkImportStatus.IMPORTED,
      };
    } catch (error) {
      await this.bulkImportRepository.update({
        id: importId,
        status: BulkImportStatus.PENDING,
        updated_by: userId,
        updated_at: new Date(),
      });

      throw error;
    }
  }

  private translateDifficulty(difficulty: string): string {
    if (!difficulty) return DifficultyEnum.Medium;

    const normalizedDifficulty = difficulty
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .toLowerCase();

    if (normalizedDifficulty.includes('facil') || normalizedDifficulty === 'f')
      return DifficultyEnum.Easy;
    if (
      normalizedDifficulty.includes('medio') ||
      normalizedDifficulty.includes('media') ||
      normalizedDifficulty === 'm'
    )
      return DifficultyEnum.Medium;
    if (normalizedDifficulty.includes('dificil') || normalizedDifficulty === 'd')
      return DifficultyEnum.Hard;

    if (normalizedDifficulty.includes('easy')) return DifficultyEnum.Easy;
    if (normalizedDifficulty.includes('medium')) return DifficultyEnum.Medium;
    if (normalizedDifficulty.includes('hard')) return DifficultyEnum.Hard;

    return DifficultyEnum.Medium;
  }

  private async importQuestions(
    validRows: IBulkImportDetail[],
    customerId: string
  ): Promise<string[]> {
    const questionsToInsert = [];
    const allAlternatives = [];
    const questionCodesMap = new Map();
    const detailsToUpdate = [];

    for (const row of validRows) {
      const rowData = row.row_data as ExcelRowData;

      const questionCode = await this.questionCodeGenerator.execute({ customerId });
      questionCodesMap.set(row.id, questionCode);

      questionsToInsert.push({
        title: questionCode,
        discipline_id: rowData.discipline_id as string,
        category_id: rowData.category_id as string,
        subcategory_id: rowData.subcategory_id as string,
        question_type_id: rowData.question_type_id as string,
        description: rowData.enunciado,
        explanation_text: rowData.explicacao,
        difficulty: this.translateDifficulty(rowData.dificuldade),
        customer_id: customerId,
        status: QuestionStatusEnum.InReview,
        published: false,
        published_at: null,
      });
    }

    const insertedQuestions = await this.questionsRepository.insertAll(questionsToInsert);
    const importedQuestionIds = insertedQuestions.map((q) => q.id as string);

    for (let i = 0; i < validRows.length; i++) {
      const row = validRows[i];
      const rowData = row.row_data as ExcelRowData;
      const questionId = importedQuestionIds[i];

      detailsToUpdate.push({
        id: row.id,
        question_id: questionId,
        row_data: null,
      });

      const alternatives = this.buildAlternatives(rowData, questionId);
      allAlternatives.push(...alternatives);
    }

    if (allAlternatives.length > 0) {
      await this.alternativesRepository.insertAll(allAlternatives);
    }

    if (detailsToUpdate.length > 0) {
      await this.bulkImportDetailRepository.updateBatch(detailsToUpdate);
    }

    return importedQuestionIds;
  }

  private buildAlternatives(rowData: ExcelRowData, questionId: string): IBaseAlternativeDTO[] {
    const alternatives: IBaseAlternativeDTO[] = [];
    const letters = ['a', 'b', 'c', 'd', 'e'];

    for (const letter of letters) {
      const alternativeKey = `alternativa_${letter}`;
      const alternativeText = rowData[alternativeKey];

      const alternativeCorrect = ['v', 'f'].includes(rowData.correta?.toLowerCase())
        ? rowData.correta?.toLowerCase() === 'v'
          ? 'a'
          : 'b'
        : rowData.correta?.toLowerCase();

      if (alternativeText) {
        alternatives.push({
          question_id: questionId,
          correct: alternativeCorrect === letter,
          option: letter.toUpperCase(),
          description: String(alternativeText),
        });
      }
    }

    return alternatives;
  }
}
