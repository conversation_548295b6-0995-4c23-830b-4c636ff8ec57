import { BulkImportsWithPaginationDTO } from '../../model/DTO/bulkImport.dto';
import { IBulkImportRepository } from '../../repositories/assign/bulkImport.assign';
import { ListBulkImportsParams } from '../../schema/bulkImport.schema';

interface ListBulkImportsRequest extends ListBulkImportsParams {}

export class ListBulkImportsUseCase {
  constructor(private readonly bulkImportRepository: IBulkImportRepository) {}

  async execute({
    page = 1,
    limit = 10,
    startDate,
    endDate,
    search,
    status,
    orderBy = 'created_at',
    orderDirection = 'desc',
    customerId,
  }: ListBulkImportsRequest): Promise<BulkImportsWithPaginationDTO> {
    const params: ListBulkImportsParams = {
      page,
      limit,
      startDate,
      endDate,
      search,
      status,
      orderBy,
      orderDirection,
      customerId,
    };

    const result = await this.bulkImportRepository.listBulkImports(params);

    return {
      paginationInfo: result.paginationInfo,
      data: result.data,
    };
  }
}
