import { BulkImportStatus } from '../../model/enums/bulkImportStatus.enum';
import { IBulkImportRepository } from '../../repositories/assign/bulkImport.assign';
import { IBulkImportDetailRepository } from '../../repositories/assign/bulkImportDetails.assign';
import { IDisciplinesRepository } from '../../repositories/assign/disciplines.assign';
import { IQuestionsRepository } from '../../repositories/assign/questions.assign';
import { IQuestionsTypesRepository } from '../../repositories/assign/questionsTypes.assign';
import { ExcelProcessor, ExcelRow } from '../../services/excelProcessor.service';
import { filterTextRegex } from '../../services/filterTextRegex.service';
import { QuestionTypeDetectorService } from '../../services/questionTypeDetector.service';
import { UploadService } from '../../services/uploadFile.service';
import { ValidationContext } from '../../services/validators/bulkQuestionsRow.validator';
import { BadRequestError } from '../errors/BadRequestError';
import { GenericError } from '../errors/GenericError';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';
import { AbstractBulkImportUseCase, ExcelRowWithErrors } from './abstractBulkImport.useCase';
import { ExcelRowData } from './uploadQuestionsExcel.useCase';

interface ReuploadQuestionsExcelRequest {
  file: Express.Multer.File;
  importId: string;
  customerId: string;
  userId: string;
}

interface ReuploadQuestionsExcelResponse {
  importId: string;
  fileName: string;
  canImport: boolean;
  status?: BulkImportStatus;
  message: string;
}

export class ReuploadQuestionsExcelUseCase extends AbstractBulkImportUseCase {
  private questionTypeDetector: QuestionTypeDetectorService;

  constructor(
    bulkImportRepository: IBulkImportRepository,
    bulkImportDetailRepository: IBulkImportDetailRepository,
    questionsRepository: IQuestionsRepository,
    disciplinesRepository: IDisciplinesRepository,
    private readonly questionsTypesRepository: IQuestionsTypesRepository,
    excelProcessor: ExcelProcessor,
    uploadService: UploadService
  ) {
    super(
      bulkImportRepository,
      bulkImportDetailRepository,
      questionsRepository,
      disciplinesRepository,
      excelProcessor,
      uploadService
    );
    this.questionTypeDetector = new QuestionTypeDetectorService();
  }

  async execute({
    file,
    importId,
    customerId,
    userId,
  }: ReuploadQuestionsExcelRequest): Promise<ReuploadQuestionsExcelResponse> {
    if (!file) {
      throw new BadRequestError('Arquivo é obrigatório');
    }

    const existingImport = await this.bulkImportRepository.findOneBy({
      id: importId,
      customer_id: customerId,
    });

    if (!existingImport) {
      throw new ResourceNotFoundError('Importação não encontrada');
    }

    if (existingImport.status === BulkImportStatus.IMPORTED) {
      throw new BadRequestError('Esta importação já foi concluída e não pode ser modificada');
    }

    try {
      await this.validateExcelFile(file);

      const { validRows, invalidRows, stats } = await this.excelProcessor.processExcel(file);

      await this.bulkImportDetailRepository.deleteByImportId(importId);

      await this.updateImportStatus(importId, BulkImportStatus.PROCESSING);

      this.completeProcessingInBackground(
        importId,
        file,
        customerId,
        userId,
        validRows,
        invalidRows,
        stats,
        existingImport.file_key
      ).catch((error) => {
        this.handleBackgroundProcessingError(importId, error);
      });

      return {
        importId,
        fileName: file.originalname,
        canImport: true,
        status: BulkImportStatus.PROCESSING,
        message: 'Arquivo recebido. O processamento foi iniciado.',
      };
    } catch (error) {
      if (error instanceof BadRequestError || error instanceof ResourceNotFoundError) {
        throw error;
      }

      if (error instanceof Error) {
        if (
          error.message.includes('Excel') ||
          error.message.includes('sheet') ||
          error.message.includes('workbook')
        ) {
          throw new BadRequestError(
            'Não foi possível processar o arquivo Excel. Verifique se o formato está correto e tente novamente.'
          );
        }
      }

      throw new GenericError('Ocorreu um erro inesperado. Por favor, tente novamente mais tarde.');
    }
  }

  protected async processRows(
    validRows: ExcelRow[],
    invalidRows: ExcelRowWithErrors[],
    customerId: string
  ): Promise<{
    updatedValidRows: ExcelRowData[];
    updatedInvalidRows: { row: ExcelRowData; errors: string[] }[];
  }> {
    const metadataMaps = await this.metadataResolver.resolve(customerId);

    const questionTypes = await this.questionsTypesRepository.findAll();
    const alternativesType = questionTypes.find((type) => type.type === 'alternatives');
    const trueOrFalseType = questionTypes.find((type) => type.type === 'true or false');

    if (!alternativesType || !trueOrFalseType) {
      throw new Error('Tipos de questão necessários não encontrados no banco de dados');
    }

    const updatedValidRows: ExcelRowData[] = [];
    const updatedInvalidRows: { row: ExcelRowData; errors: string[] }[] = [];

    const enunciadosMap = new Map<string, number>();

    for (let i = 0; i < validRows.length; i++) {
      const row = validRows[i];
      const rowData = this.convertToExcelRowData(row);
      const rowIndex = i + 2; // Ajustando para considerar o cabeçalho e índice 1-based

      const enunciado = rowData.enunciado?.trim();
      if (enunciado) {
        if (!enunciadosMap.has(enunciado)) {
          enunciadosMap.set(enunciado, rowIndex);
        }
      }
    }

    for (let i = 0; i < validRows.length; i++) {
      const row = validRows[i];
      const rowData = this.convertToExcelRowData(row);
      const rowIndex = i + 2; // Ajustando para considerar o cabeçalho e índice 1-based
      const errors: string[] = [];

      this.addMetadataIds(rowData, metadataMaps);

      this.questionTypeDetector.assignQuestionType(rowData, alternativesType, trueOrFalseType);

      const enunciado = rowData.enunciado?.trim();
      if (enunciado) {
        const primeiraOcorrencia = enunciadosMap.get(enunciado);
        if (primeiraOcorrencia !== rowIndex) {
          errors.push(`Linha ${rowIndex}: Enunciado duplicado com a linha ${primeiraOcorrencia}.`);
        }
      }

      const disciplinaLower = filterTextRegex(rowData.disciplina.toLowerCase());
      if (!metadataMaps.disciplines.has(disciplinaLower)) {
        errors.push(`Disciplina "${rowData.disciplina}" não encontrada ou não possui acessos.`);
      }

      const categoriaLower = filterTextRegex(rowData.categoria.toLowerCase());
      if (!metadataMaps.categories.has(categoriaLower)) {
        errors.push(`Categoria "${rowData.categoria}" não encontrada ou não possui acessos.`);
      }

      const subcategoriaLower = filterTextRegex(rowData.subcategoria.toLowerCase());

      if (!metadataMaps.subcategories.has(subcategoriaLower)) {
        errors.push(`Subcategoria "${rowData.subcategoria}" não encontrada ou não possui acesso.`);
      }

      if (errors.length === 0) {
        const disciplineId = metadataMaps.disciplines.get(disciplinaLower)?.id;
        const categoryId = metadataMaps.categories.get(categoriaLower)?.id;
        const categorySubcategoryAccess = metadataMaps.categorySubcategoryAccess.get(
          categoryId || ''
        );

        const subcategoryExists = categorySubcategoryAccess
          ? Array.from(categorySubcategoryAccess).find((item) => {
              return Object.keys(item).some((value) => {
                return filterTextRegex(value.toLowerCase()) === subcategoriaLower;
              });
            })
          : undefined;

        const subcategoryId = subcategoryExists
          ? subcategoryExists[subcategoriaLower]
          : metadataMaps.subcategories.get(subcategoriaLower)?.id;

        if (!metadataMaps.disciplineCategoryAccess.get(disciplineId || '')?.has(categoryId || '')) {
          errors.push(
            `A disciplina "${rowData.disciplina}" não tem acesso à categoria "${rowData.categoria}".`
          );
        }

        if (!subcategoryExists) {
          errors.push(
            `A categoria "${rowData.categoria}" não tem acesso à subcategoria "${rowData.subcategoria}".`
          );
        }
        if (
          !metadataMaps.disciplineSubcategoryAccess
            .get(disciplineId || '')
            ?.has(subcategoryId || '')
        ) {
          errors.push(
            `A disciplina "${rowData.disciplina}" não tem acesso à subcategoria "${rowData.subcategoria}".`
          );
        }
      }

      if (errors.length === 0) {
        const validationContext: ValidationContext = {
          row: rowData,
          customerId,
          rowIndex,
          existingQuestionsRepo: this.questionsRepository,
          maps: metadataMaps,
          questionTypes: {
            trueOrFalseTypeId: trueOrFalseType.id,
            alternativesTypeId: alternativesType.id,
          },
        };

        const validationErrors = await this.questionValidationService.validate(validationContext);
        if (validationErrors.length > 0) {
          errors.push(...validationErrors);
        }
      }

      if (errors.length > 0) {
        updatedInvalidRows.push({ row: rowData, errors });
      } else {
        updatedValidRows.push(rowData);
      }
    }

    for (const item of invalidRows) {
      const rowData = this.convertToExcelRowData(item.row);

      this.addMetadataIds(rowData, metadataMaps);

      this.questionTypeDetector.assignQuestionType(rowData, alternativesType, trueOrFalseType);

      updatedInvalidRows.push({
        row: rowData,
        errors: item.errors,
      });
    }

    return { updatedValidRows, updatedInvalidRows };
  }

  protected async completeProcessingInBackground(
    importId: string,
    file: Express.Multer.File,
    customerId: string,
    userId: string,
    validRows: ExcelRow[],
    invalidRows: ExcelRowWithErrors[],
    stats: {
      totalRows: number;
      validCount: number;
      invalidCount: number;
      successPercentage: number;
    },
    previousFileKey?: string | null
  ): Promise<void> {
    try {
      const importRecord = await this.bulkImportRepository.findOneBy({ id: importId });

      if (!importRecord) {
        return;
      }

      if (importRecord.status !== BulkImportStatus.PROCESSING) {
        return;
      }

      await this.bulkImportRepository.update({
        id: importId,
        updated_by: userId,
        total_rows: stats.totalRows,
        valid_count: stats.validCount,
        invalid_count: stats.invalidCount,
        success_percentage: stats.successPercentage,
        file_name: file.originalname,
        updated_at: new Date(),
        created_at: new Date(),
      });

      await this.bulkImportDetailRepository.deleteByImportId(importId);

      const { updatedValidRows, updatedInvalidRows } = await this.processRows(
        validRows,
        invalidRows,
        customerId
      );

      const currentImport = await this.bulkImportRepository.findOneBy({ id: importId });
      if (!currentImport || currentImport.status !== BulkImportStatus.PROCESSING) {
        return;
      }

      await this.insertImportDetails(importId, updatedValidRows, updatedInvalidRows);

      await this.updateImportStats(importId, updatedValidRows, updatedInvalidRows, stats.totalRows);

      const hasErrors = updatedInvalidRows.length > 0;
      const newStatus = hasErrors ? BulkImportStatus.VALIDATION_FAILED : BulkImportStatus.PENDING;

      await this.updateImportStatus(importId, newStatus);

      if (newStatus === BulkImportStatus.PENDING) {
        try {
          const finalImport = await this.bulkImportRepository.findOneBy({ id: importId });

          if (finalImport && finalImport.status === BulkImportStatus.PENDING) {
            const fileKey = await this.uploadFileToStorage(file, customerId, previousFileKey);
            await this.updateImportWithFileKey(importId, fileKey);
          }
        } catch (uploadError) {
          await this.handleBackgroundProcessingError(
            importId,
            uploadError instanceof Error ? uploadError : new Error(String(uploadError))
          );
        }
      }
    } catch (error) {
      await this.handleBackgroundProcessingError(
        importId,
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }
}
