import { env } from '../../env';
import { BulkImportStatus } from '../../model/enums/bulkImportStatus.enum';
import { IBulkImportRepository } from '../../repositories/assign/bulkImport.assign';
import { IBulkImportDetailRepository } from '../../repositories/assign/bulkImportDetails.assign';
import { IDisciplinesRepository } from '../../repositories/assign/disciplines.assign';
import { IQuestionsRepository } from '../../repositories/assign/questions.assign';
import { IQuestionsTypesRepository } from '../../repositories/assign/questionsTypes.assign';
import { ExcelProcessor, ExcelRow } from '../../services/excelProcessor.service';
import { QuestionTypeDetectorService } from '../../services/questionTypeDetector.service';
import { MetadataMaps } from '../../services/resolvers/metadataHierarchyResolver.service';
import { UploadService } from '../../services/uploadFile.service';
import { ValidationContext } from '../../services/validators/bulkQuestionsRow.validator';
import { BadRequestError } from '../errors/BadRequestError';
import { GenericError } from '../errors/GenericError';
import { AbstractBulkImportUseCase, ExcelRowWithErrors } from './abstractBulkImport.useCase';

export interface ExcelRowData {
  disciplina: string;
  categoria: string;
  subcategoria: string;
  enunciado: string;
  explicacao: string;
  correta: string;
  alternativa_a: string;
  alternativa_b: string;
  alternativa_c: string;
  alternativa_d: string;
  alternativa_e: string;
  dificuldade: string;
  discipline_id?: string;
  category_id?: string;
  subcategory_id?: string;
  question_type_id?: string;
  tipo_questao?: string;
  [key: string]: unknown;
}

interface UploadQuestionsExcelRequest {
  file: Express.Multer.File;
  customerId: string;
  userId: string;
}

interface UploadQuestionsExcelResponse {
  importId: string;
  fileName: string;
  status?: BulkImportStatus;
  canImport: boolean;
  message: string;
}

export class UploadQuestionsExcelUseCase extends AbstractBulkImportUseCase {
  private questionTypeDetector: QuestionTypeDetectorService;

  constructor(
    bulkImportRepository: IBulkImportRepository,
    bulkImportDetailRepository: IBulkImportDetailRepository,
    questionsRepository: IQuestionsRepository,
    disciplinesRepository: IDisciplinesRepository,
    private readonly questionsTypesRepository: IQuestionsTypesRepository,
    excelProcessor: ExcelProcessor,
    uploadService: UploadService
  ) {
    super(
      bulkImportRepository,
      bulkImportDetailRepository,
      questionsRepository,
      disciplinesRepository,
      excelProcessor,
      uploadService
    );
    this.questionTypeDetector = new QuestionTypeDetectorService();
  }

  async execute({
    file,
    customerId,
    userId,
  }: UploadQuestionsExcelRequest): Promise<UploadQuestionsExcelResponse> {
    if (!file) {
      throw new BadRequestError('Arquivo é obrigatório');
    }

    try {
      await this.validateExcelFile(file);

      const { validRows, invalidRows, stats } = await this.excelProcessor.processExcel(file);

      this.validateRowCount(stats.totalRows);

      const bulkImport = await this.createInitialImport(
        file.originalname,
        stats,
        customerId,
        userId
      );

      this.completeProcessingInBackground(
        bulkImport.id,
        file,
        customerId,
        userId,
        validRows,
        invalidRows,
        stats
      ).catch((error) => {
        console.error('Erro no processamento em background:', error);
        this.handleBackgroundProcessingError(bulkImport.id, error);
      });

      return {
        importId: bulkImport.id,
        fileName: file.originalname,
        canImport: true,
        status: BulkImportStatus.PROCESSING,
        message: 'Arquivo recebido. O processamento foi iniciado.',
      };
    } catch (error) {
      if (error instanceof BadRequestError) {
        throw error;
      }

      if (error instanceof Error) {
        console.error('Erro no processamento do arquivo Excel:', error);

        if (
          error.message.includes('Excel') ||
          error.message.includes('sheet') ||
          error.message.includes('workbook')
        ) {
          throw new BadRequestError(
            'Não foi possível processar o arquivo Excel. Verifique se o formato está correto e tente novamente.'
          );
        }
      }

      console.error('Erro inesperado no upload de arquivo Excel:', error);
      throw new GenericError('Ocorreu um erro inesperado. Por favor, tente novamente mais tarde.');
    }
  }

  protected async createInitialImport(
    fileName: string,
    stats: {
      totalRows: number;
      validCount: number;
      invalidCount: number;
      successPercentage: number;
    },
    customerId: string,
    userId: string
  ): Promise<{ id: string }> {
    return this.bulkImportRepository.insert({
      file_name: fileName,
      total_rows: stats.totalRows,
      valid_count: stats.validCount,
      invalid_count: stats.invalidCount,
      success_percentage: stats.successPercentage,
      status: BulkImportStatus.PROCESSING,
      customer_id: customerId,
      created_by: userId,
      created_at: new Date(),
      updated_at: new Date(),
    });
  }

  protected async validateRow(
    rowData: ExcelRowData,
    rowIndex: number,
    customerId: string,
    metadataMaps: MetadataMaps,
    duplicates: Set<string>,
    questionTypes: {
      trueOrFalseTypeId: string;
      alternativesTypeId: string;
    }
  ): Promise<string[]> {
    const validationContext: ValidationContext = {
      row: rowData,
      customerId,
      rowIndex,
      existingQuestionsRepo: this.questionsRepository,
      maps: {
        disciplines: metadataMaps.disciplines,
        categories: metadataMaps.categories,
        subcategories: metadataMaps.subcategories,
        disciplineCategoryAccess: metadataMaps.disciplineCategoryAccess,
        disciplineSubcategoryAccess: metadataMaps.disciplineSubcategoryAccess,
        categorySubcategoryAccess: metadataMaps.categorySubcategoryAccess,
      },
      questionTypes,
    };

    const errors: string[] = [];
    if (rowData.enunciado) {
      const enunciadoKey = rowData.enunciado.toLowerCase().trim();
      if (duplicates.has(enunciadoKey)) {
        errors.push(`Linha ${rowIndex}: Enunciado duplicado no arquivo.`);
      } else {
        duplicates.add(enunciadoKey);
      }
    }

    const validationErrors = await this.questionValidationService.validate(validationContext);
    errors.push(...validationErrors);

    return errors;
  }

  protected async processRows(
    validRows: ExcelRow[],
    invalidRows: ExcelRowWithErrors[],
    customerId: string
  ): Promise<{
    updatedValidRows: ExcelRowData[];
    updatedInvalidRows: { row: ExcelRowData; errors: string[] }[];
  }> {
    const updatedValidRows: ExcelRowData[] = [];
    const updatedInvalidRows: { row: ExcelRowData; errors: string[] }[] = [];
    const duplicates = new Set<string>();

    const metadataMaps = await this.metadataResolver.resolve(customerId);

    const trueOrFalseType = await this.questionsTypesRepository.findOneBy({
      type: 'true or false',
    });
    const alternativesType = await this.questionsTypesRepository.findOneBy({
      type: 'alternatives',
    });

    if (!trueOrFalseType || !alternativesType) {
      throw new GenericError('Tipos de questão não encontrados');
    }

    for (let i = 0; i < validRows.length; i++) {
      const row = validRows[i];
      const rowData = this.convertToExcelRowData(row);
      const rowIndex = i + 2; // Ajustando para considerar o cabeçalho e índice 1-based

      this.addMetadataIds(rowData, metadataMaps);

      const errors = await this.validateRow(
        rowData,
        rowIndex,
        customerId,
        metadataMaps,
        duplicates,
        {
          trueOrFalseTypeId: trueOrFalseType.id,
          alternativesTypeId: alternativesType.id,
        }
      );

      this.questionTypeDetector.assignQuestionType(rowData, alternativesType, trueOrFalseType);

      if (errors.length === 0) {
        updatedValidRows.push(rowData);
      } else {
        updatedInvalidRows.push({ row: rowData, errors });
      }
    }

    for (const item of invalidRows) {
      const rowData = this.convertToExcelRowData(item.row);

      this.addMetadataIds(rowData, metadataMaps);

      updatedInvalidRows.push({
        row: rowData,
        errors: item.errors,
      });
    }

    return { updatedValidRows, updatedInvalidRows };
  }

  protected async completeProcessingInBackground(
    importId: string,
    file: Express.Multer.File,
    customerId: string,
    userId: string,
    validRows: ExcelRow[],
    invalidRows: ExcelRowWithErrors[],
    stats: {
      totalRows: number;
      validCount: number;
      invalidCount: number;
      successPercentage: number;
    }
  ): Promise<void> {
    try {
      const importRecord = await this.bulkImportRepository.findOneBy({ id: importId });

      if (!importRecord) {
        return;
      }

      if (importRecord.status !== BulkImportStatus.PROCESSING) {
        return;
      }

      const { updatedValidRows, updatedInvalidRows } = await this.processRows(
        validRows,
        invalidRows,
        customerId
      );

      const currentImport = await this.bulkImportRepository.findOneBy({ id: importId });
      if (!currentImport || currentImport.status !== BulkImportStatus.PROCESSING) {
        return;
      }

      await this.insertImportDetails(importId, updatedValidRows, updatedInvalidRows);

      await this.updateImportStats(importId, updatedValidRows, updatedInvalidRows, stats.totalRows);

      const hasErrors = updatedInvalidRows.length > 0;
      const newStatus = hasErrors ? BulkImportStatus.VALIDATION_FAILED : BulkImportStatus.PENDING;

      await this.updateImportStatus(importId, newStatus);

      if (!hasErrors) {
        try {
          const fileKey = await this.uploadFileToStorage(file, customerId);

          const finalImport = await this.bulkImportRepository.findOneBy({ id: importId });
          if (finalImport && finalImport.status === BulkImportStatus.PENDING) {
            await this.updateImportWithFileKey(importId, fileKey);
          } else {
            try {
              await this.uploadService.deleteFile(fileKey, env.AWS_S3_PUBLIC_BUCKET);
            } catch (deleteError) {
              console.error(`Erro ao remover arquivo ${fileKey} do S3:`, deleteError);
            }
          }
        } catch (uploadError) {
          console.error(
            `Erro ao fazer upload do arquivo para importação ${importId}:`,
            uploadError
          );
          await this.handleBackgroundProcessingError(importId, uploadError);
        }
      }
    } catch (error) {
      console.error(
        `Erro no processamento completo em background para importação ${importId}:`,
        error
      );
      await this.handleBackgroundProcessingError(importId, error);
    }
  }
}
