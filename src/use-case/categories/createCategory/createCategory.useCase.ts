import { ICategory } from '../../../model/ICategory';
import { ISubCategory } from '../../../model/ISubcategory';
import { ICategoriesRepository } from '../../../repositories/assign/categories.assign';
import { ISubCategoriesRepository } from '../../../repositories/assign/subcategories.assign';
import { GenericError } from '../../errors/GenericError';
import { ResourceAlreadyExistsError } from '../../errors/ResourceAlreadyExistsError';

const DEFAULT_CATEGORY_NAME = 'Minha nova categoria';
const DEFAULT_SUBCATEGORY_NAME = 'Minha nova subcategoria';

interface ISubCategoryResponse extends ISubCategory {
  marked: boolean;
}

interface ICategoryResponse extends ICategory {
  marked: boolean;
  subcategory: ISubCategoryResponse[];
}

interface ICreateCategoryRequest {
  name?: string;
  customerId: string;
  subcategoryName?: string;
  notCreateDefaults?: boolean;
}

interface ICreateCategoryResponse {
  category: ICategoryResponse;
}

export class CreateCategoryUseCase {
  constructor(
    private readonly categoriesRepository: ICategoriesRepository,
    private readonly subcategoriesRepository: ISubCategoriesRepository
  ) {}

  async execute(request: ICreateCategoryRequest): Promise<ICreateCategoryResponse> {
    const { name, customerId, subcategoryName, notCreateDefaults = false } = request;

    const totalCategories = await this.categoriesRepository.getTotalCatregories(customerId);

    const categoryNameToCreate = name || `${DEFAULT_CATEGORY_NAME} ${totalCategories + 1}`;
    const subcategoryNameToCreate = subcategoryName || DEFAULT_SUBCATEGORY_NAME;

    await this.ensureCategoryAndSubcategoryDoesNotExist(categoryNameToCreate, customerId);

    const createdCategory = await this.createCategory(categoryNameToCreate, customerId);

    if (!createdCategory) {
      throw new GenericError('Falha ao criar a categoria.');
    }

    const categoryId = createdCategory.id;

    let createdSubcategory: ISubCategory | null = null;

    if (!notCreateDefaults) {
      createdSubcategory = await this.createSubcategory(
        subcategoryNameToCreate,
        categoryId,
        customerId
      );

      if (!createdSubcategory) {
        throw new GenericError('Falha ao criar a subcategoria.');
      }
    }

    return {
      category: {
        ...createdCategory,
        marked: false,
        subcategory: createdSubcategory ? [{ ...createdSubcategory, marked: false }] : [],
      },
    };
  }

  private async ensureCategoryAndSubcategoryDoesNotExist(
    categoryNameToCreate: string,
    customerId: string
  ): Promise<void> {
    const categoryExists = await this.categoriesRepository.findByNameAndCustomerId(
      categoryNameToCreate,
      customerId
    );

    if (categoryExists) {
      throw new ResourceAlreadyExistsError(
        `Já existe uma categoria com o nome '${categoryNameToCreate}'.`
      );
    }
  }

  private async createCategory(name: string, customerId: string): Promise<ICategory> {
    const category = await this.categoriesRepository.insert({ name, customer_id: customerId });

    if (!category) {
      throw new GenericError('Erro ao criar a categoria.');
    }

    return category;
  }

  private async createSubcategory(
    name: string,
    categoryId: string,
    customerId: string
  ): Promise<ISubCategory> {
    const subcategory = await this.subcategoriesRepository.insert({
      name,
      category_id: categoryId,
      customer_id: customerId,
    });

    if (!subcategory) {
      throw new GenericError('Erro ao criar a subcategoria.');
    }

    return subcategory;
  }
}
