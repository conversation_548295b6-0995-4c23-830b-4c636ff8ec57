// /src/use-case/categories/deleteCategory/DeleteCategoryUseCase.ts

import { ICategory } from '../../../model/ICategory';
import { IAggregatedDataAssignRepository } from '../../../repositories/assign/aggregatedData.assign';
import { ICategoriesRepository } from '../../../repositories/assign/categories.assign';
import { IDisciplinesCategoriesAccessRepository } from '../../../repositories/assign/disciplinesCategoriesAccess.assign';
import { IDisciplinesSubcategoriesAccessRepository } from '../../../repositories/assign/disciplinesSubcategoriesAccess.assign';
import { IQuestionsRepository } from '../../../repositories/assign/questions.assign';
import { ISubCategoriesRepository } from '../../../repositories/assign/subcategories.assign';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface ICategoryDeleteRequest {
  id: string;
  customerId: string;
}

interface ICategoryDeleteResponse {
  deletedCategory: ICategory;
  removedAccessesCount: number;
}

export class DeleteCategoryUseCase {
  constructor(
    private readonly categoryRepository: ICategoriesRepository,
    private readonly aggregateDataRepository: IAggregatedDataAssignRepository,
    private readonly questionRepository: IQuestionsRepository,
    private readonly accessRepository: IDisciplinesCategoriesAccessRepository,
    private readonly subcategoryRepository: ISubCategoriesRepository,
    private readonly subcategoryAccessRepository: IDisciplinesSubcategoriesAccessRepository
  ) {}

  async execute(request: ICategoryDeleteRequest): Promise<ICategoryDeleteResponse> {
    const { id, customerId } = request;

    const category = await this.getCategoryIfValid(id, customerId);

    await this.ensureNoLinkedQuestions(id, customerId);

    await this.deleteSubcategoriesAndAccesses(id, customerId);

    const removedAccessesCount = await this.deleteCategoryAccesses(id, customerId);

    await this.categoryRepository.deleteById(id);

    return {
      deletedCategory: category,
      removedAccessesCount,
    };
  }

  private async deleteSubcategoriesAndAccesses(
    categoryId: string,
    customerId: string
  ): Promise<void> {
    await Promise.all([
      this.subcategoryAccessRepository.deleteAllByCategoryId({ categoryId, customerId }),
      this.subcategoryRepository.deleteAllByCategoryId({ categoryId, customerId }),
    ]);
  }

  private async getCategoryIfValid(id: string, customerId: string): Promise<ICategory> {
    const { category } = await this.aggregateDataRepository.findEntities({
      categoryId: id,
      customerId,
    });

    if (!category) throw new ResourceNotFoundError('Categoria não encontrada.');

    return category;
  }

  private async ensureNoLinkedQuestions(categoryId: string, customerId: string): Promise<void> {
    const linkedQuestions = await this.questionRepository.getQuestionsByCategoryId({
      categoryId,
      customerId,
    });

    if (linkedQuestions.length > 0) {
      throw new ResourceNotFoundError(
        `Esta categoria não pode ser excluída porque possui questões vinculadas. Remova ou mova as questões antes de tentar novamente.`
      );
    }
  }

  private async deleteCategoryAccesses(id: string, customerId: string): Promise<number> {
    const accesses = await this.accessRepository.findAllById(id);
    const accessCount = accesses.length;

    if (accessCount > 0) {
      await this.accessRepository.deleteAll(id, customerId);
    }

    return accessCount;
  }
}
