import { ICategory } from '../../../model/ICategory';
import { IDisciplineCategoriesAccess } from '../../../model/IDisciplineCategoryAccess';
import { IDisciplineSubcategoriesAccess } from '../../../model/IDisciplineSubcategoryAccess';
import { ICategoriesRepository } from '../../../repositories/assign/categories.assign';
import { IDisciplinesRepository } from '../../../repositories/assign/disciplines.assign';
import { IDisciplinesCategoriesAccessRepository } from '../../../repositories/assign/disciplinesCategoriesAccess.assign';
import { IDisciplinesSubcategoriesAccessRepository } from '../../../repositories/assign/disciplinesSubcategoriesAccess.assign';
import { filterTextRegex } from '../../../services/filterTextRegex.service';
import { GenericError } from '../../errors/GenericError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface IListCategoriesRequest {
  customerId: string;
  disciplineId?: string;
  search?: string;
  orderByColumn?: string;
  orderDirection?: string;
}

interface IListCategoryDTO extends ICategory {
  subcategories?: IListCategoryDTO[];
  makered: boolean;
}

export class ListCategoriesUseCase {
  constructor(
    private readonly categoriesRepository: ICategoriesRepository,
    private readonly disciplineRepository: IDisciplinesRepository,
    private readonly disciplineCategoriesAccess: IDisciplinesCategoriesAccessRepository,
    private readonly disciplineSubcategoriesAccess: IDisciplinesSubcategoriesAccessRepository
  ) {}

  async execute(request: IListCategoriesRequest): Promise<IListCategoryDTO[]> {
    const {
      customerId,
      disciplineId,
      search,
      orderByColumn = 'name',
      orderDirection = 'asc',
    } = request;

    if (disciplineId) {
      await this.validateDiscipline(disciplineId);
    }

    const categories = await this.fetchCategories(customerId, {
      search,
      orderByColumn,
      orderDirection,
    });

    let formattedCategories: IListCategoryDTO[];

    if (!disciplineId) {
      formattedCategories = this.formatCategories(categories);
    } else {
      const [categoriesAccess, subcategoriesAccess] = await Promise.all([
        this.disciplineCategoriesAccess.findAllByDisciplineId(disciplineId),
        this.disciplineSubcategoriesAccess.findAllByDisciplineId(disciplineId, customerId),
      ]);

      this.validateAccesses(categoriesAccess, subcategoriesAccess);

      formattedCategories = this.formatCategoriesWithAccess(
        categories,
        categoriesAccess,
        subcategoriesAccess
      );
    }

    return this.sortCategoriesAndSubcategories(formattedCategories, orderByColumn, orderDirection);
  }

  private async validateDiscipline(disciplineId: string): Promise<void> {
    const existingDiscipline = await this.disciplineRepository.findById(disciplineId);

    if (!existingDiscipline) {
      throw new ResourceNotFoundError('Disciplina não encontrada');
    }
  }

  private async fetchCategories(
    customerId: string,
    options: {
      search?: string;
      orderByColumn?: string;
      orderDirection?: string;
    }
  ): Promise<IListCategoryDTO[]> {
    const categories = await this.categoriesRepository.findAllByCustomerId({
      customerId,
      ...options,
    });
    if (!Array.isArray(categories)) {
      throw new ResourceNotFoundError('Nenhuma categoria encontrada');
    }
    return categories as IListCategoryDTO[];
  }

  private validateAccesses(
    categoriesAccess: IDisciplineCategoriesAccess[],
    subcategoriesAccess: IDisciplineSubcategoriesAccess[]
  ): void {
    if (!Array.isArray(categoriesAccess) || !Array.isArray(subcategoriesAccess)) {
      throw new GenericError('Erro ao buscar acessos de categorias/subcategorias');
    }
  }

  private formatCategories(categories: IListCategoryDTO[]): IListCategoryDTO[] {
    return categories.map((cat) => ({
      ...cat,
      makered: false,
      subcategories: cat.subcategories?.map((subcat) => ({ ...subcat, makered: false })),
    }));
  }

  private formatCategoriesWithAccess(
    categories: IListCategoryDTO[],
    categoriesAccess: IDisciplineCategoriesAccess[],
    subcategoriesAccess: IDisciplineSubcategoriesAccess[]
  ): IListCategoryDTO[] {
    return categories.map((cat) => {
      const categoryAccess = categoriesAccess.find((access) => access.category_id === cat.id);

      const formattedSubcategories = cat.subcategories?.map((subcat) => {
        const subcategoryAccess = subcategoriesAccess.find(
          (access) => access.subcategory_id === subcat.id
        );
        return {
          ...subcat,
          makered: !!subcategoryAccess,
        };
      });

      return {
        ...cat,
        makered: !!categoryAccess,
        subcategories: formattedSubcategories,
      };
    });
  }

  private sortCategoriesAndSubcategories(
    categories: IListCategoryDTO[],
    orderByColumn: string,
    orderDirection: string
  ): IListCategoryDTO[] {
    const sortByName = (a: { name: string }, b: { name: string }, direction: string) => {
      const nameA = filterTextRegex(a.name.toLowerCase());
      const nameB = filterTextRegex(b.name.toLowerCase());

      return direction === 'asc'
        ? nameA.localeCompare(nameB, 'pt-BR', { sensitivity: 'base', numeric: true })
        : nameB.localeCompare(nameA, 'pt-BR', { sensitivity: 'base', numeric: true });
    };

    const sortedCategories = [...categories].sort((a, b) => {
      if (orderByColumn === 'name') {
        return sortByName(a, b, orderDirection);
      }
      return 0;
    });

    sortedCategories.forEach((category) => {
      if (category.subcategories && category.subcategories.length > 0) {
        category.subcategories.sort((a, b) => sortByName(a, b, orderDirection));
      }
    });

    return sortedCategories;
  }
}
