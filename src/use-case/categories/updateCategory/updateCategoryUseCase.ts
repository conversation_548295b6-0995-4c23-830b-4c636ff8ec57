import { KnexCategoriesRepository } from '../../../repositories/knex/categories.repositories';
import { ResourceAlreadyExistsError } from '../../errors/ResourceAlreadyExistsError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface IUpdateCategoryRequest {
  id: string;
  name: string;
  customerId: string;
}

export class UpdateCategoryUseCase {
  constructor(private categoriesRepository: KnexCategoriesRepository) {}

  async execute(request: IUpdateCategoryRequest): Promise<object> {
    const { id, name } = request;

    await this.validateCategoryExists(request);
    await this.validateCategoryNameUniqueness(request);

    const category = {
      id,
      name,
    };

    const updatedCategory = await this.categoriesRepository.update(category);

    return updatedCategory;
  }

  private async validateCategoryExists(request: IUpdateCategoryRequest): Promise<object> {
    const { id, customerId } = request;
    const category = await this.categoriesRepository.findCategoryIdAndCustomerId(id, customerId);

    if (!category) {
      throw new ResourceNotFoundError('Categoria nao encontrada');
    }

    return category;
  }

  private async validateCategoryNameUniqueness(request: IUpdateCategoryRequest): Promise<void> {
    const { name, customerId } = request;

    const categoryWithSameName = await this.categoriesRepository.findByNameAndCustomerId(
      name,
      customerId
    );

    if (categoryWithSameName && categoryWithSameName.id !== request.id) {
      throw new ResourceAlreadyExistsError('Já existe outra categoria com esse nome.');
    }
  }
}
