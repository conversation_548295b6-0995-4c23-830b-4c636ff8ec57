import { generateTransaction } from '../../helpers/transaction.helper';
import { IClassesRepository } from '../../repositories/assign/classes.assign';
import { ICreateClassDTO } from '../../schema/classes.schema';
import { GenericError } from '../errors/GenericError';

export class CreateClassUseCase {
  constructor(private readonly classesRepository: IClassesRepository) {}

  async execute({
    name,
    description,
    course_id: courseId,
    customerId,
    isDefault,
  }: ICreateClassDTO & { customerId: string }) {
    const trx = await generateTransaction();

    if (isDefault) {
      const verifyExistingDefaultClass = await this.classesRepository.findOneBy({
        customer_id: customerId,
        is_default: true,
        course_id: courseId,
      });

      if (verifyExistingDefaultClass) {
        const updatedClass = await this.classesRepository.updateWithTrx(
          {
            id: verifyExistingDefaultClass.id,
            is_default: false,
          },
          trx
        );

        if (!updatedClass) {
          await trx.rollback();
          throw new GenericError('Erro ao atualizar a turma padrão');
        }
      }
    }

    const classData = await this.classesRepository.createClass(
      {
        name,
        description: description ?? '',
        start_date: new Date(),
        end_date: null,
        course_id: courseId,
        customer_id: customerId,
        status: 'active',
        is_default: isDefault,
      },
      trx
    );

    if (!classData) {
      await trx.rollback();
      throw new GenericError('Erro ao criar a turma');
    }

    await trx.commit();
    return classData;
  }
}
