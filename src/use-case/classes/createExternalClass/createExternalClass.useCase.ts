import { IClassDataForCreation } from '../../../model/DTO/IClasses.dto';
import { IClasses } from '../../../model/IClasses';
import { IClassesRepository } from '../../../repositories/assign/classes.assign';
import { ICustomerRepository } from '../../../repositories/assign/customers.assign';
import { ICreateExternalClassDTO } from '../../../schema/classes.schema';
import { verifyExternalCustomer } from '../../../services/verifyExternalCustomer.service';

export interface ICreateExternalClassResponseDTO {
  message: string;
  class: IClasses;
}

export class CreateExternalClassUseCase {
  constructor(
    private readonly classesRepository: IClassesRepository,
    private readonly customerRepository: ICustomerRepository
  ) {}

  async execute(data: ICreateExternalClassDTO): Promise<ICreateExternalClassResponseDTO> {
    const customer = await verifyExternalCustomer(this.customerRepository, data.customerId);

    const existingClass = await this.classesRepository.findOneBy({
      external_class_id: data.id,
      customer_id: customer.id,
      deleted_at: null,
    });

    if (existingClass) {
      return {
        message: 'Turma já cadastrada.',
        class: existingClass,
      };
    }

    const classData: IClassDataForCreation = {
      name: data.name,
      description: data.description,
      status: data.status,
      start_date: data.startDate ? new Date(data.startDate) : new Date(),
      end_date: data.endDate ? new Date(data.endDate) : new Date(),
      instructor_id: data.instructorId,
      external_class_id: data.id,
      customer_id: customer.id,
    };

    const createdClass = await this.classesRepository.createExternalClass(classData);

    return { message: 'Turma criada com sucesso.', class: createdClass };
  }
}
