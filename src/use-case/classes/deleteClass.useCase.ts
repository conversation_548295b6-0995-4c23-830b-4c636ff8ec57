import { IClassesRepository } from '../../repositories/assign/classes.assign';
import { BadRequestError } from '../errors/BadRequestError';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

export class DeleteClassUseCase {
  constructor(private readonly classesRepository: IClassesRepository) {}

  async execute(id: string) {
    const classData = await this.classesRepository.findOneBy({ id, deleted_at: null });

    if (!classData) {
      throw new ResourceNotFoundError('Turma não encontrada');
    }

    const studentsCount = await this.classesRepository.countUsersInClass(id);
    if (studentsCount > 0) {
      throw new BadRequestError('Não é possível deletar a turma: existem alunos vinculados');
    }

    await this.classesRepository.deleteClass(id);
  }
}
