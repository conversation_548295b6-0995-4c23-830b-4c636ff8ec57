import { beforeEach, describe, expect, it, vi } from 'vitest';

import { IClasses } from '../../../model/IClasses';
import { ICustomer } from '../../../model/ICustomer';
import type { IClassesRepository } from '../../../repositories/assign/classes.assign';
import type { ICustomerRepository } from '../../../repositories/assign/customers.assign';
import { GenericError } from '../../errors/GenericError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';
import { DeleteExternalClassUseCase } from './deleteExternalClass.useCase';

describe('DeleteExternalClassUseCase', () => {
  let useCase: DeleteExternalClassUseCase;
  let classesRepository: IClassesRepository;
  let customerRepository: ICustomerRepository;

  beforeEach(() => {
    classesRepository = {
      findOneBy: vi.fn(),
      softDelete: vi.fn(),
      findByExternalClassId: vi.fn(),
      createExternalClass: vi.fn(),
      update: vi.fn(),
    };

    customerRepository = {
      findOneBy: vi.fn(),
      getTaxNumber: vi.fn(),
      findById: vi.fn(),
      insert: vi.fn(),
      findByExternalId: vi.fn(),
    };

    useCase = new DeleteExternalClassUseCase(classesRepository, customerRepository);
  });

  describe('casos de sucesso', () => {
    it('deve deletar a turma com sucesso', async () => {
      const mockCustomer: ICustomer = {
        id: 'customer-1',
        name: 'Test Customer',
        created_at: new Date(),
        updated_at: new Date(),
        deleted_at: null,
      };

      const mockClass: IClasses = {
        id: 'class-1',
        name: 'Test Class',
        start_date: new Date(),
        end_date: new Date(),
        customer_id: 'customer-1',
        status: 'active',
        created_at: new Date(),
        updated_at: new Date(),
        deleted_at: null,
      };

      const mockDeletedClass: IClasses = {
        ...mockClass,
        deleted_at: new Date(),
      };

      vi.mocked(customerRepository.findOneBy).mockResolvedValue(mockCustomer);
      vi.mocked(classesRepository.findOneBy).mockResolvedValue(mockClass);
      vi.mocked(classesRepository.softDelete).mockResolvedValue(mockDeletedClass);

      const result = await useCase.execute({
        id: 'external-1',
        customerId: 'customer-1',
      });

      expect(result).toEqual(mockDeletedClass);
    });
  });

  describe('erros de validação', () => {
    it('deve lançar erro quando customerId estiver vazio', async () => {
      await expect(() =>
        useCase.execute({
          id: 'external-1',
          customerId: '',
        })
      ).rejects.toThrowError();
    });

    it('deve lançar erro quando id estiver vazio', async () => {
      await expect(() =>
        useCase.execute({
          id: '',
          customerId: 'customer-1',
        })
      ).rejects.toThrowError();
    });
  });

  describe('validação do cliente', () => {
    it('deve lançar ResourceNotFoundError quando o cliente não for encontrado', async () => {
      vi.mocked(customerRepository.findOneBy).mockResolvedValue(undefined);

      await expect(() =>
        useCase.execute({
          id: 'external-1',
          customerId: 'invalid-customer',
        })
      ).rejects.toThrowError(ResourceNotFoundError);

      expect(customerRepository.findOneBy).toHaveBeenCalledOnce();
    });
  });

  describe('validação da turma', () => {
    it('deve lançar ResourceNotFoundError quando a turma não for encontrada para o cliente', async () => {
      const mockCustomer: ICustomer = {
        id: 'customer-1',
        name: 'Test Customer',
        created_at: new Date(),
        updated_at: new Date(),
        deleted_at: null,
      };

      vi.mocked(customerRepository.findOneBy).mockResolvedValue(mockCustomer);
      vi.mocked(classesRepository.findOneBy).mockResolvedValue(undefined);

      await expect(() =>
        useCase.execute({
          id: 'external-1',
          customerId: 'customer-1',
        })
      ).rejects.toThrowError(ResourceNotFoundError);
    });

    it('deve lançar ResourceNotFoundError quando a turma já estiver deletada', async () => {
      const mockCustomer: ICustomer = {
        id: 'customer-1',
        name: 'Test Customer',
        created_at: new Date(),
        updated_at: new Date(),
        deleted_at: null,
      };

      vi.mocked(customerRepository.findOneBy).mockResolvedValue(mockCustomer);
      vi.mocked(classesRepository.findOneBy).mockResolvedValue(undefined);

      await expect(() =>
        useCase.execute({
          id: 'external-1',
          customerId: 'customer-1',
        })
      ).rejects.toThrowError(ResourceNotFoundError);
    });

    it('deve lançar ResourceNotFoundError quando a turma pertencer a outro cliente', async () => {
      const mockCustomer: ICustomer = {
        id: 'customer-1',
        name: 'Test Customer',
        created_at: new Date(),
        updated_at: new Date(),
        deleted_at: null,
      };

      vi.mocked(customerRepository.findOneBy).mockResolvedValue(mockCustomer);
      vi.mocked(classesRepository.findOneBy).mockResolvedValue(undefined);

      await expect(() =>
        useCase.execute({
          id: 'external-1',
          customerId: 'customer-1',
        })
      ).rejects.toThrowError(ResourceNotFoundError);
    });
  });

  describe('operação de deleção', () => {
    it('deve lançar GenericError quando a deleção suave falhar', async () => {
      const mockCustomer: ICustomer = {
        id: 'customer-1',
        name: 'Test Customer',
        created_at: new Date(),
        updated_at: new Date(),
        deleted_at: null,
      };

      const mockClass: IClasses = {
        id: 'class-1',
        name: 'Test Class',
        start_date: new Date(),
        end_date: new Date(),
        customer_id: 'customer-1',
        status: 'active',
        created_at: new Date(),
        updated_at: new Date(),
        deleted_at: null,
      };

      vi.mocked(customerRepository.findOneBy).mockResolvedValue(mockCustomer);
      vi.mocked(classesRepository.findOneBy).mockResolvedValue(mockClass);
      vi.mocked(classesRepository.softDelete).mockResolvedValue(undefined);

      await expect(() =>
        useCase.execute({
          id: 'external-1',
          customerId: 'customer-1',
        })
      ).rejects.toThrowError(GenericError);
    });
  });

  describe('chamadas ao repositório', () => {
    it('deve chamar os repositórios com os parâmetros corretos', async () => {
      const mockCustomer: ICustomer = {
        id: 'customer-1',
        name: 'Test Customer',
        created_at: new Date(),
        updated_at: new Date(),
        deleted_at: null,
      };

      const mockClass: IClasses = {
        id: 'class-1',
        name: 'Test Class',
        start_date: new Date(),
        end_date: new Date(),
        customer_id: 'customer-1',
        status: 'active',
        created_at: new Date(),
        updated_at: new Date(),
        deleted_at: null,
      };

      const mockDeletedClass: IClasses = {
        ...mockClass,
        deleted_at: new Date(),
      };

      vi.mocked(customerRepository.findOneBy).mockResolvedValue(mockCustomer);
      vi.mocked(classesRepository.findOneBy).mockResolvedValue(mockClass);
      vi.mocked(classesRepository.softDelete).mockResolvedValue(mockDeletedClass);

      await useCase.execute({
        id: 'external-1',
        customerId: 'customer-1',
      });

      expect(customerRepository.findOneBy).toHaveBeenCalledWith({
        external_customer_id: 'customer-1',
        deleted_at: null,
      });

      expect(classesRepository.findOneBy).toHaveBeenCalledWith({
        external_class_id: 'external-1',
        customer_id: mockCustomer.id,
        deleted_at: null,
      });

      expect(classesRepository.softDelete).toHaveBeenCalledWith({
        id: mockClass.id,
        customerId: mockCustomer.id,
      });
    });
  });
});
// describe global --- 1 por caso de uso
