import { IClasses } from '../../../model/IClasses';
import { IClassesRepository } from '../../../repositories/assign/classes.assign';
import { ICustomerRepository } from '../../../repositories/assign/customers.assign';
import { IUserClassesRepository } from '../../../repositories/assign/userClasses.assign';
import { verifyExternalCustomer } from '../../../services/verifyExternalCustomer.service';
import { GenericError } from '../../errors/GenericError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface DeleteExternalClassPayload {
  id: string;
  customerId: string;
}

export class DeleteExternalClassUseCase {
  constructor(
    private readonly classesRepository: IClassesRepository,
    private readonly customerRepository: ICustomerRepository,
    private readonly userClassesRepository: IUserClassesRepository
  ) {}

  async execute(payloadData: DeleteExternalClassPayload): Promise<IClasses> {
    const { id, customerId } = payloadData;

    const customer = await this.validateCustomer(customerId);

    const existingClass = await this.findAndValidateClass(id, customer.id);

    await this.deleteUserAccesses(existingClass.id);

    const deletedClass = await this.deleteClass(existingClass.id, customer.id);

    return deletedClass;
  }

  private async validateCustomer(customerId: string) {
    return await verifyExternalCustomer(this.customerRepository, customerId);
  }

  private async findAndValidateClass(
    externalClassId: string,
    customerId: string
  ): Promise<IClasses> {
    const existingClass = await this.classesRepository.findOneBy({
      external_class_id: externalClassId,
      customer_id: customerId,
      deleted_at: null,
    });

    if (!existingClass) {
      throw new ResourceNotFoundError('Turma não encontrada');
    }

    return existingClass;
  }

  private async deleteUserAccesses(classId: string): Promise<void> {
    await this.userClassesRepository.softDeleteByClassId(classId);
  }

  private async deleteClass(classId: string, customerId: string): Promise<IClasses> {
    const deletedClass = await this.classesRepository.softDelete({
      id: classId,
      customerId,
    });

    if (!deletedClass) {
      throw new GenericError('Erro ao excluir turma');
    }

    return deletedClass;
  }
}
