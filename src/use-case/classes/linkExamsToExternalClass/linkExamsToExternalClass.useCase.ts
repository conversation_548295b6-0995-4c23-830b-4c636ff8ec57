import { randomUUID } from 'crypto';

import { IClassesRepository } from '../../../repositories/assign/classes.assign';
import { ICustomerRepository } from '../../../repositories/assign/customers.assign';
import { IExamsRepository } from '../../../repositories/assign/exams.assign';
import { IExamsClassesRepository } from '../../../repositories/assign/examsClasses.assign';
import { ILinkExamsToExternalClassDTO } from '../../../schema/classes.schema';
import { verifyExternalCustomer } from '../../../services/verifyExternalCustomer.service';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface LinkExamsToExternalClassRequest extends ILinkExamsToExternalClassDTO {}

interface ClassExamLink {
  classId: string;
  examId: string;
}

interface LinkExamsToExternalClassResponse {
  linkedExams: number;
  unlinkedExams: number;
  classExams: ClassExamLink[];
}

export class LinkExamsToExternalClassUseCase {
  constructor(
    private readonly classesRepo: IClassesRepository,
    private readonly examsRepo: IExamsRepository,
    private readonly examsClassesRepo: IExamsClassesRepository,
    private readonly customerRepo: ICustomerRepository
  ) {}

  async execute({
    classIds,
    examIds,
    customerId,
  }: LinkExamsToExternalClassRequest): Promise<LinkExamsToExternalClassResponse> {
    const customer = await verifyExternalCustomer(this.customerRepo, customerId);

    const classes = await this.validateClasses(classIds, customer.id);

    if (examIds.length > 0) {
      await this.validateExams(examIds, customer.id);
    }

    let totalLinked = 0;
    let totalUnlinked = 0;
    const allClassExams: ClassExamLink[] = [];

    for (const classEntity of classes) {
      const result = await this.processClassExams(classEntity.id, examIds);

      totalLinked += result.linkedCount;
      totalUnlinked += result.unlinkedCount;

      examIds.forEach((examId) => {
        allClassExams.push({
          classId: classEntity.id,
          examId,
        });
      });
    }

    return {
      linkedExams: totalLinked,
      unlinkedExams: totalUnlinked,
      classExams: allClassExams,
    };
  }

  private async processClassExams(classId: string, examIds: string[]) {
    const currentLinks = await this.examsClassesRepo.findByClassId(classId);
    const currentExamIds = currentLinks.map((link) => link.exam_id);

    const idsToAdd = examIds.filter((id) => !currentExamIds.includes(id));
    const idsToRemove = currentExamIds.filter((id) => !examIds.includes(id));

    let unlinkedCount = 0;

    if (idsToRemove.length > 0) {
      const removed = await this.examsClassesRepo.deleteByClassIdAndExamIds(classId, idsToRemove);
      unlinkedCount = removed.length;
    }

    let linkedCount = 0;

    if (idsToAdd.length > 0) {
      const links = idsToAdd.map((examId) => ({
        id: randomUUID(),
        class_id: classId,
        exam_id: examId,
        created_at: new Date(),
        updated_at: new Date(),
      }));

      await this.examsClassesRepo.insertAll(links);
      linkedCount = idsToAdd.length;
    }

    return { linkedCount, unlinkedCount };
  }

  private async validateClasses(externalClassIds: string[], customerId: string) {
    const classes = await this.classesRepo.findByExternalClassIds({
      externalClassIds,
      customerId,
    });

    if (classes.length !== externalClassIds.length) {
      const foundExternalIds = new Set(classes.map((c) => c.external_class_id));
      const missingExternalIds = externalClassIds.filter((id) => !foundExternalIds.has(id));

      if (missingExternalIds.length > 0) {
        throw new ResourceNotFoundError(`Turmas não encontradas: ${missingExternalIds.join(', ')}`);
      }
    }

    return classes;
  }

  private async validateExams(examIds: string[], customerId: string) {
    if (examIds.length === 0) return;

    const exams = await this.examsRepo.findByIds(examIds, customerId);
    const foundIds = exams.map((exam) => exam.id);
    const missingIds = examIds.filter((id) => !foundIds.includes(id));

    if (missingIds.length > 0) {
      throw new BadRequestError(`Provas não encontradas: ${missingIds.join(', ')}`);
    }
  }
}
