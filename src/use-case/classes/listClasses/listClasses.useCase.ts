import { IClassesRepository } from '../../../repositories/assign/classes.assign';
import { ListClassesQueryInput } from '../../../schema/classes.schema';

interface ListClassesUseCaseRequest extends ListClassesQueryInput {}

interface ListClassesUseCaseResponse {
  classes: Array<{
    id: string;
    name: string;
    externalId?: string;
    start_date: Date;
    end_date?: Date | null;
    status: string;
    is_default?: boolean;
  }>;
}

export class ListClassesUseCase {
  constructor(private classesRepository: IClassesRepository) {}

  async execute(request: ListClassesUseCaseRequest): Promise<ListClassesUseCaseResponse> {
    const { orderByColumn = 'name', orderDirection = 'asc', ...data } = request;

    const classes = await this.classesRepository.listByCustomerId({
      orderByColumn,
      orderDirection,
      ...data,
    });

    return { classes };
  }
}
