import { IClassWithUsers } from '../../../model/DTO/IClasses.dto';
import { IPaginationDTO } from '../../../model/DTO/IGeneric.dto';
import { IClassesRepository } from '../../../repositories/assign/classes.assign';

interface ListClassesWithUsersRequest {
  customerId: string;
  search?: string;
  orderByColumn?: string;
  orderDirection?: string;
  page: number;
  limit: number;
  simulatedId?: string;
}

interface ListClassesWithUsersResponse {
  classes: IClassWithUsers[];
  paginationInfo: IPaginationDTO;
}

export class ListClassesWithUsersUseCase {
  constructor(private readonly classesRepository: IClassesRepository) {}

  async execute({
    customerId,
    search,
    orderByColumn = 'c.name',
    orderDirection = 'asc',
    page,
    limit,
    simulatedId,
  }: ListClassesWithUsersRequest): Promise<ListClassesWithUsersResponse> {
    const result = await this.classesRepository.findClassesWithUsersPaginated({
      customerId,
      search,
      orderByColumn,
      orderDirection,
      page,
      limit,
      simulatedId,
    });

    return result;
  }
}
