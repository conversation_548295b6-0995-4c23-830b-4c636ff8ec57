import { generateTransaction } from '../../helpers/transaction.helper';
import { IClasses } from '../../model/IClasses';
import { IClassesRepository } from '../../repositories/assign/classes.assign';
import { IUpdateClassDTO } from '../../schema/classes.schema';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

export class UpdateClassUseCase {
  constructor(private readonly classesRepository: IClassesRepository) {}

  async execute({ id, ...data }: IUpdateClassDTO & { id: string }) {
    const classData = await this.classesRepository.findOneBy({ id });

    if (!classData) {
      throw new ResourceNotFoundError('Turma não encontrada');
    }

    const payload = this.buildUpdatePayload(classData, data);

    const trx = await generateTransaction();

    try {
      if (data.isDefault) {
        const existingDefault = await this.classesRepository.findOneBy({
          customer_id: classData.customer_id,
          is_default: true,
          course_id: classData.course_id,
        });

        if (existingDefault && existingDefault.id !== id) {
          const updated = await this.classesRepository.updateWithTrx(
            { id: existingDefault.id, is_default: false },
            trx
          );
          if (!updated) {
            await trx.rollback();
            throw new Error('Erro ao remover padrão da turma anterior');
          }
        }
      }

      const updated = await this.classesRepository.updateWithTrx(payload, trx);

      if (!updated) {
        await trx.rollback();
        throw new Error('Erro ao atualizar turma');
      }

      await trx.commit();
      return updated;
    } catch (err) {
      await trx.rollback();
      throw err;
    }
  }

  private buildUpdatePayload(existing: IClasses, update: Partial<IUpdateClassDTO>) {
    return {
      id: existing.id,
      name: update.name ?? existing.name,
      description: update.description ?? existing.description,
      start_date: update.started_date ? new Date(update.started_date) : existing.start_date,
      end_date: update.end_date ? new Date(update.end_date) : existing.end_date,
      course_id: update.course_id ?? existing.course_id,
      status: update.status ?? existing.status,
      is_default: update.isDefault !== undefined ? update.isDefault : existing.is_default,
    };
  }
}
