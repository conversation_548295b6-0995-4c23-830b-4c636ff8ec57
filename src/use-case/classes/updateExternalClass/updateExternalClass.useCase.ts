import { IClasses } from '../../../model/IClasses';
import { IClassesRepository } from '../../../repositories/assign/classes.assign';
import { ICustomerRepository } from '../../../repositories/assign/customers.assign';
import { IUsersRepository } from '../../../repositories/assign/users.assign';
import { IUpdateExternalClassDTO } from '../../../schema/classes.schema';
import { verifyExternalCustomer } from '../../../services/verifyExternalCustomer.service';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface UpdateExternalClassPayload extends IUpdateExternalClassDTO {
  id: string;
}

export class UpdateExternalClassUseCase {
  constructor(
    private readonly classesRepository: IClassesRepository,
    private readonly customerRepository: ICustomerRepository,
    private readonly usersRepository: IUsersRepository
  ) {}

  async execute(payload: UpdateExternalClassPayload): Promise<IClasses> {
    const customer = await verifyExternalCustomer(this.customerRepository, payload.customerId);

    const existingClass = await this.verifyClass(payload.id, customer.id);

    if (payload.instructorId) {
      await this.ensureInstructorExists(
        payload.instructorId,
        existingClass.instructor_id ?? undefined,
        customer.id
      );
    }

    const updatedData = this.buildUpdateData(payload, existingClass);

    return this.classesRepository.update(updatedData);
  }

  private async verifyClass(externalClassId: string, customerId: string): Promise<IClasses> {
    const classEntity = await this.classesRepository.findOneBy({
      external_class_id: externalClassId,
      customer_id: customerId,
      deleted_at: null,
    });

    if (!classEntity) throw new ResourceNotFoundError('Turma não encontrada');
    return classEntity;
  }

  private async ensureInstructorExists(
    newInstructorId?: string,
    currentInstructorId?: string,
    customerId?: string
  ) {
    if (!newInstructorId || newInstructorId === currentInstructorId) return;

    const user = await this.usersRepository.findOneBy({
      external_user_id: newInstructorId,
      customer_id: customerId!,
      deleted_at: null,
    });

    if (!user) throw new ResourceNotFoundError('Instrutor não encontrado');
  }

  private buildUpdateData(
    payload: UpdateExternalClassPayload,
    existingClass: IClasses
  ): Partial<IClasses> {
    const { name, description, status, startDate, endDate, instructorId } = payload;
    return {
      id: existingClass.id,
      name: name ?? existingClass.name,
      description: description ?? existingClass.description,
      status: status ?? existingClass.status,
      start_date: startDate ? new Date(startDate) : existingClass.start_date,
      end_date: endDate ? new Date(endDate) : existingClass.end_date,
      instructor_id: instructorId ?? existingClass.instructor_id,
    };
  }
}
