import { ICountry } from '../../model/countries';
import { ICountriesRepository } from '../../repositories/assign/countries.assign';
import { ListAllCountriesDto } from '../../schema/countries.schema';

export class ListAllCountriesUseCase {
  constructor(private countriesRepository: ICountriesRepository) {}

  async execute(data: ListAllCountriesDto): Promise<ICountry[]> {
    return this.countriesRepository.findAllCountries(data);
  }
}
