import { randomUUID } from 'crypto';
import { Knex } from 'knex';

import { generateTransaction } from '../../helpers/transaction.helper';
import { ICourse } from '../../model/ICourse';
import { IClassesRepository } from '../../repositories/assign/classes.assign';
import { ICourseRepository } from '../../repositories/assign/courses.assign';
import { ICoursesCategoriesRelationRepository } from '../../repositories/assign/coursesCategoriesRelation.assign';
import { CreateCourseRequestDTO } from '../../schema/courses.schema';
import { GenericError } from '../errors/GenericError';
import { ResourceAlreadyExistsError } from '../errors/ResourceAlreadyExistsError';

export class CreateCourseUseCase {
  constructor(
    private readonly courseRepository: ICourseRepository,
    private readonly coursesCategoriesRelationRepository: ICoursesCategoriesRelationRepository,
    private readonly classesRepository: IClassesRepository
  ) {}

  public async execute(
    bodyData: CreateCourseRequestDTO & { customerId: string }
  ): Promise<ICourse> {
    const trx = await generateTransaction();

    const { name, descriptionCourse, photoUrl, customerId, categoryIds } = bodyData;

    const existingCourse = await this.courseRepository.findOneBy({
      name,
      customer_id: customerId,
    });

    if (existingCourse) {
      throw new ResourceAlreadyExistsError('Já existe um curso com este nome.');
    }

    const courseInsert = {
      id: randomUUID(),
      name,
      description_course: descriptionCourse,
      photo_url: photoUrl,
      customer_id: customerId,
    };

    const response = await this.courseRepository.insert(courseInsert, trx);

    if (!response) {
      await trx.rollback();
      throw new GenericError('Erro ao criar curso.');
    }

    if (categoryIds && categoryIds.length > 0) {
      const now = new Date();
      const relations = categoryIds.map((categoryId) => ({
        id: randomUUID(),
        course_id: response.id,
        courses_categories_id: categoryId,
        created_at: now,
        updated_at: now,
        deleted_at: null,
      }));
      await this.coursesCategoriesRelationRepository.insertAll(relations, trx);
    }

    const createdClass = await this.createClass(response.id, customerId, trx);

    if (!createdClass) {
      await trx.rollback();
      throw new GenericError('Erro ao criar aula.');
    }

    await trx.commit();

    return response;
  }

  private async createClass(courseId: string, customerId: string, trx: Knex.Transaction) {
    const classInsert = {
      id: randomUUID(),
      course_id: courseId,
      customer_id: customerId,
      name: 'Turma Padrão',
      description: 'Turma inicial do curso',
      start_date: new Date(),
      end_date: null,
      instructor_id: null,
      status: 'active',
      is_default: true,
    };

    const response = await this.classesRepository.insert(classInsert, trx);

    return response;
  }
}
