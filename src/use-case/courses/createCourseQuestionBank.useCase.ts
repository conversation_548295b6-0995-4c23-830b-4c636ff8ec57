import { randomUUID } from 'crypto';

// import { generateTransaction } from '../../helpers/transaction.helper';
import { ICourseQuestionsGroupsRepository } from '../../repositories/assign/coursesQuestionsGroups.assign';

export class CreateCourseQuestionBankUseCase {
  constructor(private readonly courseQuestionGroupsRepository: ICourseQuestionsGroupsRepository) {}

  async execute(courseId: string, questionGroupId: string) {
    try {
      const courseQuestionGroup = await this.courseQuestionGroupsRepository.findOneBy({
        course_id: courseId,
      });

      if (courseQuestionGroup) {
        await this.courseQuestionGroupsRepository.update({
          id: courseQuestionGroup.id,
          course_id: courseId,
          question_group_id: questionGroupId,
        });
        return {
          statusCode: 201,
          message: 'Course and Question Bank updated.',
          data: courseQuestionGroup,
        };
      }
      const response = await this.courseQuestionGroupsRepository.insert({
        id: randomUUID(),
        course_id: courseId,
        question_group_id: questionGroupId,
        created_at: new Date(),
        updated_at: new Date(),
        deleted_at: null,
      });

      return response;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
