import { env } from '../../env';
import { ICourseDetail } from '../../model/DTO/ICourse.dto';
import { ICourseRepository } from '../../repositories/assign/courses.assign';
import S3Service from '../../services/aws/s3/S3Service';

export class GetCourseUseCase {
  constructor(
    private readonly courseRepository: ICourseRepository,
    private readonly s3Service: S3Service
  ) {}

  public async execute(courseId: string): Promise<ICourseDetail> {
    const bucketName = env.BUCKET_FILES;
    const course = await this.courseRepository.findCourseById(courseId);

    if (!course) {
      throw new Error('Curso não encontrado');
    }

    if (course.photo_url) {
      course.photo_url = await this.s3Service.getSignedUrl(bucketName, course.photo_url, 36000);
    }

    const courseDetail = course as ICourseDetail;
    return {
      id: courseDetail.id,
      name: courseDetail.name,
      description_course: courseDetail.description_course,
      photo_url: courseDetail.photo_url,
      created_at: courseDetail.created_at,
      customer_id: courseDetail.customer_id,
      modules: courseDetail.modules ?? [],
      categories: courseDetail.categories ?? [],
    } as ICourseDetail;
  }
}
