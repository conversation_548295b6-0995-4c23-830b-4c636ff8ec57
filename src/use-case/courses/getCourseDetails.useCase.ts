import { ICourseDetailsResponse } from '../../model/DTO/ICourse.dto';
import { ICourseDetailsRawData } from '../../model/DTO/request/course.request.dto';
import { ICourseRepository } from '../../repositories/assign/courses.assign';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

export class GetCourseDetailsUseCase {
  constructor(private readonly courseRepository: ICourseRepository) {}

  public async execute(courseId: string, userId: string): Promise<ICourseDetailsResponse | null> {
    const rawData = await this.courseRepository.getCourseDetails(courseId, userId);

    if (!rawData || rawData.length === 0) {
      throw new ResourceNotFoundError('Curso não encontrado');
    }

    const lastModuleAccess = await this.courseRepository.getLastModuleAccess(userId, courseId);
    const lastLessonAccess = await this.courseRepository.getLastLessonAccess(userId, courseId);

    const timeToSeconds = (time: string): number => {
      const [minutes, seconds] = time.split(':').map(Number);
      return minutes * 60 + seconds;
    };

    const secondsToTime = (seconds: number): string => {
      const minutes = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
    };

    const formattedResult = rawData.reduce(
      (acc: ICourseDetailsResponse[], row: ICourseDetailsRawData) => {
        const {
          course_name: courseName,
          course_id: courseId,
          module_id: moduleId,
          module_name: moduleName,
          module_description: moduleDescription,
          lesson_id: lessonId,
          lesson_title: lessonTitle,
          lesson_description: lessonDescription,
          lesson_url: lessonUrl,
          question_group_id: questionGroupId,
          lesson_duration: lessonDuration,
          course_status: courseStatus,
          module_status: moduleStatus,
          lesson_status: lessonStatus,
          lesson_current_position: lessonCurrentPosition,
          lesson_files: lessonFiles,
        } = row;

        let course = acc.find((course) => course.title === courseName);
        if (!course) {
          course = {
            id: courseId,
            title: courseName,
            status: courseStatus || 'not_started',
            modules: [],
            lastLessonAccessed: lastLessonAccess?.lesson_id ? lastLessonAccess.lesson_id : null,
            lastModuleAccessed: lastModuleAccess?.module_id ? lastModuleAccess.module_id : null,
          };
          acc.push(course);
        }

        let module = course!.modules.find((mod) => mod.id === String(moduleId));
        if (!module) {
          module = {
            id: String(moduleId),
            title: moduleName,
            description: moduleDescription,
            status: moduleStatus || 'not_started',
            lessons: [],
            totalLessons: 0,
            completedLessons: 0,
          };
          course!.modules.push(module);
        }

        let lesson = module!.lessons.find((lesson) => lesson.id === String(lessonId));
        if (!lesson) {
          const durationSeconds = timeToSeconds(lessonDuration || '00:00');
          const currentPositionSeconds = lessonCurrentPosition;

          const progressPercent = durationSeconds
            ? Math.round((currentPositionSeconds / durationSeconds) * 100)
            : 0;

          lesson = {
            id: String(lessonId),
            title: lessonTitle,
            description: lessonDescription,
            url: lessonUrl,
            questionGroupId: questionGroupId ? String(questionGroupId) : null,
            type: questionGroupId ? 'question' : 'video',
            duration: lessonDuration || '00:00',
            currentPosition: secondsToTime(currentPositionSeconds),
            progressPercent,
            currentPositionSeconds,
            status: lessonStatus || 'not_started',
            files: lessonFiles || [],
            classes: [],
          };
          module!.lessons.push(lesson);
          module!.totalLessons++;

          if (lesson.status === 'completed') {
            module!.completedLessons++;
          }
        }

        return acc;
      },
      [] as ICourseDetailsResponse[]
    );

    formattedResult.forEach((course: ICourseDetailsResponse) => {
      course.modules.sort((a, b) => Number(a.id) - Number(b.id));
      course.modules.forEach((module) => {
        module.lessons.sort((a, b) => Number(a.id) - Number(b.id));
      });
    });

    if (formattedResult.length > 0) {
      const firstCourse = formattedResult[0];
      if (firstCourse.modules.length > 0) {
        const firstModule = firstCourse.modules[0];
        if (firstModule.lessons.length > 0) {
          const hasStartedLesson = firstModule.lessons.some(
            (lesson: { status: string }) =>
              lesson.status === 'in_progress' || lesson.status === 'completed'
          );

          if (!hasStartedLesson) {
            firstModule.lessons[0].status = 'in_progress';
            firstModule.status = 'in_progress';
            firstCourse.status = 'in_progress';
          }
        }
        if (!firstCourse.lastLessonAccessed && firstModule && firstModule.lessons.length > 0) {
          firstCourse.lastLessonAccessed = firstModule.lessons[0].id;
        }
        if (!firstCourse.lastModuleAccessed && firstModule) {
          firstCourse.lastModuleAccessed = firstModule.id;
        }
      }
    }

    return formattedResult.length > 0 ? formattedResult[0] : null;
  }
}
