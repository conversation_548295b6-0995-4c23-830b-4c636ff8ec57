import { ICourseLastAccess } from '../../model/DTO/ICourse.dto';
import { ICourseRepository } from '../../repositories/assign/courses.assign';
import S3Service from '../../services/aws/s3/S3Service';

export class LastAccessCourseUseCase {
  constructor(
    private readonly courseRepository: ICourseRepository,
    private readonly s3Service = new S3Service()
  ) {}

  async execute(userId: string): Promise<ICourseLastAccess | undefined> {
    const responseCourseLastAccess = await this.courseRepository.lastAccess(userId);

    if (!responseCourseLastAccess) {
      return undefined;
    }

    const buckeName = process.env.BUCKET_FILES || 'propofando-lxp-files';

    if (responseCourseLastAccess.logoUrl) {
      responseCourseLastAccess.logoUrl = await this.s3Service.getSignedUrl(
        buckeName,
        responseCourseLastAccess.logoUrl,
        36000
      );
    } else {
      responseCourseLastAccess.logoUrl = await this.s3Service.getSignedUrl(
        buckeName,
        'logo-euintubo',
        3600
      );
    }
    return responseCourseLastAccess;
  }
}
