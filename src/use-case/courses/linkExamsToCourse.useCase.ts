import { randomUUID } from 'crypto';

import { ICourseExams } from '../../model/ICourseExams';
import { ICourseExamsRepository } from '../../repositories/assign/coursesExams.assign';
import { IExamsRepository } from '../../repositories/assign/exams.assign';
import { BadRequestError } from '../errors/BadRequestError';

interface LinkExamsToCourseRequest {
  courseId: string;
  examIds: string[];
  customerId: string;
}

interface LinkExamsToCourseResponse {
  linkedExams: number;
  unlinkedExams: number;
}

export class LinkExamsToCourseUseCase {
  constructor(
    private readonly examsRepo: IExamsRepository,
    private readonly courseExamsRepo: ICourseExamsRepository
  ) {}

  async execute({
    courseId,
    examIds,
    customerId,
  }: LinkExamsToCourseRequest): Promise<LinkExamsToCourseResponse> {
    let toAdd: string[] = [];
    let toRemove: ICourseExams[] = [];

    const currentExamIds = await this.courseExamsRepo.findByCourseId(courseId);

    if (examIds && examIds.length > 0) {
      await this.validateExams(examIds, customerId);

      toRemove = currentExamIds.filter((exam) => !examIds.includes(exam.exam_id));
      toAdd = examIds.filter((id) => !currentExamIds.some((exam) => exam.exam_id === id));

      if (toRemove.length > 0) {
        await this.courseExamsRepo.deleteByCourseIdAndExamIds(
          courseId,
          toRemove.map((exam) => exam.exam_id)
        );
      }

      if (toAdd.length > 0) {
        const now = new Date();
        const newLinks = toAdd.map((examId) => ({
          id: randomUUID(),
          course_id: courseId,
          exam_id: examId,
          created_at: now,
          updated_at: now,
          deleted_at: null,
        }));
        await this.courseExamsRepo.insertAll(newLinks);
      }
    } else {
      toRemove = currentExamIds;
      await this.courseExamsRepo.deleteByCourseId(courseId);
    }

    return { linkedExams: toAdd.length, unlinkedExams: toRemove.length };
  }

  private async validateExams(examIds: string[], customerId: string) {
    if (examIds.length === 0) return;

    const exams = await this.examsRepo.findByIds(examIds, customerId);
    const foundIds = exams.map((exam) => exam.id);
    const missingIds = examIds.filter((id) => !foundIds.includes(id));

    if (missingIds.length > 0) {
      throw new BadRequestError(`Provas não encontradas: ${missingIds.join(', ')}`);
    }
  }
}
