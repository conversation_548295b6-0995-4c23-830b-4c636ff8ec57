import { env } from '../../env';
import { ICourse } from '../../model/ICourse';
import { ICourseRepository } from '../../repositories/assign/courses.assign';
import S3Service from '../../services/aws/s3/S3Service';

export class ListAllCoursesUseCase {
  constructor(
    private readonly coursesRepository: ICourseRepository,
    private readonly s3Service: S3Service
  ) {}

  public async execute(customerId: string): Promise<ICourse[]> {
    const courses = await this.coursesRepository.listAllCourses(customerId);
    const bucketName = env.BUCKET_FILES;

    const coursesWithSignedUrls = await Promise.all(
      courses.map(async (course) => {
        if (course.photo_url) {
          course.photo_url = await this.s3Service.getSignedUrl(bucketName, course.photo_url, 36000);
        }
        return course;
      })
    );

    return coursesWithSignedUrls;
  }
}
