import { env } from '../../env';
import { ICourse } from '../../model/ICourse';
import { ICourseRepository } from '../../repositories/assign/courses.assign';
import { IUsersRepository } from '../../repositories/assign/users.assign';
import S3Service from '../../services/aws/s3/S3Service';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

export class ListAllCoursesSimulationsUseCase {
  constructor(
    private readonly coursesRepository: ICourseRepository,
    private readonly userRepository: IUsersRepository,
    private readonly s3Service: S3Service
  ) {}

  async execute(userId: string, customerId: string): Promise<ICourse[]> {
    const user = await this.userRepository.findOneBy({ id: userId, customer_id: customerId });

    if (!user) {
      throw new ResourceNotFoundError('Usu<PERSON>rio não encontrado');
    }

    const courses = await this.coursesRepository.listAllCoursesSimulations(
      user.customer_id,
      userId
    );

    const coursesWithSignedUrls = await Promise.all(
      courses.map(async (course) => {
        if (course.photo_url) {
          course.photo_url = await this.s3Service.getSignedUrl(
            env.BUCKET_FILES,
            course.photo_url,
            36000
          );
        }
        return course;
      })
    );

    return coursesWithSignedUrls;
  }
}
