import { ListCoursesByUserAndClassResponseDto } from '../../../model/DTO/response/course.dto';
import { ICourseRepository } from '../../../repositories/assign/courses.assign';
import S3Service from '../../../services/aws/s3/S3Service';

export class ListCoursesByUserUseCase {
  constructor(
    private readonly coursesRepository: ICourseRepository,
    private readonly s3Service: S3Service
  ) {}

  async execute(userId: string): Promise<ListCoursesByUserAndClassResponseDto[]> {
    const courses = await this.coursesRepository.listAllCoursesUser(userId);

    const coursesWithSignedPhoto = await Promise.all(
      courses.map(async (course) => {
        let signedPhotoUrl = course.photo_url;

        if (course.photo_url) {
          signedPhotoUrl = await this.s3Service.getSignedUrl(
            process.env.BUCKET_FILES || 'propofando-lxp-files',
            course.photo_url,
            36000
          );
        }
        return {
          ...course,
          photo_url: signedPhotoUrl,
          className: course.class_name,
          class_name: course.class_name,
        };
      })
    );

    return coursesWithSignedPhoto;
  }
}
