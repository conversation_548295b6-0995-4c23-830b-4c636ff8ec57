import { ICourseRepository } from '../../../repositories/assign/courses.assign';
import { IUserClassesRepository } from '../../../repositories/assign/userClasses.assign';
import { IUsersRepository } from '../../../repositories/assign/users.assign';
import { RemoveUsersFromCourseInput } from '../../../schema/courses.schema';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

export interface RemoveUsersFromCourseDTO extends RemoveUsersFromCourseInput {}

export class RemoveUsersFromCourseUseCase {
  constructor(
    private userClassesRepository: IUserClassesRepository,
    private usersRepository: IUsersRepository,
    private courseRepository: ICourseRepository
  ) {}

  async execute({
    userIds,
    courseId,
    customerId,
  }: RemoveUsersFromCourseDTO): Promise<{ totalRemoved: number }> {
    const course = await this.courseRepository.findOneBy({
      id: courseId,
      deleted_at: null,
      customer_id: customerId,
    });

    if (!course) {
      throw new ResourceNotFoundError('Curso não encontrado');
    }

    const foundUsersList = await this.usersRepository.findAllByIds(userIds, customerId);
    const foundUserIds = foundUsersList.map((user) => user.id);
    const notFound = userIds.filter((id) => !foundUserIds.includes(id));

    if (notFound.length > 0) {
      throw new ResourceNotFoundError(`Usuário(s) não encontrado(s): ${notFound.join(', ')}`);
    }

    const userLinks = await this.userClassesRepository.findByUserIdsAndClassIds(userIds, courseId);
    const linkedUserIds = userLinks.map((link) => link.user_id);
    const notLinked = userIds.filter((id) => !linkedUserIds.includes(id));

    if (notLinked.length > 0) {
      throw new BadRequestError(`Usuário(s) não vinculado(s) ao curso: ${notLinked.join(', ')}`);
    }

    await this.userClassesRepository.softDeleteByUserIdsAndCourseId(userIds, courseId);
    return { totalRemoved: linkedUserIds.length };
  }
}
