import { <PERSON><PERSON> } from 'knex';

import { generateTransaction } from '../../helpers/transaction.helper';
import { ITrackProgressResponse } from '../../model/DTO/response/progress.response.dto';
import { ProgressStatus } from '../../model/enums/progressStatus.enum';
import { IUserLessonProgress } from '../../model/IUserLessonProgress';
import { IUserCourseProgressRepository } from '../../repositories/assign/userCourseProgress.assign';
import { IUserLessonsProgressRepository } from '../../repositories/assign/userLessonsProgress.assign';
import { IUserModulesProgressRepository } from '../../repositories/assign/userModulesProgress.assign';
import { GenericError } from '../errors/GenericError';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

export class TrackProgressUseCase {
  private readonly COMPLETION_THRESHOLD = 0.99;

  constructor(
    private readonly userCourseProgressRepository: IUserCourseProgressRepository,
    private readonly userLessonsProgressRepository: IUserLessonsProgressRepository,
    private readonly userModulesProgressRepository: IUserModulesProgressRepository
  ) {}

  public async execute(
    courseId: string,
    lessonId: string,
    progress: number,
    userId: string,
    timeWatched?: number
  ): Promise<ITrackProgressResponse['data']> {
    const trx = await generateTransaction();

    try {
      const lessonData = await this.userLessonsProgressRepository.getLessonWithProgress(
        userId,
        lessonId
      );

      if (!lessonData.lesson) {
        throw new ResourceNotFoundError('Aula não encontrada');
      }

      const { lesson, currentProgress } = lessonData;

      const status = this.determineLessonStatus(
        progress,
        lesson.duration,
        lesson.questionGroupId,
        timeWatched
      );

      const lessonProgress = await this.updateLessonProgress(
        trx,
        userId,
        lessonId,
        progress,
        currentProgress && currentProgress?.status === ProgressStatus.COMPLETED
          ? ProgressStatus.COMPLETED
          : status,
        currentProgress
      );

      if (!lessonProgress) {
        await trx.rollback();
        throw new GenericError('Erro ao atualizar progresso da aula');
      }

      const existingCourseProgress = await this.userCourseProgressRepository.getCourseProgress(
        userId,
        courseId
      );

      if (!existingCourseProgress) {
        await this.userCourseProgressRepository.insertCourseProgress(
          trx,
          userId,
          courseId,
          ProgressStatus.IN_PROGRESS
        );
      }

      const moduleProgress = await this.userModulesProgressRepository.getModuleProgress(
        userId,
        lesson.moduleId
      );

      if (!moduleProgress) {
        await this.userModulesProgressRepository.insertModuleProgress(
          {
            user_id: userId,
            module_id: lesson.moduleId,
            status: ProgressStatus.IN_PROGRESS,
          },
          trx
        );
      }

      const lessonCompleted =
        status === ProgressStatus.COMPLETED &&
        (!currentProgress || currentProgress.current_position < lesson.duration);

      let moduleCompleted = false;
      let courseCompleted = false;

      if (lessonCompleted) {
        const progressData = await this.recalculateProgressHierarchy(
          trx,
          userId,
          lesson.moduleId,
          courseId
        );
        moduleCompleted = progressData.moduleCompleted;
        courseCompleted = progressData.courseCompleted;
      }

      await trx.commit();

      return {
        lessonProgress: progress,
        lessonStatus: status,
        moduleCompleted,
        courseCompleted,
      };
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  private determineLessonStatus(
    progress: number,
    lessonDuration: number,
    questionGroupId?: string,
    timeWatched?: number
  ): ProgressStatus {
    if (questionGroupId) {
      return ProgressStatus.COMPLETED;
    }

    if (progress >= lessonDuration) {
      return ProgressStatus.COMPLETED;
    }

    const completionThreshold = lessonDuration * this.COMPLETION_THRESHOLD;
    const effectiveProgress = timeWatched !== undefined ? timeWatched : progress;

    if (effectiveProgress >= completionThreshold) {
      return ProgressStatus.COMPLETED;
    }

    if (timeWatched === undefined && progress >= completionThreshold) {
      return ProgressStatus.COMPLETED;
    }

    const status = progress > 0 ? ProgressStatus.IN_PROGRESS : ProgressStatus.NOT_STARTED;
    return status;
  }

  private async updateLessonProgress(
    trx: Knex.Transaction,
    userId: string,
    lessonId: string,
    progress: number,
    status: ProgressStatus,
    currentProgress?: { current_position: number }
  ): Promise<IUserLessonProgress | undefined> {
    if (currentProgress) {
      return await this.userLessonsProgressRepository.updateLessonProgress(
        trx,
        userId,
        lessonId,
        progress,
        status
      );
    } else {
      return await this.userLessonsProgressRepository.insertLessonProgress(
        trx,
        userId,
        lessonId,
        progress,
        status
      );
    }
  }

  private async recalculateProgressHierarchy(
    trx: Knex.Transaction,
    userId: string,
    moduleId: string,
    courseId: string
  ): Promise<{ moduleCompleted: boolean; courseCompleted: boolean }> {
    const progressData = await this.userModulesProgressRepository.getProgressHierarchyData(
      userId,
      moduleId,
      courseId
    );

    const { moduleData, courseData } = progressData;

    const moduleCompleted = moduleData.completedLessons + 1 >= moduleData.totalLessons;
    const moduleStatus = moduleCompleted ? ProgressStatus.COMPLETED : ProgressStatus.IN_PROGRESS;

    if (moduleData.progress && moduleData.progress.status !== moduleStatus) {
      await this.userModulesProgressRepository.updateModuleProgress(
        trx,
        userId,
        moduleId,
        moduleStatus
      );
    }

    const courseCompleted = courseData.completedModules >= courseData.totalModules;
    const courseStatus = courseCompleted ? ProgressStatus.COMPLETED : ProgressStatus.IN_PROGRESS;

    if (courseData.progress && courseData.progress.status !== courseStatus) {
      await this.userCourseProgressRepository.updateCourseProgress(
        trx,
        userId,
        courseId,
        courseStatus
      );
    }

    return { moduleCompleted, courseCompleted };
  }
}
