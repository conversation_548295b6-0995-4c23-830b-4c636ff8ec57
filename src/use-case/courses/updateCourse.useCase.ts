import { randomUUID } from 'crypto';

import { generateTransaction } from '../../helpers/transaction.helper';
import { ICourse } from '../../model/ICourse';
import { ICourseRepository } from '../../repositories/assign/courses.assign';
import { ICoursesCategoriesRelationRepository } from '../../repositories/assign/coursesCategoriesRelation.assign';
import { UpdateCourseRequestDTO } from '../../schema/courses.schema';

interface UpdateCourseUseCaseProps extends UpdateCourseRequestDTO {}

export class UpdateCourseUseCase {
  constructor(
    private readonly coursesRepository: ICourseRepository,
    private readonly coursesCategoriesRelationRepository: ICoursesCategoriesRelationRepository
  ) {}

  async execute({
    courseId,
    name,
    descriptionCourse,
    photoUrl,
    categoryIds,
  }: UpdateCourseUseCaseProps): Promise<ICourse> {
    const trx = await generateTransaction();
    try {
      const course = await this.coursesRepository.findOneBy({ id: courseId });

      if (!course) {
        throw new Error('Curso não encontrado');
      }

      const updatedCourse = await this.coursesRepository.update(
        {
          id: courseId ?? course.id,
          name: name ?? course.name,
          description_course: descriptionCourse ?? course.description_course,
          photo_url: photoUrl ?? course.photo_url,
        },
        trx
      );

      if (!updatedCourse) {
        throw new Error('Erro ao atualizar curso');
      }

      if (categoryIds) {
        const currentRelations = await this.coursesCategoriesRelationRepository.findAllByCourseId({
          course_id: courseId,
        });
        const currentCategoryIds = currentRelations.map((rel) => rel.courses_categories_id);

        const toRemove = currentCategoryIds.filter((id) => !categoryIds.includes(id));
        const toAdd = categoryIds.filter((id) => !currentCategoryIds.includes(id));

        if (toRemove.length > 0) {
          await this.coursesCategoriesRelationRepository.deleteByCourseIdAndCategoryIds(
            courseId,
            toRemove,
            trx
          );
        }

        if (toAdd.length > 0) {
          const now = new Date();
          const newLinks = toAdd.map((categoryId) => ({
            id: randomUUID(),
            course_id: courseId,
            courses_categories_id: categoryId,
            created_at: now,
            updated_at: now,
            deleted_at: null,
          }));
          await this.coursesCategoriesRelationRepository.insertAll(newLinks, trx);
        }
      }

      await trx.commit();
      return updatedCourse;
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }
}
