import { ICourseCategory } from '../../model/courseCategory';
import {
  ICoursesCategoriesFilters,
  ICoursesCategoriesRepository,
} from '../../repositories/assign/coursesCategories.assign';
import { filterTextRegex } from '../../services/filterTextRegex.service';

export class GetAllCourseCategoriesUseCase {
  constructor(private readonly coursesCategoriesRepository: ICoursesCategoriesRepository) {}

  async execute(data: ICoursesCategoriesFilters): Promise<ICourseCategory[]> {
    const { name } = data;

    const coursesCategories = await this.coursesCategoriesRepository.findAllByFilters(data);

    if (name) {
      return coursesCategories.sort((a, b) =>
        filterTextRegex(a.name).localeCompare(filterTextRegex(b.name))
      );
    }
    return coursesCategories;
  }
}
