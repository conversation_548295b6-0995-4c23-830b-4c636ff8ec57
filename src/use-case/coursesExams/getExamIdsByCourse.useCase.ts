import { ICourseExamsRepository } from '../../repositories/assign/coursesExams.assign';
interface GetExamIdsByCourseResponse {
  data: string[];
}
export class GetExamIdsByCourseUseCase {
  constructor(private readonly courseExamsRepository: ICourseExamsRepository) {}

  async execute(courseId: string): Promise<GetExamIdsByCourseResponse> {
    const examIds = await this.courseExamsRepository.getExamIdsByCourseId(courseId);

    return { data: examIds };
  }
}
