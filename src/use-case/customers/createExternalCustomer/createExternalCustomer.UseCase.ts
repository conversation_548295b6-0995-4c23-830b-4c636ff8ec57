import { ICustomerPublicDTO, ICustomerWithTokenDTO } from '../../../model/DTO/ICustomer.dto';
import { ICustomer } from '../../../model/ICustomer';
import { ICustomerRepository } from '../../../repositories/assign/customers.assign';
import { IInstitutionsRepository } from '../../../repositories/assign/institutions.assign';
import { ResourceAlreadyExistsError } from '../../errors/ResourceAlreadyExistsError';

export class CreateExternalCustomerUseCase {
  constructor(
    private readonly customerRepository: ICustomerRepository,
    private readonly institutionRepository: IInstitutionsRepository
  ) {}

  async execute(payloadData: ICustomerWithTokenDTO): Promise<ICustomerPublicDTO> {
    await this.ensureTaxNumberIsUnique(payloadData.taxNumber);

    await this.ensureExternalCustomerIdIsUnique(payloadData.id);

    const customerDataToInsert = this.buildCustomerInsertPayload(payloadData);

    const customer = await this.customerRepository.insert(customerDataToInsert);

    const institutionData = {
      name: 'Universidade de São Paulo',
      acronym: 'USP',
      customer_id: customer.id,
    };

    await this.institutionRepository.insert({
      ...institutionData,
    });

    return this.mapToPublicUser(customer);
  }

  private async ensureTaxNumberIsUnique(taxNumber?: string) {
    if (!taxNumber) return;

    const existingCustomer = await this.customerRepository.getTaxNumber(taxNumber);

    if (existingCustomer) {
      throw new ResourceAlreadyExistsError('Já existe um cliente com esse tax_number');
    }
  }

  private async ensureExternalCustomerIdIsUnique(externalCustomerId?: string) {
    if (!externalCustomerId) return;

    const existingCustomer = await this.customerRepository.findOneBy({
      external_customer_id: externalCustomerId,
      deleted_at: null,
    });

    if (existingCustomer) {
      throw new ResourceAlreadyExistsError('Já existe um cliente com esse id');
    }
  }

  private buildCustomerInsertPayload(payloadData: ICustomerWithTokenDTO) {
    return {
      name: payloadData.customerName,
      email: payloadData.email,
      tax_number: payloadData.taxNumber,
      logo_url: payloadData.logoUrl,
      primary_color: payloadData.primaryColor,
      secondary_color: payloadData.secondaryColor,
      status: payloadData.status,
      subdomain: payloadData.subdomain,
      website: payloadData.websiteUrl,
      external_customer_id: payloadData.id,
      support_email: payloadData.supportEmail ?? payloadData.email,
    };
  }

  private mapToPublicUser(user: ICustomer): ICustomerPublicDTO {
    return user;
  }
}
