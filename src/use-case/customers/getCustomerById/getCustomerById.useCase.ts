import { env } from '../../../env';
import { ICustomerPublicDTO } from '../../../model/DTO/ICustomer.dto';
import { ICustomerRepository } from '../../../repositories/assign/customers.assign';
import { IFilesRepository } from '../../../repositories/assign/files.assign';
import { ITermsRepository } from '../../../repositories/assign/terms.assign';
import S3Service from '../../../services/aws/s3/S3Service';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

export class GetCustomerByIdUseCase {
  constructor(
    private customerRepository: ICustomerRepository,
    private s3Service: S3Service,
    private termsRepository: ITermsRepository,
    private filesRepository: IFilesRepository
  ) {}

  async execute(
    origin: string
  ): Promise<ICustomerPublicDTO & { termsSignedUrl?: string; termsId?: string }> {
    const buckeName = env.BUCKET_FILES || 'propofando-lxp-files';

    const subdomain = origin.replace(/^https?:\/\//, '');

    let customer;

    if (subdomain && subdomain !== 'www' && subdomain !== 'localhost') {
      customer = await this.customerRepository.findOneBy({ subdomain });
    }

    if (!customer) {
      customer = await this.customerRepository.getDefaultCustomer();
    }

    if (!customer) {
      throw new ResourceNotFoundError('Cliente não encontrado');
    }

    if (customer?.logo_url) {
      const responseGetSignedUrl = await this.s3Service.getSignedUrl(
        buckeName,
        customer.logo_url,
        36000
      );
      customer.logo_url = responseGetSignedUrl;
    }

    let termsSignedUrl: string | undefined;
    let termsId: string | undefined;
    try {
      const activeTerm = await this.termsRepository.findOneBy({
        customer_id: customer.id,
        active: true,
      });

      if (activeTerm && activeTerm.file_id) {
        const file = await this.filesRepository.findOneBy({ id: activeTerm.file_id });
        termsId = activeTerm.id;

        if (file) {
          termsSignedUrl = await this.s3Service.getSignedUrl(buckeName, file.url, 3600);
        }
      }
    } catch (error) {
      console.error('Erro ao buscar termos de uso:', error);
    }

    return {
      ...customer,
      termsSignedUrl,
      termsId,
    };
  }
}
