import { ICustomerPublicDTO } from '../../../model/DTO/ICustomer.dto';
import { ICustomerRepository } from '../../../repositories/assign/customers.assign';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

export class GetCustomerByTokenUseCaseUseCase {
  constructor(private customerRepository: ICustomerRepository) {}

  async execute(customerId: string): Promise<ICustomerPublicDTO> {
    const customer = await this.customerRepository.get(customerId);

    if (!customer) {
      throw new ResourceNotFoundError('Cliente não encontrado');
    }
    return customer;
  }
}
