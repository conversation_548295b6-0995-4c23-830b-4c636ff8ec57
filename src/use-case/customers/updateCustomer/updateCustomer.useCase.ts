import { env } from '../../../env';
import { ICustomerWithId } from '../../../model/DTO/ICustomer.dto';
import { ICustomerRepository } from '../../../repositories/assign/customers.assign';
import S3Service from '../../../services/aws/s3/S3Service';
import { ResourceAlreadyExistsError } from '../../errors/ResourceAlreadyExistsError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface UpdateCustomerPayload {
  customerId: string;
  customerName?: string;
  primaryColor?: string;
  secondaryColor?: string;
  websiteUrl?: string;
  taxNumber?: string;
  status?: boolean;
  subdomain?: string;
  supportEmail?: string | null;
  logoUrl?: string;
}

export class UpdateCustomerUseCase {
  constructor(
    private readonly customerRepository: ICustomerRepository,
    private readonly s3Service: S3Service
  ) {}

  async execute(payload: UpdateCustomerPayload) {
    const customer = await this.customerRepository.findOneBy({
      id: payload.customerId,
      deleted_at: null,
    });

    if (!customer) {
      throw new ResourceNotFoundError('Cliente não encontrado');
    }

    if (payload.taxNumber) {
      await this.ensureTaxNumberIsUnique(payload.taxNumber, customer.id);
    }

    const updatedData = this.buildUpdatedCustomer(payload, customer);

    const updatedCustomer = await this.customerRepository.update(updatedData);

    if (updatedCustomer?.logo_url) {
      const bucketName = env.BUCKET_FILES || 'propofando-lxp-files';
      const responseGetSignedUrl = await this.s3Service.getSignedUrl(
        bucketName,
        updatedCustomer.logo_url,
        36000
      );
      updatedCustomer.logo_url = responseGetSignedUrl;
    }

    return updatedCustomer;
  }

  private async ensureTaxNumberIsUnique(taxNumber: string, currentCustomerId: string) {
    const existingCustomer = await this.customerRepository.findOneBy({
      tax_number: taxNumber,
      deleted_at: null,
    });

    if (existingCustomer && existingCustomer.id !== currentCustomerId) {
      throw new ResourceAlreadyExistsError('Já existe um cliente com esse tax_number');
    }
  }

  private buildUpdatedCustomer(payload: UpdateCustomerPayload, customer: ICustomerWithId) {
    return {
      id: customer.id,
      name: payload.customerName ?? customer.name,
      email: customer.email,
      primary_color: payload.primaryColor ?? customer.primary_color,
      secondary_color: payload.secondaryColor ?? customer.secondary_color,
      website: payload.websiteUrl ?? customer.website,
      tax_number: payload.taxNumber ?? customer.tax_number,
      status: payload.status ?? customer.status,
      subdomain: payload.subdomain ?? customer.subdomain,
      support_email: payload.supportEmail,
      logo_url: payload.logoUrl ?? customer.logo_url,
    };
  }
}
