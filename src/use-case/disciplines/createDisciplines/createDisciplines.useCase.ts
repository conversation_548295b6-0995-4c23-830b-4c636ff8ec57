import { IDisciplinesRepository } from '../../../repositories/assign/disciplines.assign';
import { generateCode } from '../../../services/generateCode.service';
import { ResourceAlreadyExistsError } from '../../errors/ResourceAlreadyExistsError';

interface ICreateDisciplineUseCase {
  name: string;
  customerId: string;
}

export class CreateDisciplineUseCase {
  constructor(private readonly disciplinesRepository: IDisciplinesRepository) {}

  async execute(request: ICreateDisciplineUseCase): Promise<object> {
    const { name, customerId } = request;

    await this.ensureDisciplineDoesNotExist(request);

    const code = await this.generateUniqueCode(request);

    const discipline = await this.disciplinesRepository.insert({
      name,
      code,
      customer_id: customerId,
    });

    return { message: 'Disciplina criada com sucesso!', discipline };
  }

  private async ensureDisciplineDoesNotExist(request: ICreateDisciplineUseCase): Promise<void> {
    const { name, customerId } = request;
    const existingDiscipline = await this.disciplinesRepository.findByNameAndCustomerId(
      name,
      customerId
    );

    if (existingDiscipline) {
      throw new ResourceAlreadyExistsError(`Já existe uma disciplina com o nome ${name}.`);
    }
  }

  private async generateUniqueCode(request: ICreateDisciplineUseCase): Promise<string> {
    const { name, customerId } = request;

    const existingCodes = await this.disciplinesRepository.findAllCodesByCustomerId(customerId);

    return generateCode(name, 5, existingCodes);
  }
}
