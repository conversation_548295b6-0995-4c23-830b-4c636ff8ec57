import { BaseModel } from 'bq-knex-base-repository';

import { IDisciplinesRepository } from '../../../repositories/assign/disciplines.assign';
import { IDisciplinesAccessRepository } from '../../../repositories/assign/disciplinesAccess.assign';
import { IDisciplinesCategoriesAccessRepository } from '../../../repositories/assign/disciplinesCategoriesAccess.assign';
import { IDisciplinesSubcategoriesAccessRepository } from '../../../repositories/assign/disciplinesSubcategoriesAccess.assign';
import { IQuestionsRepository } from '../../../repositories/assign/questions.assign';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface IDeleteDisciplineRequest {
  id: string;
  customerId: string;
}

interface IDeleteDisciplineResponse extends BaseModel {
  name: string;
  code: string;
  customer_id: string;
}

export class DeleteDisciplinesUseCase {
  constructor(
    private readonly disciplinesRepository: IDisciplinesRepository,
    private readonly disciplinesAccessRepository: IDisciplinesAccessRepository,
    private readonly disciplinesCategoriesAccessRepository: IDisciplinesCategoriesAccessRepository,
    private readonly disciplinesSubcategoriesRepository: IDisciplinesSubcategoriesAccessRepository,
    private readonly questionsRepository: IQuestionsRepository
  ) {}

  public async execute(request: IDeleteDisciplineRequest): Promise<IDeleteDisciplineResponse> {
    const { id, customerId } = request;

    await this.validateDisciplineExists(request);

    await this.ensureNoLinkedQuestions(request);

    await this.deleteDisciplineAccessData(request);

    const discipline = await this.disciplinesRepository.deleteById(id, customerId);

    return discipline;
  }

  private async validateDisciplineExists(request: IDeleteDisciplineRequest): Promise<object> {
    const { id, customerId } = request;

    const discipline = await this.disciplinesRepository.findById(id);

    if (!discipline || discipline.customer_id !== customerId) {
      throw new ResourceNotFoundError('Disciplina não encontrada.');
    }

    return discipline;
  }

  private async ensureNoLinkedQuestions(request: IDeleteDisciplineRequest): Promise<void> {
    const { id, customerId } = request;

    const questions = await this.questionsRepository.findByDisciplineId(id, customerId);

    if (questions?.length) {
      throw new BadRequestError(
        `Esta disciplina não pode ser excluída porque possui questões vinculadas. Remova ou mova as questões antes de tentar novamente.`
      );
    }
  }

  private async deleteDisciplineAccessData(request: IDeleteDisciplineRequest): Promise<void> {
    const { id, customerId } = request;

    await Promise.all([
      this.disciplinesAccessRepository.deleteByDisciplineId(id),
      this.disciplinesCategoriesAccessRepository.deleteAllByDisciplineId(id, customerId),
      this.disciplinesSubcategoriesRepository.deleteAllByDisciplineId(id, customerId),
    ]);
  }
}
