import { IDiscipline } from '../../../model/IDiscipline';
import { IDisciplinesRepository } from '../../../repositories/assign/disciplines.assign';

interface ListDisciplinesUseCaseRequest {
  customerId: string;
  search?: string;
  orderByColumn?: string;
  orderDirection?: string;
}

interface ListDisciplinesUseCaseResponse {
  disciplines: IDiscipline[];
}

export class ListDisciplinesUseCase {
  constructor(private readonly disciplinesRepository: IDisciplinesRepository) {}

  async execute(request: ListDisciplinesUseCaseRequest): Promise<ListDisciplinesUseCaseResponse> {
    const { customerId, search, orderByColumn = 'name', orderDirection = 'asc' } = request;

    const disciplines = await this.disciplinesRepository.listByCustomerId({
      customerId,
      search,
      orderByColumn,
      orderDirection,
    });

    disciplines.sort((a, b) => {
      const nameA = a.name.toLowerCase();
      const nameB = b.name.toLowerCase();

      return orderDirection === 'asc'
        ? nameA.localeCompare(nameB, 'pt-BR', { sensitivity: 'base' })
        : nameB.localeCompare(nameA, 'pt-BR', { sensitivity: 'base' });
    });

    return { disciplines };
  }
}
