import {
  IDisciplineWithQuestionCount,
  ListDisciplinesWithQuestionCountDTO,
} from '../../../model/DTO/IDiscipline.dto';
import { IDisciplinesRepository } from '../../../repositories/assign/disciplines.assign';
import { IQuestionsGroupsRepository } from '../../../repositories/assign/questionsGroups.assign';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface IListDisciplinesWithQuestionCountResponse {
  disciplines: IDisciplineWithQuestionCount[];
}

export class ListDisciplinesWithQuestionCountUseCase {
  constructor(
    private readonly disciplinesRepository: IDisciplinesRepository,
    private readonly questionGroupRepository: IQuestionsGroupsRepository
  ) {}

  async execute(
    data: ListDisciplinesWithQuestionCountDTO
  ): Promise<IListDisciplinesWithQuestionCountResponse> {
    const { customerId, questionGroupId } = data;

    await this.ensureQuestionGroupExists(questionGroupId, customerId);

    const disciplines = await this.disciplinesRepository.listDisciplinesWithQuestionCount(data);

    return { disciplines };
  }

  private async ensureQuestionGroupExists(
    questionGroupId: string,
    customerId: string
  ): Promise<void> {
    const questionGroup = await this.questionGroupRepository.findOneBy({
      id: questionGroupId,
      customer_id: customerId,
      deleted_at: null,
    });
    if (!questionGroup) {
      throw new ResourceNotFoundError('Grupo de questões não encontrado.');
    }
  }
}
