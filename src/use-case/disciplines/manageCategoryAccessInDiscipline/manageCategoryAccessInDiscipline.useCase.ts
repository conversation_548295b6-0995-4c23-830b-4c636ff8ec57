import { ICategoriesRepository } from '../../../repositories/assign/categories.assign';
import { IDisciplinesRepository } from '../../../repositories/assign/disciplines.assign';
import { IDisciplinesCategoriesAccessRepository } from '../../../repositories/assign/disciplinesCategoriesAccess.assign';
import { IDisciplinesSubcategoriesAccessRepository } from '../../../repositories/assign/disciplinesSubcategoriesAccess.assign';
import { ISubCategoriesRepository } from '../../../repositories/assign/subcategories.assign';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface IAddCategoryInDisciplineRequest {
  categoryId: string;
  isChecked: boolean;
  customerId: string;
  disciplineId: string;
  subcategoriesIds: string[] | null;
}

export class ManageCategoryAccessInDisciplineUseCase {
  constructor(
    private readonly disciplinesRepository: IDisciplinesRepository,
    private readonly categoriesRepository: ICategoriesRepository,
    private readonly disciplinesCategoriesAccessRepository: IDisciplinesCategoriesAccessRepository,
    private readonly disciplinesSubcategoriesAccessRepository: IDisciplinesSubcategoriesAccessRepository,
    private readonly subCategoriesRepository: ISubCategoriesRepository
  ) {}

  async execute(request: IAddCategoryInDisciplineRequest): Promise<string> {
    await this.validateRequest(request);

    await this.handleCategoryAccess(request);
    const subcategoriesAccess = await this.manageSubcategoriesAccess(request);

    return subcategoriesAccess;
  }

  private async validateRequest(request: IAddCategoryInDisciplineRequest): Promise<void> {
    const { categoryId, disciplineId } = request;

    const [discipline, category] = await Promise.all([
      this.disciplinesRepository.findById(disciplineId),
      this.categoriesRepository.get(categoryId),
    ]);

    if (!discipline) throw new ResourceNotFoundError('Disciplina não encontrada.');
    if (!category) throw new ResourceNotFoundError('Categoria não encontrada.');

    await this.validateSubcategoriesExist(request);
  }

  private async validateSubcategoriesExist(
    request: IAddCategoryInDisciplineRequest
  ): Promise<void> {
    const { customerId, categoryId, subcategoriesIds } = request;

    if (!subcategoriesIds || subcategoriesIds.length === 0) {
      return;
    }

    const existingSubcategories = await this.subCategoriesRepository.findAllByCustomerId(
      customerId,
      categoryId
    );

    const missingSubcategories = subcategoriesIds.filter(
      (id) => !existingSubcategories.some((sub) => sub.id === id)
    );
    if (missingSubcategories.length > 0) {
      throw new ResourceNotFoundError(
        `Subcategorias não encontradas: ${missingSubcategories.join(', ')}`
      );
    }
  }

  private async handleCategoryAccess(request: IAddCategoryInDisciplineRequest): Promise<void> {
    const { categoryId, isChecked, customerId, disciplineId } = request;

    const existingCategoryAccess =
      await this.disciplinesCategoriesAccessRepository.findByDisciplineIdAndCategoryId({
        disciplineId,
        categoryId,
        customerId,
      });

    if (isChecked && !existingCategoryAccess) {
      await this.disciplinesCategoriesAccessRepository.insert({
        discipline_id: disciplineId,
        category_id: categoryId,
        customer_id: customerId,
      });
    }

    if (!isChecked && existingCategoryAccess) {
      await this.disciplinesCategoriesAccessRepository.deleteByDisciplineIdAndCategoryId({
        disciplineId,
        categoryId,
        customerId,
      });
    }
  }

  private async manageSubcategoriesAccess(
    request: IAddCategoryInDisciplineRequest
  ): Promise<string> {
    const { categoryId, isChecked, customerId, disciplineId, subcategoriesIds } = request;

    if (!subcategoriesIds || subcategoriesIds.length === 0) {
      return isChecked
        ? 'Categoria adicionada com sucesso. Nenhuma subcategoria foi vinculada.'
        : 'Categoria removida com sucesso. Nenhuma subcategoria foi desvinculada.';
    }

    const existingAccesses =
      await this.disciplinesSubcategoriesAccessRepository.findAllByDisciplineId(
        disciplineId,
        customerId
      );

    const accessMap = new Map(existingAccesses.map((a) => [a.subcategory_id, true]));

    let subcategoriesAdded = 0;
    let subcategoriesRemoved = 0;

    await Promise.all(
      subcategoriesIds.map(async (subcategoryId) => {
        const hasAccess = accessMap.has(subcategoryId);

        if (isChecked && !hasAccess) {
          await this.disciplinesSubcategoriesAccessRepository.insert({
            discipline_id: disciplineId,
            subcategory_id: subcategoryId,
            customer_id: customerId,
            category_id: categoryId,
          });
          subcategoriesAdded++;
        }

        if (!isChecked && hasAccess) {
          await this.disciplinesSubcategoriesAccessRepository.deleteByDisciplineIdAndSubcategoryId({
            disciplineId,
            subcategoryId,
            customerId,
          });
          subcategoriesRemoved++;
        }
      })
    );

    return this.generateResponseMessage(isChecked, subcategoriesAdded, subcategoriesRemoved);
  }

  private generateResponseMessage(
    isChecked: boolean,
    subcategoriesAdded: number,
    subcategoriesRemoved: number
  ): string {
    if (subcategoriesAdded > 0 && subcategoriesRemoved > 0) {
      return `Categoria adicionada com sucesso. Foram adicionadas ${subcategoriesAdded} subcategorias e removidas ${subcategoriesRemoved}.`;
    }

    if (subcategoriesAdded > 0) {
      return `Categoria adicionada com sucesso. Foram adicionadas ${subcategoriesAdded} subcategorias.`;
    }

    if (subcategoriesRemoved > 0) {
      return `Categoria removida com sucesso. Foram removidas ${subcategoriesRemoved} subcategorias.`;
    }

    return isChecked
      ? 'Categoria adicionada com sucesso. Nenhuma subcategoria foi vinculada.'
      : 'Categoria removida com sucesso. Nenhuma subcategoria foi desvinculada.';
  }
}
