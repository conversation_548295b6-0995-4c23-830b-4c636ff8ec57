import { IDiscipline } from '../../../model/IDiscipline';
import { IDisciplinesRepository } from '../../../repositories/assign/disciplines.assign';
import { filterTextRegex } from '../../../services/filterTextRegex.service';
import { generateCode } from '../../../services/generateCode.service';
import { ResourceAlreadyExistsError } from '../../errors/ResourceAlreadyExistsError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface IUpdateDisciplineRequest {
  id: string;
  name: string;
  customerId: string;
}

export class UpdateDisciplineUseCase {
  constructor(private disciplinesRepository: IDisciplinesRepository) {}

  async execute(request: IUpdateDisciplineRequest) {
    const { id, name, customerId } = request;

    const discipline = await this.verifyDisciplineExists(id, customerId);

    await this.ensureUniqueName(request);

    const updatedCode = await this.getUpdatedCode(discipline, name);

    const updatedDiscipline = await this.disciplinesRepository.update({
      id,
      name: name.trim(),
      code: updatedCode,
      customer_id: customerId,
    });

    return updatedDiscipline;
  }

  private async verifyDisciplineExists(id: string, customerId: string): Promise<IDiscipline> {
    const discipline = await this.disciplinesRepository.findById(id);

    if (!discipline || discipline.customer_id !== customerId) {
      throw new ResourceNotFoundError('Disciplina não encontrada.');
    }

    return discipline;
  }

  private async ensureUniqueName(request: IUpdateDisciplineRequest) {
    const { id, name, customerId } = request;

    const existingDiscipline = await this.disciplinesRepository.findByNameAndCustomerId(
      name,
      customerId
    );

    if (existingDiscipline && existingDiscipline.id !== id) {
      throw new ResourceAlreadyExistsError(
        'Já existe uma disciplina com o nome informado. Por favor, escolha um nome diferente.'
      );
    }
  }

  private async getUpdatedCode(discipline: IDiscipline, newName: string): Promise<string> {
    const newNameFormated = filterTextRegex(newName);
    const nameFormated = filterTextRegex(discipline.name);

    if (nameFormated === newNameFormated) {
      return discipline.code;
    }

    const existingCodes = await this.disciplinesRepository.findAllCodesByCustomerId(
      discipline.customer_id
    );
    return generateCode(newName, 5, existingCodes);
  }
}
