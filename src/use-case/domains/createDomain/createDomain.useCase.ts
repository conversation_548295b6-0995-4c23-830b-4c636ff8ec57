import AWS from 'aws-sdk';

import { IDomain } from '../../../model/IDomain';
import { IDomainsRepository } from '../../../repositories/assign/domains.assign';

interface ICreateDomainRequest {
  domain: string;
  customerId?: string;
}

interface ICreateDomainResponse {
  id: string;
  domain: string;
  hosted_zone_id: string;
  certificate_arn: string;
  status: IDomain['status'];
  message: string;
}

export class CreateDomainCustomerUseCase {
  constructor(
    private domainsRepository: IDomainsRepository,
    private acm: AWS.ACM,
    private route53: AWS.Route53
  ) {}

  async execute(data: ICreateDomainRequest): Promise<ICreateDomainResponse> {
    const { domain, customerId } = data;
    const updatedDomain = domain.replace(/\.$/, '');

    // Verificar se o domínio já existe
    const existingDomain = await this.domainsRepository.findOneBy({ domain: updatedDomain });

    if (existingDomain) {
      return {
        id: existingDomain.id,
        domain: existingDomain.domain,
        hosted_zone_id: existingDomain.hosted_zone_id,
        certificate_arn: existingDomain.certificate_arn,
        status: existingDomain.status,
        message: 'Domínio já existe no sistema',
      };
    }

    // Verificar se já existe hosted zone
    const existingHostedZone = await this.getExistingHostedZone(updatedDomain);
    let hostedZoneId: string;
    let certificateArn: string;

    if (existingHostedZone) {
      hostedZoneId = existingHostedZone.Id;
      // Buscar certificado existente ou criar novo
      certificateArn = await this.getOrCreateCertificate(updatedDomain);
    } else {
      // Criar nova hosted zone
      const hostedZone = await this.createHostedZone(updatedDomain);
      hostedZoneId = hostedZone.HostedZone.Id;

      // Solicitar certificado
      certificateArn = await this.requestCertificate(updatedDomain);
    }

    // Salvar no banco de dados
    const domainRecord = await this.domainsRepository.insertDomainWithTransaction({
      domain: updatedDomain,
      hosted_zone_id: hostedZoneId,
      certificate_arn: certificateArn,
      customer_id: customerId,
      status: 'pending',
    });

    return {
      id: domainRecord.id,
      domain: domainRecord.domain,
      hosted_zone_id: domainRecord.hosted_zone_id,
      certificate_arn: domainRecord.certificate_arn,
      status: domainRecord.status,
      message: 'Domínio criado com sucesso e aguardando validação',
    };
  }

  private async getExistingHostedZone(domain: string) {
    const params = {
      DNSName: domain,
      MaxItems: '1',
    };

    const result = await this.route53.listHostedZonesByName(params).promise();

    if (result.HostedZones?.length > 0 && result.HostedZones[0]?.Name === `${domain}.`) {
      return result.HostedZones[0];
    }

    return null;
  }

  private async createHostedZone(domain: string) {
    const params = {
      Name: domain,
      CallerReference: Date.now().toString(),
    };

    return await this.route53.createHostedZone(params).promise();
  }

  private async getOrCreateCertificate(domain: string): Promise<string> {
    // Listar certificados existentes
    const certificates = await this.acm.listCertificates().promise();

    // Buscar certificado para o domínio
    const existingCert = certificates.CertificateSummaryList?.find(
      (cert) => cert.DomainName === domain && cert.Status === 'ISSUED'
    );

    if (existingCert?.CertificateArn) {
      return existingCert.CertificateArn;
    }

    // Se não existir, criar novo certificado
    return this.requestCertificate(domain);
  }

  private async requestCertificate(domain: string): Promise<string> {
    const params = {
      DomainName: domain,
      ValidationMethod: 'DNS',
    };

    const result = await this.acm.requestCertificate(params).promise();
    return result.CertificateArn!;
  }
}
