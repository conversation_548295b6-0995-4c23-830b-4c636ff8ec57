import AWS from 'aws-sdk';

import { IDomain } from '../../../model/IDomain';
import { IDomainsRepository } from '../../../repositories/assign/domains.assign';

interface IProcessResult {
  processed: number;
  validated: number;
  cloudfrontCreated: number;
  errors: number;
  details: {
    validated: string[];
    cloudfrontCreated: string[];
    errors: Array<{ domain: string; error: string }>;
  };
}

export class ProcessPendingDomainsUseCase {
  constructor(
    private domainsRepository: IDomainsRepository,
    private acm: AWS.ACM,
    private route53: AWS.Route53,
    private cloudfront: AWS.CloudFront,
    private s3: AWS.S3
  ) {}

  async execute(): Promise<IProcessResult> {
    const result: IProcessResult = {
      processed: 0,
      validated: 0,
      cloudfrontCreated: 0,
      errors: 0,
      details: {
        validated: [],
        cloudfrontCreated: [],
        errors: [],
      },
    };

    // Processar domínios pendentes de validação
    const pendingDomains = await this.domainsRepository.findByPendingValidation();
    console.log(`Encontrados ${pendingDomains.length} domínios pendentes de validação`);

    for (const domain of pendingDomains) {
      try {
        result.processed++;

        // Atualizar status para validando
        await this.domainsRepository.updateDomainWithTransaction({
          id: domain.id,
          status: 'validating',
        });

        // Verificar se o certificado foi validado
        const isValidated = await this.validateCertificate(domain);

        if (isValidated) {
          await this.domainsRepository.updateDomainWithTransaction({
            id: domain.id,
            status: 'validated',
          });
          result.validated++;
          result.details.validated.push(domain.domain);
          console.log(`Certificado validado para: ${domain.domain}`);
        } else {
          // Voltar para pending se ainda não foi validado
          await this.domainsRepository.updateDomainWithTransaction({
            id: domain.id,
            status: 'pending',
          });
        }
      } catch (error) {
        console.error(`Erro ao processar domínio ${domain.domain}:`, error);
        await this.domainsRepository.updateDomainWithTransaction({
          id: domain.id,
          status: 'error',
          error_message: error instanceof Error ? error.message : 'Erro desconhecido',
        });
        result.errors++;
        result.details.errors.push({
          domain: domain.domain,
          error: error instanceof Error ? error.message : 'Erro desconhecido',
        });
      }
    }

    // Processar domínios validados que ainda não têm CloudFront
    const validatedDomains = await this.domainsRepository.findByValidatedNotCloudFront();
    console.log(`Encontrados ${validatedDomains.length} domínios validados sem CloudFront`);

    for (const domain of validatedDomains) {
      try {
        result.processed++;

        // Criar CloudFront distribution
        const cloudfrontResult = await this.createCloudFrontDistribution(domain);

        await this.domainsRepository.updateDomainWithTransaction({
          id: domain.id,
          status: 'cloudfront_created',
          cloudfront_distribution_id: cloudfrontResult.distributionId,
          cloudfront_distribution_url: cloudfrontResult.distributionUrl,
        });

        // Adicionar registro DNS para o CloudFront
        await this.addCloudFrontDnsRecord(
          domain.hosted_zone_id,
          domain.domain,
          cloudfrontResult.distributionUrl
        );

        result.cloudfrontCreated++;
        result.details.cloudfrontCreated.push(domain.domain);
        console.log(`CloudFront criado para: ${domain.domain}`);
      } catch (error) {
        console.error(`Erro ao criar CloudFront para ${domain.domain}:`, error);
        await this.domainsRepository.updateDomainWithTransaction({
          id: domain.id,
          status: 'error',
          error_message: error instanceof Error ? error.message : 'Erro desconhecido',
        });
        result.errors++;
        result.details.errors.push({
          domain: domain.domain,
          error: error instanceof Error ? error.message : 'Erro desconhecido',
        });
      }
    }

    return result;
  }

  private async validateCertificate(domain: IDomain): Promise<boolean> {
    try {
      const certDetails = await this.acm
        .describeCertificate({
          CertificateArn: domain.certificate_arn,
        })
        .promise();

      const certificate = certDetails.Certificate;
      if (!certificate) {
        return false;
      }

      // Se o certificado já está emitido, considerar validado
      if (certificate.Status === 'ISSUED') {
        return true;
      }

      // Se está pendente de validação, verificar se precisa adicionar registros DNS
      if (certificate.Status === 'PENDING_VALIDATION') {
        const validationOptions = certificate.DomainValidationOptions || [];

        for (const option of validationOptions) {
          if (option.ResourceRecord && option.ValidationStatus === 'PENDING_VALIDATION') {
            // Adicionar registro DNS para validação
            await this.addValidationRecord(domain.hosted_zone_id, option.ResourceRecord);
          }
        }
      }

      return certificate.Status === 'ISSUED';
    } catch (error) {
      console.error(`Erro ao validar certificado para ${domain.domain}:`, error);
      return false;
    }
  }

  private async addValidationRecord(
    hostedZoneId: string,
    resourceRecord: AWS.ACM.ResourceRecord
  ): Promise<void> {
    const params = {
      HostedZoneId: hostedZoneId,
      ChangeBatch: {
        Changes: [
          {
            Action: 'UPSERT',
            ResourceRecordSet: {
              Name: resourceRecord.Name,
              Type: resourceRecord.Type,
              TTL: 300,
              ResourceRecords: [{ Value: resourceRecord.Value }],
            },
          },
        ],
      },
    };

    await this.route53.changeResourceRecordSets(params).promise();
  }

  private async addCloudFrontDnsRecord(
    hostedZoneId: string,
    domainName: string,
    distributionUrl: string
  ): Promise<void> {
    // Obter o hosted zone ID do CloudFront
    const cloudfrontHostedZoneId = await this.getCloudFrontHostedZoneId();

    // Garantir que o nome do domínio tenha o ponto final para Route53
    const normalizedDomainName = domainName.endsWith('.') ? domainName : `${domainName}.`;

    console.log(`Criando/atualizando registro DNS para ${domainName}...`);
    console.log(`- Hosted Zone ID: ${hostedZoneId}`);
    console.log(`- CloudFront Hosted Zone ID: ${cloudfrontHostedZoneId}`);
    console.log(`- Distribution URL: ${distributionUrl}`);

    const params = {
      HostedZoneId: hostedZoneId,
      ChangeBatch: {
        Changes: [
          {
            Action: 'UPSERT',
            ResourceRecordSet: {
              Name: normalizedDomainName,
              Type: 'A',
              AliasTarget: {
                HostedZoneId: cloudfrontHostedZoneId,
                DNSName: distributionUrl,
                EvaluateTargetHealth: false,
              },
            },
          },
        ],
      },
    };

    try {
      await this.route53.changeResourceRecordSets(params).promise();
      console.log(`✅ Registro DNS criado/atualizado com sucesso para ${domainName}`);
    } catch (error) {
      console.error(`❌ Erro ao criar registro DNS para ${domainName}:`, error);
      throw error;
    }
  }

  private async getCloudFrontHostedZoneId(): Promise<string> {
    // CloudFront é um serviço global e sempre usa o mesmo hosted zone ID
    // As distribuições são criadas na região us-east-1
    const cloudfrontHostedZoneId = 'Z2FDTNDATAQYW2';

    console.log(`🔗 CloudFront hosted zone ID: ${cloudfrontHostedZoneId}`);
    return cloudfrontHostedZoneId;
  }

  private async createCloudFrontDistribution(
    domain: IDomain
  ): Promise<{ distributionId: string; distributionUrl: string }> {
    const s3BucketName = process.env.S3_BUCKET;
    if (!s3BucketName) {
      throw new Error('S3_BUCKET não está configurado');
    }

    console.log(`🚀 Criando CloudFront distribution para ${domain.domain}...`);
    console.log(`- S3 Bucket: ${s3BucketName}`);
    console.log(`- Certificate ARN: ${domain.certificate_arn}`);

    try {
      // Criar OAI
      console.log(`📝 Criando Origin Access Identity...`);
      const oai = await this.cloudfront
        .createCloudFrontOriginAccessIdentity({
          CloudFrontOriginAccessIdentityConfig: {
            CallerReference: `${Date.now()}`,
            Comment: `OAI for ${domain.domain}`,
          },
        })
        .promise();

      const oaiId = oai.CloudFrontOriginAccessIdentity?.Id;
      const canonicalUserId = oai.CloudFrontOriginAccessIdentity?.S3CanonicalUserId;

      if (!oaiId || !canonicalUserId) {
        throw new Error('Falha ao criar OAI');
      }

      console.log(`✅ OAI criado: ${oaiId}`);

      // Atualizar política do bucket S3
      console.log(`🔐 Atualizando política do bucket S3...`);
      await this.updateS3BucketPolicy(s3BucketName, oaiId, canonicalUserId);
      console.log(`✅ Política do bucket S3 atualizada`);

      // Criar distribuição CloudFront
      console.log(`🌐 Criando distribuição CloudFront...`);
      const params = {
        DistributionConfig: {
          CallerReference: `${Date.now()}`,
          Aliases: {
            Quantity: 1,
            Items: [domain.domain],
          },
          DefaultCacheBehavior: {
            TargetOriginId: 'S3-ORIGIN-ID',
            ViewerProtocolPolicy: 'redirect-to-https',
            ForwardedValues: {
              QueryString: false,
              Cookies: { Forward: 'none' },
            },
            MinTTL: 0,
          },
          Origins: {
            Quantity: 1,
            Items: [
              {
                Id: 'S3-ORIGIN-ID',
                DomainName: `${s3BucketName}.s3.amazonaws.com`,
                S3OriginConfig: {
                  OriginAccessIdentity: `origin-access-identity/cloudfront/${oaiId}`,
                },
              },
            ],
          },
          ViewerCertificate: {
            ACMCertificateArn: domain.certificate_arn,
            SSLSupportMethod: 'sni-only',
            MinimumProtocolVersion: 'TLSv1.2_2021',
          },
          Enabled: true,
          Comment: `CloudFront distribution for ${domain.domain}`,
        },
      };

      const result = await this.cloudfront.createDistribution(params).promise();
      const distribution = result.Distribution;

      if (!distribution?.Id || !distribution?.DomainName) {
        throw new Error('Falha ao criar distribuição CloudFront');
      }

      console.log(`✅ CloudFront distribution criada:`);
      console.log(`- Distribution ID: ${distribution.Id}`);
      console.log(`- Distribution URL: ${distribution.DomainName}`);

      return {
        distributionId: distribution.Id,
        distributionUrl: distribution.DomainName,
      };
    } catch (error) {
      console.error(`❌ Erro ao criar CloudFront distribution para ${domain.domain}:`, error);
      throw error;
    }
  }

  private async updateS3BucketPolicy(
    bucketName: string,
    oaiId: string,
    canonicalUserId: string
  ): Promise<void> {
    let existingPolicy = { Version: '2012-10-17', Statement: [] };

    try {
      const existingPolicyResult = await this.s3.getBucketPolicy({ Bucket: bucketName }).promise();
      existingPolicy = JSON.parse(
        existingPolicyResult.Policy || '{"Version": "2012-10-17", "Statement": []}'
      );
    } catch (error) {
      if (error instanceof Error && 'code' in error && error.code === 'NoSuchBucketPolicy') {
        // Policy não existe, usar policy vazia
      } else {
        throw error;
      }
    }

    const newStatements = [
      {
        Sid: `AllowCloudFrontListBucket-${oaiId}`,
        Effect: 'Allow',
        Principal: { CanonicalUser: canonicalUserId },
        Action: 's3:ListBucket',
        Resource: `arn:aws:s3:::${bucketName}`,
      },
      {
        Sid: `AllowCloudFrontGetObject-${oaiId}`,
        Effect: 'Allow',
        Principal: { CanonicalUser: canonicalUserId },
        Action: 's3:GetObject',
        Resource: `arn:aws:s3:::${bucketName}/*`,
      },
    ];

    const existingStatements = existingPolicy.Statement || [];
    const sidExists = (sid: string) =>
      existingStatements.some((stmt: { Sid: string }) => stmt.Sid === sid);

    const statementsToAdd = newStatements.filter((stmt) => !sidExists(stmt.Sid));

    if (statementsToAdd.length > 0) {
      const updatedPolicy = {
        ...existingPolicy,
        Statement: [...existingStatements, ...statementsToAdd],
      };

      await this.s3
        .putBucketPolicy({
          Bucket: bucketName,
          Policy: JSON.stringify(updatedPolicy),
        })
        .promise();
    }
  }
}
