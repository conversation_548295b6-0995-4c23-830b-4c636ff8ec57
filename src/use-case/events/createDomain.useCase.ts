import AWS from 'aws-sdk';

import { IDomainResponse } from '../../model/DTO/response/domain.response.dto';

export class CreateDomainUseCase {
  constructor(
    private readonly acm: AWS.ACM,
    private readonly sqs: AWS.SQS,
    private readonly route53: AWS.Route53
  ) {}

  public async execute(domain: string): Promise<IDomainResponse> {
    const updatedDomain = domain.replace(/\.$/, '');
    const existingHostedZone = await this.getExistingHostedZone(updatedDomain);

    if (existingHostedZone) {
      return {
        ns: existingHostedZone.DelegationSet?.NameServers || [],
        message: 'Hosted zone already exists',
        hostedZoneId: existingHostedZone.HostedZoneId,
        certificateArn: null,
      };
    }

    console.log(`Processando domínio: ${domain}`);

    // Criar Hosted Zone
    const hostedZone = await this.createHostedZone(updatedDomain);

    // Solicitar Certificado
    const certificateArn = await this.requestCertificate(updatedDomain);

    console.log(`Configuração concluída para o domínio ${domain}:`, {
      hostedZoneId: hostedZone.HostedZone.Id,
      certificateArn,
    });

    // Enviar para a fila
    const queueUrl = process.env.DOMAIN_QUEUE_URL;
    if (queueUrl) {
      await this.sendMessageToQueue(queueUrl, {
        domain: updatedDomain,
        hostedZoneId: hostedZone.HostedZone.Id,
        certificateArn,
      });
    }

    return {
      ns: hostedZone.DelegationSet?.NameServers || [],
      message: 'Hosted zone created',
      hostedZoneId: hostedZone.HostedZone.Id,
      certificateArn: certificateArn || null,
    };
  }

  private async createHostedZone(domain: string) {
    const params = {
      Name: domain,
      CallerReference: Date.now().toString(),
    };

    const result = await this.route53.createHostedZone(params).promise();
    return result;
  }

  private async getExistingHostedZone(domain: string) {
    const params = {
      DNSName: domain,
      MaxItems: '1',
    };

    const result = await this.route53.listHostedZonesByName(params).promise();

    if (result.HostedZones?.length > 0 && result.HostedZones[0]?.Name === `${domain}.`) {
      const hostedZoneId = result.HostedZones[0].Id;
      const ns = await this.getNsRecords(hostedZoneId, domain);
      console.log(ns);

      const hostedZone = {
        DelegationSet: {
          NameServers: ns,
        },
        HostedZoneId: hostedZoneId,
      };
      return hostedZone;
    }

    return null;
  }

  private async getNsRecords(hostedZoneId: string, domain: string) {
    const params = {
      HostedZoneId: hostedZoneId,
      StartRecordName: domain,
      StartRecordType: 'NS',
      MaxItems: '1',
    };

    const result = await this.route53.listResourceRecordSets(params).promise();
    const nsRecordSet = result.ResourceRecordSets?.find((recordSet) => recordSet.Type === 'NS');

    if (nsRecordSet) {
      return nsRecordSet.ResourceRecords?.map((record) => record.Value);
    }
    return [];
  }

  private async requestCertificate(domain: string) {
    const params = {
      DomainName: domain,
      ValidationMethod: 'DNS',
    };
    const result = await this.acm.requestCertificate(params).promise();
    console.log('Certificado solicitado:', result);
    return result.CertificateArn;
  }

  private async sendMessageToQueue(queueUrl: string, messageBody: object) {
    const params = {
      QueueUrl: queueUrl,
      MessageBody: JSON.stringify(messageBody),
    };

    const result = await this.sqs.sendMessage(params).promise();
    console.log('Mensagem enviada para a fila:', result);
    return result;
  }
}
