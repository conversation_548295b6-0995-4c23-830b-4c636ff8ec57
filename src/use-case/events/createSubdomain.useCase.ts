import AWS from 'aws-sdk';
import { randomUUID } from 'crypto';

import { generateTransaction } from '../../helpers/transaction.helper';
import { RolesEnum } from '../../model/enums/roles.enum';
import { IAccessLevelsRepository } from '../../repositories/assign/accessLevels.assign';
import { ICustomerRepository } from '../../repositories/assign/customers.assign';
import { IRecoveryRepository } from '../../repositories/assign/recoveries.assign';
import { IUsersRepository } from '../../repositories/assign/users.assign';
import { SESService } from '../../services/aws/ses/SESService';
import { ResourceNotFoundError } from '../../use-case/errors/ResourceNotFound';

const acm = new AWS.ACM({ region: 'us-east-1' });
const cloudfront = new AWS.CloudFront();
const route53 = new AWS.Route53();

interface ICreateSubdomainRequest {
  subdomainName: string;
  customerName: string;
  email: string;
  firstName: string;
  lastName: string;
  cpf: string;
  birthDate: string;
  gender: string;
  phoneNumber: string;
  mainDomain: string;
  hostedZoneId: string;
  cloudFrontDistributionId: string;
}

export class CreateSubdomainUseCase {
  constructor(
    private readonly customerRepository: ICustomerRepository,
    private readonly userRepository: IUsersRepository,
    private readonly accessLevelRepository: IAccessLevelsRepository,
    private readonly recoveryRepository: IRecoveryRepository,
    private readonly sesService: SESService
  ) {}

  async execute(data: ICreateSubdomainRequest) {
    const trx = await generateTransaction();

    try {
      const {
        subdomainName,
        customerName,
        email,
        firstName,
        lastName,
        cpf,
        gender,
        phoneNumber,
        birthDate,
        mainDomain,
        hostedZoneId,
        cloudFrontDistributionId,
      } = data;

      console.log(`Garantindo certificado para: ${mainDomain} e *.${mainDomain}`);
      const certArn = await ensureCertificate(mainDomain, hostedZoneId);

      console.log(`Atualizando CloudFront para o subdomínio: ${subdomainName}`);
      console.log(`ID da distribuição CloudFront: ${cloudFrontDistributionId}`);
      await updateCloudFrontDistribution(subdomainName, cloudFrontDistributionId, certArn);

      console.log(`Criando registro no Route 53 para o subdomínio: ${subdomainName}`);
      if (hostedZoneId) {
        await createRoute53Record(subdomainName, hostedZoneId, cloudFrontDistributionId);
      }

      console.log(`Subdomínio configurado com sucesso: ${subdomainName}`);

      // chamar o banco e criar o novo subdominio + empresa
      console.log('Criando novo customer no banco de dados');
      const customerId = await this.customerRepository.insert({
        subdomain: subdomainName,
        email,
        name: customerName,
        primary_color: '#4F33A6',
        secondary_color: '#4F33A6',
      });

      console.log('customer id', customerId);
      console.log('customer criado com sucesso');

      const uuidv4 = randomUUID();

      const accessLevelId = await this.accessLevelRepository.findOneBy({
        role: RolesEnum.SUPER_ADMIN,
      });

      if (!accessLevelId) {
        throw new ResourceNotFoundError('Nível de acesso não encontrado');
      }

      console.log('Criando usuário root');
      await this.userRepository.insertWithTrx(
        {
          id: uuidv4,
          first_name: firstName,
          last_name: lastName,
          email,
          birth_date: birthDate,
          gender,
          phone_number: phoneNumber,
          cpf,
          access_level_id: accessLevelId.id,
          customer_id: customerId.id,
          role: RolesEnum.SUPER_ADMIN,
          created_at: new Date(),
          updated_at: new Date(),
          deleted_at: null,
        },
        trx
      );

      const recoveryToken = randomUUID();
      await this.recoveryRepository.createRecovery(
        {
          id: randomUUID(),
          user_id: uuidv4,
          token: recoveryToken,
          expires_at: null,
        },
        trx
      );

      const urlCompleteRegister = `https://${subdomainName}/welcome/${recoveryToken}`;

      const body = `
            <p>Olá,!</p>
            <p>Obrigado!</p>
            <p>Clique no link abaixo para cadastrar sua senha de usuário administrador:</p>
            <p><a href="${urlCompleteRegister}">${urlCompleteRegister}</a></p>
            <p>Atenciosamente,<br>Equipe de Suporte.</p>
          `;

      await this.sesService.sendEmail({
        to: email,
        subject: 'Usuário administrador',
        body,
        isHtml: true,
      });

      await trx.commit();

      return { success: true, subdomainName };
    } catch (error) {
      console.error(`Erro ao processar o registro:`, error);
      await trx.rollback();
      throw error;
    }
  }
}

async function ensureCertificate(mainDomain: string, hostedZoneId: string): Promise<string> {
  const domains = [mainDomain, `*.${mainDomain}`];
  const existingCert = await findExistingCertificate(mainDomain);

  if (existingCert) {
    console.log(`Certificado existente encontrado: ${existingCert}`);
    return existingCert;
  }

  console.log(`Solicitando novo certificado para: ${domains.join(', ')}`);
  const request = await acm
    .requestCertificate({
      DomainName: mainDomain,
      SubjectAlternativeNames: domains,
      ValidationMethod: 'DNS',
    })
    .promise();

  const certArn = request.CertificateArn!;
  console.log(`Certificado solicitado: ${certArn}`);

  console.log(`Validando certificado: ${certArn}`);
  await validateCertificate(certArn, hostedZoneId);

  return certArn;
}

async function findExistingCertificate(mainDomain: string): Promise<string | undefined> {
  const certs = await acm.listCertificates({ CertificateStatuses: ['ISSUED'] }).promise();

  for (const certSummary of certs.CertificateSummaryList || []) {
    const certDetails = await acm
      .describeCertificate({ CertificateArn: certSummary.CertificateArn! })
      .promise();
    const { Certificate } = certDetails;

    if (!Certificate) continue;

    const domains = new Set(Certificate.SubjectAlternativeNames);
    if (domains.has(mainDomain) && domains.has(`*.${mainDomain}`)) {
      console.log(`Certificado encontrado com ambos os domínios: ${mainDomain} e *.${mainDomain}`);
      return Certificate.CertificateArn;
    }
  }

  console.log(`Nenhum certificado encontrado para os domínios: ${mainDomain} e *.${mainDomain}`);
  return undefined;
}

async function validateCertificate(certArn: string, hostedZoneId: string): Promise<void> {
  const certDetails = await acm.describeCertificate({ CertificateArn: certArn }).promise();
  const validationOptions = certDetails.Certificate?.DomainValidationOptions || [];

  for (const option of validationOptions) {
    if (option.ResourceRecord) {
      const { Name, Type, Value } = option.ResourceRecord;

      const params: AWS.Route53.ChangeResourceRecordSetsRequest = {
        HostedZoneId: hostedZoneId,
        ChangeBatch: {
          Changes: [
            {
              Action: 'UPSERT',
              ResourceRecordSet: {
                Name,
                Type,
                TTL: 60,
                ResourceRecords: [{ Value }],
              },
            },
          ],
        },
      };

      await route53.changeResourceRecordSets(params).promise();
      console.log(`Registro DNS criado para validar o certificado: ${Name}`);
    }
  }

  await waitForCertificateValidation(certArn);
}

async function waitForCertificateValidation(
  certArn: string,
  retries = 10,
  intervalMs = 30000
): Promise<void> {
  for (let i = 0; i < retries; i++) {
    const certDetails = await acm.describeCertificate({ CertificateArn: certArn }).promise();
    if (certDetails.Certificate?.Status === 'ISSUED') {
      console.log(`Certificado validado com sucesso: ${certArn}`);
      return;
    }

    console.log(`Certificado ainda não validado. Tentativa ${i + 1}/${retries}...`);
    await new Promise((resolve) => setTimeout(resolve, intervalMs));
  }

  throw new ResourceNotFoundError(
    `Certificado não foi validado após ${retries} tentativas: ${certArn}`
  );
}

async function updateCloudFrontDistribution(
  subdomainName: string,
  distributionId: string,
  certArn: string
): Promise<void> {
  try {
    // Primeiro, verificar se a distribuição existe
    await cloudfront.getDistribution({ Id: distributionId }).promise();
  } catch (error: unknown) {
    const awsError = error as { code?: string };
    if (awsError.code === 'NoSuchDistribution') {
      console.error(`Distribuição CloudFront não encontrada: ${distributionId}`);
      console.error('Verifique se o CLOUD_FRONT_DISTRIBUTION_ID está correto no arquivo .env');
      throw new Error(
        `Distribuição CloudFront não encontrada: ${distributionId}. Verifique a variável CLOUD_FRONT_DISTRIBUTION_ID.`
      );
    }
    throw error;
  }

  const distributionConfig = await cloudfront
    .getDistributionConfig({ Id: distributionId })
    .promise();
  const { ETag, DistributionConfig } = distributionConfig;

  if (!DistributionConfig) {
    throw new ResourceNotFoundError('Configuração do CloudFront não encontrada');
  }

  DistributionConfig.Aliases = DistributionConfig.Aliases || { Quantity: 0, Items: [] };
  if (!DistributionConfig.Aliases?.Items?.includes(subdomainName)) {
    DistributionConfig.Aliases.Items?.push(subdomainName);
    DistributionConfig.Aliases.Quantity = DistributionConfig.Aliases.Items?.length || 0;
  }

  DistributionConfig.ViewerCertificate = {
    ACMCertificateArn: certArn,
    SSLSupportMethod: 'sni-only',
    MinimumProtocolVersion: 'TLSv1.2_2021',
  };

  const params: AWS.CloudFront.UpdateDistributionRequest = {
    Id: distributionId,
    IfMatch: ETag,
    DistributionConfig,
  };

  await cloudfront.updateDistribution(params).promise();
  console.log(`CloudFront atualizado para o subdomínio: ${subdomainName}`);
}

async function createRoute53Record(
  subdomainName: string,
  hostedZoneId: string,
  cloudFrontDistributionId: string
): Promise<void> {
  const cloudFrontDomain = await getCloudFrontDomain(cloudFrontDistributionId);

  const params: AWS.Route53.ChangeResourceRecordSetsRequest = {
    HostedZoneId: hostedZoneId,
    ChangeBatch: {
      Changes: [
        {
          Action: 'UPSERT',
          ResourceRecordSet: {
            Name: subdomainName,
            Type: 'A',
            AliasTarget: {
              HostedZoneId: 'Z2FDTNDATAQYW2', // Hosted Zone ID do CloudFront
              DNSName: cloudFrontDomain,
              EvaluateTargetHealth: false,
            },
          },
        },
      ],
    },
  };

  await route53.changeResourceRecordSets(params).promise();
  console.log(`Registro criado/atualizado no Route 53 para o subdomínio: ${subdomainName}`);
}

async function getCloudFrontDomain(distributionId: string): Promise<string> {
  const result = await cloudfront.getDistribution({ Id: distributionId }).promise();
  return result.Distribution?.DomainName || '';
}
