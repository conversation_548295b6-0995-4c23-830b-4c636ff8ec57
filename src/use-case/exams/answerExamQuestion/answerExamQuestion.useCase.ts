import { IQuestionExamAnswered } from '../../../model/IQuestionExamAnswered';
import { IExamsAccessRepository } from '../../../repositories/assign/examsAccess.assign';
import { IExamsQuestionsRepository } from '../../../repositories/assign/examsQuestions.assign';
import { IQuestionsExamsAnsweredRepository } from '../../../repositories/assign/questionsExamsAnswered.assign';
import { AnswerExamQuestionInput } from '../../../schema/examsAccess.schema';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface AnswerExamQuestionRequest extends AnswerExamQuestionInput {
  userId: string;
}

interface AnswerExamQuestionResponse {
  id: string;
  examAccessId: string;
  questionId: string;
  alternativeId: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export class AnswerExamQuestionUseCase {
  constructor(
    private readonly questionsExamsAnsweredRepo: IQuestionsExamsAnsweredRepository,
    private readonly examsAccessRepo: IExamsAccessRepository,
    private readonly examsQuestionsRepo: IExamsQuestionsRepository
  ) {}

  async execute({
    examAccessId,
    questionId,
    userId,
    alternativeId,
  }: AnswerExamQuestionRequest): Promise<AnswerExamQuestionResponse> {
    const examAccess = await this.validateExamAccessOrFail(examAccessId, userId);

    const examQuestion = await this.validateQuestionInExamOrFail(examAccess.exam_id, questionId);

    const now = new Date();

    const existingAnswer = await this.questionsExamsAnsweredRepo.findOneBy({
      exam_access_id: examAccessId,
      question_exam_id: examQuestion.id,
    });

    const answeredRecord = existingAnswer
      ? await this.questionsExamsAnsweredRepo.update({
          id: existingAnswer.id,
          alternative_id: alternativeId,
          answered: true,
          date_answered: now,
        })
      : await this.questionsExamsAnsweredRepo.insert({
          exam_access_id: examAccessId,
          question_exam_id: examQuestion.id,
          alternative_id: alternativeId,
          answered: true,
          date_answered: now,
        });

    return this.buildResponse(answeredRecord, questionId);
  }

  private async validateExamAccessOrFail(examAccessId: string, userId: string) {
    const examAccess = await this.examsAccessRepo.findOneBy({
      id: examAccessId,
      user_id: userId,
    });

    if (!examAccess) {
      throw new ResourceNotFoundError('Prova não encontrada');
    }

    if (examAccess.end_exams) {
      throw new BadRequestError('Não é possível responder uma prova finalizada');
    }

    return examAccess;
  }

  private async validateQuestionInExamOrFail(examId: string, questionId: string) {
    const examQuestion = await this.examsQuestionsRepo.findOneBy({
      exam_id: examId,
      question_id: questionId,
      deleted_at: null,
    });

    if (!examQuestion) {
      throw new ResourceNotFoundError('Questão não encontrada na prova');
    }

    return examQuestion;
  }

  private buildResponse(
    answer: IQuestionExamAnswered,
    questionId: string
  ): AnswerExamQuestionResponse {
    return {
      id: answer.id,
      examAccessId: answer.exam_access_id,
      questionId,
      alternativeId: answer.alternative_id || null,
      createdAt: new Date(answer.created_at),
      updatedAt: new Date(answer.updated_at),
    };
  }
}
