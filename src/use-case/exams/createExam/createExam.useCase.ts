import { IExamCreateDTO } from '../../../model/DTO/exams.dto';
import { ExamsStatusEnum } from '../../../model/enums/examsStatus.enum';
import { IExam } from '../../../model/IExam';
import { IExamsRepository } from '../../../repositories/assign/exams.assign';
import { CreateExamInput } from '../../../schema/exams.schema';

interface ICreateExamRequest extends CreateExamInput {}

interface CreatedExamResponseDTO {
  id: string;
  name: string;
  institution: string | null;
  year: number | null;
  published: boolean;
  status: string;
  examTime: number | null;
}

export class CreateExamUseCase {
  constructor(private readonly examsRepository: IExamsRepository) {}

  async execute(payload: ICreateExamRequest): Promise<CreatedExamResponseDTO> {
    await this.validateExamUniqueness(payload);

    const exam = await this.createExam(payload);

    return this.formatResponse(exam);
  }

  private async validateExamUniqueness(payload: ICreateExamRequest): Promise<void> {
    const { name, customerId } = payload;

    await this.examsRepository.findOneBy({
      name,
      customer_id: customerId,
      deleted_at: null,
    });

    // if (existingExam) {
    //   throw new ResourceAlreadyExistsError(`Já existe uma prova com o nome '${name}'.`);
    // }
  }

  private async createExam(payload: ICreateExamRequest): Promise<IExam> {
    const { name, institution, year, examTime, customerId, published, status } = payload;

    const examData: IExamCreateDTO = {
      name,
      institution_id: institution,
      year,
      published: published ?? false,
      status: status ?? ExamsStatusEnum.DRAFT,
      customer_id: customerId,
      exam_time: examTime ?? null,
    };

    return this.examsRepository.insert(examData);
  }

  private formatResponse(exam: IExam): CreatedExamResponseDTO {
    return {
      id: exam.id,
      name: exam.name,
      institution: exam.institution_id,
      year: exam.year,
      published: exam.published,
      status: exam.status,
      examTime: exam.exam_time,
    };
  }
}
