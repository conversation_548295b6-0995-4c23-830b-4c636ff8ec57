import { IExam } from '../../../model/IExam';
import { IExamsRepository } from '../../../repositories/assign/exams.assign';
import { GenericError } from '../../errors/GenericError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface IDeleteExamRequest {
  id: string;
  customerId: string;
}

export class DeleteExamUseCase {
  constructor(private readonly examsRepository: IExamsRepository) {}

  async execute(payload: IDeleteExamRequest): Promise<IExam> {
    const { id, customerId } = payload;

    await this.findAndValidateExam(id, customerId);

    const deletedExam = await this.examsRepository.softDelete({
      id,
      customerId,
    });

    if (!deletedExam) {
      throw new GenericError('Erro ao excluir a prova');
    }

    return deletedExam;
  }

  private async findAndValidateExam(id: string, customerId: string): Promise<IExam> {
    const exam = await this.examsRepository.findOneBy({
      id,
      customer_id: customerId,
      deleted_at: null,
    });

    if (!exam) {
      throw new ResourceNotFoundError('Prova não encontrada');
    }

    return exam;
  }
}
