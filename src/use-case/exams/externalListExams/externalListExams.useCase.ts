import { ICustomerRepository } from '../../../repositories/assign/customers.assign';
import { IExamsRepository } from '../../../repositories/assign/exams.assign';
import { ExternalListExamsInput } from '../../../schema/exams.schema';
import { verifyExternalCustomer } from '../../../services/verifyExternalCustomer.service';

interface ExternalListExamsRequest extends ExternalListExamsInput {}

interface ExternalListExamsResponse {
  exams: {
    id: string;
    name: string;
  }[];
}

export class ExternalListExamsUseCase {
  constructor(
    private readonly examsRepository: IExamsRepository,
    private readonly customerRepository: ICustomerRepository
  ) {}

  async execute({
    customerId,
    search,
    orderByColumn = 'name',
  }: ExternalListExamsRequest): Promise<ExternalListExamsResponse> {
    const customer = await verifyExternalCustomer(this.customerRepository, customerId);

    const exams = await this.examsRepository.listExams({
      customerId: customer.id,
      search,
      orderByColumn,
    });

    return {
      exams: exams.map((exam) => ({
        id: exam.id,
        name: exam.name,
      })),
    };
  }
}
