import { IExam } from '../../../model/IExam';
import { IExamAccess } from '../../../model/IExamAccess';
import { IAnswerSessionsRepository } from '../../../repositories/assign/answerSessions.assign';
import { IExamsRepository } from '../../../repositories/assign/exams.assign';
import { IExamsAccessRepository } from '../../../repositories/assign/examsAccess.assign';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface FinishExamRequest {
  examAccessId: string;
  userId: string;
  customerId: string;
}

interface FinishExamResponse {
  examAccess: IExamAccess;
  exam: IExam;
}

export class FinishExamUseCase {
  constructor(
    private readonly examsRepo: IExamsRepository,
    private readonly accessRepo: IExamsAccessRepository,
    private readonly sessionsRepo: IAnswerSessionsRepository
  ) {}

  async execute({
    examAccessId,
    userId,
    customerId,
  }: FinishExamRequest): Promise<FinishExamResponse> {
    const { exam, examAccess } = await this.fetchRequiredEntities(examAccessId, userId, customerId);

    if (examAccess.end_exams) {
      return { examAccess, exam };
    }

    const endTime = new Date();

    await this.finalizeLatestOpenSessionByAccessId(examAccessId, endTime);

    const totalSeconds = await this.calculateTotalTime(examAccessId);

    const updatedAccess = await this.accessRepo.update({
      id: examAccessId,
      end_exams: endTime,
      total_seconds: totalSeconds,
      active: false,
    });

    return {
      examAccess: updatedAccess,
      exam,
    };
  }

  private async fetchRequiredEntities(examAccessId: string, userId: string, customerId: string) {
    const examAccess = await this.accessRepo.findOneBy({
      id: examAccessId,
      user_id: userId,
    });

    if (!examAccess) {
      throw new ResourceNotFoundError('Acesso à prova não encontrado');
    }

    const exam = await this.examsRepo.findOneBy({
      id: examAccess.exam_id,
      customer_id: customerId,
      deleted_at: null,
    });

    if (!exam) {
      throw new ResourceNotFoundError('Prova não encontrada');
    }

    return { exam, examAccess };
  }

  private async finalizeLatestOpenSessionByAccessId(
    examAccessId: string,
    endTime: Date
  ): Promise<void> {
    const sessions = await this.sessionsRepo.findByExamAccessId(examAccessId);

    if (!sessions.length) return;

    const last = [...sessions].sort(
      (a, b) => new Date(b.started_at).getTime() - new Date(a.started_at).getTime()
    )[0];

    if (last.paused_at) return;

    const duration = Math.floor((endTime.getTime() - new Date(last.started_at).getTime()) / 1000);

    await this.sessionsRepo.update({
      id: last.id,
      started_at: last.started_at,
      paused_at: endTime,
      duration_seconds: duration,
    });
  }

  private async calculateTotalTime(examAccessId: string): Promise<number> {
    const sessions = await this.sessionsRepo.findByExamAccessId(examAccessId);

    if (!sessions.length) return 0;

    return sessions.reduce((total, session) => {
      return total + (session.duration_seconds || 0);
    }, 0);
  }
}
