import { IQuestionWithAlternativesDTO } from '../../../model/DTO/IQuestion.dto';
import { IAnswerSession } from '../../../model/IAnswerSession';
import { IExamAccess } from '../../../model/IExamAccess';
import { IAnswerSessionsRepository } from '../../../repositories/assign/answerSessions.assign';
import { IExamsRepository } from '../../../repositories/assign/exams.assign';
import { IExamsAccessRepository } from '../../../repositories/assign/examsAccess.assign';
import { IQuestionsRepository } from '../../../repositories/assign/questions.assign';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface GetExamQuestionsRequest {
  examAccessId: string;
  userId: string;
}

interface GetExamWithQuestionsResponse {
  examAccessInfo: IExamAccess & {
    total_seconds: number;
    name: string;
    year: number;
    institution: string;
    type: 'default';
    instituition: string;
  };
  totalQuestions: number;
  questions: IQuestionWithAlternativesDTO[];
}

export class GetExamWithQuestionsUseCase {
  constructor(
    private readonly examsAccessRepository: IExamsAccessRepository,
    private readonly questionsRepository: IQuestionsRepository,
    private readonly answerSessionsRepository: IAnswerSessionsRepository,
    private readonly examsRepository: IExamsRepository
  ) {}

  async execute({
    examAccessId,
    userId,
  }: GetExamQuestionsRequest): Promise<GetExamWithQuestionsResponse> {
    const examAccess = await this.examsAccessRepository.findOneBy({
      id: examAccessId,
      user_id: userId,
    });

    if (!examAccess) {
      throw new ResourceNotFoundError('Prova não encontrada');
    }

    const exam = await this.examsRepository.findByExam({
      id: examAccess.exam_id,
      deleted_at: null,
    });

    if (!exam) {
      throw new ResourceNotFoundError('Prova não encontrada');
    }

    const [questions, sessions] = await Promise.all([
      this.questionsRepository.findQuestionsWithAlternativesByIds(exam.id, userId, examAccessId),
      this.answerSessionsRepository.findByExamAccessId(examAccessId),
    ]);

    const currentSessionDuration = this.getCurrentSessionDuration(sessions);
    const previousSessionsDuration = this.calculatePreviousSessionsTime(sessions);
    const totalSeconds = currentSessionDuration + previousSessionsDuration;

    const responseData: GetExamWithQuestionsResponse = {
      examAccessInfo: {
        ...examAccess,
        total_seconds: totalSeconds,
        name: exam.name,
        year: exam.year,
        type: 'default',
        instituition: exam.acronym,
      },
      totalQuestions: questions.length,
      questions,
    } as GetExamWithQuestionsResponse;

    return responseData;
  }

  private getCurrentSessionDuration(sessions: IAnswerSession[]): number {
    const latest = this.getLatestSession(sessions);
    if (!latest || latest.paused_at) return 0;

    const now = Date.now();
    const started = new Date(latest.started_at).getTime();
    return Math.floor((now - started) / 1000);
  }

  private calculatePreviousSessionsTime(sessions: IAnswerSession[]): number {
    return sessions.reduce((sum, session) => {
      return sum + (session.duration_seconds ?? 0);
    }, 0);
  }

  private getLatestSession(sessions: IAnswerSession[]): IAnswerSession | null {
    return sessions.reduce<IAnswerSession | null>((latest, current) => {
      if (!latest || new Date(current.started_at) > new Date(latest.started_at)) {
        return current;
      }
      return latest;
    }, null);
  }
}
