import { randomUUID } from 'crypto';

import { ExamsStatusEnum } from '../../../model/enums/examsStatus.enum';
import { IExam } from '../../../model/IExam';
import { IExamQuestion } from '../../../model/IExamQuestion';
import { IExamsRepository } from '../../../repositories/assign/exams.assign';
import { IExamsQuestionsRepository } from '../../../repositories/assign/examsQuestions.assign';
import { IQuestionsRepository } from '../../../repositories/assign/questions.assign';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface ILinkQuestionsToExamRequest {
  examId: string;
  questionIds: string[];
  customerId: string;
}

interface ILinkQuestionsToExamResponse {
  exam: IExam;
  linkedQuestions: number;
  examQuestions: IExamQuestion[];
}

export class LinkQuestionsToExamUseCase {
  constructor(
    private readonly examsRepository: IExamsRepository,
    private readonly questionsRepository: IQuestionsRepository,
    private readonly examsQuestionsRepository: IExamsQuestionsRepository
  ) {}

  async execute(payload: ILinkQuestionsToExamRequest): Promise<ILinkQuestionsToExamResponse> {
    const { examId, questionIds, customerId } = payload;

    const exam = await this.findExamOrFail(examId, customerId);

    this.ensureExamIsDraft(exam);

    await this.ensureValidQuestionsOrFail(questionIds, customerId);

    const newQuestionIds = await this.filterNewQuestionIds(examId, questionIds);

    if (newQuestionIds.length === 0) {
      throw new BadRequestError('Todas as questões já estão vinculadas a esta prova');
    }

    const examQuestions = await this.determineOrderAndLinkQuestions(examId, newQuestionIds);

    return {
      exam,
      linkedQuestions: newQuestionIds.length,
      examQuestions,
    };
  }

  private async findExamOrFail(examId: string, customerId: string): Promise<IExam> {
    const exam = await this.examsRepository.findOneBy({
      id: examId,
      customer_id: customerId,
      deleted_at: null,
    });

    if (!exam) {
      throw new ResourceNotFoundError('Prova não encontrada');
    }

    return exam;
  }

  private ensureExamIsDraft(exam: IExam): void {
    if (exam.status !== ExamsStatusEnum.DRAFT) {
      throw new BadRequestError('Apenas provas em rascunho podem receber novas questões');
    }
  }

  private async ensureValidQuestionsOrFail(
    questionIds: string[],
    customerId: string
  ): Promise<void> {
    const hasDuplicates = new Set(questionIds).size !== questionIds.length;

    if (hasDuplicates) {
      throw new BadRequestError('A lista contém IDs de questões duplicados');
    }

    const questions = await this.questionsRepository.findByIds(questionIds, customerId);

    if (questions.length !== questionIds.length) {
      const foundIds = new Set(questions.map((q) => q.id));

      const missing = questionIds.find((id) => !foundIds.has(id));

      throw new ResourceNotFoundError(
        missing ? `Questão não encontrada: ${missing}` : 'Algumas questões não existem'
      );
    }
  }

  private async filterNewQuestionIds(examId: string, questionIds: string[]): Promise<string[]> {
    const existing = await this.examsQuestionsRepository.findByExamId(examId);

    const existingSet = new Set(existing.filter((q) => !q.deleted_at).map((q) => q.question_id));

    return questionIds.filter((id) => !existingSet.has(id));
  }

  private async determineOrderAndLinkQuestions(
    examId: string,
    questionIds: string[]
  ): Promise<IExamQuestion[]> {
    const existing = await this.examsQuestionsRepository.findByExamId(examId);

    const nextOrder =
      existing.length > 0 ? Math.max(...existing.map((link) => link.order_by)) + 1 : 1;

    return this.createExamQuestionLinks(examId, questionIds, nextOrder);
  }

  private async createExamQuestionLinks(
    examId: string,
    questionIds: string[],
    startOrder: number
  ): Promise<IExamQuestion[]> {
    const now = new Date();

    const inserts: Partial<IExamQuestion>[] = questionIds.map((questionId, index) => ({
      id: randomUUID(),
      exam_id: examId,
      question_id: questionId,
      order_by: startOrder + index,
      created_at: now,
      updated_at: now,
      deleted_at: null,
    }));

    return this.examsQuestionsRepository.insertAll(inserts);
  }
}
