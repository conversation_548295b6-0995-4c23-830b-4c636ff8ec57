import { ListExamsParams } from '../../../model/DTO/exams.dto';
import { IExam } from '../../../model/IExam';
import { IExamsRepository } from '../../../repositories/assign/exams.assign';
import { OrderDirection } from '../../../services/sorting.service';

interface ListExamsUseCaseRequest extends ListExamsParams {
  customerId: string;
}

interface ListExamsUseCaseResponse {
  exams: IExam[];
}

export class ListExamsUseCase {
  constructor(private readonly examsRepository: IExamsRepository) {}

  async execute(request: ListExamsUseCaseRequest): Promise<ListExamsUseCaseResponse> {
    const {
      customerId,
      search,
      orderByColumn = 'name',
      orderDirection = OrderDirection.ASC,
    } = request;

    const exams = await this.examsRepository.listExams({
      customerId,
      search,
      orderByColumn,
      orderDirection,
    });

    return {
      exams,
    };
  }
}
