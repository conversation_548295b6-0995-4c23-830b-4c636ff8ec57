import { ICourseExamsRepository } from '../../../repositories/assign/coursesExams.assign';
import { IExamsRepository } from '../../../repositories/assign/exams.assign';

interface Institution {
  id: string;
  name: string;
  acronym: string | null;
}

interface ListExamsFiltersUseCaseRequest {
  courseId: string;
  customerId: string;
}

interface ListExamsFiltersUseCaseResponse {
  institutions: Institution[];
  years: number[];
}

export class ListExamsFiltersUseCase {
  constructor(
    private readonly examsRepository: IExamsRepository,
    private readonly courseExamsRepository: ICourseExamsRepository
  ) {}

  async execute(request: ListExamsFiltersUseCaseRequest): Promise<ListExamsFiltersUseCaseResponse> {
    const { courseId, customerId } = request;

    const courseExams = await this.courseExamsRepository.findByCourseId(courseId);
    const examIds = courseExams.map((link) => link.exam_id);

    if (examIds.length === 0) {
      return {
        institutions: [],
        years: [],
      };
    }

    const { institutions, years } = await this.examsRepository.getExamsFilters({
      examIds,
      customerId,
    });

    return {
      institutions: this.sortInstitutions(institutions),
      years: this.sortYearsDescending(years),
    };
  }

  private normalize(text: string): string {
    return text
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .toLowerCase();
  }

  private sortInstitutions(institutions: Institution[]): Institution[] {
    return [...institutions].sort((a, b) => {
      const aKey = a.acronym ? this.normalize(a.acronym) : this.normalize(a.name);
      const bKey = b.acronym ? this.normalize(b.acronym) : this.normalize(b.name);

      return aKey.localeCompare(bKey, 'pt-BR', { sensitivity: 'base' });
    });
  }

  private sortYearsDescending(years: (number | null)[]): number[] {
    return years.filter((y): y is number => y !== null).sort((a, b) => b - a);
  }
}
