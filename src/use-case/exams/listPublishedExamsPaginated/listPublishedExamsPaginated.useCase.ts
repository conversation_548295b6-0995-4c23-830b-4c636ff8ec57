import { PublishedExamsWithPaginationDTO } from '../../../model/DTO/exams.dto';
import { ICourseExamsRepository } from '../../../repositories/assign/coursesExams.assign';
import { IExamsRepository } from '../../../repositories/assign/exams.assign';
import { ListPublishedExamsFiltersInput } from '../../../schema/exams.schema';

interface ListPublishedExamsPaginatedUseCaseRequest extends ListPublishedExamsFiltersInput {
  courseId: string;
}

interface ListPublishedExamsPaginatedUseCaseResponse extends PublishedExamsWithPaginationDTO {}

export class ListPublishedExamsPaginatedUseCase {
  constructor(
    private readonly examsRepository: IExamsRepository,
    private readonly courseExamsRepository: ICourseExamsRepository
  ) {}

  async execute(
    payload: ListPublishedExamsPaginatedUseCaseRequest
  ): Promise<ListPublishedExamsPaginatedUseCaseResponse> {
    const { courseId, customerId, search, institution, year, page = 1, limit = 10 } = payload;

    const courseExams = await this.courseExamsRepository.findByCourseId(courseId);
    const examIds = courseExams.map((link) => link.exam_id);

    if (examIds.length === 0) {
      return {
        paginationInfo: {
          currentPage: 1,
          itemsPerPage: limit,
          totalItems: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false,
        },
        exams: [],
      };
    }

    const { exams, paginationInfo } = await this.examsRepository.listPublishedExamsPaginated({
      examIds,
      customerId,
      search,
      institution,
      year,
      page,
      limit,
    });

    return {
      paginationInfo,
      exams,
    };
  }
}
