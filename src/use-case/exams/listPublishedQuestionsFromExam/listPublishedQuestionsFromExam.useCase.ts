import { IExamsRepository } from '../../../repositories/assign/exams.assign';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface ListPublishedQuestionsFromExamRequest {
  examId: string;
  customerId: string;
}

export class ListPublishedQuestionsFromExamUseCase {
  constructor(private readonly examsRepository: IExamsRepository) {}

  async execute({ examId, customerId }: ListPublishedQuestionsFromExamRequest) {
    const exam = await this.examsRepository.findOneBy({
      id: examId,
      customer_id: customerId,
      deleted_at: null,
    });

    if (!exam) {
      throw new ResourceNotFoundError('Prova não encontrada');
    }

    const examDetails = await this.examsRepository.getPublishedQuestionsFromExam({
      examId,
      customerId,
    });

    return {
      exam: examDetails.exam,
      questions: examDetails.questions,
      customer: examDetails.customer,
      totalQuestions: examDetails.questions.length,
    };
  }
}
