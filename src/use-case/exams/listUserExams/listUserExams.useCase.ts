import { IExamWithProgressDTO } from '../../../model/DTO/IExam.dto';
import { IExamsRepository } from '../../../repositories/assign/exams.assign';

interface ListUserExamsRequest {
  userId: string;
  customerId: string;
  courseId: string;
}

interface ExamWithProgress extends IExamWithProgressDTO {
  totalQuestions: number;
  answeredQuestions: number;
  scorePercentage: number;
  isCompleted: boolean;
  createdAt: Date;
  end_exams: string | null;
  start_exams: string | null;
}

interface GroupedExams {
  date: string;
  exams: ExamWithProgress[];
}

interface ListUserExamsResponse {
  inProgress: GroupedExams[];
  completed: GroupedExams[];
}

export class ListUserExamsUseCase {
  constructor(private examsRepository: IExamsRepository) {}

  async execute({
    userId,
    customerId,
    courseId,
  }: ListUserExamsRequest): Promise<ListUserExamsResponse> {
    const examsWithProgress = await this.examsRepository.findByUserWithProgress({
      userId,
      customerId,
      courseId,
    });

    const inProgressMap = new Map<string, ExamWithProgress[]>();
    const completedMap = new Map<string, ExamWithProgress[]>();

    for (const exam of examsWithProgress) {
      const totalQuestions = exam.total_questions || 0;
      const answeredQuestions = exam.answered_questions || 0;
      const correctAnswers = exam.correct_answers || 0;

      const isCompleted = !exam.active;

      let dateStr: string;
      if (isCompleted) {
        const dateEnd = exam.end_exams || new Date().toISOString();
        dateStr = new Date(dateEnd).toISOString().split('T')[0];
      } else {
        const dateStart = exam.start_exams || exam.created_at;
        dateStr = new Date(dateStart).toISOString().split('T')[0];
      }

      const targetMap = isCompleted ? completedMap : inProgressMap;

      if (!targetMap.has(dateStr)) {
        targetMap.set(dateStr, []);
      }

      const processedExam: ExamWithProgress = {
        id: exam.id,
        name: exam.name,
        active: exam.active,
        institution_id: exam.institution_id,
        institution_name: exam.institution_name,
        created_at: exam.created_at,
        updated_at: exam.updated_at,
        start_exams: exam.start_exams,
        end_exams: isCompleted && !exam.end_exams ? new Date().toISOString() : exam.end_exams,
        access_id: exam.access_id,
        total_questions: exam.total_questions,
        answered_questions: exam.answered_questions,
        correct_answers: exam.correct_answers,
        totalQuestions,
        answeredQuestions,
        scorePercentage:
          totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0,
        isCompleted,
        createdAt: new Date(exam.created_at),
      };

      targetMap.get(dateStr)!.push(processedExam);
    }

    return {
      inProgress: this.mapToGroupedArray(inProgressMap),
      completed: this.mapToGroupedArray(completedMap),
    };
  }

  private mapToGroupedArray(map: Map<string, ExamWithProgress[]>): GroupedExams[] {
    return Array.from(map.entries())
      .sort((a, b) => b[0].localeCompare(a[0]))
      .map(([date, exams]) => ({
        date,
        exams: exams.sort((a, b) => {
          if (a.isCompleted && b.isCompleted && a.end_exams && b.end_exams) {
            return new Date(b.end_exams).getTime() - new Date(a.end_exams).getTime();
          }
          return b.createdAt.getTime() - a.createdAt.getTime();
        }),
      }));
  }
}
