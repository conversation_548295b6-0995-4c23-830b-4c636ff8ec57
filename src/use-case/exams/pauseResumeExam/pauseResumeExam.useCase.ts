import { ExamSessionActionEnum } from '../../../model/enums/examsStatus.enum';
import { IAnswerSession } from '../../../model/IAnswerSession';
import { IExamAccess } from '../../../model/IExamAccess';
import { IAnswerSessionsRepository } from '../../../repositories/assign/answerSessions.assign';
import { IExamsAccessRepository } from '../../../repositories/assign/examsAccess.assign';
import { ExamSessionActionPayload } from '../../../schema/exams.schema';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface PauseResumeExamRequest extends ExamSessionActionPayload {
  userId: string;
}

interface PauseResumeExamResponse {
  totalSeconds: number;
  answerSession: IAnswerSession;
  currentSessionDuration: number;
}

export class PauseResumeExamUseCase {
  constructor(
    private readonly accessRepo: IExamsAccessRepository,
    private readonly sessionsRepo: IAnswerSessionsRepository
  ) {}

  async execute({
    examAccessId,
    userId,
    action,
  }: PauseResumeExamRequest): Promise<PauseResumeExamResponse> {
    await this.ensureAccessIsValid(examAccessId, userId);
    const latestSession = await this.getLatestSession(examAccessId);
    const now = new Date();

    const currentSessionDuration = this.calculateCurrentSessionDuration(latestSession, now);

    const answerSession =
      action === ExamSessionActionEnum.PAUSE
        ? await this.pauseExam(latestSession, now)
        : await this.resumeExam(latestSession, examAccessId, now);

    const previousSessionsTime = await this.calculatePreviousSessionsTime(examAccessId);

    const totalSeconds =
      action === ExamSessionActionEnum.PAUSE
        ? previousSessionsTime
        : currentSessionDuration + previousSessionsTime;

    await this.accessRepo.update({
      id: examAccessId,
      total_seconds: totalSeconds,
    });

    return {
      answerSession,
      totalSeconds,
      currentSessionDuration,
    };
  }

  private calculateCurrentSessionDuration(session: IAnswerSession | null, now: Date): number {
    if (!session || session.paused_at) {
      return 0;
    }

    return Math.floor((now.getTime() - new Date(session.started_at).getTime()) / 1000);
  }

  private async calculatePreviousSessionsTime(examAccessId: string): Promise<number> {
    const sessions = await this.sessionsRepo.findByExamAccessId(examAccessId);

    return sessions.reduce((sum, session) => {
      if (session.duration_seconds !== null && session.duration_seconds !== undefined) {
        return sum + session.duration_seconds;
      }
      return sum;
    }, 0);
  }

  private async ensureAccessIsValid(examAccessId: string, userId: string): Promise<IExamAccess> {
    const access = await this.accessRepo.findOneBy({ id: examAccessId, user_id: userId });

    if (!access) throw new ResourceNotFoundError('Acesso à prova não encontrado');

    if (access.end_exams) throw new BadRequestError('Esta prova já foi finalizada');

    return access;
  }

  private async getLatestSession(examAccessId: string): Promise<IAnswerSession | null> {
    const sessions = await this.sessionsRepo.findByExamAccessId(examAccessId);

    if (!sessions.length) return null;

    return sessions.reduce((latest, current) =>
      new Date(current.started_at) > new Date(latest.started_at) ? current : latest
    );
  }

  private async pauseExam(session: IAnswerSession | null, now: Date): Promise<IAnswerSession> {
    if (!session) {
      throw new BadRequestError('Não é possível pausar uma prova que não foi iniciada');
    }

    if (session.paused_at) return session;

    const duration = Math.floor((now.getTime() - new Date(session.started_at).getTime()) / 1000);

    return this.sessionsRepo.update({
      id: session.id,
      paused_at: now,
      duration_seconds: duration,
    });
  }

  private async resumeExam(
    session: IAnswerSession | null,
    accessId: string,
    now: Date
  ): Promise<IAnswerSession> {
    if (!session || session.paused_at) {
      return this.sessionsRepo.insert({
        exam_access_id: accessId,
        started_at: now,
        duration_seconds: 0,
      });
    }

    return session;
  }
}
