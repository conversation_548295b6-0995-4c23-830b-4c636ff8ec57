import { ExamsStatusEnum } from '../../../model/enums/examsStatus.enum';
import { IExamQuestion } from '../../../model/IExamQuestion';
import { IExamsRepository } from '../../../repositories/assign/exams.assign';
import { IExamsQuestionsRepository } from '../../../repositories/assign/examsQuestions.assign';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface ReorderExamQuestionsUseCaseRequest {
  examId: string;
  customerId: string;
  questions: {
    questionId: string;
    order: number;
  }[];
}

interface ReorderExamQuestionsUseCaseResponse {
  examQuestions: IExamQuestion[];
}

export class ReorderExamQuestionsUseCase {
  constructor(
    private examsRepository: IExamsRepository,
    private examsQuestionsRepository: IExamsQuestionsRepository
  ) {}

  async execute({
    examId,
    customerId,
    questions,
  }: ReorderExamQuestionsUseCaseRequest): Promise<ReorderExamQuestionsUseCaseResponse> {
    await this.findDraftExamOrFail(examId, customerId);
    const activeQuestions = await this.findActiveExamQuestionsOrFail(examId);

    this.validateReorderRequest(activeQuestions, questions);

    const orderMap = new Map(questions.map((q) => [q.questionId, q.order]));
    const updates = activeQuestions.map((q) => ({
      id: q.id,
      order_by: orderMap.get(q.question_id),
      updated_at: new Date(),
    }));

    const updatedExamQuestions = await this.examsQuestionsRepository.updateBatch(updates);

    return { examQuestions: updatedExamQuestions };
  }

  private async findDraftExamOrFail(examId: string, customerId: string) {
    const exam = await this.examsRepository.findOneBy({
      id: examId,
      customer_id: customerId,
      deleted_at: null,
    });

    if (!exam) throw new ResourceNotFoundError('Prova não encontrada');

    if (exam.status !== ExamsStatusEnum.DRAFT) {
      throw new BadRequestError('Apenas provas em modo rascunho podem ser reordenadas');
    }

    return exam;
  }

  private async findActiveExamQuestionsOrFail(examId: string): Promise<IExamQuestion[]> {
    const questions = await this.examsQuestionsRepository.findByExamId(examId);
    const activeQuestions = questions.filter((q) => q.deleted_at === null);

    if (activeQuestions.length === 0) {
      throw new BadRequestError('A prova não possui questões ativas para reordenar');
    }

    return activeQuestions;
  }

  private validateReorderRequest(
    activeQuestions: IExamQuestion[],
    requestedQuestions: { questionId: string; order: number }[]
  ) {
    const currentIds = new Set(activeQuestions.map((q) => q.question_id));
    const requestedIds = requestedQuestions.map((q) => q.questionId);
    const invalidIds = requestedIds.filter((id) => !currentIds.has(id));

    if (invalidIds.length > 0) {
      throw new BadRequestError(
        `Questões não encontradas ou excluídas da prova: ${invalidIds.join(', ')}`
      );
    }

    if (requestedIds.length !== activeQuestions.length) {
      throw new BadRequestError('Todas as questões ativas devem ser incluídas na reordenação');
    }

    const orders = requestedQuestions.map((q) => q.order);
    const uniqueOrders = new Set(orders);

    if (uniqueOrders.size !== orders.length) {
      throw new BadRequestError('Não pode haver ordens duplicadas');
    }

    const min = Math.min(...orders);
    const max = Math.max(...orders);

    if (min !== 1 || max !== orders.length) {
      throw new BadRequestError(
        `As ordens devem ser contínuas, de 1 até o total de questões: ${orders.length}`
      );
    }
  }
}
