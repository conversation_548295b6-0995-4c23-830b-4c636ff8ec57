import { ExamsStatusEnum } from '../../../model/enums/examsStatus.enum';
import { IExamAccess } from '../../../model/IExamAccess';
import { IExamsRepository } from '../../../repositories/assign/exams.assign';
import { IExamsAccessRepository } from '../../../repositories/assign/examsAccess.assign';
import { IExamsQuestionsRepository } from '../../../repositories/assign/examsQuestions.assign';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface StartExamSessionRequest {
  examId: string;
  userId: string;
}

interface StartExamSessionResponse {
  id: string;
  examId: string;
  startTime: Date | null;
  endTime: Date | null;
  timeLimit: number | null;
  questionsCount: number;
}

export class StartExamSessionUseCase {
  constructor(
    private readonly examsRepository: IExamsRepository,
    private readonly examsAccessRepository: IExamsAccessRepository,
    private readonly examsQuestionsRepository: IExamsQuestionsRepository
  ) {}

  async execute({ examId, userId }: StartExamSessionRequest): Promise<StartExamSessionResponse> {
    const exam = await this.findPublishedExamOrFail(examId);

    const questionsCount = await this.validateExamHasQuestionsOrFail(examId);

    const startedAt = new Date();

    const examAccess = await this.examsAccessRepository.insert({
      exam_id: examId,
      user_id: userId,
      active: true,
      start_exams: startedAt,
      time_limit: exam.exam_time,
    });

    return this.toResponse(examAccess, questionsCount);
  }

  private async findPublishedExamOrFail(examId: string) {
    const exam = await this.examsRepository.findOneBy({
      id: examId,
      published: true,
      status: ExamsStatusEnum.PUBLISHED,
      deleted_at: null,
    });

    if (!exam) throw new ResourceNotFoundError('Prova não encontrada');

    return exam;
  }

  private async validateExamHasQuestionsOrFail(examId: string): Promise<number> {
    const questions = await this.examsQuestionsRepository.findByExamId(examId);

    if (questions.length === 0) throw new BadRequestError('Esta prova não possui questões');

    return questions.length;
  }

  private toResponse(access: IExamAccess, questionsCount: number): StartExamSessionResponse {
    return {
      id: access.id,
      examId: access.exam_id,
      startTime: access.start_exams ?? null,
      endTime: access.end_exams ?? null,
      timeLimit: access.time_limit ?? null,
      questionsCount,
    };
  }
}
