import { ExamsStatusEnum } from '../../../model/enums/examsStatus.enum';
import { IExam } from '../../../model/IExam';
import { IExamQuestion } from '../../../model/IExamQuestion';
import { IExamsRepository } from '../../../repositories/assign/exams.assign';
import { IExamsQuestionsRepository } from '../../../repositories/assign/examsQuestions.assign';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface IUnlinkQuestionFromExamRequest {
  examId: string;
  questionId: string;
  customerId: string;
}

interface IUnlinkQuestionFromExamResponse {
  exam: IExam;
  unlinkedQuestion: IExamQuestion | null;
}

export class UnlinkQuestionFromExamUseCase {
  constructor(
    private readonly examsRepository: IExamsRepository,
    private readonly examsQuestionsRepository: IExamsQuestionsRepository
  ) {}

  async execute({
    examId,
    questionId,
    customerId,
  }: IUnlinkQuestionFromExamRequest): Promise<IUnlinkQuestionFromExamResponse> {
    const exam = await this.findDraftExamOrFail(examId, customerId);

    const examQuestion = await this.findExamQuestionOrFail(examId, questionId);

    await this.examsQuestionsRepository.softDeleteByQuestionId(questionId, examId);

    return {
      exam,
      unlinkedQuestion: examQuestion,
    };
  }

  private async findDraftExamOrFail(examId: string, customerId: string): Promise<IExam> {
    const exam = await this.examsRepository.findOneBy({
      id: examId,
      customer_id: customerId,
      deleted_at: null,
    });

    if (!exam) {
      throw new ResourceNotFoundError('Prova não encontrada');
    }

    if (exam.status !== ExamsStatusEnum.DRAFT) {
      throw new BadRequestError('Esta operação só é permitida para provas em modo rascunho');
    }

    return exam;
  }

  private async findExamQuestionOrFail(examId: string, questionId: string): Promise<IExamQuestion> {
    const examQuestions = await this.examsQuestionsRepository.findByExamId(examId);

    const examQuestion = examQuestions.find((eq) => eq.question_id === questionId);

    if (!examQuestion) {
      throw new ResourceNotFoundError('A questão informada não está vinculada a esta prova');
    }

    return examQuestion;
  }
}
