import { QuestionStatusEnum } from '../../../model/enums/questions.enum';
import { IExam } from '../../../model/IExam';
import { IQuestion } from '../../../model/IQuestion';
import { IExamsRepository } from '../../../repositories/assign/exams.assign';
import { IExamsQuestionsRepository } from '../../../repositories/assign/examsQuestions.assign';
import { IQuestionsRepository } from '../../../repositories/assign/questions.assign';
import { filterTextRegex } from '../../../services/filterTextRegex.service';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface IUpdateExamRequest {
  id: string;
  customerId: string;
  name?: string;
  institutionId?: string | null;
  year?: number | null;
  published?: boolean;
  status?: string;
  examTime?: number | null;
}

export class UpdateExamUseCase {
  constructor(
    private readonly examsRepository: IExamsRepository,
    private readonly examQuestionsRepository: IExamsQuestionsRepository,
    private readonly questionsRepository: IQuestionsRepository
  ) {}

  async execute(payload: IUpdateExamRequest): Promise<IExam> {
    const exam = await this.getExistingExam(payload.id, payload.customerId);

    await this.validateNameUniqueness(payload, exam.id);

    if (payload.year) {
      this.validateYearFormat(payload.year);
    }

    if (exam.published) {
      return this.handlePublishedExamUpdate(exam, payload);
    }

    if (payload.published === true && !exam.published) {
      await this.validateRequiredFieldsForPublication(payload, exam);
      await this.validateExamHasQuestions(exam.id, payload);
    }

    const updatePayload = this.buildUpdateData(payload, exam);
    return this.examsRepository.update(updatePayload);
  }

  private async getExistingExam(id: string, customerId: string): Promise<IExam> {
    const exam = await this.examsRepository.findOneBy({
      id,
      customer_id: customerId,
      deleted_at: null,
    });

    if (!exam) {
      throw new ResourceNotFoundError('Prova não encontrada');
    }

    return exam;
  }

  private async validateNameUniqueness(
    payload: IUpdateExamRequest,
    currentExamId: string
  ): Promise<void> {
    if (!payload.name) return;

    const nameNormalized = filterTextRegex(payload.name);

    const allExams = await this.examsRepository.findAllBy({
      customer_id: payload.customerId,
      deleted_at: null,
      year: payload.year,
      institution_id: payload.institutionId,
    });

    const matchingExam = allExams
      .filter((exam: IExam) => exam.id !== currentExamId)
      .some((exam: IExam) => {
        const examNameNormalized = filterTextRegex(exam.name || '');

        if (
          examNameNormalized === nameNormalized &&
          exam.year === payload.year &&
          payload.year &&
          payload.institutionId &&
          exam.institution_id === payload.institutionId
        ) {
          return true;
        }
        return false;
      });

    if (matchingExam) {
      throw new BadRequestError(
        `Já existe uma prova para a instituição e ano informados (${payload.name})`
      );
    }
  }

  private handlePublishedExamUpdate(exam: IExam, payload: IUpdateExamRequest): Promise<IExam> {
    if (payload.published === false) {
      return this.examsRepository.update({
        id: exam.id,
        published: false,
        status: payload.status ?? exam.status,
        customer_id: exam.customer_id,
      });
    }

    if (this.isTryingToEditLockedFields(payload)) {
      throw new BadRequestError(
        'Não é possível editar uma prova que já está publicada. Apenas é possível despublicar a prova.'
      );
    }

    if (payload.status !== undefined && payload.published === undefined) {
      return this.examsRepository.update({
        id: exam.id,
        status: payload.status,
        customer_id: exam.customer_id,
      });
    }

    return Promise.resolve(exam);
  }

  private isTryingToEditLockedFields(payload: IUpdateExamRequest): boolean {
    return (
      payload.name !== undefined ||
      payload.institutionId !== undefined ||
      payload.year !== undefined ||
      payload.examTime !== undefined
    );
  }

  private validateRequiredFieldsForPublication(payload: IUpdateExamRequest, exam: IExam): void {
    const name = payload.name ?? exam.name;
    const institution =
      payload.institutionId !== undefined ? payload.institutionId : exam.institution_id;
    const year = payload.year !== undefined ? payload.year : exam.year;
    const duration = payload.examTime !== undefined ? payload.examTime : exam.exam_time;

    const missing: string[] = [];

    if (!name?.trim()) missing.push('nome');
    if (!institution?.trim()) missing.push('instituição');
    if (!year) missing.push('ano');
    if (!duration) missing.push('duração');

    if (missing.length > 0) {
      throw new BadRequestError(
        `Não é possível publicar a prova. Os seguintes campos são obrigatórios: ${missing.join(', ')}`
      );
    }

    if (year && year.toString().length !== 4) {
      throw new BadRequestError('O ano deve conter exatamente 4 dígitos');
    }
  }

  private buildUpdateData(request: IUpdateExamRequest, exam: IExam): Partial<IExam> {
    return {
      id: exam.id,
      customer_id: exam.customer_id,
      name: request.name ?? exam.name,
      institution_id:
        request.institutionId !== undefined ? request.institutionId : exam.institution_id,
      year: request.year !== undefined ? request.year : exam.year,
      published: request.published ?? exam.published,
      status: request.status ?? exam.status,
      exam_time: request.examTime !== undefined ? request.examTime : exam.exam_time,
    };
  }

  private validateYearFormat(year: number): void {
    if (year.toString().length !== 4) {
      throw new BadRequestError('O ano deve conter exatamente 4 dígitos');
    }
  }

  private async validateExamHasQuestions(
    examId: string,
    payload: IUpdateExamRequest
  ): Promise<void> {
    const examQuestions = await this.examQuestionsRepository.findByExamId(examId);

    if (examQuestions.length === 0) {
      throw new BadRequestError('A prova deve ter pelo menos uma questão para ser publicada');
    }

    const questionIds = examQuestions.map((examQuestion) => examQuestion.question_id);

    const questions = await this.questionsRepository.findByIds(questionIds, payload.customerId);

    if (!questions || questions.length !== examQuestions.length) {
      throw new BadRequestError('A prova deve ter pelo menos uma questão para ser publicada');
    }

    const unpublishedQuestions = questions.filter(
      (question: IQuestion) => question.status !== QuestionStatusEnum.Published
    );

    if (unpublishedQuestions.length > 0) {
      throw new BadRequestError(
        'Todas as questões da prova devem estar publicadas para que a prova seja publicada'
      );
    }
  }
}
