import { KnexCountriesRepository } from '../../repositories/knex/countries.repositories';
import { KnexCustomerRepository } from '../../repositories/knex/customers.repositories';
import { KnexFilesRepository } from '../../repositories/knex/files.repositories';
import { KnexRecoveryRepository } from '../../repositories/knex/recoveries.repositories';
import { KnexTermsRepository } from '../../repositories/knex/terms.repositories';
import { KnexTermsAcceptedRepository } from '../../repositories/knex/termsAccepted.repositories';
import { KnexUserRepository } from '../../repositories/knex/users.repositories';
import { KnexUsersAdditionalInfosRepository } from '../../repositories/knex/usersAdditionalInfos.repositories';
import S3Service from '../../services/aws/s3/S3Service';
import { SESService } from '../../services/aws/ses/SESService';
import { AutologinUseCase } from '../auth/autologin.useCase';
import { ChangePasswordUseCase } from '../auth/changePassword.useCase';
import { CompleteUserRegistrationUseCase } from '../auth/completeUserRegistration.useCase';
import { ForgotPasswordUseCase } from '../auth/forgotPassword.useCase';
import { GoogleAuthUseCase } from '../auth/googleAuth.useCase';
import { RedefinePasswordUseCase } from '../auth/redefinePassword.useCase';
import { TokenInfoUseCase } from '../auth/tokenInfo.useCase';

const customerRepository = new KnexCustomerRepository();
const userRepository = new KnexUserRepository();
const sesService = new SESService();
const recoveryRepository = new KnexRecoveryRepository();
const termsRepository = new KnexTermsRepository();
const termsAcceptRepository = new KnexTermsAcceptedRepository();
const filesRepository = new KnexFilesRepository();
const s3Service = new S3Service();
const countriesRepository = new KnexCountriesRepository();
const usersAdditionalInfosRepository = new KnexUsersAdditionalInfosRepository();

export function factoryAutologinUseCase() {
  return new AutologinUseCase(customerRepository, userRepository);
}

export function makeChangePasswordUseCase() {
  return new ChangePasswordUseCase(userRepository);
}

export function factoryGoogleAuthUseCase() {
  return new GoogleAuthUseCase(userRepository);
}

export function factoryForgotPasswordUseCase() {
  return new ForgotPasswordUseCase(
    customerRepository,
    userRepository,
    recoveryRepository,
    termsRepository,
    filesRepository,
    sesService,
    s3Service
  );
}

export function factoryTokenInfoUseCase() {
  return new TokenInfoUseCase(recoveryRepository);
}

export function factoryRedefinePasswordUseCase() {
  return new RedefinePasswordUseCase(recoveryRepository, userRepository);
}

export function factoryCompleteUserRegistrationUseCase() {
  return new CompleteUserRegistrationUseCase(
    recoveryRepository,
    userRepository,
    termsRepository,
    termsAcceptRepository,
    countriesRepository,
    usersAdditionalInfosRepository
  );
}
