import { KnexAlternativesRepository } from '../../repositories/knex/alternatives.repositories';
import { KnexBulkImportRepository } from '../../repositories/knex/bulkImport.repositories';
import { KnexBulkImportDetailRepository } from '../../repositories/knex/bulkImportDetails.repositories';
import { KnexDisciplinesRepository } from '../../repositories/knex/disciplines.repositories';
import { KnexQuestionsRepository } from '../../repositories/knex/questions.repositories';
import { KnexQuestionsTypesRepository } from '../../repositories/knex/questionsTypes.repositories';
import { ExcelProcessor } from '../../services/excelProcessor.service';
import { QuestionCodeGeneratorService } from '../../services/questionCodeGenerator.service';
import { UploadService } from '../../services/uploadFile.service';
import { ExportBulkImportDetailsUseCase } from '../bulkImportQuestions/exportBulkImportDetails.useCase';
import { GetBulkImportResultsUseCase } from '../bulkImportQuestions/getBulkImportResults.useCase';
import { ImportValidatedQuestionsUseCase } from '../bulkImportQuestions/importValidatedQuestions.useCase';
import { ListBulkImportsUseCase } from '../bulkImportQuestions/listBulkImports.useCase';
import { ReuploadQuestionsExcelUseCase } from '../bulkImportQuestions/reuploadQuestionsExcel.useCase';
import { UploadQuestionsExcelUseCase } from '../bulkImportQuestions/uploadQuestionsExcel.useCase';

const bulkImportRepository = new KnexBulkImportRepository();
const bulkImportDetailRepository = new KnexBulkImportDetailRepository();
const questionsRepository = new KnexQuestionsRepository();
const disciplinesRepository = new KnexDisciplinesRepository();
const questionsTypesRepository = new KnexQuestionsTypesRepository();
const alternativesRepository = new KnexAlternativesRepository();
const excelProcessor = new ExcelProcessor();
const uploadService = new UploadService();
const questionCodeGenerator = new QuestionCodeGeneratorService(questionsRepository);

export function factoryUploadQuestionsExcelUseCase() {
  return new UploadQuestionsExcelUseCase(
    bulkImportRepository,
    bulkImportDetailRepository,
    questionsRepository,
    disciplinesRepository,
    questionsTypesRepository,
    excelProcessor,
    uploadService
  );
}

export function factoryReuploadQuestionsExcelUseCase() {
  return new ReuploadQuestionsExcelUseCase(
    bulkImportRepository,
    bulkImportDetailRepository,
    questionsRepository,
    disciplinesRepository,
    questionsTypesRepository,
    excelProcessor,
    uploadService
  );
}

export function factoryGetBulkImportResultsUseCase(): GetBulkImportResultsUseCase {
  return new GetBulkImportResultsUseCase(bulkImportRepository, bulkImportDetailRepository);
}

export function factoryListBulkImportsUseCase(): ListBulkImportsUseCase {
  return new ListBulkImportsUseCase(bulkImportRepository);
}

export function factoryExportBulkImportDetailsUseCase(): ExportBulkImportDetailsUseCase {
  return new ExportBulkImportDetailsUseCase(bulkImportRepository, bulkImportDetailRepository);
}

export function factoryImportValidatedQuestionsUseCase(): ImportValidatedQuestionsUseCase {
  return new ImportValidatedQuestionsUseCase(
    bulkImportRepository,
    bulkImportDetailRepository,
    questionsRepository,
    disciplinesRepository,
    alternativesRepository,
    questionCodeGenerator
  );
}
