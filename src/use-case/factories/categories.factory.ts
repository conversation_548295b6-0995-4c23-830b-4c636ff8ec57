import { KnexAggregateDataRepository } from '../../repositories/knex/aggregatedData.repositories';
import { KnexCategoriesRepository } from '../../repositories/knex/categories.repositories';
import { KnexDisciplinesRepository } from '../../repositories/knex/disciplines.repositories';
import { KnexDisciplinesCategoriesRepository } from '../../repositories/knex/disciplinesCategoriesAccess.repositories';
import { KnexDisciplinesSubcategoriesAccessRepository } from '../../repositories/knex/disciplinesSubcategoriesAccess.repositories';
import { KnexQuestionsRepository } from '../../repositories/knex/questions.repositories';
import { KnexSubcategoriesRepository } from '../../repositories/knex/subcategories.repositories';
import { CreateCategoryUseCase } from '../categories/createCategory/createCategory.useCase';
import { DeleteCategoryUseCase } from '../categories/deleteCategory/deleteCategory.useCase';
import { ListCategoriesUseCase } from '../categories/listCategories/listCategories.useCase';
import { UpdateCategoryUseCase } from '../categories/updateCategory/updateCategoryUseCase';

const questionRepository = new KnexQuestionsRepository();
const categoryRepository = new KnexCategoriesRepository();
const disciplinesCategoriesAccessRepository = new KnexDisciplinesCategoriesRepository();
const aggregateDataRepository = new KnexAggregateDataRepository();
const subcategoryRepository = new KnexSubcategoriesRepository();
const subcategoryAccessRepository = new KnexDisciplinesSubcategoriesAccessRepository();
const disciplineRepository = new KnexDisciplinesRepository();
const disciplinesSubcategoriesAccessRepository = new KnexDisciplinesSubcategoriesAccessRepository();

export function factoryCreateCategoryUseCase() {
  return new CreateCategoryUseCase(categoryRepository, subcategoryRepository);
}

export function factoryUpdateCategoryUseCase() {
  return new UpdateCategoryUseCase(categoryRepository);
}

export function factoryDeleteCategoryUseCase() {
  return new DeleteCategoryUseCase(
    categoryRepository,
    aggregateDataRepository,
    questionRepository,
    disciplinesCategoriesAccessRepository,
    subcategoryRepository,
    subcategoryAccessRepository
  );
}

export function factoryListCategoriesUseCase() {
  return new ListCategoriesUseCase(
    categoryRepository,
    disciplineRepository,
    disciplinesCategoriesAccessRepository,
    disciplinesSubcategoriesAccessRepository
  );
}
