import { KnexClassesRepository } from '../../repositories/knex/classes.repositories';
import { KnexCustomerRepository } from '../../repositories/knex/customers.repositories';
import { KnexUserRepository } from '../../repositories/knex/users.repositories';
import { KnexUserClassesRepository } from '../../repositories/knex/usersClasses.repositories';
import { CreateClassUseCase } from '../classes/createClass.useCase';
import { CreateExternalClassUseCase } from '../classes/createExternalClass/createExternalClass.useCase';
import { DeleteClassUseCase } from '../classes/deleteClass.useCase';
import { DeleteExternalClassUseCase } from '../classes/deleteExternalClass/deleteExternalClass.useCase';
import { ListClassesUseCase } from '../classes/listClasses/listClasses.useCase';
import { ListClassesByCourseUseCase } from '../classes/listClassesByCourse.useCase';
import { ListClassesWithUsersUseCase } from '../classes/listClassesWithUsers/listClassesWithUsers.useCase';
import { UpdateClassUseCase } from '../classes/updateClass.useCase';
import { UpdateExternalClassUseCase } from '../classes/updateExternalClass/updateExternalClass.useCase';

const classesRepository = new KnexClassesRepository();
const customerRepository = new KnexCustomerRepository();
const usersRepository = new KnexUserRepository();
const userClassesRepository = new KnexUserClassesRepository();

export function factoryCreateExternalClassUseCase() {
  return new CreateExternalClassUseCase(classesRepository, customerRepository);
}

export function factoryUpdateExternalClassUseCase() {
  return new UpdateExternalClassUseCase(classesRepository, customerRepository, usersRepository);
}

export function factoryDeleteExternalClassUseCase() {
  return new DeleteExternalClassUseCase(
    classesRepository,
    customerRepository,
    userClassesRepository
  );
}

export function factoryListClassesWithUsersUseCase() {
  return new ListClassesWithUsersUseCase(classesRepository);
}

export function factoryListClassesUseCase() {
  return new ListClassesUseCase(classesRepository);
}

export function factoryListClassesByCourseUseCase() {
  return new ListClassesByCourseUseCase(classesRepository);
}

export function factoryCreateClassUseCase() {
  return new CreateClassUseCase(classesRepository);
}

export function factoryUpdateClassUseCase() {
  return new UpdateClassUseCase(classesRepository);
}

export function factoryDeleteClassUseCase() {
  return new DeleteClassUseCase(classesRepository);
}
