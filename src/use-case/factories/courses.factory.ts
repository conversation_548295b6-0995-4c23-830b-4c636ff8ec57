import { KnexClassesRepository } from '../../repositories/knex/classes.repositories';
import { KnexCourseRepository } from '../../repositories/knex/courses.repositories';
import { KnexCoursesCategoriesRelationRepository } from '../../repositories/knex/coursesCategoriesRelation.repositories';
import { KnexCourseExamsRepository } from '../../repositories/knex/coursesExams.repositories';
import { KnexExamsRepository } from '../../repositories/knex/exams.repositories';
import { KnexUserCourseProgressRepository } from '../../repositories/knex/userCourseProgress.repositories';
import { KnexUserLessonsProgressRepository } from '../../repositories/knex/userLessonsProgress.repositories';
import { KnexUserModulesProgressRepository } from '../../repositories/knex/userModulesProgress.repositories';
import { KnexUserRepository } from '../../repositories/knex/users.repositories';
import { KnexUserClassesRepository } from '../../repositories/knex/usersClasses.repositories';
import S3Service from '../../services/aws/s3/S3Service';
import { CreateCourseUseCase } from '../courses/createCourse.useCase';
import { GetCourseUseCase } from '../courses/getCourse.useCase';
import { GetCourseDetailsUseCase } from '../courses/getCourseDetails.useCase';
import { GetCourseQuestionBankUseCase } from '../courses/getCourseQuestionBank.useCase';
import { LastAccessCourseUseCase } from '../courses/lastAccessCourse.useCase';
import { LinkExamsToCourseUseCase } from '../courses/linkExamsToCourse.useCase';
import { ListAllCoursesUseCase } from '../courses/listAllCourses.useCase';
import { ListAllCoursesSimulationsUseCase } from '../courses/listAllCoursesSimulations.usecase';
import { ListCoursesByUserUseCase } from '../courses/listCoursesByUser/listCoursesByUser.usecase';
import { RemoveUsersFromCourseUseCase } from '../courses/removeUsersFromCourse/removeUsersFromCourse.useCase';
import { TrackProgressUseCase } from '../courses/trackProgress.useCase';
import { UpdateCourseUseCase } from '../courses/updateCourse.useCase';

const coursesRepository = new KnexCourseRepository();
const s3Service = new S3Service();
const userRepository = new KnexUserRepository();
const examsRepository = new KnexExamsRepository();
const courseExamsRepository = new KnexCourseExamsRepository();
const coursesCategoriesRelationRepository = new KnexCoursesCategoriesRelationRepository();
const classesRepository = new KnexClassesRepository();
const userClassesRepository = new KnexUserClassesRepository();
const courseRepository = new KnexCourseRepository();
const userCourseProgressRepository = new KnexUserCourseProgressRepository();
const userLessonsProgressRepository = new KnexUserLessonsProgressRepository();
const userModulesProgressRepository = new KnexUserModulesProgressRepository();

export function factoryLastAccessCourseUseCase(): LastAccessCourseUseCase {
  return new LastAccessCourseUseCase(coursesRepository);
}

export function factoryListCoursesByUserUseCase(): ListCoursesByUserUseCase {
  return new ListCoursesByUserUseCase(coursesRepository, s3Service);
}

export function factoryListAllCoursesSimulationsUseCase(): ListAllCoursesSimulationsUseCase {
  return new ListAllCoursesSimulationsUseCase(coursesRepository, userRepository, s3Service);
}

export function factoryListAllCourses() {
  const coursesRepository = new KnexCourseRepository();
  const s3Service = new S3Service();
  return new ListAllCoursesUseCase(coursesRepository, s3Service);
}

export function factoryCreateCourse() {
  return new CreateCourseUseCase(
    coursesRepository,
    coursesCategoriesRelationRepository,
    classesRepository
  );
}

export function factoryGetCourse() {
  return new GetCourseUseCase(coursesRepository, s3Service);
}

export function factoryGetCourseDetails() {
  return new GetCourseDetailsUseCase(coursesRepository);
}

export function factoryGetCourseQuestionBankUseCase() {
  return new GetCourseQuestionBankUseCase(coursesRepository);
}

export function factoryLinkExamsToCourseUseCase() {
  return new LinkExamsToCourseUseCase(examsRepository, courseExamsRepository);
}

export function factoryUpdateCourseUseCase() {
  return new UpdateCourseUseCase(coursesRepository, coursesCategoriesRelationRepository);
}

export function factoryRemoveUsersFromCourseUseCase() {
  return new RemoveUsersFromCourseUseCase(userClassesRepository, userRepository, courseRepository);
}

export function factoryTrackProgress() {
  return new TrackProgressUseCase(
    userCourseProgressRepository,
    userLessonsProgressRepository,
    userModulesProgressRepository
  );
}
