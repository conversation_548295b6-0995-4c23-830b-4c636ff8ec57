import { KnexCoursesCategoriesRepository } from '../../repositories/knex/coursesCategories.repositories';
import { GetAllCourseCategoriesUseCase } from '../coursesCategories/getAllCourseCategories.useCase';

const coursesCategoriesRepository = new KnexCoursesCategoriesRepository();

export function factoryGetAllCourseCategoriesUseCase(): GetAllCourseCategoriesUseCase {
  return new GetAllCourseCategoriesUseCase(coursesCategoriesRepository);
}
