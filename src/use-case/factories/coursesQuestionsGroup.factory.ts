import { KnexCourseQuestionsGroupsRepository } from '../../repositories/knex/coursesQuestionsGroups.repositories';
import { CreateCourseQuestionBankUseCase } from '../courses/createCourseQuestionBank.useCase';

const courseQuestionsGroupsRepository = new KnexCourseQuestionsGroupsRepository();

export function factoryCreateCourseQuestionBankUseCase() {
  return new CreateCourseQuestionBankUseCase(courseQuestionsGroupsRepository);
}
