import { KnexCustomerRepository } from '../../repositories/knex/customers.repositories';
import { KnexFilesRepository } from '../../repositories/knex/files.repositories';
import { KnexInstitutionsRepository } from '../../repositories/knex/institutions.repositories';
import { KnexTermsRepository } from '../../repositories/knex/terms.repositories';
import S3Service from '../../services/aws/s3/S3Service';
import { CreateExternalCustomerUseCase } from '../customers/createExternalCustomer/createExternalCustomer.UseCase';
import { GetCustomerByIdUseCase } from '../customers/getCustomerById/getCustomerById.useCase';
import { GetCustomerByTokenUseCaseUseCase } from '../customers/getCustomerByToken/getCustomerByToken.UseCase';
import { UpdateCustomerUseCase } from '../customers/updateCustomer/updateCustomer.useCase';

const customerRepository = new KnexCustomerRepository();
const institutionRepository = new KnexInstitutionsRepository();
const s3Service = new S3Service();
const termsRepository = new KnexTermsRepository();
const filesRepository = new KnexFilesRepository();

export function factoryCreateExternalCustomerUseCase() {
  return new CreateExternalCustomerUseCase(customerRepository, institutionRepository);
}

export function factoryUpdateCustomerUseCase() {
  return new UpdateCustomerUseCase(customerRepository, s3Service);
}

export function getCustomersByTokenUseCase() {
  return new GetCustomerByTokenUseCaseUseCase(customerRepository);
}

export function factoryGetCustomerByIdUseCase() {
  return new GetCustomerByIdUseCase(
    customerRepository,
    s3Service,
    termsRepository,
    filesRepository
  );
}
