import { KnexCategoriesRepository } from '../../repositories/knex/categories.repositories';
import { KnexDisciplinesRepository } from '../../repositories/knex/disciplines.repositories';
import { KnexDisciplinesCategoriesRepository } from '../../repositories/knex/disciplinesCategoriesAccess.repositories';
import { KnexDisciplinesSubcategoriesAccessRepository } from '../../repositories/knex/disciplinesSubcategoriesAccess.repositories';
import { KnexSubcategoriesRepository } from '../../repositories/knex/subcategories.repositories';
import { ManageCategoryAccessInDisciplineUseCase } from '../disciplines/manageCategoryAccessInDiscipline/manageCategoryAccessInDiscipline.useCase';

export function factoryManageCategoryAccessInDiscipline() {
  const disciplineRepository = new KnexDisciplinesRepository();
  const categoriesRepository = new KnexCategoriesRepository();
  const disciplineCategoryAccesRepository = new KnexDisciplinesCategoriesRepository();
  const disciplinesSubcategoriesAccess = new KnexDisciplinesSubcategoriesAccessRepository();
  const subCategoriesRepository = new KnexSubcategoriesRepository();

  const useCase = new ManageCategoryAccessInDisciplineUseCase(
    disciplineRepository,
    categoriesRepository,
    disciplineCategoryAccesRepository,
    disciplinesSubcategoriesAccess,
    subCategoriesRepository
  );

  return useCase;
}
