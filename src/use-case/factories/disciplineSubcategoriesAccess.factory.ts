import { KnexCategoriesRepository } from '../../repositories/knex/categories.repositories';
import { KnexDisciplinesRepository } from '../../repositories/knex/disciplines.repositories';
import { KnexDisciplinesCategoriesRepository } from '../../repositories/knex/disciplinesCategoriesAccess.repositories';
import { KnexDisciplinesSubcategoriesAccessRepository } from '../../repositories/knex/disciplinesSubcategoriesAccess.repositories';
import { KnexSubcategoriesRepository } from '../../repositories/knex/subcategories.repositories';
import { ManageSubcategoryInDisciplineUseCase } from '../subcategories/manageSubcategoryInDiscipline/manageSubcategoryInDiscipline.useCase';

export function factoryManageSubcategoryInDisciplineUseCase() {
  const disciplineRepository = new KnexDisciplinesRepository();
  const categoriesRepository = new KnexCategoriesRepository();
  const disciplinesSubcategoriesAccess = new KnexDisciplinesSubcategoriesAccessRepository();
  const subCategoriesRepository = new KnexSubcategoriesRepository();
  const disciplineCategoryAccesRepository = new KnexDisciplinesCategoriesRepository();

  const useCase = new ManageSubcategoryInDisciplineUseCase(
    disciplineRepository,
    categoriesRepository,
    disciplineCategoryAccesRepository,
    disciplinesSubcategoriesAccess,
    subCategoriesRepository
  );

  return useCase;
}
