import { KnexDisciplineAccessRepository } from '../../repositories/knex/disciplineAccess.repositories';
import { KnexDisciplinesRepository } from '../../repositories/knex/disciplines.repositories';
import { KnexDisciplinesCategoriesRepository } from '../../repositories/knex/disciplinesCategoriesAccess.repositories';
import { KnexDisciplinesSubcategoriesAccessRepository } from '../../repositories/knex/disciplinesSubcategoriesAccess.repositories';
import { KnexQuestionsRepository } from '../../repositories/knex/questions.repositories';
import { KnexQuestionsGroupsRepository } from '../../repositories/knex/questionsGroups.repositories';
import { ListDisciplinesWithAccessHierarchyUseCase } from '../Access/listDisciplinesWithAccessHierarchy.useCase';
import { CreateDisciplineUseCase } from '../disciplines/createDisciplines/createDisciplines.useCase';
import { DeleteDisciplinesUseCase } from '../disciplines/deleteDisciplines/deleteDisciplines.useCase';
import { ListDisciplinesUseCase } from '../disciplines/listDisciplines/listDisciplines.useCase';
import { ListDisciplinesWithQuestionCountUseCase } from '../disciplines/listDisciplinesWithQuestionCount/listDisciplinesWithQuestionCount.useCase';
import { UpdateDisciplineUseCase } from '../disciplines/updateDisciplines/updateDisciplines.useCase';

const disciplinesRepository = new KnexDisciplinesRepository();
const disciplinesAccessRepository = new KnexDisciplineAccessRepository();
const disciplinesCategoriesAccessRepository = new KnexDisciplinesCategoriesRepository();
const disciplinesSubcategoriesRepository = new KnexDisciplinesSubcategoriesAccessRepository();
const questionsRepository = new KnexQuestionsRepository();
const questioGroupRepostory = new KnexQuestionsGroupsRepository();

export function factoryDeleteDisciplineUseCase() {
  return new DeleteDisciplinesUseCase(
    disciplinesRepository,
    disciplinesAccessRepository,
    disciplinesCategoriesAccessRepository,
    disciplinesSubcategoriesRepository,
    questionsRepository
  );
}

export function factoryCreateDisciplineUseCase() {
  return new CreateDisciplineUseCase(disciplinesRepository);
}

export function factoryUpdateDisciplineUseCase() {
  return new UpdateDisciplineUseCase(disciplinesRepository);
}

export function factoryListDisciplinesUseCase() {
  return new ListDisciplinesUseCase(disciplinesRepository);
}

export async function factoryListDisciplinesWithAccessHierarchyUseCase() {
  return new ListDisciplinesWithAccessHierarchyUseCase(disciplinesRepository);
}

export async function factoryListDisciplinesWithQuestionCountUseCase() {
  return new ListDisciplinesWithQuestionCountUseCase(disciplinesRepository, questioGroupRepostory);
}
