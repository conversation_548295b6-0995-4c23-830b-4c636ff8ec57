import AWS from 'aws-sdk';

import { KnexDomainsRepository } from '../../repositories/knex/domains.repositories';
import { CreateDomainCustomerUseCase } from '../domains/createDomain/createDomain.useCase';
import { ProcessPendingDomainsUseCase } from '../domains/processPendingDomains/processPendingDomains.useCase';

const domainsRepository = new KnexDomainsRepository();
const acm = new AWS.ACM({ region: 'us-east-1' });
const route53 = new AWS.Route53();
const cloudfront = new AWS.CloudFront();
const s3 = new AWS.S3();

export function factoryCreateDomainCustomerUseCase(): CreateDomainCustomerUseCase {
  return new CreateDomainCustomerUseCase(domainsRepository, acm, route53);
}

export function factoryProcessPendingDomainsUseCase(): ProcessPendingDomainsUseCase {
  return new ProcessPendingDomainsUseCase(domainsRepository, acm, route53, cloudfront, s3);
}
