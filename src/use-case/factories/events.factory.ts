import AWS from 'aws-sdk';

import { KnexAccessLevelsRepository } from '../../repositories/knex/accessLevels.repositories';
import { KnexCustomerRepository } from '../../repositories/knex/customers.repositories';
import { KnexRecoveryRepository } from '../../repositories/knex/recoveries.repositories';
import { KnexUserRepository } from '../../repositories/knex/users.repositories';
import { SESService } from '../../services/aws/ses/SESService';
import { CreateDomainUseCase } from '../events/createDomain.useCase';
import { CreateSubdomainUseCase } from '../events/createSubdomain.useCase';

const customerRepository = new KnexCustomerRepository();
const userRepository = new KnexUserRepository();
const accessLevelRepository = new KnexAccessLevelsRepository();
const recoveryRepository = new KnexRecoveryRepository();
const sesService = new SESService();
const acm = new AWS.ACM({ region: 'us-east-1' });
const sqs = new AWS.SQS({ region: 'us-east-1' });
const route53 = new AWS.Route53();

export function factoryCreateDomainUseCase(): CreateDomainUseCase {
  return new CreateDomainUseCase(acm, sqs, route53);
}

export function factoryCreateSubdomainUseCase(): CreateSubdomainUseCase {
  return new CreateSubdomainUseCase(
    customerRepository,
    userRepository,
    accessLevelRepository,
    recoveryRepository,
    sesService
  );
}
