import { KnexAnswerSessionsRepository } from '../../repositories/knex/answerSessions.repositories';
import { KnexCourseExamsRepository } from '../../repositories/knex/coursesExams.repositories';
import { KnexCustomerRepository } from '../../repositories/knex/customers.repositories';
import { KnexExamsRepository } from '../../repositories/knex/exams.repositories';
import { KnexExamsAccessRepository } from '../../repositories/knex/examsAccess.repositories';
import { KnexExamsQuestionsRepository } from '../../repositories/knex/examsQuestions.repositories';
import { KnexQuestionsRepository } from '../../repositories/knex/questions.repositories';
import { KnexQuestionsExamsAnsweredRepository } from '../../repositories/knex/questionsExamsAnswered.repositories';
import { AnswerExamQuestionUseCase } from '../exams/answerExamQuestion/answerExamQuestion.useCase';
import { CreateExamUseCase } from '../exams/createExam/createExam.useCase';
import { DeleteExamUseCase } from '../exams/deleteExam/deleteExam.useCase';
import { ExternalListExamsUseCase } from '../exams/externalListExams/externalListExams.useCase';
import { FinishExamUseCase } from '../exams/finishExam/finishExam.useCase';
import { GetExamWithQuestionsUseCase } from '../exams/getExamQuestions/getExamQuestions.useCase';
import { GetExamsUseCase } from '../exams/getExams.useCase';
import { LinkQuestionsToExamUseCase } from '../exams/linkQuestionsToExam/linkQuestionsToExam.useCase';
import { ListExamsUseCase } from '../exams/listExams/listExams.useCase';
import { ListExamsFiltersUseCase } from '../exams/listExamsFilters/listExamsFilters.useCase';
import { ListPublishedExamsPaginatedUseCase } from '../exams/listPublishedExamsPaginated/listPublishedExamsPaginated.useCase';
import { ListPublishedQuestionsFromExamUseCase } from '../exams/listPublishedQuestionsFromExam/listPublishedQuestionsFromExam.useCase';
import { ListUserExamsUseCase } from '../exams/listUserExams/listUserExams.useCase';
import { PauseResumeExamUseCase } from '../exams/pauseResumeExam/pauseResumeExam.useCase';
import { ReorderExamQuestionsUseCase } from '../exams/reorderExamQuestion/reorderExamQuestion.useCase';
import { StartExamSessionUseCase } from '../exams/startExamSession/startExamSession.useCase';
import { UnlinkQuestionFromExamUseCase } from '../exams/unlinkQuestionFromExam/unlinkQuestionFromExam.useCase';
import { UpdateExamUseCase } from '../exams/updateExam/updateExam.useCase';

const examsRepository = new KnexExamsRepository();
const questionsRepository = new KnexQuestionsRepository();
const examsQuestionsRepository = new KnexExamsQuestionsRepository();
const examsAccessRepository = new KnexExamsAccessRepository();
const answerSessionsRepository = new KnexAnswerSessionsRepository();
const questionsExamsAnsweredRepository = new KnexQuestionsExamsAnsweredRepository();
const customerRepository = new KnexCustomerRepository();
const courseExamsRepository = new KnexCourseExamsRepository();

export function factoryCreateExamUseCase(): CreateExamUseCase {
  return new CreateExamUseCase(examsRepository);
}

export function factoryListExamsUseCase(): ListExamsUseCase {
  return new ListExamsUseCase(examsRepository);
}

export function factoryExternalListExamsUseCase() {
  return new ExternalListExamsUseCase(examsRepository, customerRepository);
}

export function factoryUpdateExamUseCase(): UpdateExamUseCase {
  return new UpdateExamUseCase(examsRepository, examsQuestionsRepository, questionsRepository);
}

export function factoryDeleteExamUseCase(): DeleteExamUseCase {
  return new DeleteExamUseCase(examsRepository);
}

export function factoryLinkQuestionsToExamUseCase(): LinkQuestionsToExamUseCase {
  return new LinkQuestionsToExamUseCase(
    examsRepository,
    questionsRepository,
    examsQuestionsRepository
  );
}

export function factoryListPublishedExamsPaginatedUseCase(): ListPublishedExamsPaginatedUseCase {
  return new ListPublishedExamsPaginatedUseCase(examsRepository, courseExamsRepository);
}

export function factoryStartExamSessionUseCase(): StartExamSessionUseCase {
  return new StartExamSessionUseCase(
    examsRepository,
    examsAccessRepository,
    examsQuestionsRepository
  );
}

export function factoryGetExamQuestionsUseCase(): GetExamWithQuestionsUseCase {
  return new GetExamWithQuestionsUseCase(
    examsAccessRepository,
    questionsRepository,
    answerSessionsRepository,
    examsRepository
  );
}

export function factoryManageExamSessionUseCase(): PauseResumeExamUseCase {
  return new PauseResumeExamUseCase(examsAccessRepository, answerSessionsRepository);
}

export function factoryUnlinkQuestionFromExamUseCase(): UnlinkQuestionFromExamUseCase {
  return new UnlinkQuestionFromExamUseCase(examsRepository, examsQuestionsRepository);
}

export function factoryListExamsFiltersUseCase(): ListExamsFiltersUseCase {
  return new ListExamsFiltersUseCase(examsRepository, courseExamsRepository);
}

export function factoryFinishExamUseCase(): FinishExamUseCase {
  return new FinishExamUseCase(examsRepository, examsAccessRepository, answerSessionsRepository);
}

export function factoryListUserExamsUseCase(): ListUserExamsUseCase {
  return new ListUserExamsUseCase(examsRepository);
}

export function factoryReorderExamQuestionUseCase(): ReorderExamQuestionsUseCase {
  return new ReorderExamQuestionsUseCase(examsRepository, examsQuestionsRepository);
}

export function factoryAnswerExamQuestionUseCase(): AnswerExamQuestionUseCase {
  return new AnswerExamQuestionUseCase(
    questionsExamsAnsweredRepository,
    examsAccessRepository,
    examsQuestionsRepository
  );
}

export function factoryListPublishedQuestionsFromExamUseCase() {
  return new ListPublishedQuestionsFromExamUseCase(examsRepository);
}

export function factoryGetExamsUseCase(): GetExamsUseCase {
  return new GetExamsUseCase(examsRepository);
}
