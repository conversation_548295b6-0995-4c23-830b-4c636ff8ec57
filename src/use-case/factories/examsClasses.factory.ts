import { KnexClassesRepository } from '../../repositories/knex/classes.repositories';
import { KnexCustomerRepository } from '../../repositories/knex/customers.repositories';
import { KnexExamsRepository } from '../../repositories/knex/exams.repositories';
import { KnexExamsClassesRepository } from '../../repositories/knex/examsClasses.repositories';
import { LinkExamsToExternalClassUseCase } from '../classes/linkExamsToExternalClass/linkExamsToExternalClass.useCase';

const examsClassesRepository = new KnexExamsClassesRepository();
const classesRepository = new KnexClassesRepository();
const examsRepository = new KnexExamsRepository();
const customerRepository = new KnexCustomerRepository();

export function factoryLinkExamsToExternalClassUseCase() {
  return new LinkExamsToExternalClassUseCase(
    classesRepository,
    examsRepository,
    examsClassesRepository,
    customerRepository
  );
}
