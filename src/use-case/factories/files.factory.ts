import { KnexFilesRepository } from '../../repositories/knex/files.repositories';
import { KnexUserRepository } from '../../repositories/knex/users.repositories';
import S3Service from '../../services/aws/s3/S3Service';
import { UploadService } from '../../services/uploadFile.service';
import { GetSignedFileUrlUseCase } from '../files/getSignedFileUrl.useCase';
import { UploadFileUseCase } from '../files/uploadFile.useCase';

const filesRepository = new KnexFilesRepository();
const userRepository = new KnexUserRepository();
const s3Service = new S3Service();
const uploadService = new UploadService();

export function factoryUploadFileUseCase() {
  return new UploadFileUseCase(uploadService, userRepository);
}

export function factoryGetSignedFileUrlUseCase() {
  return new GetSignedFileUrlUseCase(filesRepository, s3Service);
}
