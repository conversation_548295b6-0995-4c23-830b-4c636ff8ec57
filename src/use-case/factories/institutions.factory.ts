import { KnexExamsRepository } from '../../repositories/knex/exams.repositories';
import { KnexInstitutionsRepository } from '../../repositories/knex/institutions.repositories';
import { CreateInstitutionUseCase } from '../institutions/createInstitution/createInstitution.useCase';
import { DeleteInstitutionUseCase } from '../institutions/deleteInstitution/deleteInstitution.useCase';
import { ListInstitutionsUseCase } from '../institutions/listInstitutions/listInstitutions.useCase';
import { ListInstitutionsPublicUseCase } from '../institutions/listInstitutionsPublic/listInstitutionsPublic.useCase';
import { UpdateInstitutionUseCase } from '../institutions/updateInstitution/updateInstitution.useCase';

const institutionsRepository = new KnexInstitutionsRepository();
const examsRepository = new KnexExamsRepository();

export function factoryCreateInstitutionUseCase() {
  return new CreateInstitutionUseCase(institutionsRepository);
}

export function factoryListInstitutionsUseCase() {
  return new ListInstitutionsUseCase(institutionsRepository);
}

export function factoryListInstitutionsPublicUseCase() {
  return new ListInstitutionsPublicUseCase(institutionsRepository);
}

export function factoryUpdateInstitutionUseCase() {
  return new UpdateInstitutionUseCase(institutionsRepository);
}

export function factoryDeleteInstitutionUseCase() {
  return new DeleteInstitutionUseCase(institutionsRepository, examsRepository);
}
