import { KnexCourseLessonsRepository } from '../../repositories/knex/courseLessons.repositories';
import { KnexFilesRepository } from '../../repositories/knex/files.repositories';
import { KnexCourseModulesRepository } from '../../repositories/knex/modules.repositories';
import { CreateLessonUseCase } from '../lessons/createLesson.useCase';
import { DeleteLessonUseCase } from '../lessons/deleteLesson.useCase';
import { GetLessonByIdUseCase } from '../lessons/getLessonById.useCase';
import { ReorderLessonsUseCase } from '../lessons/reorderLessons.useCase';
import { UpdateLessonUseCase } from '../lessons/updateLesson.useCase';

const lessonRepository = new KnexCourseLessonsRepository();
const moduleRepository = new KnexCourseModulesRepository();
const filesRepository = new KnexFilesRepository();

export function factoryCreateLessonUseCase(): CreateLessonUseCase {
  return new CreateLessonUseCase(lessonRepository, filesRepository);
}

export function factoryUpdateLessonUseCase() {
  return new UpdateLessonUseCase(lessonRepository, moduleRepository, filesRepository);
}

export function factoryReorderLessonsUseCase() {
  return new ReorderLessonsUseCase(lessonRepository);
}

export function factoryGetLessonByIdUseCase() {
  return new GetLessonByIdUseCase(lessonRepository);
}

export function factoryDeleteLessonUseCase() {
  return new DeleteLessonUseCase(lessonRepository);
}
