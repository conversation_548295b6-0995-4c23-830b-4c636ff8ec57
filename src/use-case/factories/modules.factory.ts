import { KnexCourseLessonsRepository } from '../../repositories/knex/courseLessons.repositories';
import { KnexCourseModulesRepository } from '../../repositories/knex/modules.repositories';
import { CreateModuleUseCase } from '../modules/createModule.useCase';
import { CreateSimpleModuleUseCase } from '../modules/createSimpleModule/createSimpleModule.useCase';
import { DeleteModuleUseCase } from '../modules/deleteModule.useCase';
import { ReorderModulesUseCase } from '../modules/reorderModules.useCase';
import { UpdateModuleUseCase } from '../modules/updateModule.useCase';

const moduleRepository = new KnexCourseModulesRepository();
const lessonRepository = new KnexCourseLessonsRepository();

export function factoryCreateModuleUseCase() {
  return new CreateModuleUseCase(moduleRepository, lessonRepository);
}

export function factoryCreateSimpleModuleUseCase() {
  return new CreateSimpleModuleUseCase(moduleRepository);
}

export function factoryUpdateModuleUseCase() {
  return new UpdateModuleUseCase(moduleRepository, lessonRepository);
}

export function factoryReorderModulesUseCase() {
  return new ReorderModulesUseCase(moduleRepository);
}

export function factoryDeleteModuleUseCase() {
  return new DeleteModuleUseCase(moduleRepository, lessonRepository);
}
