import { KnexAggregateDataRepository } from '../../repositories/knex/aggregatedData.repositories';
import { KnexAlternativesRepository } from '../../repositories/knex/alternatives.repositories';
import { KnexDisciplinesRepository } from '../../repositories/knex/disciplines.repositories';
import { KnexExamsRepository } from '../../repositories/knex/exams.repositories';
import { KnexExamsAccessRepository } from '../../repositories/knex/examsAccess.repositories';
import { KnexExamsQuestionsRepository } from '../../repositories/knex/examsQuestions.repositories';
import { KnexQuestionsRepository } from '../../repositories/knex/questions.repositories';
import { KnexQuestionsGroupsRepository } from '../../repositories/knex/questionsGroups.repositories';
import { KnexQuestionsGroupsAccessRepository } from '../../repositories/knex/questionsGroupsAccess.repositories';
import { KnexQuestionsHighlightsRepository } from '../../repositories/knex/questionsHighlights.repositories';
import { KnexQuestionsSimulatedsRepository } from '../../repositories/knex/questionsSimulateds.repositories';
import { KnexSimulatedsRepository } from '../../repositories/knex/simulated.repositories';
import { QuestionCodeGeneratorService } from '../../services/questionCodeGenerator.service';
import { UploadService } from '../../services/uploadFile.service';
import { CreateQuestionUseCase } from '../questions/createQuestion/create-question.useCase';
import { DeleteQuestionUseCase } from '../questions/deleteQuestion/deleteQuestion.useCase';
import { GetQuestionByIdUseCase } from '../questions/getQuestionById/getQuestionById.useCase';
import { ListPaginatedQuestionsUseCase } from '../questions/listPaginatedQuestions/listPaginatedQuestions.useCase';
import { ListQuestionsInGroupUseCase } from '../questions/listQuestionInGroup/listQuestionInGroup.useCase';
import { ManageQuestionHighlightsUseCase } from '../questions/manageQuestionHighlights/manageQuestionHighlights.useCase';
import { RemoveQuestionImageUseCase } from '../questions/removeQuestionImage/removeQuestionImage.useCase';
import { UpdateQuestionUseCase } from '../questions/updateQuestion/updateQuestion.useCase';
import { UploadQuestionImageUseCase } from '../questions/uploadImageQuestion/uploadQuestionImage.useCase';

const questionsRepository = new KnexQuestionsRepository();
const alternativesRepository = new KnexAlternativesRepository();
const disciplinesRepository = new KnexDisciplinesRepository();
const aggregatedDataRepository = new KnexAggregateDataRepository();
const questionsGroupsAccessRepository = new KnexQuestionsGroupsAccessRepository();
const questionGroupRepository = new KnexQuestionsGroupsRepository();
const questionsHighlightsRepository = new KnexQuestionsHighlightsRepository();
const simulatedsRepository = new KnexSimulatedsRepository();
const examsRepository = new KnexExamsRepository();
const examsAccessRepository = new KnexExamsAccessRepository();
const examsQuestionsRepository = new KnexExamsQuestionsRepository();
const uploadService = new UploadService();
const questionsSimulatedsRepository = new KnexQuestionsSimulatedsRepository();

export function factoryCreateQuestionUseCase() {
  return new CreateQuestionUseCase(
    questionsRepository,
    alternativesRepository,
    disciplinesRepository,
    aggregatedDataRepository
  );
}

export function factoryDeleteQuestionUseCase() {
  return new DeleteQuestionUseCase(
    questionsRepository,
    questionsGroupsAccessRepository,
    alternativesRepository,
    examsQuestionsRepository,
    questionsSimulatedsRepository
  );
}

export function factoryListPaginatedQuestionsUseCase() {
  return new ListPaginatedQuestionsUseCase(questionsRepository);
}

export function factoryUpdateQuestionUseCase() {
  return new UpdateQuestionUseCase(
    questionsRepository,
    alternativesRepository,
    aggregatedDataRepository
  );
}

export function factoryGenerateCodeQuestionService() {
  return new QuestionCodeGeneratorService(questionsRepository);
}

export function factoryGetQuestionByIdUseCase() {
  return new GetQuestionByIdUseCase(questionsRepository);
}

export function fatoryUploadQuestionImageUseCase() {
  return new UploadQuestionImageUseCase(uploadService);
}

export function factoryDeleteImageUseCase() {
  return new RemoveQuestionImageUseCase(uploadService);
}

export function factoryListQuestionInGroupUseCase() {
  return new ListQuestionsInGroupUseCase(questionsRepository, questionGroupRepository);
}

export function factoryManageQuestionHighlightsUseCase() {
  return new ManageQuestionHighlightsUseCase(
    questionsHighlightsRepository,
    questionsRepository,
    simulatedsRepository,
    examsRepository,
    examsAccessRepository
  );
}
