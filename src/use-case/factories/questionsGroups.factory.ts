import { KnexAggregateDataRepository } from '../../repositories/knex/aggregatedData.repositories';
import { KnexCustomerRepository } from '../../repositories/knex/customers.repositories';
import { KnexQuestionsRepository } from '../../repositories/knex/questions.repositories';
import { KnexQuestionsGroupsRepository } from '../../repositories/knex/questionsGroups.repositories';
import { KnexQuestionsGroupsAccessRepository } from '../../repositories/knex/questionsGroupsAccess.repositories';
import { CreateQuestionGroupUseCase } from '../questionsGroups/createQuestionsGroups/createQuestionGroup.useCase';
import { DeleteQuestionGroupUseCase } from '../questionsGroups/deleteQuestionGroup/deleteQuestionGroup.useCase';
import { GetExternalQuestionGroupUseCase } from '../questionsGroups/getExternalQuestionGroup/getExternalQuestionGroup.useCase';
import { GetQuestionsGroupsWithAccessUseCase } from '../questionsGroups/getQuestionsGroupsWithAccess/getQuestionsGroupsWithAccess.useCase';
import { ListPublishedQuestionsFromGroupUseCase } from '../questionsGroups/listPublishedQuestionsFromGroup/listPublishedQuestionsFromGroup.useCase';
import { ListQuestionGroupUseCase } from '../questionsGroups/listQuestionGroup/listQuestionGroup.useCase';
import { ListQuestionGroupWithCountUseCase } from '../questionsGroups/listQuestionGroupsWithCount/listQuestionGroupWithCount.useCase';
import { UpdateQuestionGroupUseCase } from '../questionsGroups/updateQuestionGroup/updateQuestionGroup.useCase';

const questionsGroupsRepository = new KnexQuestionsGroupsRepository();
const aggregateDataRepository = new KnexAggregateDataRepository();
const questionsGroupsAccessRepository = new KnexQuestionsGroupsAccessRepository();
const questionsRepository = new KnexQuestionsRepository();
const customerRepository = new KnexCustomerRepository();

export function factoryCreateQuestionGroupUseCase() {
  return new CreateQuestionGroupUseCase(questionsGroupsRepository);
}

export function factoryUpdateQuestionGroupUseCase() {
  return new UpdateQuestionGroupUseCase(questionsGroupsRepository);
}

export function factoryDeleteQuestionGroupUseCase() {
  return new DeleteQuestionGroupUseCase(questionsGroupsRepository, questionsGroupsAccessRepository);
}

export function factoryListQuestionGroupUseCase() {
  return new ListQuestionGroupUseCase(questionsGroupsRepository, customerRepository);
}

export function factoryListQuestionsGroupsUseCase() {
  return new GetQuestionsGroupsWithAccessUseCase(
    questionsGroupsRepository,
    aggregateDataRepository
  );
}

export function factoryListPublishedQuestionsFromGroupUseCase() {
  return new ListPublishedQuestionsFromGroupUseCase(questionsRepository, questionsGroupsRepository);
}

export function factoryGetExternalQuestionGroupUseCase() {
  return new GetExternalQuestionGroupUseCase(
    questionsGroupsRepository,
    questionsRepository,
    customerRepository
  );
}

export function factoryListQuestionGroupWithCountUseCase(): ListQuestionGroupWithCountUseCase {
  return new ListQuestionGroupWithCountUseCase(questionsGroupsRepository);
}
