import { KnexAggregateDataRepository } from '../../repositories/knex/aggregatedData.repositories';
import { KnexQuestionsGroupsAccessRepository } from '../../repositories/knex/questionsGroupsAccess.repositories';
import { ManageQuestionGroupAccessUseCase } from '../questionsGroupsAccess/manageQuestionGroupAccess/manageQuestionGroupAccess.useCase';

const questionsGroupsAccessFactory = new KnexQuestionsGroupsAccessRepository();
const aggregateDataRepository = new KnexAggregateDataRepository();

export function factoryManageQuestionGroupAccessUseCase() {
  return new ManageQuestionGroupAccessUseCase(
    questionsGroupsAccessFactory,
    aggregateDataRepository
  );
}
