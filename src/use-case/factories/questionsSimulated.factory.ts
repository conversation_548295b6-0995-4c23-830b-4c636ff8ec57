import { QuestionsSimulatedAnsweredRepository } from '../../repositories/knex/questionsSimulatedAnswered.repositories';
import { KnexQuestionsSimulatedsRepository } from '../../repositories/knex/questionsSimulateds.repositories';
import { KnexSimulatedsRepository } from '../../repositories/knex/simulated.repositories';
import { KnexSimulatedsAccessRepository } from '../../repositories/knex/simulatedsAccess.repositories';
import { AnswerQuestionSimulatedUseCase } from '../simulateds/answerQuestionSimulated/answerQuestionSimulated.useCase';
import { GetAllQuestionsSimulatedUseCase } from '../simulateds/getAllQuestions/getAllQuestionsSimulated.useCase';

const questionsSimulatedRepository = new KnexQuestionsSimulatedsRepository();
const simulatedAccessRepository = new KnexSimulatedsAccessRepository();
const questionsSimulatedAnsweredRepository = new QuestionsSimulatedAnsweredRepository();
const simulatedRepository = new KnexSimulatedsRepository();

export function factoryGetAllQuestionsSimulatedUseCase() {
  return new GetAllQuestionsSimulatedUseCase(questionsSimulatedRepository, simulatedRepository);
}

export function factoryAnswerQuestionSimulatedUseCase() {
  return new AnswerQuestionSimulatedUseCase(
    questionsSimulatedRepository,
    simulatedAccessRepository,
    questionsSimulatedAnsweredRepository
  );
}
