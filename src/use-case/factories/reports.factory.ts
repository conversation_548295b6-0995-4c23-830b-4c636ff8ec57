import { KnexCustomerRepository } from '../../repositories/knex/customers.repositories';
import { KnexQuestionsRepository } from '../../repositories/knex/questions.repositories';
import { KnexReportsRepository } from '../../repositories/knex/reports.repositories';
import { KnexUserRepository } from '../../repositories/knex/users.repositories';
import { EmailService } from '../../services/email.service';
import { CreateQuestionReportUseCase } from '../reports/createQuestionReport/createQuestionReport.useCase';
import { ListReportsUseCase } from '../reports/listReports/listReports.useCase';

export function factoryCreateQuestionReportUseCase() {
  const reportsRepository = new KnexReportsRepository();
  const questionsRepository = new KnexQuestionsRepository();
  const usersRepository = new KnexUserRepository();
  const emailService = new EmailService();
  const customerRepository = new KnexCustomerRepository();

  return new CreateQuestionReportUseCase(
    reportsRepository,
    questionsRepository,
    usersRepository,
    customerRepository,
    emailService
  );
}

export function factoryListReportsUseCase() {
  const reportsRepository = new KnexReportsRepository();
  return new ListReportsUseCase(reportsRepository);
}
