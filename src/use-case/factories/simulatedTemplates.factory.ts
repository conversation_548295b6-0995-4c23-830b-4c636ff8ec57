import { KnexQuestionsRepository } from '../../repositories/knex/questions.repositories';
import { KnexQuestionsGroupsRepository } from '../../repositories/knex/questionsGroups.repositories';
import { KnexQuestionsSimulatedsRepository } from '../../repositories/knex/questionsSimulateds.repositories';
import { KnexSimulatedsRepository } from '../../repositories/knex/simulated.repositories';
import { KnexSimulatedsAccessRepository } from '../../repositories/knex/simulatedsAccess.repositories';
import { KnexSimulatedTypesRepository } from '../../repositories/knex/simulatedsTypes.repositories';
import { KnexSimulatedTemplateRepository } from '../../repositories/knex/simulatedTemplates.repositories';
import { KnexSimulatedTemplateQuestionRepository } from '../../repositories/knex/simulatedTemplatesQuestions.repositories';
import { CreateSimulatedTemplateUseCase } from '../simulatedTemplates/createSimulatedTemplate/createSimulatedTemplate.useCase';
import { DeleteSimulatedTemplateUseCase } from '../simulatedTemplates/deleteSimulatedTemplate/deleteSimulatedTemplate.useCase';
import { GetSimulatedTemplatesCountUseCase } from '../simulatedTemplates/getTotalSimulatedTemplates/getSimulatedTemplatesCount.useCase';
import { ListPublishedQuestionsFromTemplateUseCase } from '../simulatedTemplates/listPublishedQuestionsFromTemplate/listPublishedQuestionsFromTemplate.useCase';
import { ListSimulatedTemplatesUseCase } from '../simulatedTemplates/listSimulatedTemplates/listSimulatedTemplates.useCase';
import { StartSimulatedFromTemplateUseCase } from '../simulatedTemplates/startSimulatedFromTemplate/startSimulatedFromTemplate.useCase';

const simulatedTemplateRepository = new KnexSimulatedTemplateRepository();
const simulatedTemplateQuestionRepository = new KnexSimulatedTemplateQuestionRepository();
const questionsRepository = new KnexQuestionsRepository();
const questionsGroupsRepository = new KnexQuestionsGroupsRepository();
const simulatedTypesRepository = new KnexSimulatedTypesRepository();
const simulatedsRepository = new KnexSimulatedsRepository();
const simulatedsAccessRepository = new KnexSimulatedsAccessRepository();
const questionsSimulatedsRepository = new KnexQuestionsSimulatedsRepository();

export function factoryCreateSimulatedTemplateUseCase() {
  return new CreateSimulatedTemplateUseCase(
    simulatedTemplateRepository,
    simulatedTemplateQuestionRepository,
    questionsRepository,
    questionsGroupsRepository,
    simulatedTypesRepository
  );
}

export function factoryGetSimulatedTemplatesCountUseCase() {
  return new GetSimulatedTemplatesCountUseCase(simulatedTemplateRepository);
}

export function factoryListSimulatedTemplatesUseCase() {
  return new ListSimulatedTemplatesUseCase(simulatedTemplateRepository);
}

export function factoryDeleteSimulatedTemplateUseCase() {
  return new DeleteSimulatedTemplateUseCase(
    simulatedTemplateRepository,
    simulatedTemplateQuestionRepository
  );
}

export function factoryListPublishedQuestionsFromTemplateUseCase(): ListPublishedQuestionsFromTemplateUseCase {
  const simulatedTemplateRepository = new KnexSimulatedTemplateRepository();

  return new ListPublishedQuestionsFromTemplateUseCase(simulatedTemplateRepository);
}

export function factoryStartSimulatedFromTemplateUseCase() {
  return new StartSimulatedFromTemplateUseCase(
    simulatedTemplateRepository,
    simulatedTemplateQuestionRepository,
    simulatedsRepository,
    simulatedsAccessRepository,
    questionsSimulatedsRepository
  );
}
