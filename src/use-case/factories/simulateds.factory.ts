import { KnexAggregateDataRepository } from '../../repositories/knex/aggregatedData.repositories';
import { KnexAnswerSessionsRepository } from '../../repositories/knex/answerSessions.repositories';
import { KnexClassesRepository } from '../../repositories/knex/classes.repositories';
import { KnexQuestionsRepository } from '../../repositories/knex/questions.repositories';
import { KnexQuestionsSimulatedsRepository } from '../../repositories/knex/questionsSimulateds.repositories';
import { KnexSimulatedsRepository } from '../../repositories/knex/simulated.repositories';
import { KnexSimulatedsAccessRepository } from '../../repositories/knex/simulatedsAccess.repositories';
import { KnexSimulatedsClassesRepository } from '../../repositories/knex/simulatedsClasses.repositories';
import { KnexSimulatedTypesRepository } from '../../repositories/knex/simulatedsTypes.repositories';
import { KnexUserClassesRepository } from '../../repositories/knex/usersClasses.repositories';
import { CreateExclusiveSimulatedUseCase } from '../simulateds/createExclusiveSimulated/createExclusiveSimulated.useCase';
import { FinishSimulatedUseCase } from '../simulateds/finishSimulated/finishSimulated.useCase';
import { GetAllQuestionsBySimulatedIdUseCase } from '../simulateds/getAllQuestionsBySimulatedId/getAllQuestionsBySimulatedId.useCase';
import { GetExclusiveSimulatedStatsUseCase } from '../simulateds/getExclusiveSimulatedStats.useCase';
import { GetSimulatedByIdUseCase } from '../simulateds/getSimulatedById/getSimulatedById.useCase';
import { LinkQuestionsToSimulatedUseCase } from '../simulateds/linkQuestionsToSimulated/linkQuestionsToSimulated.useCase';
import { ListExclusiveSimulatedsPaginatedUseCase } from '../simulateds/listExclusiveSimulatedsPaginated.useCase';
import { ListUserExclusiveSimulatedsUseCase } from '../simulateds/listUserExclusiveSimulateds/listUserExclusiveSimulateds.useCase';
import { ListUserExclusiveSimulatedsUserHistoryUseCase } from '../simulateds/listUserExclusiveSimulateds/listUserExclusiveSimulatedsUserHistory.useCase';
import { ListUserSimulatedsUseCase } from '../simulateds/listUserSimulateds/listUserSimulateds.useCase';
import { ListUsersInExclusiveSimulatedUseCase } from '../simulateds/listUsersInExclusiveSimulated/listUsersInExclusiveSimulated.useCase';
import { ManageClassesInExclusiveSimulatedUseCase } from '../simulateds/manageClassesInExclusiveSimulated/manageClassesInExclusiveSimulated.useCase';
import { ManageUserToExclusiveSimulatedUseCase } from '../simulateds/manageUserToExclusiveSimulated/manageUserToExclusiveSimulated.useCase';
import { PauseResumeSimulatedUseCase } from '../simulateds/pauseResumeSimulated/pauseResumeSimulated.useCase';
import { ReorderSimulatedQuestionsUseCase } from '../simulateds/reorderSimulatedQuestions/reorderSimulatedQuestions.useCase';
import { StartExclusiveSimulatedSessionUseCase } from '../simulateds/startExclusiveSimulatedSession/startExclusiveSimulatedSession.useCase';
import { UnlinkQuestionFromExclusiveSimulatedUseCase } from '../simulateds/unlinkQuestionFromExclusiveSimulated/unlinkQuestionFromExclusiveSimulated.useCase';
import { UpdateExclusiveSimulatedUseCase } from '../simulateds/updateExclusiveSimulated/updateExclusiveSimulated.useCase';

const simulatedsRepository = new KnexSimulatedsRepository();
const simulatedsAccessRepository = new KnexSimulatedsAccessRepository();
const simulatedTypesRepository = new KnexSimulatedTypesRepository();
const answerSessionsRepository = new KnexAnswerSessionsRepository();
const aggregateDataRepository = new KnexAggregateDataRepository();
const simulatedsTypesRepository = new KnexSimulatedTypesRepository();
const simulatedsClassesRepository = new KnexSimulatedsClassesRepository();
const userClassesRepository = new KnexUserClassesRepository();
const classesRepository = new KnexClassesRepository();
const questionsRepository = new KnexQuestionsRepository();
const questionsSimulatedsRepository = new KnexQuestionsSimulatedsRepository();

export function factoryGetSimulatedByIdUseCase(): GetSimulatedByIdUseCase {
  const simulatedRepository = new KnexSimulatedsRepository();

  const useCase = new GetSimulatedByIdUseCase(simulatedRepository);

  return useCase;
}

export function factoryListUserSimulatedsUseCase(): ListUserSimulatedsUseCase {
  return new ListUserSimulatedsUseCase(simulatedsRepository);
}

export function factoryFinishSimulatedUseCase(): FinishSimulatedUseCase {
  return new FinishSimulatedUseCase(
    simulatedsRepository,
    simulatedsAccessRepository,
    simulatedTypesRepository,
    answerSessionsRepository,
    aggregateDataRepository
  );
}

export function factoryPauseResumeSimulatedUseCase(): PauseResumeSimulatedUseCase {
  return new PauseResumeSimulatedUseCase(simulatedsAccessRepository, answerSessionsRepository);
}

export function factoryCreateExclusiveSimulatedUseCase(): CreateExclusiveSimulatedUseCase {
  return new CreateExclusiveSimulatedUseCase(simulatedsRepository, simulatedsTypesRepository);
}

export function factoryUpdateExclusiveSimulatedUseCase(): UpdateExclusiveSimulatedUseCase {
  return new UpdateExclusiveSimulatedUseCase(simulatedsRepository, simulatedsAccessRepository);
}

export function factoryListExclusiveSimulatedsPaginatedUseCase(): ListExclusiveSimulatedsPaginatedUseCase {
  return new ListExclusiveSimulatedsPaginatedUseCase(simulatedsRepository);
}

export function factoryManageClassesInExclusiveSimulatedUseCase(): ManageClassesInExclusiveSimulatedUseCase {
  return new ManageClassesInExclusiveSimulatedUseCase(
    simulatedsClassesRepository,
    userClassesRepository,
    simulatedsAccessRepository,
    simulatedsRepository,
    classesRepository
  );
}

export function factoryLinkQuestionsToSimulatedUseCase(): LinkQuestionsToSimulatedUseCase {
  return new LinkQuestionsToSimulatedUseCase(
    simulatedsRepository,
    questionsRepository,
    questionsSimulatedsRepository
  );
}

export function factoryManageUserToExclusiveSimulatedUseCase(): ManageUserToExclusiveSimulatedUseCase {
  return new ManageUserToExclusiveSimulatedUseCase(
    simulatedsRepository,
    simulatedsAccessRepository,
    classesRepository,
    simulatedsClassesRepository
  );
}

export function factoryReorderSimulatedQuestionsUseCase(): ReorderSimulatedQuestionsUseCase {
  return new ReorderSimulatedQuestionsUseCase(simulatedsRepository, questionsSimulatedsRepository);
}

export function factoryGetAllQuestionsBySimulatedIdUseCase() {
  return new GetAllQuestionsBySimulatedIdUseCase(
    questionsSimulatedsRepository,
    simulatedsRepository
  );
}

export function factoryListUsersInExclusiveSimulatedUseCase(): ListUsersInExclusiveSimulatedUseCase {
  return new ListUsersInExclusiveSimulatedUseCase(simulatedsAccessRepository);
}

export function factoryListUserExclusiveSimulatedsUseCase(): ListUserExclusiveSimulatedsUseCase {
  return new ListUserExclusiveSimulatedsUseCase(simulatedsAccessRepository);
}

export function factoryListUserExclusiveSimulatedsUserHistoryUseCase(): ListUserExclusiveSimulatedsUserHistoryUseCase {
  return new ListUserExclusiveSimulatedsUserHistoryUseCase(simulatedsAccessRepository);
}

export function factoryUnlinkQuestionFromExclusiveSimulatedUseCase(): UnlinkQuestionFromExclusiveSimulatedUseCase {
  return new UnlinkQuestionFromExclusiveSimulatedUseCase(
    simulatedsRepository,
    questionsSimulatedsRepository
  );
}

export function factoryStartExclusiveSimulatedSessionUseCase(): StartExclusiveSimulatedSessionUseCase {
  return new StartExclusiveSimulatedSessionUseCase(
    simulatedsRepository,
    simulatedsAccessRepository,
    simulatedTypesRepository
  );
}

export function factoryGetExclusiveSimulatedStatsUseCase(): GetExclusiveSimulatedStatsUseCase {
  return new GetExclusiveSimulatedStatsUseCase(simulatedsAccessRepository, simulatedsRepository);
}
