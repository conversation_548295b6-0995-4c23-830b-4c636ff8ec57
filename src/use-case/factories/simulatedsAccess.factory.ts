import { KnexAnswerSessionsRepository } from '../../repositories/knex/answerSessions.repositories';
import { KnexQuestionsSimulatedsRepository } from '../../repositories/knex/questionsSimulateds.repositories';
import { KnexSimulatedsAccessRepository } from '../../repositories/knex/simulatedsAccess.repositories';
import { GetSimulatedWithQuestionsByIdUseCase } from '../simulateds/getSimulatedWithQuestionsById/getSimulatedWithQuestionsById.useCase';

const questionsSimulatedRepository = new KnexQuestionsSimulatedsRepository();
const simulatedAccessRepository = new KnexSimulatedsAccessRepository();
const answerSessionsRepository = new KnexAnswerSessionsRepository();

export function factoryGetSimulatedWithQuestionsByIdUseCase() {
  return new GetSimulatedWithQuestionsByIdUseCase(
    questionsSimulatedRepository,
    simulatedAccessRepository,
    answerSessionsRepository
  );
}
