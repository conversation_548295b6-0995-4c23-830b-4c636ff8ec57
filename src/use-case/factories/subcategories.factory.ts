import { KnexCategoriesRepository } from '../../repositories/knex/categories.repositories';
import { KnexDisciplinesSubcategoriesAccessRepository } from '../../repositories/knex/disciplinesSubcategoriesAccess.repositories';
import { KnexQuestionsRepository } from '../../repositories/knex/questions.repositories';
import { KnexSubcategoriesRepository } from '../../repositories/knex/subcategories.repositories';
import { AddSubcategoryUseCase } from '../subcategories/addSubcategory/addSubcategory.useCase';
import { DeleteSubcategoryUseCase } from '../subcategories/deleteSubcategory/deleteSubcategory.useCase';
import { UpdateSubcategoryUseCase } from '../subcategories/updateSubcategory/updateSubcategory.useCase';

export function factoryAddSubcategoryUseCase() {
  const categoryRepository = new KnexCategoriesRepository();
  const subcategoryRepository = new KnexSubcategoriesRepository();

  const useCase = new AddSubcategoryUseCase(subcategoryRepository, categoryRepository);

  return useCase;
}

export const factoryUpdateSubcategoryUseCase = () => {
  const subcategoriesRepository = new KnexSubcategoriesRepository();
  return new UpdateSubcategoryUseCase(subcategoriesRepository);
};

export const factoryDeleteSubcategoryUseCase = () => {
  const subcategoriesRepository = new KnexSubcategoriesRepository();
  const disciplineSubcategoryAccessRepository = new KnexDisciplinesSubcategoriesAccessRepository();
  const questionsRepository = new KnexQuestionsRepository();

  const useCase = new DeleteSubcategoryUseCase(
    subcategoriesRepository,
    disciplineSubcategoryAccessRepository,
    questionsRepository
  );

  return useCase;
};
