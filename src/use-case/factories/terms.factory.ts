import { KnexFilesRepository } from '../../repositories/knex/files.repositories';
import { KnexTermsRepository } from '../../repositories/knex/terms.repositories';
import S3Service from '../../services/aws/s3/S3Service';
import { UploadService } from '../../services/uploadFile.service';
import { CreateTermUseCase } from '../terms/createTerm.useCase';
import { GetTermUseCase } from '../terms/getTerm.useCase';
import { ListAllTermsUseCase } from '../terms/listTerms.useCase';
import { UpdateTermUseCase } from '../terms/updateTerm.useCase';

const termsRepository = new KnexTermsRepository();
const filesRepository = new KnexFilesRepository();
const uploadService = new UploadService();
const s3Service = new S3Service();

export function factoryCreateTermUseCase() {
  return new CreateTermUseCase(termsRepository, filesRepository, uploadService);
}

export function factoryUpdateTermUseCase() {
  return new UpdateTermUseCase(termsRepository);
}

export function factoryListAllTermsUseCase() {
  return new ListAllTermsUseCase(termsRepository);
}

export function factoryGetTermUseCase() {
  return new GetTermUseCase(termsRepository, filesRepository, s3Service);
}
