import { KnexAccessLevelsRepository } from '../../repositories/knex/accessLevels.repositories';
import { KnexClassesRepository } from '../../repositories/knex/classes.repositories';
import { KnexCountriesRepository } from '../../repositories/knex/countries.repositories';
import { KnexCourseRepository } from '../../repositories/knex/courses.repositories';
import { KnexCustomerRepository } from '../../repositories/knex/customers.repositories';
import { KnexFilesRepository } from '../../repositories/knex/files.repositories';
import { KnexRecoveryRepository } from '../../repositories/knex/recoveries.repositories';
import { KnexTermsRepository } from '../../repositories/knex/terms.repositories';
import { KnexUserRepository } from '../../repositories/knex/users.repositories';
import { KnexUserClassesRepository } from '../../repositories/knex/usersClasses.repositories';
import { KnexUsersTypesRepository } from '../../repositories/knex/userTypes.repositories';
import S3Service from '../../services/aws/s3/S3Service';
import { SESService } from '../../services/aws/ses/SESService';
import { AddStudentsByEmailUseCase } from '../students/addStudentsByEmail.useCase';
import { ListStudentsUseCase } from '../students/listStudents.useCase';
import { UpdateStudentUseCase } from '../students/updateStudent.useCase';
import { CreateInitialUserUseCase } from '../users/create-initial-user/createInitialUser.useCase';
import { CreateExternalUserUseCase } from '../users/createExternalUser/createExternalUser.useCase';
import { CreateStudentUseCase } from '../users/createStudent.useCase';
import { CreateUserAdminUseCase } from '../users/createUserAdmin.useCase';
import { DeleteExternalUserUseCase } from '../users/deleteExternalUser/deleteExternalUser.useCase';
import { DeleteUserAdminUseCase } from '../users/deleteUserAdmin.useCase';
import { GetUserUseCase } from '../users/getUser/getUser.useCase';
import { GetUserByIdUseCase } from '../users/getUser/getUserById.useCase';
import { GetUserByIdAndCourseIdUseCase } from '../users/getUser/getUserByIdAndCourseId.useCase';
import { ListUsersAdmByCustomerUseCase } from '../users/listUsers/listUsersAdmByCustomer.useCase';
import { ListUsersWithClassesUseCase } from '../users/listUsers/listUsersWithClasses.useCase';
import { UpdateExternalUserUseCase } from '../users/updateExternalUser/updateExternalUser.useCase';
import { UpdateUserUseCase } from '../users/updateUser/updateUser.useCase';
import { UpdateUserAdminUseCase } from '../users/updateUserAdmin.useCase';

const userRepository = new KnexUserRepository();
const customerRepository = new KnexCustomerRepository();
const accessLevelRepository = new KnexAccessLevelsRepository();
const classesRepository = new KnexClassesRepository();
const userClassRepository = new KnexUserClassesRepository();
const courseRepository = new KnexCourseRepository();
const countriesRepository = new KnexCountriesRepository();
const usersTypesRepository = new KnexUsersTypesRepository();
const sesService = new SESService();
const s3Service = new S3Service();
const termsRepository = new KnexTermsRepository();
const filesRepository = new KnexFilesRepository();
const recoveryRepository = new KnexRecoveryRepository();

export function factoryCreateInitialUserUseCase() {
  return new CreateInitialUserUseCase(userRepository, customerRepository, usersTypesRepository);
}

export function factoryCreateExternalUserUseCase() {
  return new CreateExternalUserUseCase(
    userRepository,
    customerRepository,
    userClassRepository,
    classesRepository
  );
}

export function factoryUpdateExternalUserUseCase() {
  return new UpdateExternalUserUseCase(
    userRepository,
    customerRepository,
    userClassRepository,
    classesRepository
  );
}

export function factoryDeleteExternalUserUseCase() {
  return new DeleteExternalUserUseCase(userRepository, customerRepository, userClassRepository);
}

export function factoryListUsersWithClassesUseCase() {
  return new ListUsersWithClassesUseCase(userRepository);
}

export function factoryListUsersAdmByCustomerUseCase() {
  return new ListUsersAdmByCustomerUseCase(userRepository, accessLevelRepository);
}

export function factoryGetUserByIdUseCase() {
  return new GetUserByIdUseCase(userRepository, accessLevelRepository, s3Service);
}

export function factoryCreateUserAdminUseCase() {
  return new CreateUserAdminUseCase(
    userRepository,
    customerRepository,
    accessLevelRepository,
    sesService,
    termsRepository,
    filesRepository,
    s3Service,
    recoveryRepository
  );
}

export function factoryUpdateUserAdmin(): UpdateUserAdminUseCase {
  return new UpdateUserAdminUseCase(userRepository, accessLevelRepository);
}

export function factoryDeleteUserAdminUseCase() {
  return new DeleteUserAdminUseCase(userRepository);
}

export function factoryListStudentsUseCase() {
  return new ListStudentsUseCase(userRepository, accessLevelRepository);
}

export function factoryCreateStudentUseCase() {
  return new CreateStudentUseCase(
    userRepository,
    userClassRepository,
    classesRepository,
    sesService,
    customerRepository,
    accessLevelRepository,
    termsRepository,
    filesRepository,
    s3Service
  );
}

export function factoryGetUserByIdAndCourseIdUseCase() {
  return new GetUserByIdAndCourseIdUseCase(userRepository, classesRepository, s3Service);
}

export function factoryUpdateStudentUseCase() {
  return new UpdateStudentUseCase(userRepository, userClassRepository, classesRepository);
}

export function factoryAddStudentsByEmailUseCase() {
  return new AddStudentsByEmailUseCase(
    userRepository,
    courseRepository,
    customerRepository,
    userClassRepository,
    accessLevelRepository,
    classesRepository,
    sesService,
    s3Service,
    termsRepository,
    filesRepository,
    recoveryRepository
  );
}

export class UsersFactory {
  static getUserUseCase() {
    return new GetUserUseCase(userRepository, s3Service);
  }

  static updateUserUseCase() {
    return new UpdateUserUseCase(userRepository, countriesRepository);
  }
}
