import { env } from '../../env';
import { IFile } from '../../model/IFile';
import { IFilesRepository } from '../../repositories/assign/files.assign';
import S3Service from '../../services/aws/s3/S3Service';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

export class GetSignedFileUrlUseCase {
  constructor(
    private readonly filesRepository: IFilesRepository,
    private readonly s3Service: S3Service
  ) {}

  public async execute(fileId: string): Promise<{ signedUrl: string; fileInfo: IFile }> {
    const file = await this.filesRepository.findOneBy({ id: fileId });

    if (!file) {
      throw new ResourceNotFoundError('Arquivo não encontrado');
    }

    const bucketName = env.BUCKET_FILES;
    const signedUrl = await this.s3Service.getSignedUrl(bucketName, file.url, 3600); // 1 hora de expiração

    return {
      signedUrl,
      fileInfo: {
        id: file.id,
        file_name: file.file_name,
        file_type: file.file_type,
        file_size: file.file_size,
        url: file.url,
        created_at: file.created_at,
        updated_at: file.updated_at,
        deleted_at: file.deleted_at,
      },
    };
  }
}
