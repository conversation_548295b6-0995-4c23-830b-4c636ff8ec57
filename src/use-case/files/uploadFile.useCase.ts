import { env } from '../../env';
import { IUsersRepository } from '../../repositories/assign/users.assign';
import { UploadFileDTO } from '../../schema/files.schema';
import { UploadService } from '../../services/uploadFile.service';

interface UploadFileInput {
  file: Express.Multer.File;
  bodyData: UploadFileDTO;
  userId: string;
  customerId: string;
}

export class UploadFileUseCase {
  constructor(
    private readonly uploadService: UploadService,
    private readonly usersRepository: IUsersRepository
  ) {}

  async execute({ file, bodyData, userId, customerId }: UploadFileInput) {
    const { type, version } = bodyData;

    const bucketName = env.BUCKET_FILES || 'propofando-lxp-files';

    const filePath = getFilePath(type, userId, customerId, file.originalname, version);

    if (!filePath) {
      return { statusCode: 400, message: 'Invalid file type' };
    }

    if (type === 'photo-profile') {
      await this.usersRepository.update({ id: userId, image: filePath });
    }
    await this.uploadService.uploadFile({ file, storagePath: filePath, bucket: bucketName });
    return { statusCode: 200, message: 'File uploaded successfully', data: { filePath } };
  }
}

function getFilePath(
  type: string,
  userId: string,
  customerId: string,
  fileName?: string,
  version?: string
): string | null {
  const basePaths: Record<string, string> = {
    'photo-profile': `${customerId}/profile-photo/${userId}_v${version}`,
    'photo-course': `${customerId}/profile-course/${fileName || 'loren'}_course_v${version}`,
    'logo-customer': `${customerId}/logo-customer/${fileName || 'loren'}_customer_v${version}`,
    'term-of-user': `${customerId}/term_of_user/${fileName || 'loren'}_customer_v${version}`,
    'privacy-policy': `${customerId}/privacy-policy/${fileName || 'loren'}_customer_v${version}`,
  };
  return basePaths[type] || null;
}
