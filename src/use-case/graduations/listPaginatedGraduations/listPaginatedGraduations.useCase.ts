import { IPaginationDTO } from '../../../model/DTO/IGeneric.dto';
import type { IGraduation } from '../../../model/IGraduation';
import { IGraduationsRepository } from '../../../repositories/assign/graduations.assign';
import type { ListGraduationsParams } from '../../../schema/graduations.schema';
import { filterTextRegex } from '../../../services/filterTextRegex.service';

export interface ListGraduationsResponse {
  paginationInfo: IPaginationDTO;
  graduations: IGraduation[];
}

export class ListPaginatedGraduationsUseCase {
  constructor(private readonly graduationsRepository: IGraduationsRepository) {}

  async execute(params: ListGraduationsParams): Promise<ListGraduationsResponse> {
    const { page = 1, limit = 10, search, type, listAll } = params;

    const result = await this.graduationsRepository.findAllOrPaginateWithPagination({
      page,
      limit,
      search,
      type,
      listAll,
    });

    const sortedGraduations = result.graduations.sort((a, b) => {
      const nameA = filterTextRegex(a.name.toLowerCase());
      const nameB = filterTextRegex(b.name.toLowerCase());
      return nameA.localeCompare(nameB, 'pt-BR', { sensitivity: 'base', numeric: true });
    });

    return {
      paginationInfo: {
        currentPage: result.paginationInfo.currentPage,
        itemsPerPage: result.paginationInfo.itemsPerPage,
        totalItems: result.paginationInfo.totalItems,
        totalPages: result.paginationInfo.totalPages,
        hasNextPage: result.paginationInfo.hasNextPage,
        hasPreviousPage: result.paginationInfo.hasPreviousPage,
      },
      graduations: sortedGraduations,
    };
  }
}
