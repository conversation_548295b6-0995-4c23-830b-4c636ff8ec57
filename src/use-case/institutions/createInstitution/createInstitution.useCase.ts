import { IInstitution } from '../../../model/IInstitution';
import { IInstitutionsRepository } from '../../../repositories/assign/institutions.assign';
import { CreateInstitutionDTO } from '../../../schema/institutions.schema';
import { filterTextRegex } from '../../../services/filterTextRegex.service';
import { ResourceAlreadyExistsError } from '../../errors/ResourceAlreadyExistsError';

interface ICreateInstitutionRequest extends CreateInstitutionDTO {}

export class CreateInstitutionUseCase {
  constructor(private readonly institutionsRepository: IInstitutionsRepository) {}

  async execute(request: ICreateInstitutionRequest): Promise<IInstitution | null> {
    const { name, acronym, customerId } = request;

    await this.validateInstitutionUniqueness(name, customerId);

    if (acronym && acronym.trim()) {
      await this.validateAcronymUniqueness(acronym.trim(), customerId);
    }

    const institution = await this.institutionsRepository.insert({
      name,
      acronym: acronym?.trim(),
      customer_id: customerId,
    });

    return institution || null;
  }

  private async validateInstitutionUniqueness(name: string, customerId: string): Promise<void> {
    const existingInstitution = await this.institutionsRepository.findByNameAndCustomerId({
      name,
      customerId,
    });

    if (existingInstitution) {
      throw new ResourceAlreadyExistsError(`Já existe uma instituição com o nome '${name}'.`);
    }
  }

  private async validateAcronymUniqueness(acronym: string, customerId: string): Promise<void> {
    const normalizedAcronym = filterTextRegex(acronym);

    const allInstitutions = await this.institutionsRepository.findAllBy({
      customer_id: customerId,
      deleted_at: null,
    });

    const existingInstitution = allInstitutions.find((institution) => {
      if (!institution.acronym) return false;
      return filterTextRegex(institution.acronym) === normalizedAcronym;
    });

    if (existingInstitution) {
      throw new ResourceAlreadyExistsError(`Já existe uma instituição com a sigla '${acronym}'.`);
    }
  }
}
