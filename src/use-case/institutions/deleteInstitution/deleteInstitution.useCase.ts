import { IInstitution } from '../../../model/IInstitution';
import { IExamsRepository } from '../../../repositories/assign/exams.assign';
import { IInstitutionsRepository } from '../../../repositories/assign/institutions.assign';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';
import { UnauthorizedError } from '../../errors/UnauthorizedError.ts';

interface IDeleteInstitutionRequest {
  id: string;
  customerId: string;
}
export class DeleteInstitutionUseCase {
  constructor(
    private readonly institutionsRepository: IInstitutionsRepository,
    private readonly examsRepository: IExamsRepository
  ) {}

  async execute(request: IDeleteInstitutionRequest): Promise<IInstitution> {
    const { id, customerId } = request;

    const institution = await this.getInstitutionOrFail(id);

    this.ensureCanDelete(institution, customerId);

    await this.validateNoExamLinks(id, customerId);

    const deletedInstitution = await this.institutionsRepository.softDelete({
      id,
      customerId,
    });

    if (!deletedInstitution) {
      throw new ResourceNotFoundError('Erro ao excluir instituição');
    }

    return deletedInstitution;
  }

  private async getInstitutionOrFail(id: string): Promise<IInstitution> {
    const institution = await this.institutionsRepository.findOneBy({
      id,
      deleted_at: null,
    });

    if (!institution) {
      throw new ResourceNotFoundError('Instituição não encontrada');
    }

    return institution;
  }

  private ensureCanDelete(institution: IInstitution, customerId: string): void {
    if (!institution.customer_id) {
      throw new UnauthorizedError('Não é permitido excluir instituições do sistema');
    }

    if (institution.customer_id !== customerId) {
      throw new UnauthorizedError('Você não tem permissão para excluir esta instituição');
    }
  }

  private async validateNoExamLinks(institutionId: string, customerId: string): Promise<void> {
    const linkedExams = await this.examsRepository.findByEntities({
      institution_id: institutionId,
      deleted_at: null,
      customer_id: customerId,
    });

    if (linkedExams && linkedExams.length > 0) {
      throw new BadRequestError(
        `Não é permitido excluir a instituição pois há ${linkedExams.length} provas vinculadas.`
      );
    }
  }
}
