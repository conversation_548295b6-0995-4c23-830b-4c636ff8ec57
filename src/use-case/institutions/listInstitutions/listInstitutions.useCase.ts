import { IInstitution } from '../../../model/IInstitution';
import { IInstitutionsRepository } from '../../../repositories/assign/institutions.assign';

interface IListInstitutionsRequest {
  customerId: string;
}

export class ListInstitutionsUseCase {
  constructor(private readonly institutionsRepository: IInstitutionsRepository) {}

  async execute({ customerId }: IListInstitutionsRequest): Promise<IInstitution[]> {
    const institutions = await this.institutionsRepository.listAllInstitutions(customerId);

    return this.sortByAcronym(institutions);
  }

  private sortByAcronym(institutions: IInstitution[]): IInstitution[] {
    const normalize = (text: string) =>
      text
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .toLowerCase();

    return [...institutions].sort((a, b) =>
      normalize(a.acronym ?? '').localeCompare(normalize(b.acronym ?? ''), 'pt-BR', {
        sensitivity: 'base',
      })
    );
  }
}
