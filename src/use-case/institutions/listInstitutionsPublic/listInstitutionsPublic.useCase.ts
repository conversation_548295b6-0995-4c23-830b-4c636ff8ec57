import { IPaginationDTO } from '../../../model/DTO/IGeneric.dto';
import { IInstitution } from '../../../model/IInstitution';
import { IInstitutionsRepository } from '../../../repositories/assign/institutions.assign';
import { ListInstitutionsPublicDTO } from '../../../schema/institutions.schema';

interface IListInstitutionsPublicUseCaseRequest extends ListInstitutionsPublicDTO {}

interface IListInstitutionsPublicUseCaseResponse {
  paginationInfo: IPaginationDTO;
  institutions: IInstitution[];
}

export class ListInstitutionsPublicUseCase {
  constructor(private readonly institutionsRepository: IInstitutionsRepository) {}

  async execute(
    data: IListInstitutionsPublicUseCaseRequest
  ): Promise<IListInstitutionsPublicUseCaseResponse> {
    const {
      page = 1,
      limit = 10,
      search,
      listAll = false,
      orderByColumn = 'acronym',
      orderDirection = 'asc',
    } = data;

    const { institutions, paginationInfo } =
      await this.institutionsRepository.listInstitutionsPublicPaginated({
        page,
        limit,
        search,
        listAll,
        orderByColumn,
        orderDirection,
      });

    return {
      paginationInfo,
      institutions,
    };
  }
}
