import { IInstitution } from '../../../model/IInstitution';
import { IInstitutionsRepository } from '../../../repositories/assign/institutions.assign';
import { UpdateInstitutionDTO } from '../../../schema/institutions.schema';
import { filterTextRegex } from '../../../services/filterTextRegex.service';
import { ResourceAlreadyExistsError } from '../../errors/ResourceAlreadyExistsError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';
import { UnauthorizedError } from '../../errors/UnauthorizedError.ts';

interface IUpdateInstitutionRequest extends UpdateInstitutionDTO {
  id: string;
}

export class UpdateInstitutionUseCase {
  constructor(private readonly institutionsRepository: IInstitutionsRepository) {}

  async execute(request: IUpdateInstitutionRequest): Promise<IInstitution> {
    const { id, name, acronym, customerId } = request;

    const institution = await this.getInstitutionOrFail(id);

    this.ensureCanEdit(institution, customerId);

    if (name && name.trim() !== institution.name) {
      await this.validateNameUniqueness(name.trim(), customerId, id);
    }

    if (acronym !== undefined && acronym?.trim() !== institution.acronym) {
      await this.validateAcronymUniqueness(acronym?.trim() || null, customerId, id);
    }

    const updatedInstitution = await this.institutionsRepository.update({
      id,
      name: name?.trim() || institution.name,
      acronym: acronym?.trim() || institution.acronym,
    });

    if (!updatedInstitution) {
      throw new ResourceNotFoundError('Erro ao atualizar instituição');
    }

    return updatedInstitution;
  }

  private async getInstitutionOrFail(id: string): Promise<IInstitution> {
    const institution = await this.institutionsRepository.findOneBy({
      id,
      deleted_at: null,
    });

    if (!institution) {
      throw new ResourceNotFoundError('Instituição não encontrada');
    }

    return institution;
  }

  private ensureCanEdit(institution: IInstitution, customerId: string): void {
    if (!institution.customer_id) {
      throw new UnauthorizedError('Não é permitido editar instituições do sistema');
    }

    if (institution.customer_id !== customerId) {
      throw new UnauthorizedError('Você não tem permissão para editar esta instituição');
    }
  }

  private async validateNameUniqueness(
    name: string,
    customerId: string,
    currentId: string
  ): Promise<void> {
    const existingInstitution = await this.institutionsRepository.findByNameAndCustomerId({
      name,
      customerId,
    });

    if (existingInstitution && existingInstitution.id !== currentId) {
      throw new ResourceAlreadyExistsError(`Já existe uma instituição com o nome '${name}'.`);
    }
  }

  private async validateAcronymUniqueness(
    acronym: string | null,
    customerId: string,
    currentId: string
  ): Promise<void> {
    if (!acronym) return;

    const normalizedAcronym = filterTextRegex(acronym);

    const allInstitutions = await this.institutionsRepository.findAllBy({
      customer_id: customerId,
      deleted_at: null,
    });

    const existingInstitution = allInstitutions.find((institution) => {
      if (institution.id === currentId) return false;
      if (!institution.acronym) return false;

      return filterTextRegex(institution.acronym) === normalizedAcronym;
    });

    if (existingInstitution) {
      throw new ResourceAlreadyExistsError(`Já existe uma instituição com a sigla '${acronym}'.`);
    }
  }
}
