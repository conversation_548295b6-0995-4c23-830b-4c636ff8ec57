import { randomUUID } from 'crypto';
import path from 'path';

import { env } from '../../env';
import { generateTransaction } from '../../helpers/transaction.helper';
import { LessonTypeEnum } from '../../model/enums/LessonTypeEnum.enum';
import { StatusLessonEnum } from '../../model/enums/status.enum';
import { IFile } from '../../model/IFile';
import { ICourseLessonRepository } from '../../repositories/assign/courseLessons.assign';
import { IFilesRepository } from '../../repositories/assign/files.assign';
import { TCreateLessonSchema } from '../../schema/lessons.schema';
import S3Service from '../../services/aws/s3/S3Service';
import { BadRequestError } from '../errors/BadRequestError';

interface ICreateLessonWithFilesDTO extends TCreateLessonSchema {
  files?: Express.Multer.File[];
  customerId: string;
}

export class CreateLessonUseCase {
  constructor(
    private lessonRepository: ICourseLessonRepository,
    private filesRepository: IFilesRepository
  ) {}

  public async execute(input: ICreateLessonWithFilesDTO) {
    const allowedExtensions = [
      'jpeg',
      'jpg',
      'png',
      'pdf',
      'mp4',
      'mp3',
      'pptx',
      'ppt',
      'docx',
      'doc',
      'xlsx',
      'xls',
      'zip',
      'txt',
    ];
    const allowedMimeTypes = [
      'image/jpeg',
      'image/png',
      'application/pdf',
      'video/mp4',
      'audio/mpeg',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'application/zip',
      'application/x-zip-compressed',
    ];

    const trx = await generateTransaction();
    const {
      moduleId,
      title,
      descriptionLessons,
      lessonUrl,
      duration,
      orderBy,
      questionGroupId,
      published,
      status,
      lessonType,
      files,
      customerId,
    } = input;

    const statusEnum = status as StatusLessonEnum;
    const lessonTypeEnum = (lessonType as LessonTypeEnum) || LessonTypeEnum.VIDEO;

    if (this.requiresFullFields(statusEnum)) {
      this.validateRequiredFields({ title, lessonUrl, duration, lesson_type: lessonTypeEnum });
    }

    const lessons = await this.lessonRepository.findByModuleId(moduleId);

    const createdLesson = await this.lessonRepository.insertLesson({
      module_id: moduleId,
      title,
      description_lessons: descriptionLessons || null,
      lesson_url: lessonUrl,
      duration,
      order_by: orderBy ?? lessons.length + 1,
      question_group_id: questionGroupId,
      published,
      status: StatusLessonEnum.UNPUBLISHED,
      lesson_type: lessonType,
    });

    let savedFiles: IFile[] = [];

    if (files && files.length > 0) {
      await this.validateFiles(files, allowedExtensions, allowedMimeTypes);
      const uploadedFiles = await this.uploadFilesToS3(files, createdLesson.id, customerId);

      const filesToSave = uploadedFiles.map((file) => ({
        id: randomUUID(),
        file_name: file.fileName,
        file_type: file.fileType,
        file_size: file.fileSize,
        url: file.filePath,
        lesson_id: createdLesson.id,
      }));

      savedFiles = await Promise.all(
        filesToSave.map((file) => this.filesRepository.insert(file, trx))
      );
    }

    await trx.commit();

    return {
      ...createdLesson,
      files: savedFiles,
    };
  }

  private requiresFullFields(status: StatusLessonEnum): boolean {
    return status === StatusLessonEnum.PUBLISHED || status === StatusLessonEnum.IN_REVIEW;
  }

  private validateRequiredFields(fields: {
    title?: string;
    lessonUrl?: string;
    duration?: number;
    lesson_type?: LessonTypeEnum;
  }) {
    const missingFields: string[] = [];

    if (!fields.title) missingFields.push('O título é obrigatório');
    if (fields.lesson_type === LessonTypeEnum.VIDEO && !fields.lessonUrl) {
      missingFields.push('O URL da aula é obrigatório');
    }
    if (fields.lesson_type === LessonTypeEnum.VIDEO && !fields.duration) {
      missingFields.push('A duração é obrigatória');
    }

    if (missingFields.length > 0) {
      throw new BadRequestError(missingFields.join(' | '));
    }
  }

  private async validateFiles(
    files: Express.Multer.File[],
    allowedExtensions: string[],
    allowedMimeTypes: string[]
  ) {
    const fileType = await import('file-type');
    for (const file of files) {
      const fileTypeResult = await fileType.fileTypeFromBuffer(file.buffer);

      if (!fileTypeResult) {
        throw new BadRequestError(
          `Não foi possível identificar o tipo do arquivo "${file.originalname}".`
        );
      }

      const ext = fileTypeResult.ext;
      const mime = fileTypeResult.mime;

      if (!allowedExtensions.includes(ext) || !allowedMimeTypes.includes(mime)) {
        throw new BadRequestError(
          `O arquivo "${file.originalname}" não é um tipo permitido. Tipo detectado: ${ext} (${mime})`
        );
      }
    }
  }

  private async uploadFilesToS3(
    files: Express.Multer.File[],
    lessonId: string,
    customerId: string
  ) {
    const s3Service = new S3Service();
    const bucketName = env.BUCKET_FILES || 'propofando-lxp-files';

    const uploaded = [];

    for (const file of files) {
      const filePath = `${customerId}/lessons/${lessonId}/${file.originalname}`;
      const fileType = path.extname(file.originalname).replace('.', '');

      await s3Service.uploadFile(bucketName, filePath, file.buffer, fileType);

      uploaded.push({
        fileName: file.originalname,
        fileType,
        fileSize: file.size,
        filePath,
      });
    }

    return uploaded;
  }
}
