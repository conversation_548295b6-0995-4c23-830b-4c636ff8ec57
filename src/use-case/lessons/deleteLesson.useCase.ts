import { generateTransaction } from '../../helpers/transaction.helper';
import { ICourseLessonRepository } from '../../repositories/assign/courseLessons.assign';
import { BadRequestError } from '../errors/BadRequestError';

export class DeleteLessonUseCase {
  constructor(private lessonRepository: ICourseLessonRepository) {}

  async execute(id: string): Promise<void> {
    const trx = await generateTransaction();

    const lesson = await this.lessonRepository.findOneBy({ id, deleted_at: null });

    if (!lesson) {
      throw new BadRequestError('Aula não encontrada');
    }

    const deleted = await this.lessonRepository.softDeleteLesson(id, trx);

    if (!deleted) {
      await trx.rollback();
      throw new BadRequestError('Erro ao deletar aula');
    }

    await trx.commit();
  }
}
