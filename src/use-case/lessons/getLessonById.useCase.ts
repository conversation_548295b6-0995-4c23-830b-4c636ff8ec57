import { ILessonResponse } from '../../model/DTO/response/lesson.dto';
import { ICourseLessonRepository } from '../../repositories/assign/courseLessons.assign';
import { BadRequestError } from '../errors/BadRequestError';

export class GetLessonByIdUseCase {
  constructor(private lessonRepository: ICourseLessonRepository) {}

  async execute(id: string): Promise<ILessonResponse> {
    const lesson = await this.lessonRepository.getLessonById(id);

    if (!lesson) {
      throw new BadRequestError('Aula não encontrada');
    }

    return lesson;
  }
}
