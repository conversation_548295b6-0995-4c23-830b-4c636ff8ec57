import { ICourseLesson } from '../../model/ICourseLesson';
import { ICourseLessonRepository } from '../../repositories/assign/courseLessons.assign';
import { TReorderLessonsSchema } from '../../schema/lessons.schema';
import { BadRequestError } from '../errors/BadRequestError';

export class ReorderLessonsUseCase {
  constructor(private lessonRepository: ICourseLessonRepository) {}

  async execute(input: TReorderLessonsSchema) {
    const activeLessons = await this.findActiveLessonsOrFail(input.moduleId);

    this.validateReorderRequest(activeLessons, input.lessons);

    const updates = input.lessons.map((l) => ({ id: l.lessonId, order_by: l.orderBy }));

    await this.lessonRepository.updateLessonsOrderBatch(updates);

    return { message: 'Ordem das aulas atualizada com sucesso.' };
  }

  private async findActiveLessonsOrFail(moduleId: string): Promise<ICourseLesson[]> {
    const lessons = await this.lessonRepository.findByModuleId(moduleId);

    const activeLessons = lessons.filter((l: ICourseLesson) => !l.deleted_at);

    if (activeLessons.length === 0) {
      throw new BadRequestError('O módulo não possui aulas ativas para reordenar');
    }
    return activeLessons;
  }

  private validateReorderRequest(
    activeLessons: ICourseLesson[],
    requestedLessons: { lessonId: string; orderBy: number }[]
  ) {
    const currentIds = new Set(activeLessons.map((l) => l.id));

    const requestedIds = requestedLessons.map((l) => l.lessonId);

    const invalidIds = requestedIds.filter((id) => !currentIds.has(id));

    if (invalidIds.length > 0) {
      throw new BadRequestError(`Aulas não encontradas: ${invalidIds.join(', ')}`);
    }

    if (requestedIds.length !== activeLessons.length) {
      throw new BadRequestError('Todas as aulas devem ser incluídas na reordenação');
    }

    const orders = requestedLessons.map((l) => l.orderBy);
    const uniqueOrders = new Set(orders);

    if (uniqueOrders.size !== orders.length) {
      throw new BadRequestError('Não pode haver ordens duplicadas');
    }

    const min = Math.min(...orders);
    const max = Math.max(...orders);

    if (min !== 1 || max !== orders.length) {
      throw new BadRequestError(
        `As ordens devem ser contínuas, de 1 até o total de aulas: ${orders.length}`
      );
    }
  }
}
