import { randomUUID } from 'crypto';
import path from 'path';

import { generateTransaction } from '../../helpers/transaction.helper';
import { LessonTypeEnum } from '../../model/enums/LessonTypeEnum.enum';
import { StatusLessonEnum } from '../../model/enums/status.enum';
import { IFile } from '../../model/IFile';
import { ICourseLessonRepository } from '../../repositories/assign/courseLessons.assign';
import { ICourseModuleRepository } from '../../repositories/assign/courseModules.assign';
import { IFilesRepository } from '../../repositories/assign/files.assign';
import { TUpdateLessonSchema } from '../../schema/lessons.schema';
import S3Service from '../../services/aws/s3/S3Service';
import { BadRequestError } from '../errors/BadRequestError';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

interface IUpdateLessonWithFilesDTO extends TUpdateLessonSchema {
  files?: Express.Multer.File[];
  removeFiles?: string[];
  customerId: string;
}

export class UpdateLessonUseCase {
  constructor(
    private lessonRepository: ICourseLessonRepository,
    private moduleRepository: ICourseModuleRepository,
    private filesRepository: IFilesRepository
  ) {}

  public async execute({
    id,
    moduleId,
    title,
    descriptionLessons,
    lessonUrl,
    duration,
    orderBy,
    questionGroupId,
    published,
    status,
    lessonType,
    files,
    removeFiles,
    customerId,
  }: IUpdateLessonWithFilesDTO) {
    const allowedExtensions = [
      'jpeg',
      'jpg',
      'png',
      'pdf',
      'mp4',
      'mp3',
      'pptx',
      'ppt',
      'docx',
      'doc',
      'xlsx',
      'xls',
      'zip',
      'txt',
    ];
    const allowedMimeTypes = [
      'image/jpeg',
      'image/png',
      'application/pdf',
      'video/mp4',
      'audio/mpeg',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'application/zip',
      'application/x-zip-compressed',
    ];
    const trx = await generateTransaction();
    try {
      const lessonExists = await this.lessonRepository.findOneBy({ id });
      if (!lessonExists) throw new ResourceNotFoundError('Aula não encontrada');

      const updateData = {
        id,
        module_id: moduleId,
        title: title ?? lessonExists.title,
        description_lessons: descriptionLessons ?? lessonExists.description_lessons,
        lesson_url: lessonUrl ?? lessonExists.lesson_url,
        duration: duration ?? lessonExists.duration,
        order_by: orderBy ?? lessonExists.order_by,
        question_group_id: questionGroupId ?? lessonExists.question_group_id,
        published: published ?? lessonExists.published,
        status: status ?? lessonExists.status,
        lesson_type: lessonType ?? lessonExists.lesson_type,
      };

      await this.validatePublishRequirements({
        status: updateData.status as StatusLessonEnum,
        published: updateData.published as boolean,
        title: updateData.title as string,
        lessonUrl: updateData.lesson_url as string,
        duration: updateData.duration as number,
        lessonType: updateData.lesson_type as string,
      });

      if (updateData.module_id) {
        const publishedLessons = await this.lessonRepository.countPublishedLessonsByModuleId(
          updateData.module_id
        );

        if (publishedLessons === 0) {
          await this.moduleRepository.updateModule({
            id: updateData.module_id,
            published: false,
            status: StatusLessonEnum.UNPUBLISHED,
          });
        }
      }

      await this.lessonRepository.updateLesson(updateData);

      const uploadedFiles: Array<{
        fileName: string;
        fileType: string;
        fileSize: number;
        filePath: string;
      }> = [];
      if (files && files.length > 0) {
        const fileType = await import('file-type');
        for (const file of files) {
          const fileTypeResult = await fileType.fileTypeFromBuffer(file.buffer);
          if (!fileTypeResult) {
            throw new BadRequestError(
              `Não foi possível identificar o tipo do arquivo "${file.originalname}".`
            );
          }
          const ext = fileTypeResult.ext;
          const mime = fileTypeResult.mime;
          if (!allowedExtensions.includes(ext) || !allowedMimeTypes.includes(mime)) {
            throw new BadRequestError(
              `O arquivo "${file.originalname}" não é um tipo permitido. Tipo detectado: ${ext} (${mime})`
            );
          }
        }

        const s3Service = new S3Service();
        const bucketName = process.env.BUCKET_FILES || 'propofando-lxp-files';
        for (const file of files) {
          const filePath = `${customerId}/lessons/${id}/${file.originalname}`;
          await s3Service.uploadFile(bucketName, filePath, file.buffer, file.mimetype);
          uploadedFiles.push({
            fileName: file.originalname,
            fileType: path.extname(file.originalname).replace('.', ''),
            fileSize: file.size,
            filePath,
          });
        }
      }
      let savedFiles: IFile[] = [];
      if (uploadedFiles.length > 0) {
        const filesToSave = uploadedFiles.map((file) => ({
          id: randomUUID(),
          file_name: file.fileName,
          file_type: file.fileType,
          file_size: file.fileSize,
          url: file.filePath,
          lesson_id: id,
        }));
        savedFiles = await Promise.all(
          filesToSave.map((file) => this.filesRepository.insert(file, trx))
        );
      }

      if (removeFiles && Array.isArray(removeFiles) && removeFiles.length > 0) {
        const s3Service = new S3Service();
        const bucketName = process.env.BUCKET_FILES || 'propofando-lxp-files';

        for (const filePath of removeFiles) {
          await s3Service.deleteFile(bucketName, filePath);
        }

        await this.filesRepository.deleteManyByPaths(removeFiles, trx);
      }

      await trx.commit();
      return { ...updateData, files: savedFiles };
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  private async validatePublishRequirements({
    status,
    published,
    title,
    lessonUrl,
    duration,
    lessonType,
  }: {
    status: StatusLessonEnum;
    published: boolean;
    title: string;
    lessonUrl: string;
    duration: number;
    lessonType: string;
  }) {
    if (
      (published && status === StatusLessonEnum.PUBLISHED) ||
      status === StatusLessonEnum.IN_REVIEW
    ) {
      if (!title) throw new BadRequestError('O título da aula é obrigatório para ser publicado');
      if (!lessonType) throw new BadRequestError('O tipo da aula é obrigatório para ser publicado');
      if (lessonType === LessonTypeEnum.VIDEO) {
        if (!lessonUrl)
          throw new BadRequestError(
            'A url da aula é obrigatória para ser publicado quando o tipo for vídeo'
          );
        if (!duration)
          throw new BadRequestError(
            'A duração da aula é obrigatória para ser publicado quando o tipo for vídeo'
          );
      }
    }
  }
}
