import {
  ICreateCourseLessonDTO,
  ICreateCourseModuleDTO,
} from '../../model/DTO/ICreateCourseModule.dto';
import { ICourseLessonRepository } from '../../repositories/assign/courseLessons.assign';
import { ICourseModuleRepository } from '../../repositories/assign/courseModules.assign';

function parseDurationToSeconds(val: string | number | null | undefined): number | null {
  if (val === '' || val === null || val === undefined) return null;
  if (typeof val === 'number') return val;
  if (typeof val === 'string') {
    if (/^-?\d+(\.\d+)?$/.test(val)) return Number(val);
    const parts = val.split(':').map(Number);
    if (parts.length === 2) return parts[0] * 60 + parts[1];
    if (parts.length === 3) return parts[0] * 3600 + parts[1] * 60 + parts[2];
    return null;
  }
  return null;
}

export class CreateModuleUseCase {
  constructor(
    private moduleRepository: ICourseModuleRepository,
    private lessonRepository: ICourseLessonRepository
  ) {}

  public async execute({
    courseId,
    modules,
  }: {
    courseId: string;
    modules: ICreateCourseModuleDTO[];
  }): Promise<object> {
    const [moduleIds] = await Promise.all(
      modules.map((module, index) => this.processModule(courseId, module, index))
    );

    return {
      moduleIds,
    };
  }

  private async processModule(
    courseId: string,
    module: ICreateCourseModuleDTO,
    orderIndex: number
  ): Promise<void> {
    const savedModuleId = await this.saveModule(courseId, module, orderIndex);

    if (module.classes?.length) {
      await this.saveLessons(savedModuleId, module.classes);
    }
  }

  private async saveModule(
    courseId: string,
    module: ICreateCourseModuleDTO,
    orderIndex: number
  ): Promise<string> {
    const payload = {
      course_id: courseId,
      name: module.name,
      description_module: module.description,
      order_by: orderIndex,
    };

    if (module.id) {
      await this.moduleRepository.updateModuleById(module.id, payload);
      return module.id;
    }

    const createdModule = await this.moduleRepository.createModule(payload);
    return createdModule.id;
  }

  private async saveLessons(moduleId: string, classes: ICreateCourseLessonDTO[]): Promise<void> {
    await Promise.all(
      classes.map((classItem, classIndex) => this.saveLesson(moduleId, classItem, classIndex))
    );
  }

  private async saveLesson(
    moduleId: string,
    classItem: ICreateCourseLessonDTO,
    orderIndex: number
  ): Promise<void> {
    const lessonPayload = {
      module_id: moduleId,
      title: classItem.name,
      description_lessons: classItem.description,
      lesson_url: classItem.video || null,
      duration: parseDurationToSeconds(classItem.duration),
      order_by: orderIndex,
      question_group_id: classItem.questionGroupId || null,
    };

    if (classItem.id) {
      await this.lessonRepository.updateLessonById(classItem.id, lessonPayload);
    } else {
      await this.lessonRepository.createLesson(lessonPayload);
    }
  }
}
