import { StatusModuleEnum } from '../../../model/enums/status.enum';
import { ICourseModuleRepository } from '../../../repositories/assign/courseModules.assign';
import { TCreateSimpleModuleSchema } from '../../../schema/modules.schema';

export class CreateSimpleModuleUseCase {
  constructor(private moduleRepository: ICourseModuleRepository) {}

  public async execute(payload: TCreateSimpleModuleSchema) {
    const { courseId, name, descriptionModule, orderBy, published } = payload;

    const finalName = name || `Módulo ${await this.getNextModuleIndex(courseId)}`;

    const createdModule = await this.moduleRepository.insertModule({
      course_id: courseId,
      name: finalName,
      description_module: descriptionModule,
      order_by: orderBy,
      published,
      status: StatusModuleEnum.UNPUBLISHED,
    });

    return createdModule;
  }

  private async getNextModuleIndex(courseId: string): Promise<number> {
    const total = await this.moduleRepository.countModulesByCourseId(courseId);
    return total + 1;
  }
}
