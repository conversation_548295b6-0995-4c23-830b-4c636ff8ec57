import { generateTransaction } from '../../helpers/transaction.helper';
import { ICourseLessonRepository } from '../../repositories/assign/courseLessons.assign';
import { ICourseModuleRepository } from '../../repositories/assign/courseModules.assign';
import { BadRequestError } from '../errors/BadRequestError';

export class DeleteModuleUseCase {
  constructor(
    private moduleRepository: ICourseModuleRepository,
    private lessonRepository: ICourseLessonRepository
  ) {}

  async execute(id: string): Promise<void> {
    const trx = await generateTransaction();

    const module = await this.moduleRepository.findOneBy({ id, deleted_at: null });

    if (!module) {
      throw new BadRequestError('Módulo não encontrado');
    }
    const deletedModule = await this.moduleRepository.softDeleteModule(id, trx);

    if (!deletedModule) {
      await trx.rollback();
      throw new BadRequestError('Erro ao deletar módulo');
    }

    const deletedLessons = await this.lessonRepository.softDeleteLessonsByModuleId(id, trx);

    if (!deletedLessons) {
      await trx.rollback();
      throw new BadRequestError('Erro ao deletar aulas do módulo');
    }

    await trx.commit();
  }
}
