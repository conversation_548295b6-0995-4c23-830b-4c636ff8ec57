import { ICourseModule } from '../../model/ICourseModule';
import { ICourseModuleRepository } from '../../repositories/assign/courseModules.assign';
import { TReorderModulesSchema } from '../../schema/modules.schema';
import { BadRequestError } from '../errors/BadRequestError';

export class ReorderModulesUseCase {
  constructor(private moduleRepository: ICourseModuleRepository) {}

  async execute(input: TReorderModulesSchema) {
    const activeModules = await this.findActiveModulesOrFail(input.courseId);

    this.validateReorderRequest(activeModules, input.modules);

    const updates = input.modules.map((m) => ({ id: m.moduleId, order_by: m.orderBy }));

    await this.moduleRepository.updateModulesOrderBatch(updates);

    return { message: 'Ordem dos módulos atualizada com sucesso.' };
  }

  private async findActiveModulesOrFail(courseId: string): Promise<ICourseModule[]> {
    const modules = await this.moduleRepository.findByCourseId(courseId);

    if (modules.length === 0) {
      throw new BadRequestError('O curso não possui módulos ativos para reordenar');
    }

    return modules;
  }

  private validateReorderRequest(
    activeModules: ICourseModule[],
    requestedModules: { moduleId: string; orderBy: number }[]
  ) {
    const currentIds = new Set(activeModules.map((m) => m.id));
    const requestedIds = requestedModules.map((m) => m.moduleId);
    const invalidIds = requestedIds.filter((id) => !currentIds.has(id));

    if (invalidIds.length > 0) {
      throw new BadRequestError(`Módulos não encontrados: ${invalidIds.join(', ')}`);
    }

    if (requestedIds.length !== activeModules.length) {
      throw new BadRequestError('Todos os módulos devem ser incluídos na reordenação');
    }

    const orders = requestedModules.map((m) => m.orderBy);
    const uniqueOrders = new Set(orders);

    if (uniqueOrders.size !== orders.length) {
      throw new BadRequestError('Não pode haver ordens duplicadas');
    }

    const min = Math.min(...orders);
    const max = Math.max(...orders);

    if (min !== 1 || max !== orders.length) {
      throw new BadRequestError(
        `As ordens devem ser contínuas, de 1 até o total de módulos: ${orders.length}`
      );
    }
  }
}
