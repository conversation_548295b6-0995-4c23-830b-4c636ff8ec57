import { StatusModuleEnum } from '../../model/enums/status.enum';
import { ICourseModule } from '../../model/ICourseModule';
import { ICourseLessonRepository } from '../../repositories/assign/courseLessons.assign';
import { ICourseModuleRepository } from '../../repositories/assign/courseModules.assign';
import { TUpdateModuleSchema } from '../../schema/modules.schema';
import { BadRequestError } from '../errors/BadRequestError';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

export class UpdateModuleUseCase {
  constructor(
    private moduleRepository: ICourseModuleRepository,
    private lessonRepository: ICourseLessonRepository
  ) {}

  public async execute({
    id,
    name,
    descriptionModule,
    orderBy,
    published,
    status,
  }: TUpdateModuleSchema) {
    const moduleExists = await this.moduleRepository.findOneBy({ id });

    if (!moduleExists) {
      throw new ResourceNotFoundError('Módulo não encontrado');
    }

    await this.validatePublicationRules({
      id,
      name,
      statusToCheck: (status ?? moduleExists.status) as StatusModuleEnum,
    });

    const updateData = this.buildUpdateData({
      moduleExists,
      id,
      name,
      descriptionModule,
      orderBy,
      published,
      status,
    });

    await this.moduleRepository.updateModule(updateData);

    return updateData;
  }

  private async validatePublicationRules({
    id,
    name,
    statusToCheck,
  }: {
    id: string;
    name?: string;
    statusToCheck: StatusModuleEnum;
  }) {
    if (
      statusToCheck === StatusModuleEnum.PUBLISHED ||
      statusToCheck === StatusModuleEnum.IN_REVIEW
    ) {
      const missingFields: string[] = [];
      if (!name) missingFields.push('O nome do módulo é obrigatório');

      const publishedLessons = await this.lessonRepository.countPublishedLessonsByModuleId(id);

      if (publishedLessons < 1) {
        missingFields.push('O módulo precisa ter pelo menos uma aula publicada para ser publicado');
      }
      if (missingFields.length > 0) {
        throw new BadRequestError(missingFields.join(' | '));
      }
    }
  }

  private buildUpdateData({
    moduleExists,
    id,
    name,
    descriptionModule,
    orderBy,
    published,
    status,
  }: {
    moduleExists: ICourseModule;
    id: string;
    name?: string;
    descriptionModule?: string | null;
    orderBy?: number;
    published?: boolean;
    status?: string;
  }) {
    return {
      id,
      name: name ?? moduleExists.name,
      description_module: descriptionModule ?? moduleExists.description_module,
      order_by: orderBy ?? moduleExists.order_by,
      published: published ?? moduleExists.published,
      status: status ?? moduleExists.status,
    };
  }
}
