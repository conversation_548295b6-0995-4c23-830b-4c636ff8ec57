import { ICustomerIdDTO } from '../../model/DTO/ICustumers.dto';
import { IQuestionType } from '../../model/IQuestionType';
import { IQuestionsTypesRepository } from '../../repositories/assign/questionsTypes.assign';
import { verifyCustomer } from '../../services/verifyCustomer.service';

export class ListQuestionTypesUseCase {
  constructor(private readonly questionTypeRepository: IQuestionsTypesRepository) {}

  async execute({ customerId }: ICustomerIdDTO): Promise<IQuestionType[]> {
    await verifyCustomer(customerId);

    const questionsTypes = await this.questionTypeRepository.findAll();

    return questionsTypes;
  }
}
