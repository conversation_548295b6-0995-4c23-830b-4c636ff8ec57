import { IBaseAlternativeDTO } from '../../../model/DTO/IAlternative.dto';
import { IQuestionCreateDTO } from '../../../model/DTO/IQuestion.dto';
import { QuestionStatusEnum } from '../../../model/enums/questions.enum';
import { IAlternative } from '../../../model/IAlternative';
import { IQuestion } from '../../../model/IQuestion';
import { IAggregatedDataAssignRepository } from '../../../repositories/assign/aggregatedData.assign';
import { IAlternativesRepository } from '../../../repositories/assign/alternatives.assign';
import { IDisciplinesRepository } from '../../../repositories/assign/disciplines.assign';
import { IQuestionsRepository } from '../../../repositories/assign/questions.assign';
import { QuestionValidator } from '../../../services/validators/question.validator';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

export class CreateQuestionUseCase {
  constructor(
    private readonly questionRepo: IQuestionsRepository,
    private readonly alternativeRepo: IAlternativesRepository,
    private readonly disciplineRepo: IDisciplinesRepository,
    private readonly aggregatedDataRepository: IAggregatedDataAssignRepository
  ) {}

  async execute(questionInput: IQuestionCreateDTO): Promise<
    IQuestion & {
      alternatives: IAlternative[];
      disciplineName?: string;
      categoryName?: string;
      subcategoryName?: string;
    }
  > {
    const { discipline, category, subcategory } = await this.getEntities(questionInput);

    this.validateQuestionInput(questionInput);

    await this.validateDiscipline(questionInput.disciplineId, questionInput.customerId);

    const question = await this.createQuestion(questionInput);
    const alternatives = await this.createAlternativesIfPresent(
      questionInput.alternatives,
      question.id
    );

    return {
      ...question,
      alternatives,
      disciplineName: discipline?.name,
      categoryName: category?.name,
      subcategoryName: subcategory?.name,
    };
  }

  private async getEntities(inputData: IQuestionCreateDTO) {
    const { disciplineId, categoryId, subcategoryId, customerId, questionTypeId } = inputData;
    const { discipline, category, subcategory, questionType } =
      await this.aggregatedDataRepository.findEntities({
        customerId,
        disciplineId,
        categoryId,
        subcategoryId,
        questionTypeId,
      });

    if (disciplineId && !discipline)
      throw new ResourceNotFoundError('A disciplina não foi encontrada.');
    if (categoryId && !category) throw new ResourceNotFoundError('A categoria não foi encontrada.');
    if (subcategoryId && !subcategory)
      throw new ResourceNotFoundError('A subcategoria não foi encontrada.');
    if (!questionType) throw new BadRequestError('O tipo de questão é inválido.');

    return { discipline, category, subcategory };
  }

  private validateQuestionInput(questionInput: IQuestionCreateDTO): void {
    QuestionValidator.validate(questionInput, false);

    if (questionInput.alternatives) {
      this.validateAlternatives(questionInput.alternatives, questionInput.status!);
    }
  }

  private async validateDiscipline(disciplineId?: string, customerId?: string): Promise<void> {
    if (!disciplineId) return;
    const discipline = await this.disciplineRepo.findById(disciplineId);
    if (!discipline || discipline.customer_id !== customerId) {
      throw new BadRequestError('Disciplina não encontrada ou não pertence ao cliente.');
    }
  }

  private async createQuestion(questionInput: IQuestionCreateDTO): Promise<IQuestion> {
    const questionData = this.mapQuestionData(questionInput);
    const question = await this.questionRepo.insert(questionData);

    if (!question) {
      throw new BadRequestError('Erro ao criar a questão.');
    }
    return question;
  }

  private mapQuestionData(questionInput: IQuestionCreateDTO): IQuestion {
    return {
      title: questionInput.title,
      customer_id: questionInput.customerId,
      status: questionInput.status ?? 'draft',
      description: questionInput.description ?? '',
      difficulty: questionInput.difficulty ?? '',
      explanation_text: questionInput.explanationText ?? '',
      correct_text: questionInput.correctText ?? '',
      question_type_id: questionInput.questionTypeId ?? undefined,
      discipline_id: questionInput.disciplineId ?? undefined,
      category_id: questionInput.categoryId ?? undefined,
      subcategory_id: questionInput.subcategoryId ?? undefined,
      reference: questionInput.reference ?? '',
      institution: questionInput.institution ?? '',
      year: questionInput.year ?? undefined,
      image_url: questionInput.imageUrl ?? '',
      explanation_video: questionInput.explanationVideo ?? '',
      explanation_image: questionInput.explanationImage ?? '',
      published: questionInput.published ?? false,
    } as IQuestion;
  }

  private validateAlternatives(
    alternatives: IBaseAlternativeDTO[],
    status: QuestionStatusEnum
  ): void {
    const correctOptions = alternatives.filter((alt) => alt.correct);

    if (correctOptions.length === 0 && status !== QuestionStatusEnum.Draft) {
      throw new BadRequestError('Deve haver pelo menos uma alternativa correta.');
    }
    if (correctOptions.length > 1) {
      throw new BadRequestError('Apenas uma alternativa pode ser correta.');
    }
  }

  private async createAlternativesIfPresent(
    alternatives?: IBaseAlternativeDTO[],
    questionId?: string
  ): Promise<IAlternative[]> {
    if (!questionId || !alternatives || alternatives.length === 0) return [];

    const validAlternatives = alternatives.filter((alt) => alt.description?.trim() !== '');

    if (validAlternatives.length === 0) return [];

    const alternativeData = validAlternatives.map((alt) => ({
      question_id: questionId,
      correct: alt.correct,
      option: alt.option,
      description: alt.description,
    }));

    const insertedAlternatives = await this.alternativeRepo.insertAll(alternativeData);

    if (!insertedAlternatives) {
      throw new BadRequestError('Erro ao criar alternativas.');
    }

    return insertedAlternatives;
  }
}
