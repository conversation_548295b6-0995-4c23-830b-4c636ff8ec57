import { IQuestion } from '../../../model/IQuestion';
import { IAlternativesRepository } from '../../../repositories/assign/alternatives.assign';
import { IExamsQuestionsRepository } from '../../../repositories/assign/examsQuestions.assign';
import { IQuestionsRepository } from '../../../repositories/assign/questions.assign';
import { IQuestionsGroupsAccessRepository } from '../../../repositories/assign/questionsGroupsAccess.assign';
import { IQuestionsSimulatedsRepository } from '../../../repositories/assign/questionsSimulateds.assign';
import { GenericError } from '../../errors/GenericError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface IDeleteQuestionUseCaseRequest {
  id: string;
  customerId: string;
}

export class DeleteQuestionUseCase {
  constructor(
    private readonly questionsRepository: IQuestionsRepository,
    private readonly questionsGroupsAccessRepository: IQuestionsGroupsAccessRepository,
    private readonly alternativesRepository: IAlternativesRepository,
    private readonly examsQuestionsRepository: IExamsQuestionsRepository,
    private readonly questionsSimulatedsRepository: IQuestionsSimulatedsRepository
  ) {}

  async execute(inputData: IDeleteQuestionUseCaseRequest): Promise<IQuestion> {
    const { id, customerId } = inputData;

    await this.findQuestionOrThrow(id, customerId);
    await this.deleteRelatedData(id, customerId);

    return this.deleteQuestion(id);
  }

  private async findQuestionOrThrow(id: string, customerId: string): Promise<IQuestion> {
    const question = await this.questionsRepository.findById({ id, customerId });

    if (!question) {
      throw new ResourceNotFoundError('Questão não encontrada.');
    }

    return question;
  }

  private async deleteRelatedData(id: string, customerId: string): Promise<void> {
    const [groupAccessExists, alternativesExist] = await Promise.all([
      this.questionsGroupsAccessRepository.findByQuestionId({ questionId: id, customerId }),
      this.alternativesRepository.findByQuestionId({ questionId: id }),
      this.examsQuestionsRepository.findByQuestionId(id),
    ]);

    const deletions = [];

    if (groupAccessExists && groupAccessExists?.length > 0) {
      deletions.push(
        this.questionsGroupsAccessRepository.deleteByQuestionId({ questionId: id, customerId })
      );
    }

    if (alternativesExist && alternativesExist.length > 0) {
      deletions.push(this.alternativesRepository.deleteByQuestionId({ questionId: id }));
    }

    await Promise.all([
      this.examsQuestionsRepository.softDeleteByQuestionId(id),
      this.questionsSimulatedsRepository.softDeleteByQuestionId(id),
    ]);

    await Promise.all(deletions);
  }

  private async deleteQuestion(id: string): Promise<IQuestion> {
    const questionDeleted = await this.questionsRepository.deleteById({ id });

    if (!questionDeleted) {
      throw new GenericError('Erro inesperado ao excluir a questão.');
    }

    return questionDeleted;
  }
}
