import {
  IQuestionIdentifierDTO,
  IQuestionWithAlternativesDTO,
} from '../../../model/DTO/IQuestion.dto';
import { IQuestionsRepository } from '../../../repositories/assign/questions.assign';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

export class GetQuestionByIdUseCase {
  constructor(private readonly questionsRepository: IQuestionsRepository) {}

  async execute({
    questionId,
    customerId,
  }: IQuestionIdentifierDTO): Promise<IQuestionWithAlternativesDTO> {
    const { question } = await this.questionsRepository.findQuestionFullData({
      questionId,
      customerId,
    });

    if (!question) {
      throw new ResourceNotFoundError('Questão não encontrada');
    }

    return question;
  }
}
