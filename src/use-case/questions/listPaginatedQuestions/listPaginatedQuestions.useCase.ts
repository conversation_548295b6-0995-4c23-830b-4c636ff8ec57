import { IPaginationDTO } from '../../../model/DTO/IGeneric.dto';
import { IQuestionWithAlternativesDTO } from '../../../model/DTO/IQuestion.dto';
import { IQuestionsRepository } from '../../../repositories/assign/questions.assign';

interface IListPaginatedQuestionsUseCaseRequest {
  customerId: string;
  page?: number;
  limit?: number;
  difficulty?: string[];
  status?: string[];
  disciplineIds?: string[];
  categoryIds?: string[];
  subcategoryIds?: string[];
  search?: string;
  orderByColumn?: string;
  orderDirection?: string;
  questionGroupId?: string;
  examId?: string;
  allQuestions?: string;
}

interface IListPaginatedQuestionsUseCaseResponse {
  questionList: IQuestionWithAlternativesDTO[];
  paginationInfo: IPaginationDTO;
}

export class ListPaginatedQuestionsUseCase {
  constructor(private readonly questionsRepository: IQuestionsRepository) {}

  async execute(
    inputData: IListPaginatedQuestionsUseCaseRequest
  ): Promise<IListPaginatedQuestionsUseCaseResponse> {
    const {
      customerId,
      page,
      limit,
      difficulty,
      status,
      disciplineIds,
      categoryIds,
      subcategoryIds,
      search,
      orderByColumn,
      orderDirection,
      questionGroupId,
      examId,
      allQuestions,
    } = inputData;

    const { questionList, paginationInfo } = await this.questionsRepository.listPaginatedQuestions({
      customerId,
      page,
      limit,
      difficulty,
      status,
      disciplineIds,
      categoryIds,
      subcategoryIds,
      search,
      orderByColumn,
      orderDirection,
      questionGroupId,
      examId,
      allQuestions,
    });

    return {
      paginationInfo: { ...paginationInfo },
      questionList: questionList ?? [],
    };
  }
}
