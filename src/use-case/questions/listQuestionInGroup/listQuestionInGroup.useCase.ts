import { IQuestionWithAlternativesDTO } from '../../../model/DTO/IQuestion.dto';
import { IQuestionGroup } from '../../../model/IQuestionGroup';
import { IQuestionsRepository } from '../../../repositories/assign/questions.assign';
import { IQuestionsGroupsRepository } from '../../../repositories/assign/questionsGroups.assign';
import {
  ListQuestionInQuestionGroupParamsDTO,
  ListQuestionInQuestionGroupQueryDTO,
} from '../../../schema/questions.schema';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface IListQuestionsInGroupUseCaseRequest
  extends ListQuestionInQuestionGroupQueryDTO,
    ListQuestionInQuestionGroupParamsDTO {
  customerId: string;
}

interface IListQuestionsInGroupUseCaseResponse {
  message: string;
  totalQuestions: number;
  questionGroup: IQuestionGroup;
  questions: IQuestionWithAlternativesDTO[];
}

export class ListQuestionsInGroupUseCase {
  constructor(
    private readonly questionsRepository: IQuestionsRepository,
    private readonly questionsGroupsRepository: IQuestionsGroupsRepository
  ) {}

  async execute(
    payload: IListQuestionsInGroupUseCaseRequest
  ): Promise<IListQuestionsInGroupUseCaseResponse> {
    const questionGroup = await this.getQuestionGroup(payload.id, payload.customerId);

    const questions = await this.getQuestions(payload);

    return {
      message: 'Questões listadas com sucesso',
      totalQuestions: questions.length,
      questionGroup,
      questions,
    };
  }

  private async getQuestionGroup(id: string, customerId: string): Promise<IQuestionGroup> {
    const questionGroup = await this.questionsGroupsRepository.findById({ id, customerId });

    if (!questionGroup) {
      throw new ResourceNotFoundError('Grupo de questões não encontrado');
    }

    return questionGroup;
  }

  private async getQuestions(
    filters: IListQuestionsInGroupUseCaseRequest
  ): Promise<IQuestionWithAlternativesDTO[]> {
    const {
      customerId,
      difficulty,
      disciplineIds,
      categoryIds,
      subcategoryIds,
      id: questionGroupId,
    } = filters;

    const { items } = await this.questionsRepository.listQuestionsInGroup({
      customerId,
      difficulty,
      disciplineIds,
      categoryIds,
      subcategoryIds,
      questionGroupId,
    });

    return items;
  }
}
