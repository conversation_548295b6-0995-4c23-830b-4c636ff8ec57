import { IQuestionHighlight } from '../../../model/IQuestionHighlight';
import { IExamsRepository } from '../../../repositories/assign/exams.assign';
import { IExamsAccessRepository } from '../../../repositories/assign/examsAccess.assign';
import { IQuestionsRepository } from '../../../repositories/assign/questions.assign';
import { IQuestionsHighlightsRepository } from '../../../repositories/assign/questionsHighlights.assign';
import { ISimulatedsRepository } from '../../../repositories/assign/simulateds.assign';
import { QuestionHighlightsRequestDTO } from '../../../schema/questions.schema';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface ManageQuestionHighlightsRequest extends QuestionHighlightsRequestDTO {}

export class ManageQuestionHighlightsUseCase {
  constructor(
    private readonly highlightsRepo: IQuestionsHighlightsRepository,
    private readonly questionsRepo: IQuestionsRepository,
    private readonly simulatedRepo: ISimulatedsRepository,
    private readonly examRepo: IExamsRepository,
    private readonly examAccessRepo: IExamsAccessRepository
  ) {}

  async execute(request: ManageQuestionHighlightsRequest): Promise<IQuestionHighlight> {
    await this.validateRequestEntities(request);

    const existingHighlight = await this.findExistingHighlight(request);

    const highlightPayload = this.buildHighlightPayload(request);

    return existingHighlight
      ? this.updateExistingHighlight(existingHighlight, highlightPayload)
      : this.createNewHighlight({
          ...highlightPayload,
          user_id: request.userId,
          question_id: request.questionId,
        });
  }

  private async validateRequestEntities(request: ManageQuestionHighlightsRequest): Promise<void> {
    const { questionId, customerId, simulatedId, examId, examAccessId } = request;

    await this.verifyQuestionExists(questionId, customerId);

    if (!simulatedId && !examId) {
      throw new BadRequestError('É necessário informar um simulatedId ou examId');
    }

    if (simulatedId != null) await this.verifySimulatedExists(simulatedId);
    if (examId != null) await this.verifyExamExists(examId);
    if (examAccessId && examAccessId != null) await this.verifyExamAccessExists(examAccessId);
  }

  private async findExistingHighlight(
    request: ManageQuestionHighlightsRequest
  ): Promise<IQuestionHighlight | undefined> {
    const { questionId, userId, simulatedId, examId, simulatedAccessId } = request;

    const searchParams: Record<string, unknown> = {
      question_id: questionId,
      user_id: userId,
    };

    if (simulatedId) searchParams.simulated_id = simulatedId;
    if (examId) searchParams.exam_id = examId;
    if (simulatedAccessId) searchParams.simulated_access_id = simulatedAccessId;

    return this.highlightsRepo.findOneBy(searchParams);
  }

  private buildHighlightPayload(
    request: ManageQuestionHighlightsRequest
  ): Partial<IQuestionHighlight> {
    const payload: Partial<IQuestionHighlight> = {
      saved: request.saved ?? false,
      highlight_description: JSON.stringify(request.highlightDescription ?? []),
      highlight_explanation_text: JSON.stringify(request.highlightExplanationText ?? []),
      alternatives_line_through: request.alternativesLineThrough ?? [],
    };

    if (request.simulatedId !== null && request.simulatedId !== undefined) {
      payload.simulated_id = request.simulatedId;
    }

    if (request.examId !== null && request.examId !== undefined) {
      payload.exam_id = request.examId;
    }
    if (request.simulatedAccessId !== null && request.simulatedAccessId !== undefined) {
      payload.simulated_access_id = request.simulatedAccessId;
    }
    if (request.examAccessId !== null && request.examAccessId !== undefined) {
      payload.exam_access_id = request.examAccessId;
    }

    return payload;
  }

  private async updateExistingHighlight(
    existing: IQuestionHighlight,
    data: Partial<IQuestionHighlight>
  ): Promise<IQuestionHighlight> {
    const updatedHighlight = { ...existing, ...data };
    await this.highlightsRepo.update(updatedHighlight);
    return updatedHighlight;
  }

  private async createNewHighlight(data: Partial<IQuestionHighlight>): Promise<IQuestionHighlight> {
    return this.highlightsRepo.insert(data);
  }

  private async verifyQuestionExists(questionId: string, customerId: string): Promise<void> {
    const question = await this.questionsRepo.findOneBy({
      id: questionId,
      customer_id: customerId,
    });
    if (!question) throw new ResourceNotFoundError('Questão não encontrada');
  }

  private async verifySimulatedExists(simulatedId: string): Promise<void> {
    const simulated = await this.simulatedRepo.findOneBy({ id: simulatedId });
    if (!simulated) throw new ResourceNotFoundError('Simulado não encontrado');
  }

  private async verifyExamExists(examId: string): Promise<void> {
    const exam = await this.examRepo.findOneBy({ id: examId });
    if (!exam) throw new ResourceNotFoundError('Exame não encontrado');
  }

  private async verifyExamAccessExists(examAccessId: string): Promise<void> {
    const examAccess = await this.examAccessRepo.findOneBy({ id: examAccessId });
    if (!examAccess) throw new ResourceNotFoundError('Aceesso ao prova não encontrado');
  }
}
