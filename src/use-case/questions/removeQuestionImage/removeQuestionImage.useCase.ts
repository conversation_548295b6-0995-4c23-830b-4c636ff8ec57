import { env } from '../../../env';
import { UploadService } from '../../../services/uploadFile.service';

export class RemoveQuestionImageUseCase {
  constructor(private readonly uploadService: UploadService) {}

  async execute(inputData: { fileKey: string }): Promise<void> {
    const { fileKey } = inputData;
    const bucketName = env.AWS_S3_PUBLIC_BUCKET;

    await this.uploadService.checkFileExists(fileKey, bucketName);

    await this.uploadService.deleteFile(fileKey, bucketName);
  }
}
