import { IAlternativeWithIdDTO, IBaseAlternativeDTO } from '../../../model/DTO/IAlternative.dto';
import {
  IQuestionRequestDTO,
  IQuestionUpdateDTO,
  IQuestionWithAlternativesDTO,
} from '../../../model/DTO/IQuestion.dto';
import { QuestionStatusEnum } from '../../../model/enums/questions.enum';
import { IAlternative } from '../../../model/IAlternative';
import { IQuestion } from '../../../model/IQuestion';
import { IAggregatedDataAssignRepository } from '../../../repositories/assign/aggregatedData.assign';
import { IAlternativesRepository } from '../../../repositories/assign/alternatives.assign';
import { IQuestionsRepository } from '../../../repositories/assign/questions.assign';
import { QuestionValidator } from '../../../services/validators/question.validator';
import { BadRequestError } from '../../errors/BadRequestError';
import { GenericError } from '../../errors/GenericError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface IUpdateQuestionRequest extends IQuestionRequestDTO {}

interface IUpdateQuestionUseCaseResponse {
  question: IQuestion & {
    alternatives: IAlternative[];
    disciplineName?: string;
    categoryName?: string;
    subcategoryName?: string;
  };
}

export class UpdateQuestionUseCase {
  constructor(
    private readonly questionsRepository: IQuestionsRepository,
    private readonly alternativesRepository: IAlternativesRepository,
    private readonly aggregatedDataRepository: IAggregatedDataAssignRepository
  ) {}

  async execute(inputData: IUpdateQuestionRequest): Promise<IUpdateQuestionUseCaseResponse> {
    this.validateInput(inputData);

    const { question, discipline, category, subcategory, questionWithAlternatives } =
      await this.getEntities(inputData);

    if (inputData.status !== QuestionStatusEnum.Draft) {
      this.validatePublicationConstraints(questionWithAlternatives!, inputData);
    }

    const existingAlternatives = questionWithAlternatives?.alternatives ?? [];

    const updatedAlternatives = inputData.alternatives
      ? await this.processAlternatives(questionWithAlternatives!, inputData, existingAlternatives)
      : existingAlternatives;

    const updatedData = this.buildUpdatedQuestionData(inputData, question);

    const updatedQuestion = await this.questionsRepository.update({ ...question, ...updatedData });

    if (!updatedQuestion) {
      throw new GenericError('Ocorreu um erro ao atualizar a questão. Tente novamente.');
    }

    const sortedAlternatives = updatedAlternatives.sort((a, b) => a.option.localeCompare(b.option));

    return {
      question: {
        ...updatedQuestion,
        alternatives: sortedAlternatives,
        disciplineName: discipline?.name,
        categoryName: category?.name,
        subcategoryName: subcategory?.name,
      },
    };
  }

  private validateInput(input: IQuestionRequestDTO): void {
    QuestionValidator.validate(input, true);

    if (input.alternatives) {
      const alternativesWithQuestionId = input.alternatives.map((alternative) => ({
        ...alternative,
        question_id: input.questionId,
      }));
      this.validateAlternatives(alternativesWithQuestionId, input.status!);
    }
  }

  private validateAlternatives(
    alternatives: IBaseAlternativeDTO[],
    status: QuestionStatusEnum
  ): void {
    const correctOptions = alternatives.filter((alt) => alt.correct);

    if (correctOptions.length === 0 && status !== QuestionStatusEnum.Draft) {
      throw new BadRequestError('A questão deve ter pelo menos uma alternativa correta.');
    }

    if (correctOptions.length > 1) {
      throw new BadRequestError('Cada questão pode ter apenas uma alternativa correta.');
    }
  }

  private validatePublicationConstraints(
    existingQuestion: IQuestionWithAlternativesDTO,
    inputData: IUpdateQuestionRequest
  ): void {
    if (inputData.status !== QuestionStatusEnum.Published || !inputData.published) return;

    if (
      existingQuestion.published_at &&
      existingQuestion.question_type_id !== inputData.questionTypeId
    ) {
      throw new BadRequestError(
        'O tipo da questão já foi definido e não pode ser alterado após a publicação.'
      );
    }

    QuestionValidator.validate(inputData, true);
  }

  private async getEntities(inputData: IUpdateQuestionRequest) {
    const { disciplineId, categoryId, subcategoryId, questionId, customerId, questionTypeId } =
      inputData;
    const { question, discipline, category, subcategory, questionType, questionWithAlternatives } =
      await this.aggregatedDataRepository.findEntities({
        questionId,
        customerId,
        disciplineId,
        categoryId,
        subcategoryId,
        questionTypeId,
        questionWithAlternatives: true,
      });

    if (!question) throw new ResourceNotFoundError('A questão não foi encontrada.');
    if (disciplineId && !discipline)
      throw new ResourceNotFoundError('A disciplina não foi encontrada.');
    if (categoryId && !category) throw new ResourceNotFoundError('A categoria não foi encontrada.');
    if (subcategoryId && !subcategory)
      throw new ResourceNotFoundError('A subcategoria não foi encontrada.');
    if (!questionType) throw new BadRequestError('O tipo de questão é inválido.');
    if (!questionWithAlternatives) throw new BadRequestError('A questão não foi encontrada.');

    return { question, discipline, category, subcategory, questionWithAlternatives };
  }

  private async processAlternatives(
    question: IQuestionWithAlternativesDTO,
    inputData: IUpdateQuestionRequest,
    existingAlternatives: IAlternative[]
  ): Promise<IAlternative[]> {
    await this.removeEmptyAlternatives(question, existingAlternatives);
    return this.updateAlternatives(question, inputData, existingAlternatives);
  }

  private async removeEmptyAlternatives(
    questionWithAlternatives: IQuestionWithAlternativesDTO,
    existingAlternatives: IAlternative[]
  ): Promise<IAlternative[]> {
    if (questionWithAlternatives.published_at) {
      return existingAlternatives;
    }

    return existingAlternatives;
  }

  private async updateAlternatives(
    question: IQuestionWithAlternativesDTO,
    inputData: IUpdateQuestionRequest,
    existingAlternatives: IAlternative[]
  ): Promise<IAlternative[]> {
    const alternativesToProcess = inputData.alternatives;
    if (!alternativesToProcess || !alternativesToProcess.length) return existingAlternatives;

    const existingAltMap = new Map(existingAlternatives.map((alt) => [alt.option, alt]));

    const updates: IAlternativeWithIdDTO[] = [];
    const newAlternatives: IBaseAlternativeDTO[] = [];
    const toDelete = new Set<string>();
    let modified = false;

    for (const alt of alternativesToProcess) {
      const existingAlternative = existingAltMap.get(alt.option);

      if (existingAlternative) {
        if (!alt.description || alt.description.trim() === '') {
          if (question.published_at) {
            throw new BadRequestError(
              `Não é permitido remover alternativas de uma questão publicada.`
            );
          }
          toDelete.add(existingAlternative.id);
          modified = true;
          continue;
        }

        if (
          alt.description.trim() !== existingAlternative.description.trim() ||
          alt.correct !== existingAlternative.correct
        ) {
          updates.push({
            id: existingAlternative.id,
            question_id: question.id,
            description: alt.description.trim(),
            correct: alt.correct,
            option: alt.option,
          });

          existingAlternative.description = alt.description.trim();
          existingAlternative.correct = alt.correct;
          modified = true;
        }
      } else {
        if (!alt.description || alt.description.trim() === '') {
          continue;
        }

        if (question.published_at) {
          throw new BadRequestError(
            `Não é permitido adicionar novas alternativas a uma questão publicada.`
          );
        }

        newAlternatives.push({ ...alt, question_id: question.id });
        modified = true;
      }
    }

    if (!modified) {
      return existingAlternatives;
    }

    if (updates.length > 0) {
      await this.alternativesRepository.updateAll(updates);
    }

    if (toDelete.size > 0) {
      await this.alternativesRepository.deleteAllPermanent([...toDelete]);
      existingAlternatives = existingAlternatives.filter((alt) => !toDelete.has(alt.id));
    }

    if (newAlternatives.length > 0) {
      const filteredNewAlternatives = newAlternatives.filter(
        (alt) => !existingAltMap.has(alt.option)
      );

      if (filteredNewAlternatives.length > 0) {
        const insertedAlternatives = await this.alternativesRepository.insertAll(
          filteredNewAlternatives.map((alt) => ({
            question_id: question.id,
            correct: alt.correct,
            option: alt.option,
            description: alt.description.trim(),
          }))
        );
        existingAlternatives.push(...insertedAlternatives);
      }
    }

    return existingAlternatives;
  }

  private buildUpdatedQuestionData(
    inputData: IUpdateQuestionRequest,
    existingQuestion: IQuestion
  ): Partial<IQuestionUpdateDTO> {
    const updatedData = {
      id: inputData.questionId,
      title: existingQuestion.title,
      description: inputData.description ?? existingQuestion.description,
      difficulty: inputData.difficulty ?? existingQuestion.difficulty,
      explanation_text: inputData.explanationText ?? existingQuestion.explanation_text,
      explanation_video: inputData.explanationVideo ?? existingQuestion.explanation_video,
      explanation_image: inputData.explanationImage ?? existingQuestion.explanation_image,
      image_url: inputData.imageUrl ?? existingQuestion.image_url,
      published: inputData.published ?? existingQuestion.published,
      status: inputData.status ?? existingQuestion.status ?? QuestionStatusEnum.Draft,
      correct_text: inputData.correctText ?? existingQuestion.correct_text,
      discipline_id: inputData.disciplineId ?? existingQuestion.discipline_id,
      category_id: inputData.categoryId ?? existingQuestion.category_id,
      subcategory_id: inputData.subcategoryId ?? existingQuestion.subcategory_id,
      reference: inputData.reference ?? existingQuestion.reference,
      institution: inputData.institution ?? existingQuestion.institution,
      year:
        inputData.year === undefined || inputData.year === null
          ? null
          : (inputData.year ?? existingQuestion.year),
      customer_id: inputData.customerId,
      published_at:
        existingQuestion.published_at ||
        (inputData.published && inputData.status === QuestionStatusEnum.Published
          ? new Date()
          : null),
      question_type_id:
        existingQuestion.published_at && existingQuestion.question_type_id
          ? existingQuestion.question_type_id
          : inputData.questionTypeId,
    };

    return Object.fromEntries(
      Object.entries(updatedData).filter(([, value]) => value !== undefined)
    );
  }
}
