import { env } from '../../../env';
import { UploadTypeEnum } from '../../../model/enums/uploadType.enum';
import { UploadService } from '../../../services/uploadFile.service';
import {
  maxFileSizeMb,
  QuestionImageValidator,
} from '../../../services/validators/uploadImage.validator';
import { BadRequestError } from '../../errors/BadRequestError';

interface UploadQuestionImageRequest {
  file: Express.Multer.File;
  codeQuestion: string;
  customerId: string;
  imageType?: string;
  previousImageKey?: string;
}

interface UploadQuestionImageResponse {
  imageUrl: string;
}

export class UploadQuestionImageUseCase {
  private readonly validator = new QuestionImageValidator();

  constructor(private readonly uploadService: UploadService) {}

  async execute({
    file,
    codeQuestion,
    customerId,
    previousImageKey,
    imageType = 'enunciado',
  }: UploadQuestionImageRequest): Promise<UploadQuestionImageResponse> {
    if (!file) throw new BadRequestError('Arquivo e obrigatório!');

    const bucketName = env.AWS_S3_PUBLIC_BUCKET;

    const isImageValid = await this.validator.isValidImage(file);

    if (!isImageValid) {
      throw new BadRequestError(
        `Formato inválido! Apenas imagens PNG e JPG de até ${maxFileSizeMb}MB são permitidas.`
      );
    }

    const storagePath = `${customerId}/${UploadTypeEnum.Questions}/${codeQuestion}/${imageType}-${file.originalname}`;

    const uploadedImageUrl = await this.uploadService.uploadFile({
      file,
      storagePath,
      bucket: bucketName,
    });

    if (previousImageKey && previousImageKey !== uploadedImageUrl) {
      await this.uploadService.deleteFile(previousImageKey, bucketName);
    }

    return { imageUrl: uploadedImageUrl };
  }
}
