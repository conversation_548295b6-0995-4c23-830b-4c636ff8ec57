import { IQuestionGroup } from '../../../model/IQuestionGroup';
import { IQuestionsGroupsRepository } from '../../../repositories/assign/questionsGroups.assign';
import { GenericError } from '../../errors/GenericError';
import { ResourceAlreadyExistsError } from '../../errors/ResourceAlreadyExistsError';

interface ICreateQuestionGroupRequest {
  name: string;
  customerId: string;
}

export class CreateQuestionGroupUseCase {
  constructor(private readonly questionsGroupsRepository: IQuestionsGroupsRepository) {}

  public async execute(request: ICreateQuestionGroupRequest): Promise<IQuestionGroup> {
    await this.validateGroupUniqueness(request);

    return this.createQuestionGroup(request);
  }

  private async validateGroupUniqueness(request: ICreateQuestionGroupRequest): Promise<void> {
    const { name, customerId } = request;

    const existingGroup = await this.questionsGroupsRepository.findQuestionGroup({
      name,
      customerId,
    });

    if (existingGroup) {
      throw new ResourceAlreadyExistsError(`O grupo '${name}' já existe.`);
    }
  }

  private async createQuestionGroup(request: ICreateQuestionGroupRequest): Promise<IQuestionGroup> {
    const { name, customerId } = request;

    const createdGroup = await this.questionsGroupsRepository.insert({
      name,
      customer_id: customerId,
    } as IQuestionGroup);

    if (!createdGroup) {
      throw new GenericError('Falha ao criar o grupo. Tente novamente.');
    }

    return createdGroup;
  }
}
