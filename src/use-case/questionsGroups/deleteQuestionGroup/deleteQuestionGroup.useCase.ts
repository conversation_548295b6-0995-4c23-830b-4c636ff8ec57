import { IQuestionsGroupsRepository } from '../../../repositories/assign/questionsGroups.assign';
import { IQuestionsGroupsAccessRepository } from '../../../repositories/assign/questionsGroupsAccess.assign';
import { BadRequestError } from '../../errors/BadRequestError';
import { GenericError } from '../../errors/GenericError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface IDeleteQuestionGroupUseCaseRequest {
  id: string;
  customerId: string;
}

export class DeleteQuestionGroupUseCase {
  constructor(
    private readonly questionsGroupsRepository: IQuestionsGroupsRepository,
    private readonly questionsGroupsAccessRepository: IQuestionsGroupsAccessRepository
  ) {}

  async execute(request: IDeleteQuestionGroupUseCaseRequest): Promise<object> {
    await this.validateQuestionGroupExists(request);

    await this.validateNoAccessLinked(request);

    const deletedQuestionGroup = await this.deleteQuestionGroup(request);

    return { deletedQuestionGroup };
  }

  private async validateQuestionGroupExists(
    request: IDeleteQuestionGroupUseCaseRequest
  ): Promise<void> {
    const { id, customerId } = request;

    const questionGroup = await this.questionsGroupsRepository.findById({ id, customerId });

    if (!questionGroup) {
      throw new ResourceNotFoundError('Grupo de questões não encontrado.');
    }
  }

  private async validateNoAccessLinked(request: IDeleteQuestionGroupUseCaseRequest): Promise<void> {
    const { id, customerId } = request;

    const questionGroupAccess = await this.questionsGroupsAccessRepository.findAllAccessByGroupId({
      questionGroupId: id,
      customerId,
    });

    if (questionGroupAccess.length > 0) {
      throw new BadRequestError(
        'Não é possível excluir o grupo. Existem questões vinculadas. Remova-as antes de continuar.'
      );
    }
  }

  private async deleteQuestionGroup(request: IDeleteQuestionGroupUseCaseRequest): Promise<object> {
    const { id, customerId } = request;

    const deletedQuestionGroup = await this.questionsGroupsRepository.deleteById({
      id,
      customerId,
    });

    if (!deletedQuestionGroup) {
      throw new GenericError('Erro ao deletar o grupo de questões.');
    }

    return deletedQuestionGroup;
  }
}
