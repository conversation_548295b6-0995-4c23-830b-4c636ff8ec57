import { IQuestionGroup } from '../../../model/IQuestionGroup';
import { ICustomerRepository } from '../../../repositories/assign/customers.assign';
import { IQuestionsRepository } from '../../../repositories/assign/questions.assign';
import { IQuestionsGroupsRepository } from '../../../repositories/assign/questionsGroups.assign';
import { verifyExternalCustomer } from '../../../services/verifyExternalCustomer.service';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface IGetExternalQuestionGroupRequest {
  id: string;
  customerId: string;
}

interface IGetExternalQuestionGroupResponse {
  questionGroup: IQuestionGroup & { totalPublishedQuestions: number };
}

export class GetExternalQuestionGroupUseCase {
  constructor(
    private readonly questionsGroupsRepository: IQuestionsGroupsRepository,
    private readonly questionsRepository: IQuestionsRepository,
    private readonly customerRepository: ICustomerRepository
  ) {}

  async execute(
    payload: IGetExternalQuestionGroupRequest
  ): Promise<IGetExternalQuestionGroupResponse> {
    const customer = await this.getCustomer(payload.customerId);

    const questionGroup = await this.getQuestionGroup(payload.id, customer.id);

    const totalPublishedQuestions = await this.countPublishedQuestions(payload.id, customer.id);

    return {
      questionGroup: {
        ...questionGroup,
        totalPublishedQuestions,
      },
    };
  }

  private async getCustomer(customerId: string) {
    return await verifyExternalCustomer(this.customerRepository, customerId);
  }

  private async getQuestionGroup(id: string, customerId: string) {
    const questionGroup = await this.questionsGroupsRepository.findById({ id, customerId });
    if (!questionGroup) {
      throw new ResourceNotFoundError('Grupo de questões não encontrado');
    }
    return questionGroup;
  }

  private async countPublishedQuestions(groupId: string, customerId: string) {
    const publishedQuestions = await this.questionsRepository.getPublishedQuestionsByGroupId({
      questionGroupId: groupId,
      customerId,
    });
    return publishedQuestions.length;
  }
}
