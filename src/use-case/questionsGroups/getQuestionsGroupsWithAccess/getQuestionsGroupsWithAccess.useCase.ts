import { IQuestionGroupQueryParamsDTO } from '../../../model/DTO/IQuestionGroup.dto';
import { IQuestionGroup } from '../../../model/IQuestionGroup';
import { IAggregatedDataAssignRepository } from '../../../repositories/assign/aggregatedData.assign';
import { IQuestionsGroupsRepository } from '../../../repositories/assign/questionsGroups.assign';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface IGetQuestionsGroupsWithAccessRequest extends IQuestionGroupQueryParamsDTO {}

export class GetQuestionsGroupsWithAccessUseCase {
  constructor(
    private readonly questionsGroupsRepository: IQuestionsGroupsRepository,
    private readonly aggregateDataRepository: IAggregatedDataAssignRepository
  ) {}

  async execute(inputData: IGetQuestionsGroupsWithAccessRequest): Promise<IQuestionGroup[]> {
    const { questionId, customerId } = inputData;

    const { question } = await this.aggregateDataRepository.findEntities({
      questionId,
      customerId,
    });

    if (!question) {
      throw new ResourceNotFoundError('Questão não encontrada!');
    }

    const questionsGroups = await this.questionsGroupsRepository.listSelectableQuestionGroups({
      questionId,
      customerId,
    });

    return questionsGroups;
  }
}
