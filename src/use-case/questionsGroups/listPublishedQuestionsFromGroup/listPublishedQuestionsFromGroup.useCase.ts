import { IQuestionWithAlternativesDTO } from '../../../model/DTO/IQuestion.dto';
import { IQuestionsRepository } from '../../../repositories/assign/questions.assign';
import { IQuestionsGroupsRepository } from '../../../repositories/assign/questionsGroups.assign';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface ListPublishedQuestionsParams {
  questionGroupId: string;
  customerId: string;
}

interface IListPublishedQuestionsFromGroupUseCaseResponse {
  questions: IQuestionWithAlternativesDTO[];
  questionGroupName: string;
  totalQuestions: number;
}

export class ListPublishedQuestionsFromGroupUseCase {
  constructor(
    private readonly questionsRepository: IQuestionsRepository,
    private readonly questionsGroupsRepository: IQuestionsGroupsRepository
  ) {}

  async execute(
    inputData: ListPublishedQuestionsParams
  ): Promise<IListPublishedQuestionsFromGroupUseCaseResponse> {
    const { questionGroupId, customerId } = inputData;

    const questionGroup = await this.getValidatedQuestionGroup(questionGroupId, customerId);

    const questions = await this.getPublishedQuestions(questionGroupId, customerId);

    return {
      questionGroupName: questionGroup.name,
      totalQuestions: questions.length,
      questions,
    };
  }

  private async getValidatedQuestionGroup(questionGroupId: string, customerId: string) {
    const group = await this.questionsGroupsRepository.findById({
      id: questionGroupId,
      customerId,
    });

    if (!group) {
      throw new ResourceNotFoundError('Grupo de questões não encontrado');
    }

    return group;
  }

  private async getPublishedQuestions(questionGroupId: string, customerId: string) {
    return this.questionsRepository.getPublishedQuestionsByGroupId({
      questionGroupId,
      customerId,
    });
  }
}
