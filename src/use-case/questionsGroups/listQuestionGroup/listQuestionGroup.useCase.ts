import { IQuestionGroup } from '../../../model/IQuestionGroup';
import { ICustomerRepository } from '../../../repositories/assign/customers.assign';
import { IQuestionsGroupsRepository } from '../../../repositories/assign/questionsGroups.assign';
import { verifyExternalCustomer } from '../../../services/verifyExternalCustomer.service';

interface IListQuestionGroupUseCaseRequest {
  customerId: string;
  isExternal?: boolean;
  search?: string;
  orderByColumn?: string;
  orderDirection?: string;
}

interface IListQuestionGroupUseCaseResponse {
  questionsGroups: IQuestionGroup[];
  totalItems: number;
}

export class ListQuestionGroupUseCase {
  constructor(
    private readonly questionsGroupsRepository: IQuestionsGroupsRepository,
    private readonly customerRepository: ICustomerRepository
  ) {}

  async execute(
    request: IListQuestionGroupUseCaseRequest
  ): Promise<IListQuestionGroupUseCaseResponse> {
    const { search, orderByColumn, orderDirection } = request;
    let customerId = request.customerId;

    if (request.isExternal) {
      const customer = await verifyExternalCustomer(this.customerRepository, customerId);
      customerId = customer.id;
    }

    const questionsGroups = await this.questionsGroupsRepository.listAllQuestionsGroups({
      customerId,
      search,
      orderByColumn: orderByColumn || 'name',
      orderDirection: orderDirection || 'asc',
    });

    if (orderByColumn === 'name') {
      questionsGroups.sort((a, b) => {
        const nameA = a.name.toLowerCase();
        const nameB = b.name.toLowerCase();

        return orderDirection === 'asc'
          ? nameA.localeCompare(nameB, 'pt-BR', { sensitivity: 'base' })
          : nameB.localeCompare(nameA, 'pt-BR', { sensitivity: 'base' });
      });
    }

    return { totalItems: questionsGroups.length, questionsGroups };
  }
}
