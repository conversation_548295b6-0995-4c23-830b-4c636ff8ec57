import { IQuestionGroup } from '../../../model/IQuestionGroup';
import { IQuestionsGroupsRepository } from '../../../repositories/assign/questionsGroups.assign';

interface IListQuestionGroupWithCountUseCaseRequest {
  customerId: string;
  search?: string;
  orderByColumn?: string;
  orderDirection?: string;
}

interface IListQuestionGroupWithCountUseCaseResponse {
  questionsGroups: IQuestionGroup[];
  totalItems: number;
}

export class ListQuestionGroupWithCountUseCase {
  constructor(private readonly questionsGroupsRepository: IQuestionsGroupsRepository) {}

  async execute(
    request: IListQuestionGroupWithCountUseCaseRequest
  ): Promise<IListQuestionGroupWithCountUseCaseResponse> {
    const { search, orderByColumn, orderDirection } = request;
    const customerId = request.customerId;

    const questionsGroups = await this.questionsGroupsRepository.listAllQuestionsGroups({
      customerId,
      search,
      orderByColumn: orderByColumn || 'name',
      orderDirection: orderDirection || 'asc',
    });

    if (orderByColumn === 'name') {
      questionsGroups.sort((a, b) => {
        const nameA = a.name.toLowerCase();
        const nameB = b.name.toLowerCase();

        return orderDirection === 'asc'
          ? nameA.localeCompare(nameB, 'pt-BR', { sensitivity: 'base' })
          : nameB.localeCompare(nameA, 'pt-BR', { sensitivity: 'base' });
      });
    }

    return { totalItems: questionsGroups.length, questionsGroups };
  }
}
