import { IQuestionGroup } from '../../../model/IQuestionGroup';
import { IQuestionsGroupsRepository } from '../../../repositories/assign/questionsGroups.assign';
import { ResourceAlreadyExistsError } from '../../errors/ResourceAlreadyExistsError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface IUpdateRequest {
  id: string;
  name: string;
  customerId: string;
}

export class UpdateQuestionGroupUseCase {
  constructor(private readonly questionsGroupsRepository: IQuestionsGroupsRepository) {}

  async execute(request: IUpdateRequest): Promise<{ updatedQuestionGroup: IQuestionGroup }> {
    const { id, name, customerId } = request;

    const questionGroup = await this.validateExistingQuestionGroup(id, customerId);

    const sameName = this.isSameName(questionGroup, name);

    if (sameName) {
      return { updatedQuestionGroup: questionGroup };
    }

    await this.validateUniqueGroupName(name, customerId, id);

    return this.updateQuestionGroup(questionGroup, name);
  }

  private async validateExistingQuestionGroup(
    id: string,
    customerId: string
  ): Promise<IQuestionGroup> {
    const questionGroup = await this.questionsGroupsRepository.findById({ id, customerId });

    if (!questionGroup) {
      throw new ResourceNotFoundError('Grupo de questões não encontrado.');
    }

    return questionGroup;
  }

  private isSameName(questionGroup: IQuestionGroup, name: string) {
    if (questionGroup.name === name) {
      return true;
    }
  }

  private async validateUniqueGroupName(
    name: string,
    customerId: string,
    id: string
  ): Promise<void> {
    const existingGroupWithSameName = await this.questionsGroupsRepository.findQuestionGroup({
      name,
      customerId,
    });

    if (existingGroupWithSameName && existingGroupWithSameName.id !== id) {
      throw new ResourceAlreadyExistsError(`Já existe um grupo de questões com o nome '${name}'.`);
    }
  }

  private async updateQuestionGroup(
    questionGroup: IQuestionGroup,
    name: string
  ): Promise<{ updatedQuestionGroup: IQuestionGroup }> {
    const updatedQuestionGroup: IQuestionGroup = {
      ...questionGroup,
      name,
    };

    const updatedQuestionGroups = await this.questionsGroupsRepository.update(updatedQuestionGroup);
    console.log(updatedQuestionGroups);
    return { updatedQuestionGroup };
  }
}
