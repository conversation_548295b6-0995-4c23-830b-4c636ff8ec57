import { IQuestionGroupAccessIdentifiersDTO } from '../../../model/DTO/IQuestionGroupAccess.dto';
import { IAggregatedDataAssignRepository } from '../../../repositories/assign/aggregatedData.assign';
import { IQuestionsGroupsAccessRepository } from '../../../repositories/assign/questionsGroupsAccess.assign';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

export class ManageQuestionGroupAccessUseCase {
  constructor(
    private readonly questionsGroupsAccessRepository: IQuestionsGroupsAccessRepository,
    private readonly aggregatedDataRepository: IAggregatedDataAssignRepository
  ) {}

  async execute(
    groupAccessRequest: IQuestionGroupAccessIdentifiersDTO
  ): Promise<{ message: string }> {
    await this.ensureEntitiesExist(groupAccessRequest);

    const isInGroup = await this.isQuestionInGroup(groupAccessRequest);

    await this.toggleGroupAccess(groupAccessRequest, isInGroup);

    return {
      message: isInGroup ? 'Questão removida do grupo' : 'Questão adicionada ao grupo',
    };
  }

  private async ensureEntitiesExist({
    questionId,
    questionGroupId,
    customerId,
  }: IQuestionGroupAccessIdentifiersDTO): Promise<void> {
    const { question, questionGroup } = await this.aggregatedDataRepository.findEntities({
      questionId,
      customerId,
      questionGroupId,
    });

    if (!question) throw new ResourceNotFoundError('Questão não encontrada');
    if (!questionGroup) throw new ResourceNotFoundError('Grupo de questões não encontrado');
  }

  private async isQuestionInGroup(
    groupAccessRequest: IQuestionGroupAccessIdentifiersDTO
  ): Promise<boolean> {
    const existingGroup =
      await this.questionsGroupsAccessRepository.getQuestionGroupAccess(groupAccessRequest);
    return !!existingGroup;
  }

  private async toggleGroupAccess(
    groupAccessRequest: IQuestionGroupAccessIdentifiersDTO,
    isInGroup: boolean
  ): Promise<void> {
    if (isInGroup) {
      await this.questionsGroupsAccessRepository.removeQuestionFromGroup(groupAccessRequest);
      return;
    }

    await this.questionsGroupsAccessRepository.insert({
      question_id: groupAccessRequest.questionId,
      question_group_id: groupAccessRequest.questionGroupId,
      customer_id: groupAccessRequest.customerId,
    });
  }
}
