import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import fs from 'fs';
import Handlebars from 'handlebars';
import { IncomingHttpHeaders } from 'http';
import path from 'path';
import { UAParser } from 'ua-parser-js';

import { env } from '../../../env';
import { ReportStatus } from '../../../model/enums/reportStatus.enum';
import { RolesEnum } from '../../../model/enums/roles.enum';
import { IReport } from '../../../model/IReport';
import { ICustomerRepository } from '../../../repositories/assign/customers.assign';
import { IQuestionsRepository } from '../../../repositories/assign/questions.assign';
import { IReportsRepository } from '../../../repositories/assign/reports.assign';
import { IUsersRepository } from '../../../repositories/assign/users.assign';
import { EmailService } from '../../../services/email.service';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface CreateQuestionReportRequest {
  description?: string;
  category: string;
  questionId: string;
  studyMode: string;
  userId: string;
  customerId: string;
  headers: IncomingHttpHeaders;
}

export class CreateQuestionReportUseCase {
  constructor(
    private readonly reportsRepo: IReportsRepository,
    private readonly questionsRepo: IQuestionsRepository,
    private readonly usersRepo: IUsersRepository,
    private readonly customersRepo: ICustomerRepository,
    private readonly emailService: EmailService
  ) {}

  async execute(payload: CreateQuestionReportRequest): Promise<IReport> {
    const { question, user, customer, adminUser } = await this.loadReportDependencies(payload);
    const userAgentData = this.parseUserAgent(payload.headers);
    const createdAt = new Date();

    const userName = this.formatUserName(user);

    await this.sendReportEmail({
      userName,
      userEmail: user.email,
      questionCode: question.title,
      studyMode: payload.studyMode,
      reportDate: this.formatDate(createdAt),
      reportCategory: payload.category,
      descriptionProblem: payload.description || '',
      supportEmail: customer.support_email || customer.email || env.EMAIL_FROM,
      customerName: customer.name,
      adminName: adminUser?.first_name || '',
    });

    const report = await this.reportsRepo.insert({
      description: payload.description,
      category: payload.category,
      question_id: payload.questionId,
      user_id: payload.userId,
      status: ReportStatus.PENDING,
      viewed: false,
      finished: false,
      send_email: false,
      platform: userAgentData.platform,
      web_browser: userAgentData.webBrowser,
      isMobile: userAgentData.isMobile,
      device: userAgentData.device,
      browser: userAgentData.browser,
      operational_system: userAgentData.operationalSystem,
      user_agent: userAgentData.userAgent,
    });

    if (!report) throw new BadRequestError('Erro ao criar o reporte');

    return report;
  }

  private async loadReportDependencies(request: CreateQuestionReportRequest) {
    const { questionId, userId, customerId } = request;

    const question = await this.questionsRepo.findById({ id: questionId, customerId });

    if (!question) throw new ResourceNotFoundError('Questão não encontrada');

    const user = await this.usersRepo.findOneBy({
      id: userId,
      customer_id: customerId,
      deleted_at: null,
    });

    if (!user) throw new ResourceNotFoundError('Usuário não encontrado');

    const customer = await this.customersRepo.findById(customerId);

    if (!customer) throw new ResourceNotFoundError('Cliente não encontrado');

    const adminUser = await this.usersRepo.findOneBy({
      customer_id: customerId,
      role: RolesEnum.SUPER_ADMIN,
      deleted_at: null,
    });

    return { question, user, customer, adminUser };
  }

  private parseUserAgent(headers: IncomingHttpHeaders) {
    const parser = new UAParser(headers['user-agent']);
    const result = parser.getResult();

    return {
      device: result.device?.type,
      browser: result.browser?.name,
      operationalSystem: result.os?.name,
      platform: this.getHeaderValue(headers['sec-ch-ua-platform']),
      webBrowser: this.getHeaderValue(headers['sec-ch-ua']),
      userAgent: headers['user-agent'],
      isMobile: result.device?.type === 'mobile',
    };
  }

  private getHeaderValue(header?: string | string[]): string | undefined {
    return Array.isArray(header) ? header[0] : header;
  }

  private formatUserName(user: {
    first_name?: string;
    last_name?: string;
    email?: string;
  }): string {
    if (user.first_name || user.last_name) {
      return `${user.first_name || ''} ${user.last_name || ''}`.trim();
    }
    return user.email || 'Usuário';
  }

  private formatDate(date: Date): string {
    return format(date, 'd MMM yyyy, HH:mm', { locale: ptBR });
  }

  private async sendReportEmail(data: {
    userName: string;
    userEmail: string;
    questionCode: string;
    studyMode: string;
    reportDate: string;
    reportCategory: string;
    descriptionProblem: string;
    supportEmail: string;
    customerName: string;
    adminName: string;
  }): Promise<void> {
    try {
      const templatePath = path.resolve(__dirname, '../../../templates/email/index.template.html');
      const templateContent = fs.readFileSync(templatePath, 'utf8');
      const compiledTemplate = Handlebars.compile(templateContent);

      const html = compiledTemplate({
        userName: data.userName,
        userEmail: data.userEmail,
        questionCode: data.questionCode,
        studyMode: data.studyMode,
        reportDate: data.reportDate,
        reportCategory: data.reportCategory,
        descriptionProblem: data.descriptionProblem,
        hasDescription: !!data.descriptionProblem,
        institution: data.customerName,
        supportEmail: data.supportEmail,
        AdminName: data.adminName,
      });

      await this.emailService.sendEmail({
        to: data.supportEmail,
        subject: `[Reporte de Questão] Um aluno reportou problema em uma questão`,
        html,
        from: env.EMAIL_FROM,
        ReplyToAddresses: data.userEmail,
      });
    } catch (error) {
      console.error('Erro ao enviar email de reporte:', error);
      throw new BadRequestError('Erro ao enviar email de reporte');
    }
  }
}
