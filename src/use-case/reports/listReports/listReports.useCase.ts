import { IReportListParams, IReportPaginatedResponse } from '../../../model/DTO/IReport.dto';
import { IReportsRepository } from '../../../repositories/assign/reports.assign';

export class ListReportsUseCase {
  constructor(private readonly reportsRepository: IReportsRepository) {}

  async execute(request: IReportListParams): Promise<IReportPaginatedResponse> {
    const {
      customerId,
      userId,
      questionId,
      examId,
      simulatedAccessId,
      questionGroupId,
      status,
      orderByColumn = 'created_at',
      orderDirection = 'desc',
    } = request;

    const { reports } = await this.reportsRepository.listReports({
      customerId,
      userId,
      questionId,
      examId,
      simulatedAccessId,
      questionGroupId,
      status,
      orderByColumn,
      orderDirection,
    });

    return {
      reports,
    };
  }
}
