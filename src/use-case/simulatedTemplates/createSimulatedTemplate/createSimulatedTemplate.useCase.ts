import { randomUUID } from 'crypto';

import { SimulatedTypeEnum } from '../../../model/enums/simulatedType.enum';
import { IQuestionGroup } from '../../../model/IQuestionGroup';
import { IQuestionsRepository } from '../../../repositories/assign/questions.assign';
import { IQuestionsGroupsRepository } from '../../../repositories/assign/questionsGroups.assign';
import { ISimulatedTemplateRepository } from '../../../repositories/assign/simulatedsTemplate.assign';
import { ISimulatedTemplateQuestionRepository } from '../../../repositories/assign/simulatedsTemplateQuestions.assign';
import { ISimulatedTypesRepository } from '../../../repositories/assign/simulatedTypes.assign';
import {
  CreateSimulatedTemplatePayload,
  QuestionFiltersPayload,
} from '../../../schema/simulatedTemplates.schema';
import { shuffleArray } from '../../../services/shuffleArray.service';
import { ResourceAlreadyExistsError } from '../../errors/ResourceAlreadyExistsError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

export type CreateSimulatedTemplateRequest = CreateSimulatedTemplatePayload & {
  customerId: string;
  id: string;
};

export interface ICreateSimulatedTemplateResponse {
  template: {
    id: string;
    name: string;
    questionsCount: number;
    isFreeStudy: boolean;
    simulatedTypeId: string;
    timeLimit?: number;
  };
}

export class CreateSimulatedTemplateUseCase {
  constructor(
    private readonly simulatedTemplateRepository: ISimulatedTemplateRepository,
    private readonly simulatedTemplateQuestionRepository: ISimulatedTemplateQuestionRepository,
    private readonly questionsRepository: IQuestionsRepository,
    private readonly questionsGroupsRepository: IQuestionsGroupsRepository,
    private readonly simulatedTypesRepository: ISimulatedTypesRepository
  ) {}

  async execute(
    payload: CreateSimulatedTemplateRequest
  ): Promise<ICreateSimulatedTemplateResponse> {
    const {
      name,
      customerId,
      id: userId,
      difficulty,
      disciplineIds,
      categoryIds,
      subcategoryIds,
      questionGroupId,
      quantity,
      isFreeStudy = false,
      timeLimit,
    } = payload;

    await this.validateUniqueTemplateName(name, userId, questionGroupId);
    await this.getQuestionGroup(questionGroupId, customerId);

    const simulatedTypeName = isFreeStudy ? SimulatedTypeEnum.FREE : SimulatedTypeEnum.DEFAULT;
    const simulatedType = await this.getSimulatedTypeByName(simulatedTypeName);

    if (!simulatedType) {
      throw new ResourceNotFoundError(`Tipo de simulado "${simulatedTypeName}" não encontrado.`);
    }

    const selectedQuestions = await this.resolveQuestions({
      filters: {
        customerId,
        difficulty,
        disciplineIds,
        categoryIds,
        subcategoryIds,
        questionGroupId,
        quantity,
        isFreeStudy,
      },
    });

    const template = await this.simulatedTemplateRepository.insert({
      name,
      user_id: userId,
      question_group_id: questionGroupId,
      simulated_type_id: simulatedType.id,
      time_limit: timeLimit ?? null,
    });

    const questionsToInsert = new Array(selectedQuestions.length);
    for (let i = 0; i < selectedQuestions.length; i++) {
      questionsToInsert[i] = {
        id: randomUUID(),
        simulated_template_id: template.id,
        question_id: selectedQuestions[i],
        order_by: i + 1,
      };
    }

    await this.simulatedTemplateQuestionRepository.addQuestionsToTemplate(questionsToInsert);

    return {
      template: {
        id: template.id,
        name: template.name,
        questionsCount: selectedQuestions.length,
        isFreeStudy,
        simulatedTypeId: simulatedType.id,
      },
    };
  }

  private async getQuestionGroup(id: string, customerId: string): Promise<IQuestionGroup> {
    const questionGroup = await this.questionsGroupsRepository.findById({ id, customerId });

    if (!questionGroup) {
      throw new ResourceNotFoundError('Grupo de questões não encontrado');
    }

    return questionGroup;
  }

  private async resolveQuestions({
    filters,
  }: {
    filters: QuestionFiltersPayload;
  }): Promise<string[]> {
    const hasFilters = Boolean(
      filters.difficulty?.length ||
        filters.disciplineIds?.length ||
        filters.categoryIds?.length ||
        filters.subcategoryIds?.length ||
        filters.questionGroupId
    );

    if (!hasFilters) {
      throw new ResourceNotFoundError('É necessário especificar filtros');
    }

    const { items } = await this.questionsRepository.listQuestionsWithFilters({
      customerId: filters.customerId,
      difficulty: filters.difficulty,
      disciplineIds: filters.disciplineIds,
      categoryIds: filters.categoryIds,
      subcategoryIds: filters.subcategoryIds,
      questionGroupId: filters.questionGroupId,
      quantity: filters.quantity,
    });

    if (items.length === 0) {
      throw new ResourceNotFoundError(
        'Não foram encontradas questões com os filtros especificados.'
      );
    }

    if (items.length < filters.quantity!) {
      throw new ResourceNotFoundError(
        `Não há questões suficientes para o sorteio. Encontradas: ${items.length}, Solicitadas: ${filters.quantity}`
      );
    }

    return shuffleArray(items)
      .slice(0, filters.quantity)
      .map((q) => q.id);
  }

  private async validateUniqueTemplateName(
    name: string,
    userId: string,
    questionGroupId: string
  ): Promise<void> {
    const existingTemplate = await this.simulatedTemplateRepository.findOneBy({
      name,
      user_id: userId,
      question_group_id: questionGroupId,
    });

    if (existingTemplate) {
      throw new ResourceAlreadyExistsError(
        `Já existe um template com o nome '${name}'. Por favor, escolha um nome diferente.`
      );
    }
  }

  private async getSimulatedTypeByName(typeName: SimulatedTypeEnum) {
    const simulatedTypes = await this.simulatedTypesRepository.findAll();
    return simulatedTypes.find((type) => type.name.toLowerCase() === typeName.toLowerCase());
  }
}
