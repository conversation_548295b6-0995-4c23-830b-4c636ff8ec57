import { ISimulatedTemplate } from '../../../model/ISimulatedTemplate';
import { ISimulatedTemplateRepository } from '../../../repositories/assign/simulatedsTemplate.assign';
import { ISimulatedTemplateQuestionRepository } from '../../../repositories/assign/simulatedsTemplateQuestions.assign';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface DeleteSimulatedTemplateRequest {
  id: string;
  userId: string;
}

interface DeleteSimulatedTemplateResponse {
  deletedTemplate: ISimulatedTemplate;
}

export class DeleteSimulatedTemplateUseCase {
  constructor(
    private readonly simulatedTemplateRepository: ISimulatedTemplateRepository,
    private readonly simulatedTemplateQuestionRepository: ISimulatedTemplateQuestionRepository
  ) {}

  async execute({
    id,
    userId,
  }: DeleteSimulatedTemplateRequest): Promise<DeleteSimulatedTemplateResponse> {
    await this.verifyTemplateExists(id, userId);

    await this.removeTemplateQuestions(id);

    const deletedTemplate = await this.removeTemplate(id);

    return { deletedTemplate };
  }

  private async verifyTemplateExists(id: string, userId: string): Promise<ISimulatedTemplate> {
    const template = await this.simulatedTemplateRepository.findOneBy({
      id,
      user_id: userId,
    });

    if (!template || template.deleted_at) {
      throw new ResourceNotFoundError('Template do simulado não encontrado.');
    }

    return template;
  }

  private async removeTemplateQuestions(id: string): Promise<void> {
    await this.simulatedTemplateQuestionRepository.deleteByTemplateId(id);
  }

  private async removeTemplate(id: string): Promise<ISimulatedTemplate> {
    return this.simulatedTemplateRepository.deleteById(id);
  }
}
