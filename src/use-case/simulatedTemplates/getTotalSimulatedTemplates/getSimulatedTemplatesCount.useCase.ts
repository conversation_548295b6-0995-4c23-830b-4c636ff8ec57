import { ISimulatedTemplateRepository } from '../../../repositories/assign/simulatedsTemplate.assign';

interface IGetSimulatedTemplatesCountUseCase {
  id: string;
  customerId: string;
  questionGroupId: string;
}

export class GetSimulatedTemplatesCountUseCase {
  constructor(private readonly simulatedTemplatesRepository: ISimulatedTemplateRepository) {}

  async execute({
    id,
    customerId,
    questionGroupId,
  }: IGetSimulatedTemplatesCountUseCase): Promise<number> {
    const count = await this.simulatedTemplatesRepository.countByUserId({
      userId: id,
      customerId,
      questionGroupId,
    });

    return count;
  }
}
