import {
  IGetSimulatedTemplatesDTO,
  IPublishedQuestionsFromTemplateDTO,
} from '../../../model/DTO/ISimulatedTemplate.dto';
import { ISimulatedTemplateRepository } from '../../../repositories/assign/simulatedsTemplate.assign';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface ListPublishedQuestionsFromTemplateParams extends IGetSimulatedTemplatesDTO {
  userId: string;
}

interface ListPublishedQuestionsFromTemplateResponse extends IPublishedQuestionsFromTemplateDTO {
  totalQuestions: number;
}

export class ListPublishedQuestionsFromTemplateUseCase {
  constructor(private readonly simulatedTemplateRepository: ISimulatedTemplateRepository) {}

  async execute({
    templateId,
    userId,
    customerId,
  }: ListPublishedQuestionsFromTemplateParams): Promise<ListPublishedQuestionsFromTemplateResponse> {
    await this.ensureTemplateExistsForUser(templateId, userId);

    const { questions, template, customer } =
      await this.simulatedTemplateRepository.getPublishedQuestionsFromTemplate({
        templateId,
        customerId,
      });

    return {
      template,
      customer,
      totalQuestions: questions.length,
      questions,
    };
  }

  private async ensureTemplateExistsForUser(templateId: string, userId: string): Promise<void> {
    const templateExists = await this.simulatedTemplateRepository.findOneBy({
      id: templateId,
      user_id: userId,
      deleted_at: null,
    });

    if (!templateExists) {
      throw new ResourceNotFoundError('Template de simulado não encontrado.');
    }
  }
}
