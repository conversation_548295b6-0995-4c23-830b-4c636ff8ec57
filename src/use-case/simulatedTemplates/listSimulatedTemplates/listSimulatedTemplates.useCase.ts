import { ISimulatedTemplatesResult } from '../../../model/DTO/ISimulatedTemplate.dto';
import { ISimulatedTemplateRepository } from '../../../repositories/assign/simulatedsTemplate.assign';
import { CustomerUserPayload } from '../../../schema/generic.schema';

interface ListSimulatedTemplatesRequest extends CustomerUserPayload {
  questionGroupId: string;
}

export class ListSimulatedTemplatesUseCase {
  constructor(private readonly simulatedTemplateRepository: ISimulatedTemplateRepository) {}

  async execute({
    id,
    customerId,
    questionGroupId,
  }: ListSimulatedTemplatesRequest): Promise<ISimulatedTemplatesResult> {
    return this.simulatedTemplateRepository.findTemplatesWithQuestionCountByUser({
      userId: id,
      customerId,
      questionGroupId,
    });
  }
}
