import { randomUUID } from 'crypto';

import { ISimulatedTemplate } from '../../../model/ISimulatedTemplate';
import { ISimulatedTemplateQuestion } from '../../../model/ISimulatedTemplateQuestion';
import { IQuestionsSimulatedsRepository } from '../../../repositories/assign/questionsSimulateds.assign';
import { ISimulatedsRepository } from '../../../repositories/assign/simulateds.assign';
import { ISimulatedsAccessRepository } from '../../../repositories/assign/simulatedsAccess.assign';
import { ISimulatedTemplateRepository } from '../../../repositories/assign/simulatedsTemplate.assign';
import { ISimulatedTemplateQuestionRepository } from '../../../repositories/assign/simulatedsTemplateQuestions.assign';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface StartSimulatedFromTemplateRequest {
  templateId: string;
  userId: string;
}

interface StartSimulatedFromTemplateResponse {
  id: string;
  name: string;
  simulatedTypeId: string;
  simulatedAccessId: string;
  questionsCount: number;
  timeLimit?: number | null;
}

export class StartSimulatedFromTemplateUseCase {
  constructor(
    private readonly templateRepo: ISimulatedTemplateRepository,
    private readonly templateQuestionsRepo: ISimulatedTemplateQuestionRepository,
    private readonly simulatedRepo: ISimulatedsRepository,
    private readonly simulatedAccessRepo: ISimulatedsAccessRepository,
    private readonly questionsSimulatedRepo: IQuestionsSimulatedsRepository
  ) {}

  async execute({
    templateId,
    userId,
  }: StartSimulatedFromTemplateRequest): Promise<StartSimulatedFromTemplateResponse> {
    const [template, questions] = await Promise.all([
      this.ensureTemplateExists(templateId, userId),
      this.ensureTemplateHasQuestions(templateId),
    ]);

    const simulated = await this.createSimulatedFromTemplate(template);

    const access = await this.createSimulatedUserAccess(
      simulated.id,
      userId,
      template.time_limit ?? null
    );

    await this.attachQuestionsToSimulated(simulated.id, questions);

    return {
      id: simulated.id,
      name: simulated.name,
      simulatedTypeId: simulated.simulated_type_id,
      simulatedAccessId: access.id,
      questionsCount: questions.length,
      timeLimit: access.time_limit,
    };
  }

  private async ensureTemplateExists(
    templateId: string,
    userId: string
  ): Promise<ISimulatedTemplate> {
    const template = await this.templateRepo.findOneBy({ id: templateId, user_id: userId });

    if (!template) {
      throw new ResourceNotFoundError('Template de simulado não encontrado');
    }
    return template;
  }

  private async ensureTemplateHasQuestions(
    templateId: string
  ): Promise<ISimulatedTemplateQuestion[]> {
    const questions = await this.templateQuestionsRepo.findByTemplateId(templateId);

    if (!questions?.length) {
      throw new ResourceNotFoundError('O template não possui questões');
    }

    return questions;
  }

  private async createSimulatedFromTemplate(template: ISimulatedTemplate) {
    return this.simulatedRepo.insert({
      name: template.name,
      simulated_type_id: template.simulated_type_id,
      question_group_id: template.question_group_id,
      active: true,
    });
  }

  private async createSimulatedUserAccess(
    simulatedId: string,
    userId: string,
    timeLimit: number | null
  ) {
    return this.simulatedAccessRepo.insert({
      simulated_id: simulatedId,
      user_id: userId,
      time_limit: timeLimit,
    });
  }

  private async attachQuestionsToSimulated(
    simulatedId: string,
    templateQuestions: ISimulatedTemplateQuestion[]
  ) {
    const now = new Date();

    const questions = templateQuestions.map((q, index) => ({
      id: randomUUID(),
      simulated_id: simulatedId,
      question_id: q.question_id,
      order_by: q.order_by ?? index + 1,
      created_at: now,
      updated_at: now,
      deleted_at: null,
    }));

    await this.questionsSimulatedRepo.insertAll(questions);
  }
}
