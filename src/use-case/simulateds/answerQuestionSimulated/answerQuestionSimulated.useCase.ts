import { IQuestionSimulatedAnswered } from '../../../model/IQuestionSimulatedAnswered';
import { IQuestionsSimulatedAnsweredRepository } from '../../../repositories/assign/questionsSimulatedAnswered.assign';
import { IQuestionsSimulatedsRepository } from '../../../repositories/assign/questionsSimulateds.assign';
import { ISimulatedsAccessRepository } from '../../../repositories/assign/simulatedsAccess.assign';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface AnswerQuestionSimulatedRequest {
  simulatedAccessId: string;
  questionSimulatedId: string;
  userId: string;
  alternativeId: string;
}

export class AnswerQuestionSimulatedUseCase {
  constructor(
    private readonly questionsSimulatedRepo: IQuestionsSimulatedsRepository,
    private readonly simulatedAccessRepo: ISimulatedsAccessRepository,
    private readonly answeredRepo: IQuestionsSimulatedAnsweredRepository
  ) {}

  async execute({
    simulatedAccessId,
    questionSimulatedId,
    userId,
    alternativeId,
  }: AnswerQuestionSimulatedRequest): Promise<IQuestionSimulatedAnswered> {
    const access = await this.getValidSimulatedAccess(simulatedAccessId, userId);

    await this.validateQuestionAssociation(questionSimulatedId, access.simulated_id);

    const existingAnswer = await this.getExistingAnswer(simulatedAccessId, questionSimulatedId);

    return existingAnswer
      ? this.updateAnswer(existingAnswer.id, alternativeId)
      : this.createAnswer(simulatedAccessId, questionSimulatedId, alternativeId);
  }

  private async getValidSimulatedAccess(accessId: string, userId: string) {
    const access = await this.simulatedAccessRepo.findOneBy({ id: accessId, user_id: userId });

    if (!access) throw new ResourceNotFoundError('Acesso ao simulado não encontrado');

    if (access.end_simulated) throw new BadRequestError('Este simulado já foi finalizado');
    return access;
  }

  private async validateQuestionAssociation(questionId: string, simulatedId: string) {
    const question = await this.questionsSimulatedRepo.findOneBy({
      id: questionId,
      simulated_id: simulatedId,
    });
    if (!question) throw new ResourceNotFoundError('Questão não encontrada no simulado');
  }

  private async getExistingAnswer(simulatedAccessId: string, questionSimulatedId: string) {
    return this.answeredRepo.findOneBy({
      simulated_access: simulatedAccessId,
      questions_simulated_id: questionSimulatedId,
    });
  }

  private async updateAnswer(answerId: string, alternativeId: string) {
    return this.answeredRepo.update({
      id: answerId,
      alternative_id: alternativeId,
      answered: true,
      date_answered: new Date(),
    });
  }

  private async createAnswer(
    simulatedAccessId: string,
    questionSimulatedId: string,
    alternativeId: string
  ) {
    return this.answeredRepo.insert({
      simulated_access: simulatedAccessId,
      questions_simulated_id: questionSimulatedId,
      alternative_id: alternativeId,
      answered: true,
      date_answered: new Date(),
    });
  }
}
