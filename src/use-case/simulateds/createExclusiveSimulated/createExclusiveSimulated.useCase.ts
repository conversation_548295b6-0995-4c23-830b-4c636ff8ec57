import { SimulatedStatusEnum } from '../../../model/enums/simulatedStatus.enum';
import { SimulatedTypeEnum } from '../../../model/enums/simulatedType.enum';
import { ISimulated } from '../../../model/ISimulated';
import { ISimulatedsRepository } from '../../../repositories/assign/simulateds.assign';
import { ISimulatedTypesRepository } from '../../../repositories/assign/simulatedTypes.assign';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface CreateExclusiveSimulatedRequest {
  name: string;
  active?: boolean;
  timeLimit?: number;
  status: SimulatedStatusEnum;
  startDate?: Date | null;
  endDate?: Date | null;
  customerId: string;
}

interface CreateExclusiveSimulatedResponse {
  simulated: ISimulated;
}

type PublishRequirements = Pick<
  CreateExclusiveSimulatedRequest,
  'name' | 'startDate' | 'endDate' | 'status' | 'active'
>;

export class CreateExclusiveSimulatedUseCase {
  constructor(
    private simulatedsRepository: ISimulatedsRepository,
    private simulatedsTypesRepository: ISimulatedTypesRepository
  ) {}

  async execute({
    name,
    active,
    timeLimit,
    status,
    startDate,
    endDate,
    customerId,
  }: CreateExclusiveSimulatedRequest): Promise<CreateExclusiveSimulatedResponse> {
    if (timeLimit && timeLimit < 60) {
      throw new BadRequestError('O tempo mínimo para o simulado é de 1 minuto');
    }

    const simulatedTypes = await this.simulatedsTypesRepository.findAll();
    const exclusiveType = simulatedTypes.find((type) => type.name === SimulatedTypeEnum.EXCLUSIVE);

    if (!exclusiveType) {
      throw new ResourceNotFoundError('Tipo de simulado exclusivo não encontrado');
    }

    this.validatePublishRequirements({
      name,
      startDate,
      endDate,
      status,
      active,
    });

    const simulated = await this.simulatedsRepository.insert({
      name,
      simulated_type_id: exclusiveType.id,
      active: active ?? false,
      time_limit: timeLimit,
      status,
      start_date: startDate ?? null,
      end_date: endDate ?? null,
      customer_id: customerId,
    });

    return {
      simulated,
    };
  }

  private validatePublishRequirements({
    name,
    startDate,
    endDate,
    status,
    active,
  }: PublishRequirements) {
    if (status === SimulatedStatusEnum.PUBLISHED) {
      if (!name) {
        throw new BadRequestError('O nome é obrigatório para publicar o simulado');
      }

      if (!startDate || !endDate) {
        throw new BadRequestError(
          'As datas de início e fim são obrigatórias para publicar o simulado'
        );
      }

      if (startDate >= endDate) {
        throw new BadRequestError('A data de início deve ser anterior à data de término');
      }

      if (!active) {
        throw new BadRequestError('O simulado deve estar ativo para ser publicado');
      }
    }
  }
}
