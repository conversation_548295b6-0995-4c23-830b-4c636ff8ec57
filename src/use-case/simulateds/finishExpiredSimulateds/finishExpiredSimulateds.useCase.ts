import { Knex } from 'knex';

import { generateTransaction } from '../../../helpers/transaction.helper';
import { SimulatedStatusEnum } from '../../../model/enums/simulatedStatus.enum';
import { IAnswerSession } from '../../../model/IAnswerSession';
import { ISimulatedAccess } from '../../../model/ISimulatedAcces';
import { IAnswerSessionsRepository } from '../../../repositories/assign/answerSessions.assign';
import { ISimulatedsRepository } from '../../../repositories/assign/simulateds.assign';
import { ISimulatedsAccessRepository } from '../../../repositories/assign/simulatedsAccess.assign';

interface FinishExpiredSimulatedsResponse {
  totalSimuladosProcessados: number;
  totalAcessosFinalizados: number;
  simuladosDesativados: string[];
  erros: Array<{ simuladoId: string; error: string }>;
}

interface BatchUpdateData {
  accessUpdates: Array<{
    id: string;
    end_simulated: Date;
    total_seconds: number;
    active: boolean;
  }>;
  sessionUpdates: Array<{
    id: string;
    started_at: Date;
    paused_at: Date;
    duration_seconds: number;
  }>;
  simuladoUpdates: Array<{
    id: string;
    active: boolean;
    status: SimulatedStatusEnum;
  }>;
}

export class FinishExpiredSimulatedsUseCase {
  constructor(
    private readonly simulatedsRepo: ISimulatedsRepository,
    private readonly simulatedsAccessRepo: ISimulatedsAccessRepository,
    private readonly answerSessionsRepo: IAnswerSessionsRepository
  ) {}

  async execute(): Promise<FinishExpiredSimulatedsResponse> {
    const response: FinishExpiredSimulatedsResponse = {
      totalSimuladosProcessados: 0,
      totalAcessosFinalizados: 0,
      simuladosDesativados: [],
      erros: [],
    };

    const transaction: Knex.Transaction = await generateTransaction();
    try {
      const expiredSimulados = await this.simulatedsRepo.findExpiredExclusiveSimulateds();

      if (!expiredSimulados.length) {
        console.log('Nenhum simulado exclusivo expirado para finalizar.');
        await transaction.commit();
        return response;
      }

      response.totalSimuladosProcessados = expiredSimulados.length;
      const simuladoIds = expiredSimulados.map((s) => s.id);

      const CHUNK_SIZE = 50;
      const chunks = this.chunkArray(simuladoIds, CHUNK_SIZE);

      for (const chunk of chunks) {
        await this.processChunk(chunk, response, transaction);
      }

      await transaction.commit();
      console.log(
        `Processamento concluído: ${response.totalAcessosFinalizados} acessos, ${response.simuladosDesativados.length} simulados`
      );
    } catch (err) {
      await transaction.rollback();
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      console.error('Erro geral na execução:', errorMessage);
      response.erros.push({
        simuladoId: 'GENERAL',
        error: `Erro geral na execução: ${errorMessage}`,
      });
    }

    return response;
  }

  private async processChunk(
    simuladoIds: string[],
    response: FinishExpiredSimulatedsResponse,
    transaction: Knex.Transaction
  ): Promise<void> {
    const accessPromises = simuladoIds.map((simuladoId) =>
      this.simulatedsAccessRepo.findBy({
        simulated_id: simuladoId,
        end_simulated: null,
      })
    );

    const allAccessesArrays = await Promise.all(accessPromises);
    const allAccesses = allAccessesArrays.flat();

    if (allAccesses.length === 0) {
      await this.batchUpdateSimulados(simuladoIds, transaction);
      response.simuladosDesativados.push(...simuladoIds);
      return;
    }

    const accessIds = allAccesses.map((a) => a.id);
    const sessionChunks = this.chunkArray(accessIds, 20);
    const sessionPromises = sessionChunks.map((chunk) => this.getSessionsForChunk(chunk));
    const allSessionsArrays = await Promise.all(sessionPromises);
    const allSessions = allSessionsArrays.flat();

    const batchData = this.prepareBatchUpdates(allAccesses, allSessions, simuladoIds);

    await this.executeBatchUpdates(batchData, transaction);

    response.totalAcessosFinalizados += batchData.accessUpdates.length;
    response.simuladosDesativados.push(...batchData.simuladoUpdates.map((s) => s.id));
  }

  private async getSessionsForChunk(accessIds: string[]): Promise<IAnswerSession[]> {
    const sessionPromises = accessIds.map((accessId) =>
      this.answerSessionsRepo.findBySimulatedAccessId(accessId)
    );

    const sessionsArrays = await Promise.all(sessionPromises);
    return sessionsArrays.flat();
  }

  private prepareBatchUpdates(
    accesses: ISimulatedAccess[],
    sessions: IAnswerSession[],
    simuladoIds: string[]
  ): BatchUpdateData {
    const endTime = new Date();
    const accessUpdates: BatchUpdateData['accessUpdates'] = [];
    const sessionUpdates: BatchUpdateData['sessionUpdates'] = [];

    const sessionsByAccess = this.groupSessionsByAccess(sessions);

    for (const access of accesses) {
      const accessSessions = sessionsByAccess.get(access.id) || [];
      let updatedSessions = [...accessSessions];

      for (const session of accessSessions) {
        if (!session.paused_at) {
          const duration = Math.floor(
            (endTime.getTime() - new Date(session.started_at).getTime()) / 1000
          );
          sessionUpdates.push({
            id: session.id,
            started_at: session.started_at,
            paused_at: endTime,
            duration_seconds: duration,
          });
          updatedSessions = updatedSessions.map((s) =>
            s.id === session.id ? { ...s, paused_at: endTime, duration_seconds: duration } : s
          );
        }
      }

      const totalSeconds = updatedSessions.reduce((sum, s) => sum + (s.duration_seconds ?? 0), 0);

      accessUpdates.push({
        id: access.id,
        end_simulated: endTime,
        total_seconds: totalSeconds,
        active: false,
      });
    }

    const simuladoUpdates: BatchUpdateData['simuladoUpdates'] = simuladoIds.map((id) => ({
      id,
      active: false,
      status: SimulatedStatusEnum.CLOSED,
    }));

    return {
      accessUpdates,
      sessionUpdates,
      simuladoUpdates,
    };
  }

  private async executeBatchUpdates(
    batchData: BatchUpdateData,
    transaction: Knex.Transaction
  ): Promise<void> {
    const promises: Promise<void>[] = [];

    if (batchData.sessionUpdates.length > 0) {
      const sessionChunks = this.chunkArray(batchData.sessionUpdates, 30);
      const sessionPromises = sessionChunks.map((chunk) =>
        this.batchUpdateSessions(chunk, transaction)
      );
      promises.push(...sessionPromises);
    }

    if (batchData.accessUpdates.length > 0) {
      const accessChunks = this.chunkArray(batchData.accessUpdates, 30);
      const accessPromises = accessChunks.map((chunk) =>
        this.batchUpdateAccesses(chunk, transaction)
      );
      promises.push(...accessPromises);
    }

    if (batchData.simuladoUpdates.length > 0) {
      const simuladoChunks = this.chunkArray(
        batchData.simuladoUpdates.map((s) => s.id),
        30
      );
      const simuladoPromises = simuladoChunks.map((chunk) =>
        this.batchUpdateSimulados(chunk, transaction)
      );
      promises.push(...simuladoPromises);
    }

    await Promise.all(promises);
  }

  private async batchUpdateSessions(
    sessionUpdates: BatchUpdateData['sessionUpdates'],
    transaction: Knex.Transaction
  ): Promise<void> {
    const promises = sessionUpdates.map((session) =>
      this.answerSessionsRepo.update(session, transaction)
    );
    await Promise.all(promises);
  }

  private async batchUpdateAccesses(
    accessUpdates: BatchUpdateData['accessUpdates'],
    transaction: Knex.Transaction
  ): Promise<void> {
    const promises = accessUpdates.map((access) =>
      this.simulatedsAccessRepo.update(access, transaction)
    );
    await Promise.all(promises);
  }

  private async batchUpdateSimulados(
    simuladoIds: string[],
    transaction: Knex.Transaction
  ): Promise<void> {
    const promises = simuladoIds.map((id) =>
      this.simulatedsRepo.update(
        {
          id,
          active: false,
          status: SimulatedStatusEnum.CLOSED,
        },
        transaction
      )
    );
    await Promise.all(promises);
  }

  private groupSessionsByAccess(sessions: IAnswerSession[]): Map<string, IAnswerSession[]> {
    const grouped = new Map<string, IAnswerSession[]>();

    for (const session of sessions) {
      const accessId = session.simulated_access_id;
      if (accessId && !grouped.has(accessId)) {
        grouped.set(accessId, []);
      }
      if (accessId) {
        grouped.get(accessId)!.push(session);
      }
    }

    return grouped;
  }

  private getLatestSession(sessions: IAnswerSession[]): IAnswerSession | null {
    if (sessions.length === 0) return null;

    return sessions.sort(
      (a, b) => new Date(b.started_at).getTime() - new Date(a.started_at).getTime()
    )[0];
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }
}
