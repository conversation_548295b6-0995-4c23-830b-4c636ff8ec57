import { SimulatedTypeEnum } from '../../../model/enums/simulatedType.enum';
import { IAnswerSession } from '../../../model/IAnswerSession';
import { ISimulated } from '../../../model/ISimulated';
import { ISimulatedAccess } from '../../../model/ISimulatedAcces';
import { IAggregatedDataAssignRepository } from '../../../repositories/assign/aggregatedData.assign';
import { IAnswerSessionsRepository } from '../../../repositories/assign/answerSessions.assign';
import { ISimulatedsRepository } from '../../../repositories/assign/simulateds.assign';
import { ISimulatedsAccessRepository } from '../../../repositories/assign/simulatedsAccess.assign';
import { ISimulatedTypesRepository } from '../../../repositories/assign/simulatedTypes.assign';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface FinishSimulatedRequest {
  simulatedAccessId: string;
  userId: string;
  customerId: string;
  simulatedId: string;
}

interface FinishSimulatedResponse {
  simulatedAccess: ISimulatedAccess;
  simulated: ISimulated;
}

export class FinishSimulatedUseCase {
  constructor(
    private readonly simulatedsRepo: ISimulatedsRepository,
    private readonly accessRepo: ISimulatedsAccessRepository,
    private readonly typesRepo: ISimulatedTypesRepository,
    private readonly sessionsRepo: IAnswerSessionsRepository,
    private readonly aggregateDataRepository: IAggregatedDataAssignRepository
  ) {}

  async execute({
    simulatedAccessId,
    userId,
    customerId,
    simulatedId,
  }: FinishSimulatedRequest): Promise<FinishSimulatedResponse> {
    const {
      simulated,
      simulatedAccess,
      answerSessionsData: sessions,
    } = await this.fetchRequiredEntities(simulatedId, simulatedAccessId, userId, customerId);

    if (simulatedAccess.end_simulated) {
      return { simulatedAccess, simulated };
    }

    const endTime = new Date();

    if (sessions.length) {
      await this.finalizeLatestOpenSession(sessions, endTime);
    } else {
      await this.finalizeLatestOpenSessionByAccessId(simulatedAccessId, endTime);
    }

    const totalSeconds = await this.calculateTotalTime(simulatedAccessId);

    const updatedAccess = await this.accessRepo.update({
      id: simulatedAccessId,
      end_simulated: endTime,
      total_seconds: totalSeconds,
      active: false,
    });

    const type = await this.typesRepo.findOneBy({ id: simulated.simulated_type_id });

    if (!type) throw new ResourceNotFoundError('Tipo de simulado não encontrado');

    if (type.name !== SimulatedTypeEnum.EXCLUSIVE.toLowerCase()) {
      await this.simulatedsRepo.update({ id: simulatedAccess.simulated_id, active: false });
    }

    return {
      simulatedAccess: updatedAccess,
      simulated,
    };
  }

  private async fetchRequiredEntities(
    simulatedId: string,
    simulatedAccessId: string,
    userId: string,
    customerId: string
  ): Promise<{
    simulated: ISimulated;
    simulatedAccess: ISimulatedAccess;
    answerSessionsData: IAnswerSession[];
  }> {
    const { simulated, simulatedAccess, answerSessionsData } =
      await this.aggregateDataRepository.findEntities({
        simulatedId,
        simulatedAccessId,
        userId,
        customerId,
        answerSessions: true,
      });

    if (!simulated) throw new ResourceNotFoundError('Simulado não encontrado.');
    if (!simulatedAccess) throw new ResourceNotFoundError('Acesso ao simulado não encontrado.');
    if (!answerSessionsData)
      throw new ResourceNotFoundError('Sessões de resposta não encontradas.');

    return { simulated, simulatedAccess, answerSessionsData };
  }

  private async finalizeLatestOpenSession(sessions: IAnswerSession[], endTime: Date) {
    const last = [...sessions].sort(
      (a, b) => new Date(b.started_at).getTime() - new Date(a.started_at).getTime()
    )[0];

    if (last.paused_at) return;

    const duration = Math.floor((endTime.getTime() - new Date(last.started_at).getTime()) / 1000);

    await this.sessionsRepo.update({
      id: last.id,
      started_at: last.started_at,
      paused_at: endTime,
      duration_seconds: duration,
    });
  }

  private async finalizeLatestOpenSessionByAccessId(
    simulatedAccessId: string,
    endTime: Date
  ): Promise<void> {
    const sessions = await this.sessionsRepo.findBySimulatedAccessId(simulatedAccessId);

    if (!sessions.length) return;

    await this.finalizeLatestOpenSession(sessions, endTime);
  }

  private async calculateTotalTime(simulatedAccessId: string): Promise<number> {
    const sessions = await this.sessionsRepo.findBySimulatedAccessId(simulatedAccessId);

    return sessions.reduce((sum, s) => sum + (s.duration_seconds ?? 0), 0);
  }
}
