/* eslint-disable no-useless-constructor */

import { IQuestionSimulatedWithAlternativesDTO } from '../../../model/DTO/IQuestionsSimulated.dto';
import { ISimulated } from '../../../model/ISimulated';
import { IQuestionsSimulatedsRepository } from '../../../repositories/assign/questionsSimulateds.assign';
import { KnexSimulatedsRepository } from '../../../repositories/knex/simulated.repositories';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface GetAllQuestionsResponse {
  simulatedInfo: ISimulated;
  totalQuestions: number;
  questions: IQuestionSimulatedWithAlternativesDTO[];
}

export class GetAllQuestionsSimulatedUseCase {
  constructor(
    private questionsSimulatedRepository: IQuestionsSimulatedsRepository,
    private simulatedRepository: KnexSimulatedsRepository
  ) {}

  async getAllQuestions(simulatedId: string, userId: string): Promise<GetAllQuestionsResponse> {
    const simulatedExists = await this.simulatedRepository.get(simulatedId);

    if (!simulatedExists) {
      throw new ResourceNotFoundError('Nenhuma simulado encontrada');
    }
    const { questions, totalQuestions } =
      (await this.questionsSimulatedRepository.getAllQuestionsSimulated(simulatedId, userId)) as {
        questions: IQuestionSimulatedWithAlternativesDTO[];
        totalQuestions: number;
      };

    console.log(questions);

    return {
      simulatedInfo: simulatedExists,
      totalQuestions,
      questions,
    };
  }
}
