import { IQuestionSimulatedWithAlternativesDTO } from '../../../model/DTO/IQuestionsSimulated.dto';
import { ISimulated } from '../../../model/ISimulated';
import { IQuestionsSimulatedsRepository } from '../../../repositories/assign/questionsSimulateds.assign';
import { KnexSimulatedsRepository } from '../../../repositories/knex/simulated.repositories';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface GetAllQuestionsBySimulatedIdResponse {
  simulatedInfo: ISimulated;
  questions: IQuestionSimulatedWithAlternativesDTO[];
}

export class GetAllQuestionsBySimulatedIdUseCase {
  constructor(
    private questionsSimulatedRepository: IQuestionsSimulatedsRepository,
    private simulatedRepository: KnexSimulatedsRepository
  ) {}

  async execute({
    simulatedId,
  }: {
    simulatedId: string;
  }): Promise<GetAllQuestionsBySimulatedIdResponse> {
    const simulatedExists = await this.simulatedRepository.get(simulatedId);
    if (!simulatedExists) {
      throw new ResourceNotFoundError('Nenhuma simulado encontrada');
    }
    const questions =
      await this.questionsSimulatedRepository.getAllQuestionsBySimulatedId(simulatedId);
    return {
      simulatedInfo: simulatedExists,
      questions,
    };
  }
}
