import { IExclusiveSimulatedStatsDTO } from '../../model/DTO/ISimulated.dto';
import { ISimulatedsRepository } from '../../repositories/assign/simulateds.assign';
import { ISimulatedsAccessRepository } from '../../repositories/assign/simulatedsAccess.assign';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

export class GetExclusiveSimulatedStatsUseCase {
  constructor(
    private simulatedsAccessRepository: ISimulatedsAccessRepository,
    private simulatedsRepository: ISimulatedsRepository
  ) {}

  async execute(simulatedId: string): Promise<IExclusiveSimulatedStatsDTO> {
    const simulated = await this.simulatedsRepository.findOneBy({ id: simulatedId });

    if (!simulated) {
      throw new ResourceNotFoundError('Simulado não encontrado');
    }

    return this.simulatedsAccessRepository.getExclusiveSimulatedStats(simulatedId);
  }
}
