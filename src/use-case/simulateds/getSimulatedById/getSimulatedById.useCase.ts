/* eslint-disable no-useless-constructor */

import { KnexSimulatedsRepository } from '../../../repositories/knex/simulated.repositories';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

export class GetSimulatedByIdUseCase {
  constructor(private simulatedsRepository: KnexSimulatedsRepository) {}

  async getSimulatedById(simulatedId: string): Promise<object> {
    const simulatedExists = await this.simulatedsRepository.getSimulatedById(simulatedId);

    if (!simulatedExists) {
      throw new ResourceNotFoundError('Nenhuma simulado encontrada');
    }

    return simulatedExists;
  }
}
