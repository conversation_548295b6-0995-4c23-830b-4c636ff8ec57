import { IQuestionSimulatedWithExtendedDataDTO } from '../../../model/DTO/IQuestionsSimulated.dto';
import { IAnswerSession } from '../../../model/IAnswerSession';
import { ISimulatedAccess } from '../../../model/ISimulatedAcces';
import { IAnswerSessionsRepository } from '../../../repositories/assign/answerSessions.assign';
import { IQuestionsSimulatedsRepository } from '../../../repositories/assign/questionsSimulateds.assign';
import { ISimulatedsAccessRepository } from '../../../repositories/assign/simulatedsAccess.assign';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface GetSimulatedWithQuestionsByIdResponse {
  simulatedAccessInfo: ISimulatedAccess;
  totalQuestions: number;
  questions: IQuestionSimulatedWithExtendedDataDTO[];
}

export class GetSimulatedWithQuestionsByIdUseCase {
  constructor(
    private questionsSimulatedRepository: IQuestionsSimulatedsRepository,
    private simulatedAccessRepository: ISimulatedsAccessRepository,
    private sessionsRepository: IAnswerSessionsRepository
  ) {}

  async execute(
    simulatedAccessId: string,
    userId: string
  ): Promise<GetSimulatedWithQuestionsByIdResponse> {
    const simulatedAccessExists =
      await this.simulatedAccessRepository.getSimulatedAccessWithSimulatedName({
        id: simulatedAccessId,
        user_id: userId,
      });

    if (!simulatedAccessExists) {
      throw new ResourceNotFoundError('Simulado não encontrado');
    }

    const { questions, totalQuestions } =
      await this.questionsSimulatedRepository.getQuestionsWithExtendedData(
        simulatedAccessId,
        userId
      );

    const sessions = await this.sessionsRepository.findBySimulatedAccessId(simulatedAccessId);
    let currentSessionDuration = 0;

    if (sessions.length > 0) {
      const latestSession = this.getLatestSession(sessions);
      if (latestSession && !latestSession.paused_at) {
        const now = new Date();
        currentSessionDuration = Math.floor(
          (now.getTime() - new Date(latestSession.started_at).getTime()) / 1000
        );
      }
    }
    const previousSessionsTime = await this.calculatePreviousSessionsTime(simulatedAccessId);

    const totalSeconds = previousSessionsTime + currentSessionDuration;

    const simulatedAccessInfo = {
      ...simulatedAccessExists,
      total_seconds: totalSeconds,
    };

    return {
      simulatedAccessInfo,
      totalQuestions,
      questions,
    };
  }

  private async calculatePreviousSessionsTime(simulatedAccessId: string): Promise<number> {
    const sessions = await this.sessionsRepository.findBySimulatedAccessId(simulatedAccessId);

    return sessions.reduce((sum, session) => {
      if (session.duration_seconds !== null && session.duration_seconds !== undefined) {
        return sum + session.duration_seconds;
      }
      return sum;
    }, 0);
  }

  private getLatestSession(sessions: IAnswerSession[]): IAnswerSession | null {
    if (!sessions.length) return null;

    return sessions.reduce((latest, current) =>
      new Date(current.started_at) > new Date(latest.started_at) ? current : latest
    );
  }
}
