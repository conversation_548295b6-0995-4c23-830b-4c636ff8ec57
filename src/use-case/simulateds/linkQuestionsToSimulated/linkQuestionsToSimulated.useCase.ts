import { randomUUID } from 'crypto';

import { SimulatedStatusEnum } from '../../../model/enums/simulatedStatus.enum';
import { IQuestionSimulated } from '../../../model/IQuestionSimulated';
import { ISimulated } from '../../../model/ISimulated';
import { IQuestionsRepository } from '../../../repositories/assign/questions.assign';
import { IQuestionsSimulatedsRepository } from '../../../repositories/assign/questionsSimulateds.assign';
import { ISimulatedsRepository } from '../../../repositories/assign/simulateds.assign';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface ILinkQuestionsToSimulatedRequest {
  simulatedId: string;
  questionIds: string[];
  customerId: string;
}

interface ILinkQuestionsToSimulatedResponse {
  simulated: ISimulated;
  linkedQuestions: number;
  simulatedQuestions: IQuestionSimulated[];
}

export class LinkQuestionsToSimulatedUseCase {
  constructor(
    private readonly simulatedsRepository: ISimulatedsRepository,
    private readonly questionsRepository: IQuestionsRepository,
    private readonly questionsSimulatedsRepository: IQuestionsSimulatedsRepository
  ) {}

  async execute(
    payload: ILinkQuestionsToSimulatedRequest
  ): Promise<ILinkQuestionsToSimulatedResponse> {
    const { simulatedId, questionIds, customerId } = payload;

    const simulated = await this.findSimulatedOrFail(simulatedId, customerId);
    this.ensureSimulatedIsDraft(simulated);

    await this.ensureValidQuestionsOrFail(questionIds, customerId);

    const existingLinks = await this.questionsSimulatedsRepository.findAllBy({
      simulated_id: simulatedId,
    });

    const newQuestionIds = this.filterOnlyNewQuestionIds(existingLinks, questionIds);
    if (newQuestionIds.length === 0) {
      throw new BadRequestError('Todas as questões já estão vinculadas a este simulado');
    }

    const simulatedQuestions = await this.linkQuestionsWithOrder(
      simulatedId,
      newQuestionIds,
      existingLinks
    );

    return {
      simulated,
      linkedQuestions: newQuestionIds.length,
      simulatedQuestions,
    };
  }

  private async findSimulatedOrFail(simulatedId: string, customerId: string): Promise<ISimulated> {
    const simulated = await this.simulatedsRepository.findOneBy({
      id: simulatedId,
      customer_id: customerId,
      deleted_at: null,
    });

    if (!simulated) {
      throw new ResourceNotFoundError('Simulado não encontrado');
    }

    return simulated;
  }

  private ensureSimulatedIsDraft(simulated: ISimulated): void {
    if (simulated.status !== SimulatedStatusEnum.DRAFT) {
      throw new BadRequestError('Apenas simulados em rascunho podem ter questões vinculadas');
    }
  }

  private async ensureValidQuestionsOrFail(
    questionIds: string[],
    customerId: string
  ): Promise<void> {
    const questions = await this.questionsRepository.findByIds(questionIds, customerId);

    if (questions.length !== questionIds.length) {
      throw new BadRequestError('Uma ou mais questões não foram encontradas');
    }
  }

  private filterOnlyNewQuestionIds(
    existing: IQuestionSimulated[],
    questionIds: string[]
  ): string[] {
    const existingSet = new Set(existing.filter((q) => !q.deleted_at).map((q) => q.question_id));

    return questionIds.filter((id) => !existingSet.has(id));
  }

  private async linkQuestionsWithOrder(
    simulatedId: string,
    questionIds: string[],
    existing: IQuestionSimulated[]
  ): Promise<IQuestionSimulated[]> {
    const nextOrder =
      existing.length > 0 ? Math.max(...existing.map((link) => link.order_by || 0)) + 1 : 1;

    return this.createSimulatedQuestionLinks(simulatedId, questionIds, nextOrder);
  }

  private async createSimulatedQuestionLinks(
    simulatedId: string,
    questionIds: string[],
    startOrder: number
  ): Promise<IQuestionSimulated[]> {
    const now = new Date();

    const inserts: IQuestionSimulated[] = questionIds.map((questionId, index) => ({
      id: randomUUID(),
      simulated_id: simulatedId,
      question_id: questionId,
      order_by: startOrder + index,
      created_at: now,
      updated_at: now,
      deleted_at: null,
    }));

    return this.questionsSimulatedsRepository.insertAll(inserts);
  }
}
