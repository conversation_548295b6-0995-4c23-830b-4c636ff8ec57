import { IListExclusiveSimulatedsPaginatedResponse } from '../../model/DTO/ISimulated.dto';
import { ISimulatedsRepository } from '../../repositories/assign/simulateds.assign';
import { IListExclusiveSimulatedsPaginatedInput } from '../../schema/simulateds.schema';

interface IListExclusiveSimulatedsPaginatedInputWithCustomerId
  extends IListExclusiveSimulatedsPaginatedInput {}

export class ListExclusiveSimulatedsPaginatedUseCase {
  constructor(private readonly simulatedsRepository: ISimulatedsRepository) {}

  async execute(
    params: IListExclusiveSimulatedsPaginatedInputWithCustomerId
  ): Promise<IListExclusiveSimulatedsPaginatedResponse> {
    return this.simulatedsRepository.listExclusiveSimulatedsPaginated(params);
  }
}
