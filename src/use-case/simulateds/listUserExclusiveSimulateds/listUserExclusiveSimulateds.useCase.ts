import {
  ISimulatedWithProgressDTO,
  SimulatedInfoDTO,
  UserSimulatedAccessDTO,
} from '../../../model/DTO/ISimulated.dto';
import { ISimulatedsAccessRepository } from '../../../repositories/assign/simulatedsAccess.assign';
import { IListUserExclusiveSimulatedsQueryInput } from '../../../schema/simulateds.schema';

interface ListUserExclusiveSimulatedsRequest extends IListUserExclusiveSimulatedsQueryInput {}

interface ISimulatedWithProgressAndExtra extends ISimulatedWithProgressDTO {
  status?: string;
  time_limit?: number;
  start_date?: string | null;
  end_date?: string | null;
  customer_id?: string;
  total_questions?: number;
}

export class ListUserExclusiveSimulatedsUseCase {
  constructor(private simulatedsAccessRepository: ISimulatedsAccessRepository) {}

  async execute({
    userId,
    customerId,
    courseId,
    isClosed,
  }: ListUserExclusiveSimulatedsRequest): Promise<
    { simulated: SimulatedInfoDTO; userAccess: UserSimulatedAccessDTO }[]
  > {
    const result = await this.simulatedsAccessRepository.findExclusiveSimulatedsByUserId({
      userId,
      customerId,
      courseId,
      isClosed,
    });
    return result.map((item: ISimulatedWithProgressAndExtra) => ({
      simulated: {
        id: item.id,
        name: item.name,
        active: item.active,
        simulated_type_id: item.simulated_type_id,
        simulated_type_name: item.simulated_type_name,
        status: item.status,
        time_limit: item.time_limit,
        start_date: item.start_date,
        end_date: item.end_date,
        customer_id: item.customer_id,
        total_questions: item.total_questions,
      },
      userAccess: {
        access_id: item.access_id,
        start_simulated: item.start_simulated,
        end_simulated: item.end_simulated,
        active: item.access_active,
      },
    }));
  }
}
