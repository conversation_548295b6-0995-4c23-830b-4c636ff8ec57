import { ISimulatedWithProgressDTO } from '../../../model/DTO/ISimulated.dto';
import { ISimulatedsAccessRepository } from '../../../repositories/assign/simulatedsAccess.assign';

export interface ListUserExclusiveSimulatedsUserHistoryInput {
  userId: string;
  customerId: string;
  courseId: string;
}

interface SimulatedWithProgress extends ISimulatedWithProgressDTO {
  totalQuestions: number;
  answeredQuestions: number;
  scorePercentage: number;
  isCompleted: boolean;
  createdAt: Date;
}

interface GroupedSimulateds {
  date: string;
  simulateds: SimulatedWithProgress[];
}

interface ListUserExclusiveSimulatedsUserHistoryResponse {
  inProgress: GroupedSimulateds[];
  completed: GroupedSimulateds[];
}

interface ExclusiveSimulatedAccess {
  id: string;
  simulated_id: string;
  name: string;
  active: boolean;
  simulated_type_id: string;
  simulated_type_name: string;
  created_at: string;
  updated_at: string;
  start_simulated?: string | null;
  end_simulated?: string | null;
  total_questions?: number;
  answered_questions?: number;
  correct_answers?: number;
}

function toIsoOrNow(date: string | null | undefined): string {
  const d = date ? new Date(date) : null;
  return d && !isNaN(d.getTime()) ? d.toISOString() : new Date().toISOString();
}

export class ListUserExclusiveSimulatedsUserHistoryUseCase {
  constructor(private simulatedsAccessRepository: ISimulatedsAccessRepository) {}

  async execute({
    userId,
    customerId,
    courseId,
  }: ListUserExclusiveSimulatedsUserHistoryInput): Promise<ListUserExclusiveSimulatedsUserHistoryResponse> {
    const accesses =
      await this.simulatedsAccessRepository.findExclusiveSimulatedsAccessHistoryByUserId({
        userId,
        customerId,
        courseId,
      });

    const inProgressMap = new Map<string, SimulatedWithProgress[]>();
    const completedMap = new Map<string, SimulatedWithProgress[]>();

    for (const access of accesses as unknown as ExclusiveSimulatedAccess[]) {
      const totalQuestions = access.total_questions || 0;
      const answeredQuestions = access.answered_questions || 0;
      const correctAnswers = access.correct_answers || 0;

      const isCompleted = !access.active;

      let dateStr: string;
      if (isCompleted) {
        dateStr = toIsoOrNow(access.end_simulated).split('T')[0];
      } else {
        dateStr = toIsoOrNow(access.start_simulated || access.created_at).split('T')[0];
      }

      const targetMap = isCompleted ? completedMap : inProgressMap;

      if (!targetMap.has(dateStr)) {
        targetMap.set(dateStr, []);
      }

      const processedSimulated: SimulatedWithProgress = {
        id: access.simulated_id,
        name: access.name,
        active: access.active,
        simulated_type_id: access.simulated_type_id,
        simulated_type_name: access.simulated_type_name,
        created_at: access.created_at,
        updated_at: access.updated_at,
        start_simulated: access.start_simulated ?? null,
        end_simulated: access.end_simulated ?? null,
        access_id: access.id,
        totalQuestions,
        answeredQuestions,
        scorePercentage:
          totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0,
        isCompleted,
        createdAt: new Date(access.created_at),
      };

      targetMap.get(dateStr)!.push(processedSimulated);
    }

    return {
      inProgress: this.mapToGroupedArray(inProgressMap),
      completed: this.mapToGroupedArray(completedMap),
    };
  }

  private mapToGroupedArray(map: Map<string, SimulatedWithProgress[]>): GroupedSimulateds[] {
    return Array.from(map.entries())
      .sort((a, b) => b[0].localeCompare(a[0]))
      .map(([date, simulateds]) => ({
        date,
        simulateds: simulateds.sort((a, b) => {
          if (a.isCompleted && b.isCompleted && a.end_simulated && b.end_simulated) {
            return new Date(b.end_simulated).getTime() - new Date(a.end_simulated).getTime();
          }
          return b.createdAt.getTime() - a.createdAt.getTime();
        }),
      }));
  }
}
