import { ISimulatedWithProgressDTO } from '../../../model/DTO/ISimulated.dto';
import { ISimulatedsRepository } from '../../../repositories/assign/simulateds.assign';

interface ListUserSimulatedsRequest {
  userId: string;
  customerId: string;
  questionGroupId: string;
}

interface SimulatedWithProgress extends ISimulatedWithProgressDTO {
  totalQuestions: number;
  answeredQuestions: number;
  scorePercentage: number;
  isCompleted: boolean;
  createdAt: Date;
}

interface GroupedSimulateds {
  date: string;
  simulateds: SimulatedWithProgress[];
}

interface ListUserSimulatedsResponse {
  inProgress: GroupedSimulateds[];
  completed: GroupedSimulateds[];
}

export class ListUserSimulatedsUseCase {
  constructor(private simulatedsRepository: ISimulatedsRepository) {}

  async execute({
    userId,
    customerId,
    questionGroupId,
  }: ListUserSimulatedsRequest): Promise<ListUserSimulatedsResponse> {
    const simulatedsWithProgress = await this.simulatedsRepository.findByUserWithProgress({
      userId,
      customerId,
      questionGroupId,
    });

    const inProgressMap = new Map<string, SimulatedWithProgress[]>();
    const completedMap = new Map<string, SimulatedWithProgress[]>();

    for (const simulated of simulatedsWithProgress) {
      const totalQuestions = simulated.total_questions || 0;
      const answeredQuestions = simulated.answered_questions || 0;
      const correctAnswers = simulated.correct_answers || 0;

      const isCompleted = !simulated.active;

      let dateStr: string;
      if (isCompleted) {
        const dateEnd = simulated.end_simulated || new Date().toISOString();
        dateStr = new Date(dateEnd).toISOString().split('T')[0];
      } else {
        const dateStart = simulated.start_simulated || simulated.created_at;
        dateStr = new Date(dateStart).toISOString().split('T')[0];
      }

      const targetMap = isCompleted ? completedMap : inProgressMap;

      if (!targetMap.has(dateStr)) {
        targetMap.set(dateStr, []);
      }

      const processedSimulated: SimulatedWithProgress = {
        id: simulated.id,
        name: simulated.name,
        active: simulated.active,
        simulated_type_id: simulated.simulated_type_id,
        simulated_type_name: simulated.simulated_type_name,
        created_at: simulated.created_at,
        updated_at: simulated.updated_at,
        start_simulated: simulated.start_simulated,
        end_simulated:
          isCompleted && !simulated.end_simulated
            ? new Date().toISOString()
            : simulated.end_simulated,
        access_id: simulated.access_id,
        totalQuestions,
        answeredQuestions,
        scorePercentage:
          totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0,
        isCompleted,
        createdAt: new Date(simulated.created_at),
      };

      targetMap.get(dateStr)!.push(processedSimulated);
    }

    return {
      inProgress: this.mapToGroupedArray(inProgressMap),
      completed: this.mapToGroupedArray(completedMap),
    };
  }

  private mapToGroupedArray(map: Map<string, SimulatedWithProgress[]>): GroupedSimulateds[] {
    return Array.from(map.entries())
      .sort((a, b) => b[0].localeCompare(a[0]))
      .map(([date, simulateds]) => ({
        date,
        simulateds: simulateds.sort((a, b) => {
          if (a.isCompleted && b.isCompleted && a.end_simulated && b.end_simulated) {
            return new Date(b.end_simulated).getTime() - new Date(a.end_simulated).getTime();
          }
          return b.createdAt.getTime() - a.createdAt.getTime();
        }),
      }));
  }
}
