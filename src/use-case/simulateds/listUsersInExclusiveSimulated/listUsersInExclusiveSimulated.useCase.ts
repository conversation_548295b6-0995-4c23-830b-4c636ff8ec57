import { IUserSimulatedAccessDTO } from '../../../model/DTO/IUserSimulatedAccess.dto';
import { ISimulatedsAccessRepository } from '../../../repositories/assign/simulatedsAccess.assign';

export class ListUsersInExclusiveSimulatedUseCase {
  constructor(private readonly simulatedsAccessRepo: ISimulatedsAccessRepository) {}

  async execute(simulatedId: string): Promise<IUserSimulatedAccessDTO[]> {
    return this.simulatedsAccessRepo.findUsersBySimulatedId(simulatedId);
  }
}
