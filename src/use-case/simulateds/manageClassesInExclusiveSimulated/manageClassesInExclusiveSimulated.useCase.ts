import { randomUUID } from 'crypto';
import { Knex } from 'knex';

import { generateTransaction } from '../../../helpers/transaction.helper';
import { SimulatedStatusEnum } from '../../../model/enums/simulatedStatus.enum';
import { IUserClasses } from '../../../model/IUserClasses';
import { IClassesRepository } from '../../../repositories/assign/classes.assign';
import { ISimulatedsRepository } from '../../../repositories/assign/simulateds.assign';
import { ISimulatedsAccessRepository } from '../../../repositories/assign/simulatedsAccess.assign';
import { ISimulatedsClassesRepository } from '../../../repositories/assign/simulatedsClasses.assign';
import { IUserClassesRepository } from '../../../repositories/assign/userClasses.assign';
import { ISimulatedClassesInput } from '../../../schema/simulateds.schema';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface IManageClassesInExclusiveSimulatedInput extends ISimulatedClassesInput {}

interface Response {
  totalClassesAdded: number;
  totalClassesKept: number;
  totalClassesRemoved: number;
  totalUsersAdded: number;
  totalUsersRemoved: number;
}

export class ManageClassesInExclusiveSimulatedUseCase {
  constructor(
    private simulatedsClassesRepo: ISimulatedsClassesRepository,
    private userClassesRepo: IUserClassesRepository,
    private simulatedsAccessRepo: ISimulatedsAccessRepository,
    private simulatedsRepo: ISimulatedsRepository,
    private classesRepo: IClassesRepository
  ) {}

  async execute({
    simulatedId,
    classIds = [],
  }: IManageClassesInExclusiveSimulatedInput): Promise<Response> {
    const transaction = await generateTransaction();

    try {
      const simulated = await this.validateSimulated(simulatedId);

      if (classIds.length > 0) {
        await this.validateClasses(classIds);
      }

      const oldClassIds = (
        await this.simulatedsClassesRepo.findBy({ simulated_id: simulatedId })
      ).map((c) => c.class_id);

      const classesToRemove = oldClassIds.filter((id) => !classIds.includes(id));
      const classesToAdd = classIds.filter((id) => !oldClassIds.includes(id));
      const classesToKeep = classIds.filter((id) => oldClassIds.includes(id));

      if (simulated.status === SimulatedStatusEnum.PUBLISHED && classesToRemove.length > 0) {
        throw new BadRequestError('Não é permitido remover turmas de um simulado publicado');
      }

      let usersRemoved = 0;

      if (classesToRemove.length > 0) {
        usersRemoved = await this.removeClassesAndAccesses(
          simulatedId,
          classesToRemove,
          transaction
        );
      }

      let usersToAdd: IUserClasses[] = [];

      if (classesToAdd.length > 0) {
        await this.addNewClasses(simulatedId, classesToAdd, transaction);
        usersToAdd = await this.userClassesRepo.findByClassIds(classesToAdd);
        await this.addUserAccesses(simulatedId, usersToAdd, transaction);
      }

      await transaction.commit();

      return {
        totalClassesAdded: classesToAdd.length,
        totalClassesKept: classesToKeep.length,
        totalClassesRemoved: classesToRemove.length,
        totalUsersAdded: usersToAdd.length,
        totalUsersRemoved: usersRemoved,
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  private async validateClasses(classIds: string[]) {
    const existingClasses = await this.classesRepo.findByIds(classIds);
    const existingClassIds = existingClasses.map((c) => c.id);
    const nonExistentClassIds = classIds.filter((id) => !existingClassIds.includes(id));

    if (nonExistentClassIds.length > 0) {
      throw new BadRequestError(`Turmas não encontradas: ${nonExistentClassIds.join(', ')}`);
    }
  }

  private async validateSimulated(simulatedId: string) {
    const simulated = await this.simulatedsRepo.findOneBy({ id: simulatedId });

    if (!simulated) {
      throw new ResourceNotFoundError('Simulado não encontrado');
    }

    return simulated;
  }

  private async removeClassesAndAccesses(
    simulatedId: string,
    classIds: string[],
    transaction: Knex.Transaction
  ) {
    if (!classIds.length) return 0;

    await this.simulatedsClassesRepo.deleteBySimulatedIdAndClassIds(
      simulatedId,
      classIds,
      transaction
    );

    const users = await this.userClassesRepo.findByClassIds(classIds);
    const userIds = users.map((u) => u.user_id);

    if (userIds.length > 0) {
      await this.simulatedsAccessRepo.deleteByUserIdsAndSimulatedId(
        userIds,
        simulatedId,
        transaction
      );
    }

    return userIds.length;
  }

  private async addNewClasses(
    simulatedId: string,
    classIds: string[],
    transaction: Knex.Transaction
  ) {
    const now = new Date();
    const links = classIds.map((classId) => ({
      id: randomUUID(),
      class_id: classId,
      simulated_id: simulatedId,
      created_at: now,
      updated_at: now,
    }));

    await this.simulatedsClassesRepo.insertAll(links, transaction);
  }

  private async addUserAccesses(
    simulatedId: string,
    users: IUserClasses[],
    transaction: Knex.Transaction
  ) {
    if (!users.length) return;

    const now = new Date();

    const existingAccesses = await this.simulatedsAccessRepo.findBy({ simulated_id: simulatedId });
    const existingUserIds = new Set(existingAccesses.map((a) => a.user_id));

    const newUsers = users.filter((u) => !existingUserIds.has(u.user_id));

    const accessLinks = newUsers.map((user) => ({
      id: randomUUID(),
      user_id: user.user_id,
      simulated_id: simulatedId,
      active: true,
      start_simulated: null,
      end_simulated: null,
      created_at: now,
      updated_at: now,
    }));

    if (accessLinks.length > 0) {
      await this.simulatedsAccessRepo.insertAll(accessLinks, transaction);
    }
  }
}
