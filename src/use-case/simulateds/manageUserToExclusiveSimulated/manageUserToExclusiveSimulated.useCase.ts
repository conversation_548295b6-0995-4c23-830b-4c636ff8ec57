import { randomUUID } from 'crypto';
import { Knex } from 'knex';

import { generateTransaction } from '../../../helpers/transaction.helper';
import { SimulatedStatusEnum } from '../../../model/enums/simulatedStatus.enum';
import { ISimulated } from '../../../model/ISimulated';
import { IClassesRepository } from '../../../repositories/assign/classes.assign';
import { ISimulatedsRepository } from '../../../repositories/assign/simulateds.assign';
import { ISimulatedsAccessRepository } from '../../../repositories/assign/simulatedsAccess.assign';
import { ISimulatedsClassesRepository } from '../../../repositories/assign/simulatedsClasses.assign';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

type PerClassResult = {
  classId: string;
  usersAdded: number;
};

type SimulatedAccessLink = {
  id: string;
  user_id: string;
  simulated_id: string;
  active: boolean;
  start_simulated: Date | null;
  end_simulated: Date | null;
  created_at: Date;
  updated_at: Date;
  class_id: string;
  time_limit: number | null;
};

type SimulatedClassLink = {
  id: string;
  class_id: string;
  simulated_id: string;
  created_at: Date;
  updated_at: Date;
};

interface AddUsersToExclusiveSimulatedInput {
  simulatedId: string;
  data: Array<{
    classId: string;
    users: string[];
  }>;
}

interface AddUsersToExclusiveSimulatedResult {
  totalLinksCreated: number;
  totalUsersRemoved: number;
  totalClassesRemoved: number;
  perClass: PerClassResult[];
}

export class ManageUserToExclusiveSimulatedUseCase {
  constructor(
    private simulatedsRepo: ISimulatedsRepository,
    private simulatedsAccessRepo: ISimulatedsAccessRepository,
    private classesRepo: IClassesRepository,
    private simulatedsClassesRepo: ISimulatedsClassesRepository
  ) {}

  async execute({
    simulatedId,
    data,
  }: AddUsersToExclusiveSimulatedInput): Promise<AddUsersToExclusiveSimulatedResult> {
    const transaction = await generateTransaction();

    try {
      const simulated = await this.simulatedsRepo.findOneBy({ id: simulatedId });
      if (!simulated) throw new ResourceNotFoundError('Simulado não encontrado');

      const { existingAccessKeys, existingClassLinkIds } =
        await this.getExistingAccessesAndLinks(simulatedId);

      if (!data || data.length === 0) {
        this.ensureNoRemovalInPublished(simulated);
        const totalUsersRemoved = await this.simulatedsAccessRepo.deleteAllBySimulatedId(
          simulatedId,
          transaction
        );
        const totalClassesRemoved = await this.simulatedsClassesRepo.deleteAllBySimulatedId(
          simulatedId,
          transaction
        );
        await transaction.commit();
        return { totalLinksCreated: 0, perClass: [], totalUsersRemoved, totalClassesRemoved };
      }

      await this.validateSimulatedAndClasses(simulated, data);

      this.ensureNoInvalidRemovals(simulated, data, existingAccessKeys, existingClassLinkIds);

      const { usersRemoved } = await this.removeUnlinkedUsers(
        simulatedId,
        data,
        existingAccessKeys,
        transaction
      );
      const { classesRemoved } = await this.removeUnlinkedClasses(
        simulatedId,
        data,
        existingClassLinkIds,
        transaction
      );

      const now = new Date();
      const { accessLinks, classLinks, perClass } = this.generateLinks({
        data,
        simulatedId,
        now,
        existingAccessKeys,
        existingClassLinkIds,
        simulated,
      });

      await this.persistLinks({ accessLinks, classLinks, transaction });
      await transaction.commit();

      return {
        totalLinksCreated: perClass.reduce((acc, cur) => acc + cur.usersAdded, 0),
        perClass,
        totalUsersRemoved: usersRemoved,
        totalClassesRemoved: classesRemoved,
      };
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  }

  private async validateSimulatedAndClasses(
    simulated: ISimulated,
    data: AddUsersToExclusiveSimulatedInput['data']
  ) {
    const allClassIds = data.map((d) => d.classId);
    const existingClasses = await this.classesRepo.findByIds(allClassIds);
    const existingClassIds = existingClasses.map((c) => c.id);

    const notFound = allClassIds.filter((id) => !existingClassIds.includes(id));
    if (notFound.length > 0)
      throw new BadRequestError(`Turmas não encontradas: ${notFound.join(', ')}`);
  }

  private async getExistingAccessesAndLinks(simulatedId: string) {
    const existingAccesses = await this.simulatedsAccessRepo.findBy({ simulated_id: simulatedId });
    const existingAccessKeys = new Set(existingAccesses.map((a) => `${a.user_id}::${a.class_id}`));

    const existingClassLinks = await this.simulatedsClassesRepo.findBy({
      simulated_id: simulatedId,
    });
    const existingClassLinkIds = new Set(existingClassLinks.map((c) => c.class_id));

    return { existingAccessKeys, existingClassLinkIds };
  }

  private ensureNoRemovalInPublished(simulated: ISimulated) {
    const status = simulated.status as SimulatedStatusEnum;
    if ([SimulatedStatusEnum.PUBLISHED, SimulatedStatusEnum.SCHEDULED].includes(status)) {
      throw new BadRequestError(
        'Não é permitido remover usuários ou turmas de um simulado publicado ou agendado'
      );
    }
  }

  private ensureNoInvalidRemovals(
    simulated: ISimulated,
    data: AddUsersToExclusiveSimulatedInput['data'],
    existingAccessKeys: Set<string>,
    existingClassLinkIds: Set<string>
  ) {
    const requestedClassIds = new Set(data.map((d) => d.classId));
    const requestedAccessKeys = new Set(
      data.flatMap((d) => d.users.map((u) => `${u}::${d.classId}`))
    );

    const isRemovingClass = Array.from(existingClassLinkIds).some(
      (id) => !requestedClassIds.has(id)
    );
    const isRemovingUser = Array.from(existingAccessKeys).some(
      (key) => !requestedAccessKeys.has(key)
    );

    const status = simulated.status as SimulatedStatusEnum;
    if (
      (isRemovingClass || isRemovingUser) &&
      [SimulatedStatusEnum.PUBLISHED, SimulatedStatusEnum.SCHEDULED].includes(status)
    ) {
      throw new BadRequestError(
        'Não é permitido remover usuários ou turmas de um simulado publicado ou agendado'
      );
    }
  }

  private async removeUnlinkedUsers(
    simulatedId: string,
    data: AddUsersToExclusiveSimulatedInput['data'],
    existingAccessKeys: Set<string>,
    transaction: Knex.Transaction
  ): Promise<{ usersRemoved: number }> {
    const requestedAccessKeys = new Set(
      data.flatMap((d) => d.users.map((u) => `${u}::${d.classId}`))
    );
    const accessKeysToRemove = Array.from(existingAccessKeys).filter(
      (key) => !requestedAccessKeys.has(key)
    );

    const userIdsToRemove: string[] = [];
    if (accessKeysToRemove.length > 0) {
      for (const key of accessKeysToRemove) {
        const [userId] = key.split('::');
        userIdsToRemove.push(userId);
      }
      await this.simulatedsAccessRepo.deleteByUserIdsAndSimulatedId(
        userIdsToRemove,
        simulatedId,
        transaction
      );
    }
    return { usersRemoved: userIdsToRemove.length };
  }

  private async removeUnlinkedClasses(
    simulatedId: string,
    data: AddUsersToExclusiveSimulatedInput['data'],
    existingClassLinkIds: Set<string>,
    transaction: Knex.Transaction
  ): Promise<{ classesRemoved: number }> {
    const requestedClassIds = new Set(data.map((d) => d.classId));
    const classIdsToRemove = Array.from(existingClassLinkIds).filter(
      (id) => !requestedClassIds.has(id)
    );
    if (classIdsToRemove.length > 0) {
      await this.simulatedsClassesRepo.deleteBySimulatedIdAndClassIds(
        simulatedId,
        classIdsToRemove,
        transaction
      );
    }
    return { classesRemoved: classIdsToRemove.length };
  }

  private generateLinks(params: {
    data: AddUsersToExclusiveSimulatedInput['data'];
    simulatedId: string;
    now: Date;
    existingAccessKeys: Set<string>;
    existingClassLinkIds: Set<string>;
    simulated: ISimulated;
  }): {
    accessLinks: SimulatedAccessLink[];
    classLinks: SimulatedClassLink[];
    perClass: PerClassResult[];
  } {
    const { data, simulatedId, now, existingAccessKeys, existingClassLinkIds, simulated } = params;
    const accessLinks: SimulatedAccessLink[] = [];
    const classLinks: SimulatedClassLink[] = [];
    const perClass: PerClassResult[] = [];

    for (const { classId, users } of data) {
      let usersAdded = 0;

      if (!existingClassLinkIds.has(classId)) {
        classLinks.push({
          id: randomUUID(),
          class_id: classId,
          simulated_id: simulatedId,
          created_at: now,
          updated_at: now,
        });
        existingClassLinkIds.add(classId);
      }

      for (const userId of users) {
        const accessKey = `${userId}::${classId}`;
        if (!existingAccessKeys.has(accessKey)) {
          accessLinks.push({
            id: randomUUID(),
            user_id: userId,
            simulated_id: simulatedId,
            active: true,
            start_simulated: null,
            end_simulated: null,
            created_at: now,
            updated_at: now,
            class_id: classId,
            time_limit: simulated.time_limit ?? null,
          });
          existingAccessKeys.add(accessKey);
          usersAdded++;
        }
      }

      perClass.push({ classId, usersAdded });
    }

    return { accessLinks, classLinks, perClass };
  }

  private async persistLinks(params: {
    accessLinks: SimulatedAccessLink[];
    classLinks: SimulatedClassLink[];
    transaction: Knex.Transaction;
  }) {
    const { accessLinks, classLinks, transaction } = params;

    if (accessLinks.length > 0) {
      await this.simulatedsAccessRepo.insertAll(accessLinks, transaction);
    }

    if (classLinks.length > 0) {
      await this.simulatedsClassesRepo.insertAll(classLinks, transaction);
    }
  }
}
