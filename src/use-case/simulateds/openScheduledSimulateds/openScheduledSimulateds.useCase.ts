import { Knex } from 'knex';

import { generateTransaction } from '../../../helpers/transaction.helper';
import { SimulatedStatusEnum } from '../../../model/enums/simulatedStatus.enum';
import { ISimulatedsRepository } from '../../../repositories/assign/simulateds.assign';

interface OpenScheduledSimulatedsResponse {
  totalSimuladosAbertos: number;
  simuladosAbertos: string[];
  erros: Array<{ simuladoId: string; error: string }>;
}

export class OpenScheduledSimulatedsUseCase {
  constructor(private readonly simulatedsRepo: ISimulatedsRepository) {}

  async execute(): Promise<OpenScheduledSimulatedsResponse> {
    const response: OpenScheduledSimulatedsResponse = {
      totalSimuladosAbertos: 0,
      simuladosAbertos: [],
      erros: [],
    };
    const transaction: Knex.Transaction = await generateTransaction();
    try {
      const now = new Date();
      const toOpen = await this.simulatedsRepo.findScheduledExclusiveSimulatedsToOpen(now);
      if (!toOpen.length) {
        await transaction.commit();
        return response;
      }
      const ids = toOpen.map((s) => s.id);
      await this.simulatedsRepo.updateManyStatusAndActive(
        ids,
        SimulatedStatusEnum.PUBLISHED,
        true,
        transaction
      );
      response.simuladosAbertos = ids;
      response.totalSimuladosAbertos = ids.length;
      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      response.erros.push({ simuladoId: 'BATCH', error: errorMessage });
    }
    return response;
  }
}
