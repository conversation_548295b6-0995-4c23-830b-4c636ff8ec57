import { StatusSimulatedEnum } from '../../../model/enums/statusSimulated.enum';
import { IAnswerSession } from '../../../model/IAnswerSession';
import { ISimulatedAccess } from '../../../model/ISimulatedAcces';
import { IAnswerSessionsRepository } from '../../../repositories/assign/answerSessions.assign';
import { ISimulatedsAccessRepository } from '../../../repositories/assign/simulatedsAccess.assign';
import { PauseResumeSimulatedPayload } from '../../../schema/simulateds.schema';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface PauseResumeSimulatedRequest extends PauseResumeSimulatedPayload {}

interface PauseResumeSimulatedResponse {
  totalSeconds: number;
  answerSession: IAnswerSession;
  currentSessionDuration: number;
}

export class PauseResumeSimulatedUseCase {
  constructor(
    private readonly accessRepo: ISimulatedsAccessRepository,
    private readonly sessionsRepo: IAnswerSessionsRepository
  ) {}

  async execute({
    simulatedAccessId,
    userId,
    action,
  }: PauseResumeSimulatedRequest): Promise<PauseResumeSimulatedResponse> {
    await this.ensureAccessIsValid(simulatedAccessId, userId);
    const latestSession = await this.getLatestSession(simulatedAccessId);
    const now = new Date();

    const currentSessionDuration = this.calculateCurrentSessionDuration(latestSession, now);

    const answerSession =
      action === StatusSimulatedEnum.PAUSE
        ? await this.pauseSimulated(latestSession, now)
        : await this.resumeSimulated(latestSession, simulatedAccessId, now);

    const previousSessionsTime = await this.calculatePreviousSessionsTime(simulatedAccessId);

    const totalSeconds =
      action === StatusSimulatedEnum.PAUSE
        ? previousSessionsTime
        : currentSessionDuration + previousSessionsTime;

    await this.accessRepo.update({
      id: simulatedAccessId,
      total_seconds: totalSeconds,
    });

    return {
      answerSession,
      totalSeconds,
      currentSessionDuration,
    };
  }

  private calculateCurrentSessionDuration(session: IAnswerSession | null, now: Date): number {
    if (!session || session.paused_at) {
      return 0;
    }

    return Math.floor((now.getTime() - new Date(session.started_at).getTime()) / 1000);
  }

  private async calculatePreviousSessionsTime(simulatedAccessId: string): Promise<number> {
    const sessions = await this.sessionsRepo.findBySimulatedAccessId(simulatedAccessId);

    return sessions.reduce((sum, session) => {
      if (session.duration_seconds !== null && session.duration_seconds !== undefined) {
        return sum + session.duration_seconds;
      }
      return sum;
    }, 0);
  }

  private async ensureAccessIsValid(
    simulatedAccessId: string,
    userId: string
  ): Promise<ISimulatedAccess> {
    const access = await this.accessRepo.findOneBy({ id: simulatedAccessId, user_id: userId });

    if (!access) throw new ResourceNotFoundError('Acesso ao simulado não encontrado');

    if (access.end_simulated) throw new BadRequestError('Este simulado já foi finalizado');

    return access;
  }

  private async getLatestSession(simulatedAccessId: string): Promise<IAnswerSession | null> {
    const sessions = await this.sessionsRepo.findBySimulatedAccessId(simulatedAccessId);

    if (!sessions.length) return null;

    return sessions.reduce((latest, current) =>
      new Date(current.started_at) > new Date(latest.started_at) ? current : latest
    );
  }

  private async pauseSimulated(session: IAnswerSession | null, now: Date): Promise<IAnswerSession> {
    if (!session) {
      throw new BadRequestError('Não é possível pausar um simulado que não foi iniciado');
    }

    if (session.paused_at) return session;

    const duration = Math.floor((now.getTime() - new Date(session.started_at).getTime()) / 1000);

    return this.sessionsRepo.update({
      id: session.id,
      paused_at: now,
      duration_seconds: duration,
    });
  }

  private async resumeSimulated(
    session: IAnswerSession | null,
    accessId: string,
    now: Date
  ): Promise<IAnswerSession> {
    if (!session || session.paused_at) {
      return this.sessionsRepo.insert({
        simulated_access_id: accessId,
        started_at: now,
        duration_seconds: 0,
      });
    }

    return session;
  }
}
