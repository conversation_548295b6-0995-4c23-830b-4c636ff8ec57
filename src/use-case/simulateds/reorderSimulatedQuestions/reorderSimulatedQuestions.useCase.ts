import { IQuestionSimulated } from '../../../model/IQuestionSimulated';
import { IQuestionsSimulatedsRepository } from '../../../repositories/assign/questionsSimulateds.assign';
import { ISimulatedsRepository } from '../../../repositories/assign/simulateds.assign';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface ReorderSimulatedQuestionsUseCaseRequest {
  simulatedId: string;
  customerId: string;
  questions: {
    questionId: string;
    order: number;
  }[];
}

interface ReorderSimulatedQuestionsUseCaseResponse {
  simulatedQuestions: IQuestionSimulated[];
}

export class ReorderSimulatedQuestionsUseCase {
  constructor(
    private simulatedsRepository: ISimulatedsRepository,
    private questionsSimulatedsRepository: IQuestionsSimulatedsRepository
  ) {}

  async execute({
    simulatedId,
    customerId,
    questions,
  }: ReorderSimulatedQuestionsUseCaseRequest): Promise<ReorderSimulatedQuestionsUseCaseResponse> {
    await this.findDraftSimulatedOrFail(simulatedId, customerId);
    const activeQuestions = await this.findActiveSimulatedQuestionsOrFail(simulatedId);

    this.validateReorderRequest(activeQuestions, questions);

    const orderMap = new Map(questions.map((q) => [q.questionId, q.order]));
    const updates = activeQuestions.map((q) => ({
      id: q.id,
      order_by: orderMap.get(q.question_id),
      updated_at: new Date(),
    }));

    const updatedSimulatedQuestions = await this.questionsSimulatedsRepository.updateBatch(updates);
    return { simulatedQuestions: updatedSimulatedQuestions };
  }

  private async findDraftSimulatedOrFail(simulatedId: string, customerId: string) {
    const simulated = await this.simulatedsRepository.findOneBy({
      id: simulatedId,
      customer_id: customerId,
      deleted_at: null,
    });

    if (!simulated) throw new ResourceNotFoundError('Simulado não encontrado');

    if (simulated.status !== 'draft') {
      throw new BadRequestError('Apenas simulados em modo rascunho podem ser reordenados');
    }

    return simulated;
  }

  private async findActiveSimulatedQuestionsOrFail(
    simulatedId: string
  ): Promise<IQuestionSimulated[]> {
    const questions = await this.questionsSimulatedsRepository.findAllBy({
      simulated_id: simulatedId,
    });
    const activeQuestions = questions.filter((q) => q.deleted_at === null);

    if (activeQuestions.length === 0) {
      throw new BadRequestError('O simulado não possui questões ativas para reordenar');
    }

    return activeQuestions;
  }

  private validateReorderRequest(
    activeQuestions: IQuestionSimulated[],
    requestedQuestions: { questionId: string; order: number }[]
  ) {
    const currentIds = new Set(activeQuestions.map((q) => q.question_id));
    const requestedIds = requestedQuestions.map((q) => q.questionId);
    const invalidIds = requestedIds.filter((id) => !currentIds.has(id));

    if (invalidIds.length > 0) {
      throw new BadRequestError(
        `Questões não encontradas ou excluídas do simulado: ${invalidIds.join(', ')}`
      );
    }

    if (requestedIds.length !== activeQuestions.length) {
      throw new BadRequestError('Todas as questões ativas devem ser incluídas na reordenação');
    }

    const orders = requestedQuestions.map((q) => q.order);
    const uniqueOrders = new Set(orders);

    if (uniqueOrders.size !== orders.length) {
      throw new BadRequestError('Não pode haver ordens duplicadas');
    }

    const min = Math.min(...orders);
    const max = Math.max(...orders);

    if (min !== 1 || max !== orders.length) {
      throw new BadRequestError(
        `As ordens devem ser contínuas, de 1 até o total de questões: ${orders.length}`
      );
    }
  }
}
