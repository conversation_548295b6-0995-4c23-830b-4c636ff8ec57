import { SimulatedStatusEnum } from '../../../model/enums/simulatedStatus.enum';
import { SimulatedTypeEnum } from '../../../model/enums/simulatedType.enum';
import { ISimulated } from '../../../model/ISimulated';
import { ISimulatedAccess } from '../../../model/ISimulatedAcces';
import { ISimulatedsRepository } from '../../../repositories/assign/simulateds.assign';
import { ISimulatedsAccessRepository } from '../../../repositories/assign/simulatedsAccess.assign';
import { ISimulatedTypesRepository } from '../../../repositories/assign/simulatedTypes.assign';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface StartSessionRequest {
  simulatedAccessId: string;
  userId: string;
}

interface StartSessionResponse {
  access: ISimulatedAccess;
  simulated: ISimulated;
}

export class StartExclusiveSimulatedSessionUseCase {
  constructor(
    private readonly simulatedsRepository: ISimulatedsRepository,
    private readonly simulatedsAccessRepository: ISimulatedsAccessRepository,
    private readonly simulatedTypesRepository: ISimulatedTypesRepository
  ) {}

  async execute({ simulatedAccessId, userId }: StartSessionRequest): Promise<StartSessionResponse> {
    const access = await this.getValidAccess(simulatedAccessId, userId);
    const simulated = await this.getValidSimulated(access.simulated_id);

    if (access.start_simulated) {
      return { access, simulated };
    }

    const now = new Date();
    const updatedAccess = await this.simulatedsAccessRepository.update({
      id: simulatedAccessId,
      start_simulated: now,
      active: true,
    });

    return { access: updatedAccess, simulated };
  }

  private async getValidAccess(
    simulatedAccessId: string,
    userId: string
  ): Promise<ISimulatedAccess> {
    const access = await this.simulatedsAccessRepository.findOneBy({
      id: simulatedAccessId,
      user_id: userId,
    });
    if (!access) throw new ResourceNotFoundError('Simulado não encontrado');
    return access;
  }

  private async getValidSimulated(simulatedId: string): Promise<ISimulated> {
    const simulated = await this.simulatedsRepository.findOneBy({
      id: simulatedId,
      deleted_at: null,
    });
    if (!simulated) throw new ResourceNotFoundError('Simulado não encontrado');

    if (simulated.status === SimulatedStatusEnum.SCHEDULED) {
      throw new BadRequestError('Simulado não está disponível.');
    }

    const simulatedType = await this.simulatedTypesRepository.findOneBy({
      id: simulated.simulated_type_id,
    });

    if (!simulatedType || simulatedType.name !== SimulatedTypeEnum.EXCLUSIVE) {
      throw new BadRequestError('Simulado não está disponível.');
    }

    if (simulated.status === SimulatedStatusEnum.CLOSED) {
      throw new BadRequestError('Simulado já foi encerrado.');
    }

    return simulated;
  }
}
