import { SimulatedStatusEnum } from '../../../model/enums/simulatedStatus.enum';
import { IQuestionsSimulatedsRepository } from '../../../repositories/assign/questionsSimulateds.assign';
import { ISimulatedsRepository } from '../../../repositories/assign/simulateds.assign';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface IUnlinkQuestionFromExclusiveSimulatedRequest {
  simulatedId: string;
  questionId: string;
  customerId: string;
}

export class UnlinkQuestionFromExclusiveSimulatedUseCase {
  constructor(
    private readonly simulatedsRepository: ISimulatedsRepository,
    private readonly questionsSimulatedsRepository: IQuestionsSimulatedsRepository
  ) {}

  async execute({
    simulatedId,
    questionId,
    customerId,
  }: IUnlinkQuestionFromExclusiveSimulatedRequest): Promise<void> {
    const simulated = await this.simulatedsRepository.findOneBy({
      id: simulatedId,
      customer_id: customerId,
      deleted_at: null,
    });

    if (!simulated) {
      throw new BadRequestError('Simulado não encontrado');
    }

    if (simulated.status !== SimulatedStatusEnum.DRAFT) {
      throw new BadRequestError('Esta operação só é permitida para simulados em modo rascunho');
    }

    const questionSimulated = await this.questionsSimulatedsRepository.findOneBy({
      question_id: questionId,
      simulated_id: simulatedId,
      deleted_at: null,
    });

    if (!questionSimulated) {
      throw new ResourceNotFoundError('Vínculo da questão não encontrado neste simulado');
    }

    await this.questionsSimulatedsRepository.update({
      id: questionSimulated.id,
      deleted_at: new Date(),
    });
  }
}
