import { SimulatedStatusEnum } from '../../../model/enums/simulatedStatus.enum';
import { ISimulated } from '../../../model/ISimulated';
import { ISimulatedsRepository } from '../../../repositories/assign/simulateds.assign';
import { ISimulatedsAccessRepository } from '../../../repositories/assign/simulatedsAccess.assign';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface UpdateSimulatedDTO {
  name?: string;
  active?: boolean;
  simulatedTypeId?: string;
  status?: string;
  timeLimit?: number;
  startDate?: Date | null;
  endDate?: Date | null;
}

export class UpdateExclusiveSimulatedUseCase {
  constructor(
    private simulatedsRepository: ISimulatedsRepository,
    private simulatedsAccessRepository: ISimulatedsAccessRepository
  ) {}

  async execute(
    id: string,
    data: UpdateSimulatedDTO,
    customerId: string
  ): Promise<{ simulated: ISimulated }> {
    const simulated = await this.ensureExistsExclusiveSimulated(id, customerId);

    const resolved = this.mergeDataWithDefaults(data, simulated);

    if (resolved.status === SimulatedStatusEnum.PUBLISHED) {
      this.validateRequiredFields(resolved);
      if (resolved.startDate && resolved.endDate) {
        this.validateDateOrder(resolved.startDate, resolved.endDate);
      }
    }

    await this.updateSimulated(id, resolved);

    if (data.timeLimit !== undefined && data.timeLimit !== simulated.time_limit) {
      await this.simulatedsAccessRepository.updateTimeLimitBySimulatedId(id, resolved.timeLimit);
    }

    return {
      simulated,
    };
  }

  private mergeDataWithDefaults(
    data: UpdateSimulatedDTO,
    simulated: ISimulated
  ): Required<UpdateSimulatedDTO> {
    return {
      name: data.name ?? simulated.name,
      active: data.active ?? simulated.active ?? false,
      simulatedTypeId: data.simulatedTypeId ?? simulated.simulated_type_id,
      status: data.status ?? simulated.status ?? SimulatedStatusEnum.DRAFT,
      timeLimit: data.timeLimit ?? simulated.time_limit ?? 0,
      startDate: data.startDate ?? simulated.start_date ?? null,
      endDate: data.endDate ?? simulated.end_date ?? null,
    };
  }

  private validateRequiredFields(data: Required<UpdateSimulatedDTO>) {
    const missingFields: string[] = [];

    if (!data.name) missingFields.push('name');
    if (!data.simulatedTypeId) missingFields.push('simulatedTypeId');
    if (!data.timeLimit) missingFields.push('timeLimit');
    if (!data.startDate) missingFields.push('startDate');
    if (!data.endDate) missingFields.push('endDate');

    if (missingFields.length > 0) {
      throw new BadRequestError(
        `Campos obrigatórios para publicação ausentes: ${missingFields.join(', ')}`
      );
    }
  }

  private validateDateOrder(startDate: Date, endDate: Date) {
    if (startDate >= endDate) {
      throw new BadRequestError('A data de início deve ser anterior à data de término');
    }
  }

  private async ensureExistsExclusiveSimulated(
    id: string,
    customerId: string
  ): Promise<ISimulated> {
    const simulated = await this.simulatedsRepository.findOneBy({
      id,
      customer_id: customerId,
    });

    if (!simulated) {
      throw new ResourceNotFoundError('Simulado não encontrado');
    }

    return simulated;
  }

  private updateSimulated(id: string, data: UpdateSimulatedDTO) {
    return this.simulatedsRepository.update({
      id,
      name: data.name,
      active: data.active,
      simulated_type_id: data.simulatedTypeId,
      status: data.status,
      time_limit: data.timeLimit,
      start_date: data.startDate,
      end_date: data.endDate,
    });
  }
}
