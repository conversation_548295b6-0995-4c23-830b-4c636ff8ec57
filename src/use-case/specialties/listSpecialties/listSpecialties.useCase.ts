import { IPaginationDTO } from '../../../model/DTO/IGeneric.dto';
import type { ISpecialty } from '../../../model/ISpecialty';
import { ISpecialtiesRepository } from '../../../repositories/assign/specialties.assign';
import type { ListSpecialtiesParams } from '../../../schema/specialties.schema';

export interface ListSpecialtiesResponse {
  paginationInfo: IPaginationDTO;
  specialties: ISpecialty[];
}

export class ListSpecialtiesUseCase {
  constructor(private readonly specialtiesRepository: ISpecialtiesRepository) {}

  async execute(params: ListSpecialtiesParams): Promise<ListSpecialtiesResponse> {
    const { page = 1, limit = 10, search, listAll } = params;

    const result = await this.specialtiesRepository.findAllOrPaginateWithPagination({
      page,
      limit,
      search,
      listAll,
    });

    return {
      paginationInfo: {
        currentPage: result.paginationInfo.currentPage,
        itemsPerPage: result.paginationInfo.itemsPerPage,
        totalItems: result.paginationInfo.totalItems,
        totalPages: result.paginationInfo.totalPages,
        hasNextPage: result.paginationInfo.hasNextPage,
        hasPreviousPage: result.paginationInfo.hasPreviousPage,
      },
      specialties: result.specialties,
    };
  }
}
