import { randomUUID } from 'crypto';
import { promises as fs } from 'fs';
import path from 'path';

import { env } from '../../env';
import { generateTransaction } from '../../helpers/transaction.helper';
import { RolesEnum } from '../../model/enums/roles.enum';
import { IUser } from '../../model/IUser';
import { IAccessLevelsRepository } from '../../repositories/assign/accessLevels.assign';
import { IClassesRepository } from '../../repositories/assign/classes.assign';
import { ICourseRepository } from '../../repositories/assign/courses.assign';
import { ICustomerRepository } from '../../repositories/assign/customers.assign';
import { IFilesRepository } from '../../repositories/assign/files.assign';
import { IRecoveryRepository } from '../../repositories/assign/recoveries.assign';
import { ITermsRepository } from '../../repositories/assign/terms.assign';
import { IUserClassesRepository } from '../../repositories/assign/userClasses.assign';
import { IUsersRepository } from '../../repositories/assign/users.assign';
import S3Service from '../../services/aws/s3/S3Service';
import { SESService } from '../../services/aws/ses/SESService';
import { GenericError } from '../errors/GenericError';

interface AddStudentsByEmailDTO {
  courseId: string;
  customerId: string;
  emails: string[];
  classId: string;
}

export class AddStudentsByEmailUseCase {
  constructor(
    private readonly usersRepository: IUsersRepository,
    private readonly courseRepository: ICourseRepository,
    private readonly customerRepository: ICustomerRepository,
    private readonly userClassesRepository: IUserClassesRepository,
    private readonly accessLevelRepository: IAccessLevelsRepository,
    private readonly classesRepository: IClassesRepository,
    private readonly sesService: SESService,
    private readonly s3Service: S3Service,
    private readonly termsRepository: ITermsRepository,
    private readonly filesRepository: IFilesRepository,
    private readonly recoveryRepository: IRecoveryRepository
  ) {}

  async execute({ courseId, customerId, emails, classId }: AddStudentsByEmailDTO) {
    const trx = await generateTransaction();
    try {
      const course = await this.courseRepository.findOneBy({ id: courseId });
      const customer = await this.customerRepository.findOneBy({ id: customerId });

      if (!course) throw new GenericError('Curso não encontrado.');
      if (!customer) throw new GenericError('Cliente não encontrado.');

      const courseName = course.name || 'Curso';
      const courseImageUrl = course.photo_url;
      const company = customer.name || 'Nossa plataforma';
      const year = new Date().getFullYear();
      const primaryColor = customer.primary_color || '#C1086C';

      const normalizedEmails = emails.map((email) => email.toLowerCase().trim());

      const existingUsers = await this.usersRepository.findManyByEmailsAndCustomerId(
        normalizedEmails,
        customerId
      );
      const existingEmailsSet = new Set(existingUsers.map((user) => user.email.toLowerCase()));

      let alreadyLinkedSet: Set<string> = new Set();
      if (classId && existingUsers.length > 0) {
        const alreadyLinked = await this.userClassesRepository.findByUserIdsAndClassId(
          existingUsers.map((user) => user.id),
          classId
        );
        alreadyLinkedSet = new Set(alreadyLinked.map((uc) => uc.user_id));

        const toRemove = existingUsers
          .map((user) => user.id)
          .filter((id) => !alreadyLinkedSet.has(id));
        if (toRemove.length > 0) {
          await this.userClassesRepository.softDeleteByUserIdsAndCourseId(toRemove, courseId, trx);
        }
      }

      const newEmails = normalizedEmails.filter((email) => !existingEmailsSet.has(email));

      const accessLevels = await this.accessLevelRepository.findByRole(RolesEnum.STUDENT);

      if (!accessLevels) throw new GenericError('Nível de acesso não encontrado.');

      let createdUsers: IUser[] = [];
      const recoveryTokens = new Map<string, string>();

      if (newEmails.length > 0) {
        createdUsers = await this.usersRepository.createUserFromEmails(
          newEmails.map((email) => ({
            email,
            customer_id: customerId,
            access_level_id: accessLevels.id,
          })),
          trx
        );

        const recoveriesToInsert = createdUsers.map((user) => {
          const recoveryToken = randomUUID();
          recoveryTokens.set(user.id, recoveryToken);
          return {
            id: randomUUID(),
            user_id: user.id,
            token: recoveryToken,
            expires_at: null,
          };
        });
        if (recoveriesToInsert.length > 0) {
          await this.recoveryRepository.createManyRecoveries(recoveriesToInsert, trx);
        }
      }

      const allUsers = [...createdUsers, ...existingUsers];

      if (classId && allUsers.length > 0) {
        const now = new Date();

        const userIds = allUsers.map((u) => u.id);
        const alreadyLinked = await this.userClassesRepository.findByUserIdsAndClassId(
          userIds,
          classId
        );
        alreadyLinkedSet = new Set(alreadyLinked.map((uc) => uc.user_id));

        const userClassesToInsert = allUsers
          .filter((user) => !alreadyLinkedSet.has(user.id))
          .map((user) => ({
            user_id: user.id,
            class_id: classId,
            status: 'active',
            apply_date: now,
          }));

        if (userClassesToInsert.length > 0) {
          await this.userClassesRepository.insertAllWithTrx(userClassesToInsert, trx);
        }
      }

      let studentsThatChangedClass: IUser[] = [];
      if (classId && existingUsers.length > 0) {
        const userIds = existingUsers.map((u) => u.id);

        const oldUserClasses = await this.userClassesRepository.findByUserIdsAndClassIds(
          userIds,
          courseId
        );

        const studentsThatChangedClassIds = new Set(
          oldUserClasses.filter((v) => v.class_id !== classId).map((v) => v.user_id)
        );
        studentsThatChangedClass = existingUsers.filter((u) =>
          studentsThatChangedClassIds.has(u.id)
        );
      }

      const changedStudentIds = new Set(studentsThatChangedClass.map((u) => u.id));
      const existingUsersToNotify = existingUsers.filter(
        (user) => !alreadyLinkedSet.has(user.id) && !changedStudentIds.has(user.id)
      );

      let termsUrl = '#';
      try {
        const activeTerm = await this.termsRepository.findOneBy({
          customer_id: customerId,
          active: true,
        });
        if (activeTerm && activeTerm.file_id) {
          const file = await this.filesRepository.findOneBy({ id: activeTerm.file_id });
          if (file) {
            termsUrl = await this.s3Service.getSignedUrl(env.BUCKET_FILES, file.url, 3600);
          }
        }
      } catch (error) {
        console.error('Erro ao buscar termos de uso:', error);
      }

      let termsLinks = '';
      if (termsUrl && termsUrl !== '#') {
        termsLinks = `<a href="${termsUrl}" style="color:#2F80ED; text-decoration:underline; margin:0 8px; font-size:11px; font-family:Inter,sans-serif; font-style:normal; font-weight:500; line-height:155%; letter-spacing:0.22px;">Termos de uso</a>`;
      }

      let supportLink = '';
      if (customer.support_email || customer.email) {
        supportLink = `<a href="mailto:${customer.support_email || customer.email}" style="color:#2F80ED; text-decoration:underline; margin:0 8px; font-size:11px; font-family:Inter,sans-serif; font-style:normal; font-weight:500; line-height:155%; letter-spacing:0.22px;">Suporte</a>`;
      }

      for (const user of existingUsersToNotify) {
        try {
          await this.sendEmailForExistingUser(user.first_name || user.email, {
            email: user.email,
            courseName,
            company,
            courseImageUrl,
            year,
            subdomain: customer.subdomain,
            primaryColor,
            termsLinks,
            supportLink,
          });
        } catch (err) {
          await trx.rollback();
          console.error('Erro ao enviar e-mail para existente:', user.email, err);
          throw new GenericError(
            'Houve um erro ao adicionar usuários. Por favor, tente novamente.'
          );
        }
      }

      for (const user of createdUsers) {
        try {
          const recoveryToken = recoveryTokens.get(user.id) || user.id;

          await this.sendEmailForNewUser(user.email, recoveryToken, {
            company,
            year,
            subdomain: customer.subdomain,
            primaryColor,
            termsLinks,
            supportLink,
          });
        } catch (err) {
          await trx.rollback();
          console.error('Erro ao enviar e-mail para novo:', user.email, err);
          throw new GenericError(
            'Houve um erro ao adicionar usuários. Por favor, tente novamente.'
          );
        }
      }

      for (const user of studentsThatChangedClass) {
        try {
          const classObj = await this.classesRepository.findOneBy({ id: classId });
          await this.sendChangeClassEmail(user, {
            student: user.first_name || user.email,
            course: courseName,
            newClass: classObj?.name || '',
            company,
            year: year.toString(),
            termsLinks,
            supportLink,
            primaryColor,
          });
        } catch (err) {
          await trx.rollback();
          console.error('Erro ao enviar e-mail de mudança de turma:', user.email, err);
          throw new GenericError(
            'Houve um erro ao adicionar usuários. Por favor, tente novamente.'
          );
        }
      }

      await trx.commit();

      return {
        totalAdded: normalizedEmails.length,
        totalExisting: existingUsers.length,
        totalNew: createdUsers.length,
      };
    } catch (error) {
      await trx.rollback();
      console.error('Erro ao adicionar alunos por e-mail:', error);
      throw error;
    }
  }

  private async sendEmailForExistingUser(
    userName: string,
    {
      email,
      courseName,
      company,
      courseImageUrl,
      year,
      subdomain,
      primaryColor,
      termsLinks,
      supportLink,
    }: {
      email: string;
      courseName: string;
      company: string;
      courseImageUrl?: string;
      year: number;
      subdomain?: string;
      primaryColor: string;
      termsLinks: string;
      supportLink: string;
    }
  ) {
    const buttonUrl = `https://${subdomain}/home`;

    let signedCourseImageUrl = '';
    if (courseImageUrl) {
      signedCourseImageUrl = await this.s3Service.getSignedUrl(
        env.BUCKET_FILES || 'propofando-lxp-dev',
        courseImageUrl
      );
    }

    const html = await this.loadAndRenderTemplate(
      'addUserToCourse.template.html',
      {
        userName,
        courseName,
        company,
        buttonUrl,
        courseImageUrl: signedCourseImageUrl,
        year: year.toString(),
        termsLinks,
        supportLink,
      },
      primaryColor
    );

    await this.sesService.sendEmail({
      to: email,
      // from: `no-reply@${subdomain?.replace(/^www\./, '')}`,
      subject: `Você foi adicionado(a) ao curso ${courseName}`,
      body: html,
      isHtml: true,
    });
  }

  private async sendEmailForNewUser(
    email: string,
    recoveryToken: string,
    {
      company,
      year,
      subdomain,
      primaryColor,
      termsLinks,
      supportLink,
    }: {
      company: string;
      year: number;
      subdomain?: string;
      primaryColor: string;
      termsLinks: string;
      supportLink: string;
    }
  ) {
    const link = `https://${subdomain}/welcome/${recoveryToken}`;

    const html = await this.loadAndRenderTemplate(
      'createStudent.template.html',
      {
        userEmail: email,
        company,
        year: year.toString(),
        link,
        termsLinks,
        supportLink,
      },
      primaryColor
    );

    await this.sesService.sendEmail({
      to: email,
      // from: `no-reply@${subdomain?.replace(/^www\./, '')}`,
      subject: 'Bem-vindo(a)! Complete seu cadastro para começar 🎉',
      body: html,
      isHtml: true,
    });
  }

  private async loadAndRenderTemplate(
    fileName: string,
    variables: Record<string, string>,
    primaryColor: string
  ): Promise<string> {
    const templatePath = path.resolve(__dirname, '../../templates/email', fileName);
    let html = await fs.readFile(templatePath, 'utf-8');

    for (const [key, value] of Object.entries(variables)) {
      html = html.replace(new RegExp(`{{\\s*${key}\\s*}}`, 'g'), value);
    }

    return html.replaceAll('#C1086C', primaryColor);
  }

  private async sendChangeClassEmail(
    user: IUser,
    {
      student,
      course,
      newClass,
      company,
      year,
      termsLinks,
      supportLink,
      primaryColor,
    }: {
      student: string;
      course: string;
      newClass: string;
      company: string;
      year: string;
      termsLinks: string;
      supportLink: string;
      primaryColor: string;
    }
  ) {
    const html = await this.loadAndRenderTemplate(
      'changeClass.template.html',
      {
        student,
        course,
        newClass,
        company,
        year,
        termsLinks,
        supportLink,
      },
      primaryColor
    );
    await this.sesService.sendEmail({
      to: user.email,
      subject: `Você foi trocado de turma: ${newClass}`,
      body: html,
      isHtml: true,
    });
  }
}
