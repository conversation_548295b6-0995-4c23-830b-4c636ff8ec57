import { ListStudentsResponseDto } from '../../model/DTO/response/user.dto';
import { RolesEnum } from '../../model/enums/roles.enum';
import { IAccessLevelsRepository } from '../../repositories/assign/accessLevels.assign';
import { IUsersRepository } from '../../repositories/assign/users.assign';
import { filterTextRegex } from '../../services/filterTextRegex.service';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

interface ListStudentsUseCaseRequest {
  customerId: string;
  orderByColumn?: string;
  orderByDirection?: 'asc' | 'desc';
  courseId?: string;
}

export class ListStudentsUseCase {
  constructor(
    private readonly usersRepository: IUsersRepository,
    private readonly accessLevelRepository: IAccessLevelsRepository
  ) {}

  async execute({
    customerId,
    orderByColumn,
    orderByDirection,
    courseId,
  }: ListStudentsUseCaseRequest): Promise<ListStudentsResponseDto[]> {
    const accessLevelStudent = await this.accessLevelRepository.findByRole(RolesEnum.STUDENT);

    if (!accessLevelStudent) {
      throw new ResourceNotFoundError('Nível de acesso não encontrado');
    }

    const students = await this.usersRepository.findUsersStudents({
      customerId,
      orderByColumn,
      orderByDirection,
      courseId,
      accessLevelId: accessLevelStudent.id,
    });

    students.sort((a, b) =>
      filterTextRegex(a.firstName || '').localeCompare(filterTextRegex(b.firstName || ''), 'pt-BR')
    );

    return students;
  }
}
