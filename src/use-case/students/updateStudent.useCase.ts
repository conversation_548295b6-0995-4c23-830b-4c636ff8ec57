import { <PERSON><PERSON> } from 'knex';

import { generateTransaction } from '../../helpers/transaction.helper';
import { UpdateStudentResponseDto } from '../../model/DTO/response/user.dto';
import { IUser } from '../../model/IUser';
import { IClassesRepository } from '../../repositories/assign/classes.assign';
import { IUserClassesRepository } from '../../repositories/assign/userClasses.assign';
import { IUsersRepository } from '../../repositories/assign/users.assign';
import { UpdateStudentBodySchema } from '../../schema/users.schema';
import { GenericError } from '../errors/GenericError';
import { ResourceAlreadyExistsError } from '../errors/ResourceAlreadyExistsError';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

export class UpdateStudentUseCase {
  constructor(
    private readonly userRepository: IUsersRepository,
    private readonly userClassesRepository: IUserClassesRepository,
    private readonly classRepository: IClassesRepository
  ) {}

  async execute(userId: string, data: UpdateStudentBodySchema): Promise<UpdateStudentResponseDto> {
    const trx = await generateTransaction();
    try {
      const existingUser = await this.userRepository.findOneBy({ id: userId });
      if (!existingUser) throw new ResourceNotFoundError('Aluno não encontrado');

      await this.checkEmailOrCpfConflict(existingUser, data);

      let classIdToReturn: string | null = null;

      if (data.classId != null) {
        const existingClass = await this.classRepository.findOneBy({ id: data.classId });
        if (!existingClass) throw new ResourceNotFoundError('Turma não encontrada');

        await this.handleUserClassLink(userId, data.classId, existingClass.course_id!, trx);
        classIdToReturn = data.classId;
      }

      const updatedUser = await this.updateUser(userId, existingUser, data, trx);

      if (!updatedUser || !updatedUser.id) {
        throw new GenericError('Erro ao atualizar aluno');
      }

      await trx.commit();

      const response = this.buildResponse(updatedUser, classIdToReturn);

      return response;
    } catch (error) {
      await trx.rollback().catch(console.error);
      console.error('❌ Erro em ao atualizar aluno:', error);
      throw error instanceof Error ? error : new GenericError('Erro ao atualizar aluno');
    }
  }

  private async checkEmailOrCpfConflict(
    existingUser: IUser,
    data: UpdateStudentBodySchema
  ): Promise<void> {
    const emailToCheck = data.email ?? existingUser.email;
    const cpfToCheck = data.cpf ?? existingUser.cpf;

    if (!emailToCheck && !cpfToCheck) return;

    const conflictingUser = await this.userRepository.findUserByEmailOrCpfAndCustomerId(
      emailToCheck ?? '',
      cpfToCheck ?? '',
      existingUser.customer_id,
      existingUser.id
    );

    if (conflictingUser && conflictingUser.id !== existingUser.id) {
      throw new ResourceAlreadyExistsError(
        'Já existe outro usuário com este e-mail ou CPF para este cliente.'
      );
    }
  }

  private async handleUserClassLink(
    userId: string,
    classId: string,
    courseId: string,
    trx: Knex.Transaction
  ): Promise<void> {
    const existingLink = await this.userClassesRepository.findByUserIdAndCourseIdExcludingClass(
      userId,
      courseId,
      classId
    );

    if (existingLink) {
      await this.userClassesRepository.update(existingLink, trx);
      return;
    }

    const exactClass = await this.userClassesRepository.findOneBy({
      user_id: userId,
      class_id: classId,
    });

    if (!exactClass) {
      const now = new Date();
      await this.userClassesRepository.insertWithTrx(
        {
          user_id: userId,
          class_id: classId,
          apply_date: now,
          status: 'active',
        },
        trx
      );
    }
  }

  private async updateUser(
    userId: string,
    existingUser: IUser,
    userFields: UpdateStudentBodySchema,
    trx: Knex.Transaction
  ): Promise<IUser> {
    const userUpdatePayload = {
      id: existingUser.id,
      first_name: userFields.firstName ?? existingUser.first_name,
      last_name: userFields.lastName ?? existingUser.last_name,
      email: userFields.email ?? existingUser.email,
      birth_date: userFields.birthDate ?? existingUser.birth_date,
      gender: userFields.gender ?? existingUser.gender,
      phone_number: userFields.phoneNumber ?? existingUser.phone_number,
      cpf: userFields.cpf ?? existingUser.cpf,
    };

    const result = await this.userRepository.updateWithTrx(userUpdatePayload, trx);
    let updatedUser = Array.isArray(result) ? result[0] : result;

    if (!updatedUser) {
      updatedUser = await this.userRepository.findOneBy({ id: userId });
    }

    return updatedUser;
  }

  private buildResponse(user: IUser, classId: string | null) {
    return {
      id: user.id,
      firstName: user.first_name,
      lastName: user.last_name,
      email: user.email,
      birthDate: user.birth_date,
      gender: user.gender,
      phoneNumber: user.phone_number,
      class_id: classId,
      cpf: user.cpf,
    };
  }
}
