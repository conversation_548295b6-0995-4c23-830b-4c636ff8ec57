import { ICategoriesRepository } from '../../../repositories/assign/categories.assign';
import { ISubCategoriesRepository } from '../../../repositories/assign/subcategories.assign';
import { ResourceAlreadyExistsError } from '../../errors/ResourceAlreadyExistsError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface IAddSubcategoryRequest {
  categoryId: string;
  name: string;
  customerId: string;
}

export class AddSubcategoryUseCase {
  constructor(
    private subcategoriesRepository: ISubCategoriesRepository,
    private categoriesRepository: ICategoriesRepository
  ) {}

  async execute(inputData: IAddSubcategoryRequest): Promise<object> {
    const { categoryId, name, customerId } = inputData;

    await this.validateCategory(inputData);

    await this.ensureUniqueSubcategory(inputData);

    const subcategory = await this.subcategoriesRepository.insert({
      name,
      category_id: categoryId,
      customer_id: customerId,
    });

    return {
      subcategory,
    };
  }

  private async validateCategory(inputData: IAddSubcategoryRequest): Promise<void> {
    const { categoryId, customerId } = inputData;

    const category = await this.categoriesRepository.get(categoryId);

    if (!category || category.customer_id !== customerId) {
      throw new ResourceNotFoundError('Categoria nao encontrada');
    }
  }

  private async ensureUniqueSubcategory(inputData: IAddSubcategoryRequest): Promise<void> {
    const { name, categoryId, customerId } = inputData;

    const existingSubcategory = await this.subcategoriesRepository.getSubcategoryCustomer({
      name,
      categoryId,
      customerId,
    });

    if (existingSubcategory) {
      throw new ResourceAlreadyExistsError(
        'Já existe uma subcategoria com esse nome nesta categoria.'
      );
    }

    return existingSubcategory;
  }
}
