import { ISubCategory } from '../../../model/ISubcategory';
import { IDisciplinesSubcategoriesAccessRepository } from '../../../repositories/assign/disciplinesSubcategoriesAccess.assign';
import { IQuestionsRepository } from '../../../repositories/assign/questions.assign';
import { ISubCategoriesRepository } from '../../../repositories/assign/subcategories.assign';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

export interface IDeleteSubcategoryRequest {
  id: string;
  customerId: string;
}

interface IDeleteSubcategoryResponse {
  deletedSubcategory: object;
  totalAccessesRemoved: number;
  removedAccessRecords: object[];
}

export class DeleteSubcategoryUseCase {
  constructor(
    private readonly subcategoriesRepository: ISubCategoriesRepository,
    private readonly disciplineSubcategoryAccessRepository: IDisciplinesSubcategoriesAccessRepository,
    private readonly questionsRepository: IQuestionsRepository
  ) {}

  public async execute(request: IDeleteSubcategoryRequest): Promise<IDeleteSubcategoryResponse> {
    const { id } = request;

    await this.validateSubcategoryExists(request);

    await this.ensureNoLinkedQuestions(request);

    const removedAccessRecords = await this.deleteSubcategoryAccess(request);

    const deletedSubcategory = await this.subcategoriesRepository.deleteById(id);

    return {
      deletedSubcategory,
      totalAccessesRemoved: removedAccessRecords.length,
      removedAccessRecords,
    };
  }

  private async validateSubcategoryExists(
    request: IDeleteSubcategoryRequest
  ): Promise<ISubCategory> {
    const { id: subcategoryId, customerId } = request;

    const subCategory = await this.subcategoriesRepository.get(subcategoryId);

    if (!subCategory || subCategory.customer_id !== customerId || subCategory.deleted_at) {
      throw new ResourceNotFoundError('Subcategoria não encontrada.');
    }

    return subCategory;
  }

  private async ensureNoLinkedQuestions(request: IDeleteSubcategoryRequest): Promise<void> {
    const { id, customerId } = request;

    const questions = await this.questionsRepository.findBySubcategoryId({
      subcategoryId: id,
      customerId,
    });

    if (questions?.length) {
      throw new BadRequestError(
        `Esta subcategoria não pode ser excluída porque possui questões vinculadas. Remova ou mova as questões antes de tentar novamente.`
      );
    }
  }

  private async deleteSubcategoryAccess(reuqest: IDeleteSubcategoryRequest): Promise<object[]> {
    const { id: subcategoryId, customerId } = reuqest;

    const subcategoryAccess = await this.disciplineSubcategoryAccessRepository.getBysubcategoryId(
      subcategoryId,
      customerId
    );

    if (subcategoryAccess.length > 0) {
      await this.disciplineSubcategoryAccessRepository.deleteAll(subcategoryId, customerId);
    }

    return subcategoryAccess;
  }
}
