import { ICategoriesRepository } from '../../../repositories/assign/categories.assign';
import { IDisciplinesRepository } from '../../../repositories/assign/disciplines.assign';
import { IDisciplinesCategoriesAccessRepository } from '../../../repositories/assign/disciplinesCategoriesAccess.assign';
import { IDisciplinesSubcategoriesAccessRepository } from '../../../repositories/assign/disciplinesSubcategoriesAccess.assign';
import { ISubCategoriesRepository } from '../../../repositories/assign/subcategories.assign';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface IManageSubcategoryInDisciplineUseCaseRequest {
  disciplineId: string;
  categoryId: string;
  subcategoryId: string;
  customerId: string;
  isChecked: boolean;
}

export class ManageSubcategoryInDisciplineUseCase {
  constructor(
    private readonly disciplinesRepository: IDisciplinesRepository,
    private readonly categoriesRepository: ICategoriesRepository,
    private readonly disciplineCategoryAccessRepository: IDisciplinesCategoriesAccessRepository,
    private readonly disciplinesSubcategoriesAccessRepository: IDisciplinesSubcategoriesAccessRepository,
    private readonly subCategoriesRepository: ISubCategoriesRepository
  ) {}

  async execute(request: IManageSubcategoryInDisciplineUseCaseRequest): Promise<object> {
    const { isChecked } = request;

    await this.validateRequest(request);

    await this.ensureCategoryAccessExists(request);

    await this.updateSubcategoryAccess(request);

    return {
      message: isChecked
        ? `Subcategoria vinculada com sucesso à disciplina.`
        : `Subcategoria desvinculada com sucesso da disciplina.`,
    };
  }

  private async validateRequest(
    request: IManageSubcategoryInDisciplineUseCaseRequest
  ): Promise<void> {
    const { categoryId, disciplineId, customerId, subcategoryId } = request;

    const [discipline, category] = await Promise.all([
      this.disciplinesRepository.findById(disciplineId),
      this.categoriesRepository.get(categoryId),
    ]);

    if (!discipline) throw new ResourceNotFoundError('Disciplina não encontrada.');
    if (!category) throw new ResourceNotFoundError('Categoria não encontrada.');

    await this.validateSubcategoryExistence(subcategoryId, customerId, categoryId);
  }

  private async validateSubcategoryExistence(
    subcategoryId: string,
    customerId: string,
    categoryId: string
  ): Promise<void> {
    const subcategories = await this.subCategoriesRepository.findAllByCustomerId(
      customerId,
      categoryId
    );

    const existsSubcategory = subcategories.some((sub) => sub.id === subcategoryId);

    if (!existsSubcategory) throw new ResourceNotFoundError('Subcategoria não encontrada.');
  }

  private async ensureCategoryAccessExists(
    request: IManageSubcategoryInDisciplineUseCaseRequest
  ): Promise<void> {
    const { disciplineId, categoryId, customerId } = request;

    const existingCategoryAccess =
      await this.disciplineCategoryAccessRepository.findByDisciplineIdAndCategoryId({
        disciplineId,
        categoryId,
        customerId,
      });

    if (!existingCategoryAccess) {
      await this.disciplineCategoryAccessRepository.insert({
        discipline_id: disciplineId,
        category_id: categoryId,
        customer_id: customerId,
      });
    }
  }

  private async updateSubcategoryAccess(
    request: IManageSubcategoryInDisciplineUseCaseRequest
  ): Promise<void> {
    const { disciplineId, categoryId, customerId, isChecked, subcategoryId } = request;

    const existingSubcategoryAccess =
      await this.disciplinesSubcategoriesAccessRepository.findByDisciplineIdAndSubcategoryId({
        disciplineId,
        subcategoryId,
        customerId,
      });

    if (isChecked && !existingSubcategoryAccess) {
      await this.disciplinesSubcategoriesAccessRepository.insert({
        discipline_id: disciplineId,
        subcategory_id: subcategoryId,
        customer_id: customerId,
        category_id: categoryId,
      });
    }

    if (!isChecked && existingSubcategoryAccess) {
      await this.disciplinesSubcategoriesAccessRepository.deleteByDisciplineIdAndSubcategoryId({
        disciplineId,
        subcategoryId,
        customerId,
      });

      const remainingSubcategories =
        await this.disciplinesSubcategoriesAccessRepository.findAllByDisciplineId(
          disciplineId,
          customerId
        );

      const remainingSubcategoriesCount = remainingSubcategories.filter(
        (sub) => sub.category_id === categoryId
      ).length;

      if (remainingSubcategoriesCount === 0) {
        await this.disciplineCategoryAccessRepository.deleteByDisciplineIdAndCategoryId({
          disciplineId,
          categoryId,
          customerId,
        });
      }
    }
  }
}
