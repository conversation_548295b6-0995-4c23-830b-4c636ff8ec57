import { ISubCategoriesRepository } from '../../../repositories/assign/subcategories.assign';
import { ResourceAlreadyExistsError } from '../../errors/ResourceAlreadyExistsError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

export interface IUpdateSubcategoryRequest {
  name: string;
  id: string;
  customerId: string;
  categoryId: string;
}

export class UpdateSubcategoryUseCase {
  constructor(private readonly subcategoriesRepository: ISubCategoriesRepository) {}

  public async execute(request: IUpdateSubcategoryRequest): Promise<object> {
    const { name, id } = request;

    await this.validateSubcategoryExists(request);

    await this.ensureUniqueSubcategory(request);

    const updatedSubcategory = await this.subcategoriesRepository.update({
      id,
      name: name.trim(),
    });

    return {
      updatedSubcategory,
    };
  }

  private async validateSubcategoryExists(request: IUpdateSubcategoryRequest): Promise<object> {
    const { id } = request;

    const subCategory = await this.subcategoriesRepository.get(id);

    if (!subCategory) {
      throw new ResourceNotFoundError('Subcategoria não encontrada');
    }
    return subCategory;
  }

  private async ensureUniqueSubcategory(request: IUpdateSubcategoryRequest): Promise<object> {
    const { name, customerId, categoryId } = request;

    const existingSubcategory = await this.subcategoriesRepository.getSubcategoryCustomer({
      name: name.trim(),
      categoryId,
      customerId,
    });

    if (existingSubcategory && existingSubcategory.id !== request.id) {
      throw new ResourceAlreadyExistsError(
        `Já existe uma subcategoria chamada ${name} nesta categoria.`
      );
    }

    return { existingSubcategory };
  }
}
