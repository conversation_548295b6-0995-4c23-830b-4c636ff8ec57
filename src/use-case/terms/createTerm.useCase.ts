import { randomUUID } from 'crypto';
import { Knex } from 'knex';

import { env } from '../../env';
import { generateTransaction } from '../../helpers/transaction.helper';
import { IFile } from '../../model/IFile';
import { IFilesRepository } from '../../repositories/assign/files.assign';
import { ITermsRepository } from '../../repositories/assign/terms.assign';
import { UploadService } from '../../services/uploadFile.service';
import { GenericError } from '../errors/GenericError';

export class CreateTermUseCase {
  constructor(
    private termsRepository: ITermsRepository,
    private filesRepository: IFilesRepository,
    private uploadService: UploadService
  ) {}

  async execute(data: { file: Express.Multer.File; customerId: string }) {
    const trx = await generateTransaction();
    const { file, customerId } = data;

    await this.deactivateExistingTerm(customerId, trx);

    const storagePath = await this.uploadTermFile(file, customerId);

    const fileRecord = await this.createFileRecord(file, storagePath, trx);

    if (!fileRecord) {
      await trx.rollback();
      throw new GenericError('Erro ao criar termo');
    }

    await trx.commit();

    return this.termsRepository.createTerm({
      name: file.originalname,
      active: true,
      file_id: fileRecord.id,
      customer_id: customerId,
    });
  }

  private async deactivateExistingTerm(customerId: string, trx: Knex.Transaction) {
    const termExists = await this.termsRepository.findOneBy({
      active: true,
      customer_id: customerId,
    });

    if (termExists) {
      const termUpdated = await this.termsRepository.updateTerm(
        { id: termExists.id, active: false },
        trx
      );

      if (!termUpdated) {
        await trx.rollback();
        throw new GenericError('Erro ao criar termo');
      }
    }
  }

  private async uploadTermFile(file: Express.Multer.File, customerId: string): Promise<string> {
    const storagePath = `${customerId}/terms/${file.originalname}`;

    await this.uploadService.uploadFile({
      file,
      storagePath,
      bucket: env.BUCKET_FILES,
    });

    return storagePath;
  }

  private async createFileRecord(
    file: Express.Multer.File,
    storagePath: string,
    trx: Knex.Transaction
  ): Promise<IFile> {
    const fileType = file.mimetype.split('/')[1];

    return this.filesRepository.insert(
      {
        id: randomUUID(),
        file_name: file.originalname,
        file_type: fileType,
        file_size: file.size,
        url: storagePath,
        user_id: undefined,
      },
      trx
    );
  }
}
