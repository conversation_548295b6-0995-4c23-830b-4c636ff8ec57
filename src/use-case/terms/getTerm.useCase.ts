import { env } from '../../env';
import { ITerm } from '../../model/ITerm';
import { IFilesRepository } from '../../repositories/assign/files.assign';
import { ITermsRepository } from '../../repositories/assign/terms.assign';
import S3Service from '../../services/aws/s3/S3Service';

export class GetTermUseCase {
  constructor(
    private termsRepository: ITermsRepository,
    private filesRepository: IFilesRepository,
    private s3Service: S3Service
  ) {}

  async execute(id: string, customerId: string): Promise<{ term: ITerm; signedUrl?: string }> {
    const term = await this.termsRepository.findOneBy({ id, customer_id: customerId });

    if (!term) throw new Error('Termo não encontrado');

    let signedUrl: string | undefined;
    if (term.file_id) {
      const file = await this.filesRepository.findOneBy({ id: term.file_id });

      if (file) {
        const bucket = env.BUCKET_FILES;
        signedUrl = await this.s3Service.getSignedUrl(bucket, file.url, 3600);
      }
    }

    return { term, signedUrl };
  }
}
