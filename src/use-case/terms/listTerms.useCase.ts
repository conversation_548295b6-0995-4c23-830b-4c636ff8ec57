import { ITerm } from '../../model/ITerm';
import { ITermsRepository } from '../../repositories/assign/terms.assign';
import { FilterListTermsInput } from '../../schema/terms.schema';

export class ListAllTermsUseCase {
  constructor(private termsRepository: ITermsRepository) {}

  async execute(customerId: string, filter: FilterListTermsInput): Promise<ITerm[]> {
    return this.termsRepository.listAllTerms(customerId, filter);
  }
}
