import { <PERSON><PERSON> } from 'knex';

import { generateTransaction } from '../../helpers/transaction.helper';
import { ITermsRepository } from '../../repositories/assign/terms.assign';
import { UpdateTermInput } from '../../schema/terms.schema';
import { GenericError } from '../errors/GenericError';

export class UpdateTermUseCase {
  constructor(private termsRepository: ITermsRepository) {}

  async execute(data: UpdateTermInput & { id: string; customerId: string }) {
    const trx = await generateTransaction();

    await this.deactivateOtherActiveTerms(data.customerId, data, trx);

    const { customerId, ...rest } = data;
    const dataToUpdate = {
      ...rest,
      customer_id: customerId,
    };

    const termUpdated = await this.termsRepository.updateTerm(dataToUpdate, trx);

    if (!termUpdated) {
      await trx.rollback();
      throw new GenericError('Erro ao atualizar termo');
    }

    await trx.commit();

    return termUpdated;
  }

  private async deactivateOtherActiveTerms(
    customerId: string,
    data: UpdateTermInput & { id: string },
    trx: Knex.Transaction
  ) {
    const termExists = await this.termsRepository.findOneBy({
      active: true,
      customer_id: customerId,
    });

    if (termExists && termExists.id !== data.id) {
      await this.termsRepository.updateTerm({ id: termExists.id, active: false }, trx);
    }
  }
}
