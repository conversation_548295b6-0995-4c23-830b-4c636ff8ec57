import bcrypt from 'bcryptjs';

import { env } from '../../../env';
import { ICreateUserDTO } from '../../../model/DTO/ICreateUser.dto';
import { IUser } from '../../../model/IUser';
import { ICustomerRepository } from '../../../repositories/assign/customers.assign';
import { IUsersRepository } from '../../../repositories/assign/users.assign';
import { IUsersTypesRepository } from '../../../repositories/assign/usersTypes.assign';
import { BadRequestError } from '../../errors/BadRequestError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

export class CreateInitialUserUseCase {
  constructor(
    private usersRepository: IUsersRepository,
    private customersRepository: ICustomerRepository,
    private usersTypesRepository: IUsersTypesRepository
  ) {}

  async execute(data: ICreateUserDTO): Promise<{ message: string; user?: IUser }> {
    const customer = await this.customersRepository.getTaxNumber(env.FIRST_CUSTOMER_TAX_NUMBER);

    if (!customer) {
      throw new ResourceNotFoundError('Cliente inicial não encontrado.');
    }

    const existingUser = await this.usersRepository.getUsersByEmail(data.email, customer.id);

    if (existingUser) {
      throw new BadRequestError('Já existe um usuário cadastrado com este email.');
    }

    const userType = await this.usersTypesRepository.getByName('Anestesista');

    if (!userType) {
      throw new ResourceNotFoundError('Tipo de usuário "Anestesista" não encontrado.');
    }

    const hashedPassword = await bcrypt.hash(data.password, 8);

    const firstUser = {
      first_name: data.first_name,
      last_name: data.last_name,
      cpf: data.cpf,
      email: data.email,
      password: hashedPassword,
      active: true,
      terms_accept: data.terms_accept ?? false,
      user_type_id: userType.id,
      customer_id: customer.id,
      role: 'STUDENT',
    };

    await this.usersRepository.insert(firstUser);

    return {
      message: `Usuário ${firstUser.first_name} ${firstUser.last_name} criado com sucesso.`,
    };
  }
}
