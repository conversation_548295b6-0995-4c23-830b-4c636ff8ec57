import { IAddUserToClassDTO } from '../../../model/DTO/IUserClasses.dto';
import { UserPublicDTO } from '../../../model/DTO/IUsers.dto';
import { StatusUserEnum } from '../../../model/enums/status.enum';
import { ICustomer } from '../../../model/ICustomer';
import { IClassesRepository } from '../../../repositories/assign/classes.assign';
import { ICustomerRepository } from '../../../repositories/assign/customers.assign';
import { IUserClassesRepository } from '../../../repositories/assign/userClasses.assign';
import { IUsersRepository } from '../../../repositories/assign/users.assign';
import { CreateExternalUserDTO } from '../../../schema/users.schema';
import { ResourceAlreadyExistsError } from '../../errors/ResourceAlreadyExistsError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

type CreateUserPayload = {
  externalUserId: string;
  email: string;
  firstName: string;
  lastName: string;
  createdBy: string;
  customerId: string;
  role: string;
};

export class CreateExternalUserUseCase {
  constructor(
    private readonly usersRepository: IUsersRepository,
    private readonly customersRepository: ICustomerRepository,
    private readonly userClassRepository: IUserClassesRepository,
    private readonly classesRepository: IClassesRepository
  ) {}

  async execute(data: CreateExternalUserDTO): Promise<UserPublicDTO> {
    const {
      id,
      customerId,
      firstName,
      lastName,
      email,
      createdBy = 'LMS',
      classIds,
      status = StatusUserEnum.ACTIVE,
      role,
    } = data;

    const customer = await this.validateCustomer(customerId);

    await this.validateUniqueUser(email, customerId);

    let validatedClassIds: string[] = [];

    if (classIds && classIds.length > 0) {
      validatedClassIds = await this.getValidatedClassIds(classIds, customer.id);
    }

    const user = await this.usersRepository.createUserFromExternalSystem(
      this.buildCreateUserPayload({
        externalUserId: id,
        email,
        firstName,
        lastName,
        createdBy,
        customerId: customer.id,
        role,
      })
    );

    if (validatedClassIds.length > 0) {
      await this.associateUserToClasses(user.id!, validatedClassIds, status);
    }

    return user;
  }

  private async getValidatedClassIds(
    externalClassIds: string[],
    customerId: string
  ): Promise<string[]> {
    const classes = await this.classesRepository.findByExternalClassIds({
      externalClassIds,
      customerId,
    });

    if (classes.length !== externalClassIds.length) {
      const foundExternalIds = new Set(classes.map((c) => c.external_class_id));
      const missingExternalIds = externalClassIds.filter((id) => !foundExternalIds.has(id));

      if (missingExternalIds.length > 0) {
        throw new ResourceNotFoundError(`Turmas não encontradas: ${missingExternalIds.join(', ')}`);
      }
    }

    return classes.map((c) => c.id);
  }

  private async validateCustomer(customerId: string): Promise<ICustomer> {
    const customer = await this.customersRepository.findByExternalId(customerId);

    if (!customer) {
      throw new ResourceNotFoundError('Cliente não encontrado.');
    }

    return customer;
  }

  private async validateUniqueUser(email: string, externalCustomerId: string) {
    const existingUser = await this.usersRepository.findByEmailAndExternalCustomerId({
      email,
      externalCustomerId,
    });

    if (existingUser) {
      throw new ResourceAlreadyExistsError('Não foi possível concluir o cadastro.');
    }
  }

  private buildCreateUserPayload(data: CreateUserPayload) {
    return {
      external_user_id: data.externalUserId,
      email: data.email,
      first_name: data.firstName,
      last_name: data.lastName,
      created_by: data.createdBy,
      customer_id: data.customerId,
      role: data.role,
    };
  }

  private async associateUserToClasses(userId: string, classIds: string[], status: string) {
    const insertData: IAddUserToClassDTO[] = classIds.map((classId) => ({
      user_id: userId,
      class_id: classId,
      status,
      apply_date: new Date(),
    }));

    await this.userClassRepository.insertAll(insertData);
  }
}
