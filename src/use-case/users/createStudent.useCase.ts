import bcrypt from 'bcryptjs';
import { randomUUID } from 'crypto';
import { promises as fs } from 'fs';
import path from 'path';

import { env } from '../../env';
import { generateTransaction } from '../../helpers/transaction.helper';
import { RolesEnum } from '../../model/enums/roles.enum';
import { IUser } from '../../model/IUser';
import { IAccessLevelsRepository } from '../../repositories/assign/accessLevels.assign';
import { IClassesRepository } from '../../repositories/assign/classes.assign';
import { ICustomerRepository } from '../../repositories/assign/customers.assign';
import { IFilesRepository } from '../../repositories/assign/files.assign';
import { ITermsRepository } from '../../repositories/assign/terms.assign';
import { IUserClassesRepository } from '../../repositories/assign/userClasses.assign';
import { IUsersRepository } from '../../repositories/assign/users.assign';
import { CreateStudentSchema } from '../../schema/createStudent.schema';
import S3Service from '../../services/aws/s3/S3Service';
import { SESService } from '../../services/aws/ses/SESService';
import { ResourceAlreadyExistsError } from '../errors/ResourceAlreadyExistsError';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

interface CreateStudentResponseDto {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  birthDate: string;
  gender: string;
  phoneNumber: string;
  cpf: string;
  customerId: string;
  classId: string;
}

export class CreateStudentUseCase {
  constructor(
    private readonly userRepository: IUsersRepository,
    private readonly userClassesRepository: IUserClassesRepository,
    private readonly classRepository: IClassesRepository,
    private readonly sesService: SESService,
    private readonly customerRepository: ICustomerRepository,
    private readonly accessLevelRepository: IAccessLevelsRepository,
    private readonly termsRepository: ITermsRepository,
    private readonly filesRepository: IFilesRepository,
    private readonly s3Service: S3Service
  ) {}

  public async execute(data: CreateStudentSchema): Promise<CreateStudentResponseDto> {
    const trx = await generateTransaction();
    let createdUser: IUser | undefined;
    const now = new Date();
    const {
      firstName,
      lastName,
      email,
      birthDate,
      gender,
      cpf,
      phoneNumber,
      customerId,
      classesId,
    } = data;

    try {
      const existingUser = await this.userRepository.findOneBy({
        email,
        cpf,
        customer_id: customerId,
      });

      if (existingUser) {
        throw new ResourceAlreadyExistsError(
          'Já existe usuário com este e-mail ou CPF para este cliente.'
        );
      }

      if (classesId != null) {
        const turma = await this.classRepository.findOneBy({
          id: classesId,
          customer_id: customerId,
        });

        if (!turma) throw new ResourceNotFoundError('Turma não encontrada');
      }

      const accessLevel = await this.accessLevelRepository.findByRole(RolesEnum.STUDENT);

      if (!accessLevel) throw new ResourceNotFoundError('Nível de acesso não encontrado');

      const user: IUser = {
        id: randomUUID(),
        first_name: firstName,
        last_name: lastName,
        email: email.toLowerCase().trim(),
        birth_date: birthDate ? formatDateToSQL(birthDate) : undefined,
        gender,
        cpf,
        phone_number: phoneNumber,
        customer_id: customerId,
        access_level_id: accessLevel.id,
        created_at: now,
        updated_at: now,
        role: RolesEnum.STUDENT,
        deleted_at: null,
      };

      createdUser = await this.userRepository.insertWithTrx(user, trx);

      if (!createdUser) throw new ResourceNotFoundError('Erro ao criar usuário');

      if (classesId != null) {
        await this.userClassesRepository.insertWithTrx(
          {
            user_id: createdUser.id,
            class_id: classesId,
            status: 'active',
            apply_date: now,
          },
          trx
        );
      }
      await trx.commit();
    } catch (error) {
      await trx.rollback();
      throw error;
    }
    const customer = await this.customerRepository.findOneBy({ id: customerId });

    if (!customer || !customer.subdomain) {
      throw new Error('Customer não informado ou sem subdomínio');
    }

    let termsLinks = '';
    try {
      const activeTerm = await this.termsRepository.findOneBy({
        customer_id: customerId,
        active: true,
      });
      if (activeTerm && activeTerm.file_id) {
        const file = await this.filesRepository.findOneBy({ id: activeTerm.file_id });
        if (file) {
          const termsUrl = await this.s3Service.getSignedUrl(env.BUCKET_FILES, file.url, 3600);
          if (termsUrl && termsUrl !== '#') {
            termsLinks = `<a href="${termsUrl}" style="color:#2F80ED; text-decoration:underline; margin:0 8px; font-size:11px; font-family:Inter,sans-serif; font-style:normal; font-weight:500; line-height:155%; letter-spacing:0.22px;">Termos de uso</a>`;
          }
        }
      }
    } catch (error) {
      console.error('Erro ao buscar termos de uso:', error);
    }

    let supportLink = '';
    const supportEmail = customer.support_email || customer.email;
    if (supportEmail) {
      supportLink = `<a href="mailto:${supportEmail}" style="color:#2F80ED; text-decoration:underline; margin:0 8px; font-size:11px; font-family:Inter,sans-serif; font-style:normal; font-weight:500; line-height:155%; letter-spacing:0.22px;">Suporte</a>`;
    }

    const encryptedUserId = bcrypt.hash(createdUser.id, 10);
    const siteUrlCompleteRegister = `https://${customer.subdomain}/welcome/${encryptedUserId}`;
    const templatePath = path.resolve(
      __dirname,
      '../../templates/email/createStudent.template.html'
    );
    let template = await fs.readFile(templatePath, 'utf-8');
    template = template
      .replace(/{{userEmail}}/g, email.toLowerCase())
      .replace(/{{link}}/g, siteUrlCompleteRegister)
      .replace(/{{company}}/g, customer?.name || 'Nossa plataforma')
      .replace(/{{year}}/g, new Date().getFullYear().toString())
      .replaceAll('#C1086C', customer.primary_color || '#C1086C')
      .replace(/{{termsLinks}}/g, termsLinks)
      .replace(/{{supportLink}}/g, supportLink);
    await this.sesService.sendEmail({
      to: email,
      subject: 'Seu acesso está liberado! Seja bem-vindo(a)!',
      body: template,
      isHtml: true,
    });
    return {
      id: createdUser.id as string,
      firstName: createdUser.first_name as string,
      lastName: createdUser.last_name as string,
      email: createdUser.email as string,
      birthDate: createdUser.birth_date as string,
      gender: createdUser.gender as string,
      phoneNumber: createdUser.phone_number as string,
      cpf: createdUser.cpf as string,
      customerId: createdUser.customer_id as string,
      classId: classesId as string,
    };
  }
}

function formatDateToSQL(date: string): string {
  const [day, month, year] = date.split('/');
  return `${year}/${month}/${day}`;
}
