import { randomUUID } from 'crypto';
import { promises as fs } from 'fs';
import { Knex } from 'knex';
import path from 'path';

import { env } from '../../env';
import { generateTransaction } from '../../helpers/transaction.helper';
import { CreateUserAdminResponseDto } from '../../model/DTO/response/user.dto';
import { IAccessLevelsRepository } from '../../repositories/assign/accessLevels.assign';
import { ICustomerRepository } from '../../repositories/assign/customers.assign';
import { IFilesRepository } from '../../repositories/assign/files.assign';
import { IRecoveryRepository } from '../../repositories/assign/recoveries.assign';
import { ITermsRepository } from '../../repositories/assign/terms.assign';
import { IUsersRepository } from '../../repositories/assign/users.assign';
import { CreateUserAdminRequestDto } from '../../schema/users.schema';
import S3Service from '../../services/aws/s3/S3Service';
import { SESService } from '../../services/aws/ses/SESService';
import { ResourceAlreadyExistsError } from '../errors/ResourceAlreadyExistsError';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

export class CreateUserAdminUseCase {
  constructor(
    private readonly userRepository: IUsersRepository,
    private readonly customerRepository: ICustomerRepository,
    private readonly accessLevelRepository: IAccessLevelsRepository,
    private readonly sesService: SESService,
    private readonly termsRepository: ITermsRepository,
    private readonly filesRepository: IFilesRepository,
    private readonly s3Service: S3Service,
    private readonly recoveryRepository: IRecoveryRepository
  ) {}

  public async execute(
    request: CreateUserAdminRequestDto & { customerId: string }
  ): Promise<CreateUserAdminResponseDto> {
    const trx = await generateTransaction();
    try {
      const { email, fullName, accessLevelId } = this.formatRequest(request);

      const [firstName, lastName] = this.splitFullName(fullName);

      await this.ensureEmailIsUnique(email, request.customerId);

      const customer = await this.getCustomerOrFail(request.customerId);
      const siteUrl = this.buildCustomerUrl(customer.subdomain ?? '');
      const userId = randomUUID();
      const accessLevel = await this.getAccessLevelOrFail(accessLevelId.toString());

      const recoveryToken = randomUUID();
      const recoveryId = randomUUID();

      await this.createUserInDatabase(
        userId,
        firstName,
        lastName,
        email,
        request.customerId,
        accessLevel.id,
        accessLevel.role,
        trx
      );

      await this.recoveryRepository.createRecovery(
        {
          id: recoveryId,
          user_id: userId,
          token: recoveryToken,
          expires_at: null,
        },
        trx
      );

      const link = `${siteUrl}/welcome/${recoveryToken}`;

      let termsLinks = '';
      try {
        const activeTerm = await this.termsRepository.findOneBy({
          customer_id: request.customerId,
          active: true,
        });
        if (activeTerm && activeTerm.file_id) {
          const file = await this.filesRepository.findOneBy({ id: activeTerm.file_id });
          if (file) {
            const termsUrl = await this.s3Service.getSignedUrl(env.BUCKET_FILES, file.url, 3600);
            if (termsUrl && termsUrl !== '#') {
              termsLinks = `<a href="${termsUrl}" style="color:#2F80ED; text-decoration:underline; margin:0 8px; font-size:11px; font-family:Inter,sans-serif; font-style:normal; font-weight:500; line-height:155%; letter-spacing:0.22px;">Termos de uso</a>`;
            }
          }
        }
      } catch (error) {
        console.error('Erro ao buscar termos de uso:', error);
      }

      let supportLink = '';
      const supportEmail = customer.support_email || customer.email;
      if (supportEmail) {
        supportLink = `<a href="mailto:${supportEmail}" style="color:#2F80ED; text-decoration:underline; margin:0 8px; font-size:11px; font-family:Inter,sans-serif; font-style:normal; font-weight:500; line-height:155%; letter-spacing:0.22px;">Suporte</a>`;
      }
      const year = new Date().getFullYear().toString();
      const primaryColor = customer.primary_color || '#C1086C';

      await this.sendWelcomeEmail(
        email,
        firstName,
        accessLevel.description,
        link,
        customer.name,
        termsLinks,
        supportLink,
        year,
        primaryColor
      );

      await trx.commit();
      return {
        userId,
        customerId: request.customerId,
        email,
        fullName,
        accessLevelId: accessLevel.id,
      };
    } catch (error) {
      await trx.rollback();
      console.error(error);
      throw error;
    }
  }

  private formatRequest(request: CreateUserAdminRequestDto) {
    return {
      email: request.email.trim().toLowerCase(),
      fullName: request.fullName.trim(),
      accessLevelId: request.accessLevelId,
    };
  }

  private splitFullName(fullName: string): [string, string] {
    const [firstName, ...lastNameArr] = fullName.split(' ');
    const lastName = lastNameArr.join(' ').trim();
    return [firstName, lastName];
  }

  private async ensureEmailIsUnique(email: string, customerId: string) {
    const existingUser = await this.userRepository.findOneBy({
      email,
      customer_id: customerId,
      deleted_at: null,
    });

    if (existingUser) {
      throw new ResourceAlreadyExistsError('Email já registrado.');
    }
  }

  private async getCustomerOrFail(customerId: string) {
    const customer = await this.customerRepository.findOneBy({ id: customerId });

    if (!customer || !customer.subdomain) {
      throw new ResourceNotFoundError('Cliente não encontrado');
    }
    return customer;
  }

  private async getAccessLevelOrFail(accessLevelId: string) {
    const accessLevel = await this.accessLevelRepository.findOneBy({ id: accessLevelId });

    if (!accessLevel) {
      throw new ResourceNotFoundError('Nível de acesso não encontrado');
    }
    return accessLevel;
  }

  private buildCustomerUrl(subdomain: string): string {
    return `https://${subdomain}`;
  }

  private async createUserInDatabase(
    userId: string,
    firstName: string,
    lastName: string,
    email: string,
    customerId: string,
    accessLevelId: string,
    role: string,
    trx: Knex.Transaction
  ) {
    const user = await this.userRepository.insertWithTrx(
      {
        id: userId,
        first_name: firstName,
        last_name: lastName,
        email,
        customer_id: customerId,
        access_level_id: accessLevelId,
        role,
        created_at: new Date(),
        updated_at: new Date(),
        deleted_at: null,
      },
      trx
    );
    return user;
  }

  private async sendWelcomeEmail(
    to: string,
    firstName: string,
    description: string,
    link: string,
    company: string,
    termsLinks: string,
    supportLink: string,
    year: string,
    primaryColor: string
  ) {
    const templatePath = path.resolve(
      __dirname,
      '../../templates/email/createUserAdmin.template.html'
    );
    let html = await fs.readFile(templatePath, 'utf-8');
    html = html
      .replace(/{{firstName}}/g, firstName)
      .replace(/{{description}}/g, description)
      .replace(/{{company}}/g, company)
      .replace(/{{link}}/g, link)
      .replace(/{{termsLinks}}/g, termsLinks)
      .replace(/{{supportLink}}/g, supportLink)
      .replace(/{{year}}/g, year)
      .replaceAll('#C1086C', primaryColor);

    await this.sesService.sendEmail({
      to,
      subject: 'Acesso à plataforma',
      body: html,
      isHtml: true,
    });
  }
}
