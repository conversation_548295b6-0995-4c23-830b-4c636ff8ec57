import { UserPublicDTO } from '../../../model/DTO/IUsers.dto';
import { IUser } from '../../../model/IUser';
import { IUserClasses } from '../../../model/IUserClasses';
import { ICustomerRepository } from '../../../repositories/assign/customers.assign';
import { IUserClassesRepository } from '../../../repositories/assign/userClasses.assign';
import { IUsersRepository } from '../../../repositories/assign/users.assign';
import { verifyExternalCustomer } from '../../../services/verifyExternalCustomer.service';
import { GenericError } from '../../errors/GenericError';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface DeleteExternalUserRequest {
  id: string;
  customerId: string;
  hardDelete?: boolean;
}

export class DeleteExternalUserUseCase {
  constructor(
    private readonly usersRepository: IUsersRepository,
    private readonly customerRepository: ICustomerRepository,
    private readonly userClassRepository: IUserClassesRepository
  ) {}

  async execute(request: DeleteExternalUserRequest): Promise<UserPublicDTO> {
    const { id, customerId, hardDelete } = request;

    const customer = await this.validateCustomer(customerId);

    const user = await this.findAndValidateUser(id, customer.id);

    if (hardDelete) {
      await this.userClassRepository.hardDeleteByUserId(user.id);
      await this.usersRepository.hardDelete(user.id, customer.id);
      return this.mapToPublicUser(user);
    }

    await this.deleteUserAccesses(user.id);

    const deletedUser = await this.deleteUser(user.id, customer.id);

    return this.mapToPublicUser(deletedUser);
  }

  private async validateCustomer(customerId: string) {
    return await verifyExternalCustomer(this.customerRepository, customerId);
  }

  private async findAndValidateUser(externalUserId: string, customerId: string): Promise<IUser> {
    const user = await this.usersRepository.findOneBy({
      external_user_id: externalUserId,
      customer_id: customerId,
      deleted_at: null,
    });

    if (!user) {
      throw new ResourceNotFoundError('Usuário não encontrado');
    }

    if (user.deleted_at) {
      throw new ResourceNotFoundError('Usuário já foi excluído');
    }

    return user;
  }

  private async deleteUserAccesses(userId: string): Promise<IUserClasses> {
    return this.userClassRepository.softDeleteByUserId(userId);
  }

  private async deleteUser(userId: string, customerId: string): Promise<IUser> {
    const deletedUser = await this.usersRepository.softDelete({
      id: userId,
      customerId,
    });

    if (!deletedUser) {
      throw new GenericError('Erro ao excluir usuário');
    }

    return deletedUser;
  }

  private mapToPublicUser(user: IUser): UserPublicDTO {
    const omitKeys = ['terms_accept', 'created_by', 'user_type_id', 'external_user_id'];

    return Object.fromEntries(
      Object.entries(user).filter(([key]) => !omitKeys.includes(key as (typeof omitKeys)[number]))
    ) as UserPublicDTO;
  }
}
