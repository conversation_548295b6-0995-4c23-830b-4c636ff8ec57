import { IUser } from '../../model/IUser';
import { IUsersRepository } from '../../repositories/assign/users.assign';
import { GenericError } from '../errors/GenericError';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

export class DeleteUserAdminUseCase {
  constructor(private readonly userRepository: IUsersRepository) {}

  public async execute(userId: string, customerId: string): Promise<IUser> {
    const user = await this.userRepository.findOneBy({ id: userId, customer_id: customerId });
    if (!user) {
      throw new ResourceNotFoundError('Usuário não encontrado.');
    }
    const deleted = await this.userRepository.softDelete({ id: userId, customerId });

    if (!deleted) {
      throw new GenericError('Erro ao deletar usuário.');
    }

    delete deleted.password;

    return deleted;
  }
}
