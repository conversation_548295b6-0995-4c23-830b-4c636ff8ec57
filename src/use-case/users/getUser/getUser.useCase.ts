import { IUser } from '../../../model/IUser';
import { IUsersRepository } from '../../../repositories/assign/users.assign';
import S3Service from '../../../services/aws/s3/S3Service';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

export class GetUserUseCase {
  constructor(
    private readonly usersRepository: IUsersRepository,
    private readonly s3Service: S3Service
  ) {}

  async execute(userId: string): Promise<IUser> {
    const user = await this.usersRepository.findOneBy({ id: userId });
    if (!user) throw new ResourceNotFoundError('Usuário não encontrado');

    if (user.image) {
      const bucketName = process.env.BUCKET_FILES || 'propofando-lxp-files';
      const responseGetSignedUrl = await this.s3Service.getSignedUrl(
        bucketName,
        user.image as string,
        36000
      );
      user.image = responseGetSignedUrl;
    }

    delete user.password;

    return user;
  }
}
