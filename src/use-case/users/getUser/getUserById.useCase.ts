import { RolesEnum } from '../../../model/enums/roles.enum';
import { IAccessLevelsRepository } from '../../../repositories/assign/accessLevels.assign';
import { IUsersRepository } from '../../../repositories/assign/users.assign';
import S3Service from '../../../services/aws/s3/S3Service';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

export class GetUserByIdUseCase {
  constructor(
    private userRepository: IUsersRepository,
    private accessLevelRepository: IAccessLevelsRepository,
    private s3Service: S3Service
  ) {}

  async execute(userId: string) {
    const user = await this.userRepository.getUserWithAccessLevel(userId);

    if (!user) {
      throw new ResourceNotFoundError('Usu<PERSON>rio não encontrado');
    }

    if (user.image) {
      const bucketName = process.env.BUCKET_FILES || 'propofando-lxp-files';
      user.image = await this.s3Service.getSignedUrl(bucketName, user.image as string, 36000);
    }

    const accessLevel = await this.accessLevelRepository.findByRole(RolesEnum.STUDENT);

    if (!accessLevel) {
      throw new ResourceNotFoundError('Nível de acesso não encontrado');
    }

    if (user.access_level_id === accessLevel.id) {
      user.access_level_id = accessLevel.id;
    }

    delete user.password;

    return user;
  }
}
