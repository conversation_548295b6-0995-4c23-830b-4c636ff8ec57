import { env } from '../../../env';
import { IUserWithCoursesAndClasses } from '../../../model/DTO/request/user.request.dto';
import { IClassesRepository } from '../../../repositories/assign/classes.assign';
import { IUsersRepository } from '../../../repositories/assign/users.assign';
import S3Service from '../../../services/aws/s3/S3Service';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface CourseWithClasses {
  id: string;
  name: string;
  description: string;
  active: string;
  className: string;
  classId: string;
  photoUrl: string | null;
  classes: { id: string; name: string }[];
}

interface UserWithCoursesAndClasses {
  user: {
    firstName: string;
    lastName: string;
    email: string;
    id: string;
    role: string;
    image: string | null;
    cpf: string;
    phoneNumber: string;
    gender: string;
    birthDate: string;
  };
  courses: CourseWithClasses[];
}
export class GetUserByIdAndCourseIdUseCase {
  constructor(
    private readonly userRepository: IUsersRepository,
    private readonly classRepository: IClassesRepository,
    private readonly s3Service: S3Service
  ) {}

  async execute(userId: string, customerId: string): Promise<UserWithCoursesAndClasses> {
    const rows = await this.userRepository.getUserWithCoursesAndClasses(userId, customerId);

    if (!rows || rows.length === 0) {
      throw new ResourceNotFoundError('Usuário não encontrado ou não vinculado ao curso');
    }

    const user = await this.buildUserInfo(rows[0]);
    const filteredCourses = this.filterActiveCourses(rows);
    const courseIds = filteredCourses.map((r) => r.course_id);
    const classes = (await this.classRepository.findByClassesByCoursesId(courseIds))
      .filter((c) => c.course_id !== undefined)
      .map((c) => ({
        id: c.id,
        name: c.name,
        course_id: c.course_id as string,
      }));

    const courses = await this.buildCourses(filteredCourses, classes);

    return {
      user,
      courses,
    };
  }

  private async buildUserInfo(userRow: IUserWithCoursesAndClasses) {
    const bucketName = env.BUCKET_FILES;
    const image = userRow.user_image
      ? await this.s3Service.getSignedUrl(bucketName, userRow.user_image, 36000)
      : null;

    return {
      firstName: userRow.first_name,
      lastName: userRow.last_name,
      email: userRow.email,
      id: String(userRow.user_id),
      role: userRow.role,
      image,
      cpf: userRow.cpf,
      phoneNumber: userRow.phone_number,
      gender: userRow.gender,
      birthDate: userRow.birth_date,
    };
  }

  private filterActiveCourses(rows: IUserWithCoursesAndClasses[]) {
    const map = new Map<string, IUserWithCoursesAndClasses>();

    for (const row of rows) {
      const isInactive = !!row.deletedAt;
      const existing = map.get(row.course_id);

      if (!existing || (!!existing.deletedAt && !isInactive)) {
        map.set(row.course_id, {
          ...row,
          status: isInactive ? 'inactive' : 'active',
        });
      }
    }

    return Array.from(map.values());
  }

  private async buildCourses(
    rows: IUserWithCoursesAndClasses[],
    classes: { id: string; name: string; course_id: string }[]
  ): Promise<CourseWithClasses[]> {
    return Promise.all(
      rows.map(async (row) => {
        const classesFiltered = classes
          .filter((c) => c.course_id === row.course_id)
          .map((cls) => ({ id: cls.id, name: cls.name }));

        return {
          id: row.course_id,
          name: row.course_name,
          description: row.description_course,
          active: row.status,
          className: row.class_name,
          classId: row.class_id,
          photoUrl: row.course_photo_url
            ? await this.s3Service.getSignedUrl(env.BUCKET_FILES, row.course_photo_url, 36000)
            : null,
          deletedAt: row.deletedAt,
          classes: classesFiltered,
        };
      })
    );
  }
}
