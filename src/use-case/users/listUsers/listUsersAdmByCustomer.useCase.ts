import { RolesEnum } from '../../../model/enums/roles.enum';
import { IAccessLevelsRepository } from '../../../repositories/assign/accessLevels.assign';
import { IUsersRepository } from '../../../repositories/assign/users.assign';
import { filterTextRegex } from '../../../services/filterTextRegex.service';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

export class ListUsersAdmByCustomerUseCase {
  constructor(
    private userRepository: IUsersRepository,
    private readonly accessLevelRepository: IAccessLevelsRepository
  ) {}

  async execute(customerId: string, orderByColumn?: string, orderByDirection?: string) {
    const allowedRoles = [RolesEnum.ADMIN, RolesEnum.COORDINATOR, RolesEnum.CREATOR];
    const accessLevels = await this.accessLevelRepository.findByRoles(allowedRoles);

    if (!accessLevels || accessLevels.length === 0) {
      throw new ResourceNotFoundError('Níveis de acesso não encontrados');
    }

    const accessLevelIds = accessLevels.map((al) => al.id);

    let users = await this.userRepository.findUsersByCustomerAdm(
      customerId,
      orderByColumn,
      orderByDirection,
      accessLevelIds
    );

    if ((orderByColumn as string) === 'firstName') {
      users = users.sort((a, b) =>
        filterTextRegex(a.firstName).localeCompare(filterTextRegex(b.firstName), 'pt-BR')
      );
    }

    return users;
  }
}
