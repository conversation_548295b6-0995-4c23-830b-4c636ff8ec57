import { IPaginationDTO } from '../../../model/DTO/IGeneric.dto';
import { IUsersRepository } from '../../../repositories/assign/users.assign';
import { ListUsersWithClassesSchema } from '../../../schema/users.schema';

export interface UserClassWithUserAndClassDTO {
  user_id: string;
  firstName: string;
  lastName?: string;
  email: string;
  classId: string;
  className: string;
}

export interface ListUsersWithClassesParams extends ListUsersWithClassesSchema {}

export interface ListUsersWithClassesResponse {
  paginationInfo: IPaginationDTO;
  users: UserClassWithUserAndClassDTO[];
}

export class ListUsersWithClassesUseCase {
  constructor(private userRepository: IUsersRepository) {}
  async execute(params: ListUsersWithClassesParams): Promise<ListUsersWithClassesResponse> {
    const data = await this.userRepository.findAllWithUserAndClass(params);
    return data;
  }
}
