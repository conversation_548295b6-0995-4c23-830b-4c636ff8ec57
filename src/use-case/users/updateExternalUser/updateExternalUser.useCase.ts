import { IAddUserToClassDTO } from '../../../model/DTO/IUserClasses.dto';
import { UserPublicDTO } from '../../../model/DTO/IUsers.dto';
import { StatusUserEnum } from '../../../model/enums/status.enum';
import { IUser } from '../../../model/IUser';
import { IClassesRepository } from '../../../repositories/assign/classes.assign';
import { ICustomerRepository } from '../../../repositories/assign/customers.assign';
import { IUserClassesRepository } from '../../../repositories/assign/userClasses.assign';
import { IUsersRepository } from '../../../repositories/assign/users.assign';
import { UpdateExternalUserDTO } from '../../../schema/users.schema';
import { verifyExternalCustomer } from '../../../services/verifyExternalCustomer.service';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface UpdateExternalUserPayload extends UpdateExternalUserDTO {
  id: string;
}
export class UpdateExternalUserUseCase {
  constructor(
    private readonly usersRepository: IUsersRepository,
    private readonly customerRepository: ICustomerRepository,
    private readonly userClassRepository: IUserClassesRepository,
    private readonly classesRepository: IClassesRepository
  ) {}

  async execute(payload: UpdateExternalUserPayload): Promise<UserPublicDTO> {
    const customer = await verifyExternalCustomer(this.customerRepository, payload.customerId);

    const existingUser = await this.usersRepository.findOneBy({
      external_user_id: payload.id,
      customer_id: customer.id,
      deleted_at: null,
    });

    if (!existingUser) {
      throw new ResourceNotFoundError('Usuário não encontrado');
    }

    const updatedData = this.buildUpdateData(payload, existingUser);

    const updatedUser = await this.usersRepository.update(updatedData);

    if (!updatedUser) {
      throw new ResourceNotFoundError('Erro ao atualizar usuário');
    }

    if (payload.classIds !== undefined) {
      await this.updateUserClassAssociations(
        existingUser.id,
        payload.classIds,
        customer.id,
        payload.status || StatusUserEnum.ACTIVE
      );
    }

    return this.mapToPublicUser(updatedUser);
  }

  private async updateUserClassAssociations(
    userId: string,
    classIds: string[],
    customerId: string,
    status: string
  ): Promise<void> {
    if (classIds.length === 0) {
      await this.userClassRepository.softDeleteByUserId(userId);
      return;
    }

    const validClassIds = await this.getValidatedClassIds(classIds, customerId);

    const existingAssociations = await this.userClassRepository.findByUserId(userId);
    const existingClassIds = new Set(existingAssociations.map((a) => a.class_id));

    const classIdsToRemove = existingAssociations
      .filter((a) => !validClassIds.includes(a.class_id))
      .map((a) => a.class_id);

    const classIdsToAdd = validClassIds.filter((id) => !existingClassIds.has(id));

    if (classIdsToRemove.length > 0) {
      await this.userClassRepository.softDeleteByUserIdAndClassIds(userId, classIdsToRemove);
    }

    if (classIdsToAdd.length > 0) {
      const insertData: IAddUserToClassDTO[] = classIdsToAdd.map((classId) => ({
        user_id: userId,
        class_id: classId,
        status,
        apply_date: new Date(),
      }));

      await this.userClassRepository.insertAll(insertData);
    }
  }

  private async getValidatedClassIds(
    externalClassIds: string[],
    customerId: string
  ): Promise<string[]> {
    const classes = await this.classesRepository.findByExternalClassIds({
      externalClassIds,
      customerId,
    });

    if (classes.length !== externalClassIds.length) {
      const foundExternalIds = new Set(classes.map((c) => c.external_class_id));
      const missingExternalIds = externalClassIds.filter((id) => !foundExternalIds.has(id));

      if (missingExternalIds.length > 0) {
        throw new ResourceNotFoundError(`Turmas não encontradas: ${missingExternalIds.join(', ')}`);
      }
    }

    return classes.map((c) => c.id);
  }

  private buildUpdateData(payload: UpdateExternalUserPayload, existingUser: IUser) {
    return {
      id: existingUser.id,
      first_name: payload.firstName ?? existingUser.first_name,
      last_name: payload.lastName ?? existingUser.last_name,
      email: payload.email ?? existingUser.email,
      phone_number: payload.phoneNumber ?? existingUser.phone_number,
      gender: payload.gender ?? existingUser.gender,
      birth_date: payload.birthDate ?? existingUser.birth_date,
      cpf: payload.cpf ?? existingUser.cpf,
      role: payload.role ?? existingUser.role,
      password: payload.password ?? existingUser.password,
    };
  }

  private mapToPublicUser(user: IUser): UserPublicDTO {
    const omitKeys = ['terms_accept', 'created_by', 'user_type_id', 'external_user_id'] as const;

    return Object.fromEntries(
      Object.entries(user).filter(([key]) => !omitKeys.includes(key as (typeof omitKeys)[number]))
    ) as UserPublicDTO;
  }
}
