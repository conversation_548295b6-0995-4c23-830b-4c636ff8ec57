import { IUser } from '../../../model/IUser';
import { ICountriesRepository } from '../../../repositories/assign/countries.assign';
import { IUsersRepository } from '../../../repositories/assign/users.assign';
import { UpdateUserDTO } from '../../../schema/users.schema';
import { ResourceNotFoundError } from '../../errors/ResourceNotFound';

interface UpdateUserInput extends UpdateUserDTO {}

export class UpdateUserUseCase {
  constructor(
    private readonly usersRepository: IUsersRepository,
    private readonly countriesRepository: ICountriesRepository
  ) {}

  async execute({
    userId,
    firstName,
    lastName,
    birthDate,
    gender,
    phoneNumber,
    countryId,
  }: UpdateUserInput): Promise<(IUser & { countryName?: string }) | undefined> {
    const user = await this.usersRepository.findOneBy({ id: userId });

    if (!user || user.id !== userId) {
      throw new Error('Usuário não encontrado');
    }

    let countryName: string | undefined;

    if (countryId) {
      const countryExists = await this.countriesRepository.findOneBy({ id: countryId });
      if (!countryExists) throw new ResourceNotFoundError('País não encontrado');
      countryName = countryExists.name;
    }

    const userUpdate = {
      id: userId,
      first_name: firstName,
      last_name: lastName,
      birth_date: birthDate,
      gender,
      phone_number: phoneNumber,
      country_id: countryId ?? user.country_id,
    };

    const userUpdated = (await this.usersRepository.update(userUpdate)) as IUser;

    delete userUpdated?.password;

    return {
      ...userUpdated,
      countryName,
    };
  }
}
