import { UpdateUserAdminResponseDto } from '../../model/DTO/response/user.dto';
import { IAccessLevelsRepository } from '../../repositories/assign/accessLevels.assign';
import { IUsersRepository } from '../../repositories/assign/users.assign';
import { UpdateUserAdminRequestDto } from '../../schema/users.schema';
import { ForbiddenError } from '../errors/ForbiddenError';
import { ResourceAlreadyExistsError } from '../errors/ResourceAlreadyExistsError';
import { ResourceNotFoundError } from '../errors/ResourceNotFound';

export class UpdateUserAdminUseCase {
  constructor(
    private readonly userRepository: IUsersRepository,
    private readonly accessLevelRepository: IAccessLevelsRepository
  ) {}

  public async execute(
    bodyData: UpdateUserAdminRequestDto,
    requesterId: string,
    customerId: string
  ): Promise<UpdateUserAdminResponseDto> {
    const { userId, email, fullName, accessLevelId } = bodyData;

    const formattedData = this.formatUserData(fullName, email);
    await this.validateUserExists(userId);
    await this.validatePermission(requesterId, userId, accessLevelId);
    await this.ensureEmailIsUnique(email, customerId, userId);

    await this.userRepository.update({
      id: userId,
      first_name: formattedData.firstName,
      last_name: formattedData.lastName,
      access_level_id: accessLevelId,
      email: formattedData.emailLowerCase,
      customer_id: customerId,
    });

    return {
      userId,
      customerId,
      email,
      fullName,
      accessLevelId,
    };
  }

  private formatUserData(fullName: string, email: string) {
    const [firstName, ...rest] = fullName.trim().split(' ');
    return {
      firstName,
      lastName: rest.join(' ').trim(),
      emailLowerCase: email.trim().toLowerCase(),
    };
  }

  private async validateUserExists(userId: string) {
    const user = await this.userRepository.findOneBy({ id: userId });
    if (!user) {
      throw new ResourceNotFoundError('Usuário não encontrado.');
    }
  }

  private async validatePermission(
    requesterId: string,
    targetUserId: string,
    newAccessLevelId: string
  ) {
    const requester = await this.userRepository.findOneBy({ id: requesterId });
    const targetUser = await this.userRepository.findOneBy({ id: targetUserId });

    if (!requester || !targetUser) {
      throw new ResourceNotFoundError('Usuário não encontrado.');
    }

    const requesterAccessLevel = await this.accessLevelRepository.findOneBy({
      id: requester.access_level_id,
    });
    const targetUserAccessLevel = await this.accessLevelRepository.findOneBy({
      id: targetUser.access_level_id,
    });
    const newAccessLevelObj = await this.accessLevelRepository.findOneBy({
      id: newAccessLevelId,
    });

    if (!requesterAccessLevel || !targetUserAccessLevel || !newAccessLevelObj) {
      throw new ForbiddenError('Nível de acesso inválido.');
    }

    const ROLE_PRIORITY: { [role: string]: number } = {
      OWNER: 0,
      SYSTEM_ADMIN: 1,
      SUPER_ADMIN: 2,
      ADMIN: 3,
      COORDINATOR: 4,
      CREATOR: 5,
      STUDENT: 6,
    };

    const requesterRole = requesterAccessLevel.name;
    const targetRole = targetUserAccessLevel.name;
    const newRole = newAccessLevelObj.name;

    const requesterPriority = ROLE_PRIORITY[requesterRole];
    const targetPriority = ROLE_PRIORITY[targetRole];
    const newPriority = ROLE_PRIORITY[newRole];

    if (requesterId === targetUserId) {
      return;
    }

    if (requesterRole === 'OWNER') {
      return;
    }

    if (requesterRole === 'COORDINATOR' || requesterRole === 'CREATOR') {
      throw new ForbiddenError('Coordenadores e Criadores só podem alterar seus próprios dados.');
    }

    if (requesterRole === 'SYSTEM_ADMIN' && targetRole === 'OWNER') {
      throw new ForbiddenError('Você não pode editar o Owner.');
    }

    if (requesterRole === 'SYSTEM_ADMIN' && newRole === 'OWNER') {
      throw new ForbiddenError('Você não pode promover para Owner.');
    }

    if (requesterPriority > targetPriority) {
      throw new ForbiddenError('Você só pode editar usuários com nível abaixo do seu.');
    }

    if (requesterPriority > newPriority) {
      throw new ForbiddenError('Você só pode promover para cargos abaixo ou igual ao seu.');
    }
  }

  private async ensureEmailIsUnique(email: string, customerId: string, userId: string) {
    const emailLowerCase = email.trim().toLowerCase();

    const existingUser = await this.userRepository.findOneBy({
      email: emailLowerCase,
      customer_id: customerId,
      deleted_at: null,
    });
    if (existingUser && existingUser.id !== userId) {
      throw new ResourceAlreadyExistsError('Email já registrado.');
    }
  }
}
