import AWS from 'aws-sdk';
import { env } from '../env';

const s3 = new AWS.S3({ region: env.CUSTOM_AWS_REGION });

/**
 * Verifica se o bucket S3 tem um arquivo index.html e cria um básico se não existir
 */
export async function ensureS3BucketHasIndexFile(bucketName: string): Promise<void> {
  try {
    console.log(`🔍 Verificando se existe index.html no bucket ${bucketName}...`);
    
    // Verificar se o arquivo index.html existe
    try {
      await s3.headObject({
        Bucket: bucketName,
        Key: 'index.html'
      }).promise();
      
      console.log(`✅ Arquivo index.html já existe no bucket ${bucketName}`);
      return;
    } catch (error: any) {
      if (error.code !== 'NotFound') {
        throw error;
      }
      console.log(`📝 Arquivo index.html não encontrado, criando um básico...`);
    }

    // Criar um arquivo index.html básico
    const basicIndexHtml = `<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BQ - Plataforma de Questões</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            background: white;
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 2rem;
        }
        .logo {
            font-size: 3rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 1rem;
        }
        .title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 1rem;
        }
        .subtitle {
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        .status {
            background: #e8f5e8;
            color: #2d5a2d;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">BQ</div>
        <h1 class="title">Plataforma de Questões</h1>
        <p class="subtitle">
            Sua plataforma está sendo configurada e estará disponível em breve.
        </p>
        <div class="status">
            <div class="loading"></div>
            Configurando domínio e certificados...
        </div>
        <p style="color: #999; font-size: 0.9rem; margin-top: 2rem;">
            Se você continuar vendo esta página, entre em contato com o suporte.
        </p>
    </div>
</body>
</html>`;

    // Upload do arquivo index.html
    await s3.putObject({
      Bucket: bucketName,
      Key: 'index.html',
      Body: basicIndexHtml,
      ContentType: 'text/html',
      CacheControl: 'max-age=300' // Cache por 5 minutos
    }).promise();

    console.log(`✅ Arquivo index.html criado com sucesso no bucket ${bucketName}`);

  } catch (error) {
    console.error(`❌ Erro ao configurar arquivo index.html no bucket ${bucketName}:`, error);
    throw error;
  }
}

/**
 * Verifica se o bucket S3 existe e tem as permissões corretas
 */
export async function verifyS3BucketConfiguration(bucketName: string): Promise<void> {
  try {
    console.log(`🔍 Verificando configuração do bucket ${bucketName}...`);
    
    // Verificar se o bucket existe
    await s3.headBucket({ Bucket: bucketName }).promise();
    console.log(`✅ Bucket ${bucketName} existe e é acessível`);

    // Verificar se o bucket tem website hosting habilitado
    try {
      const websiteConfig = await s3.getBucketWebsite({ Bucket: bucketName }).promise();
      console.log(`✅ Website hosting configurado:`, websiteConfig);
    } catch (error: any) {
      if (error.code === 'NoSuchWebsiteConfiguration') {
        console.log(`⚠️  Website hosting não configurado no bucket ${bucketName}`);
        console.log(`💡 Isso é normal para buckets que usam CloudFront com OAI`);
      } else {
        throw error;
      }
    }

    // Verificar CORS se necessário
    try {
      const corsConfig = await s3.getBucketCors({ Bucket: bucketName }).promise();
      console.log(`✅ CORS configurado:`, corsConfig);
    } catch (error: any) {
      if (error.code === 'NoSuchCORSConfiguration') {
        console.log(`⚠️  CORS não configurado no bucket ${bucketName}`);
      } else {
        throw error;
      }
    }

  } catch (error) {
    console.error(`❌ Erro ao verificar configuração do bucket ${bucketName}:`, error);
    throw error;
  }
}
