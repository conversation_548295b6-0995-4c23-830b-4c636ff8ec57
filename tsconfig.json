{
  "compilerOptions": {
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "noImplicitAny": true,
    "skipLibCheck": true,
    "typeRoots": [
      "./src/@types",
      "./src/types"
    ],
    "strictPropertyInitialization": false,
    "resolveJsonModule": true,
    "lib": [
      "ES2022"
    ],
    "module": "node16",
    "target": "ES2022",
    "outDir": "./dist" // Define a pasta onde os arquivos JavaScript serão gerados
  },
  "include": [
    "src/**/*", // Inclui todos os arquivos TypeScript na pasta src
    "src/@types",
    "src/@types/env.d.ts",
    "src/database/questions.json",
    "src/templates/**/*.html"
  ],
  "exclude": [
    "node_modules", // Exclui a pasta node_modules
    "dist",
    "**/*.spec.ts",
    "**/*.test.ts"
    // "src/events"
  ]
}